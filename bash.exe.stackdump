Stack trace:
Frame         Function      Args
0007FFFFBBD0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFBBD0, 0007FFFFAAD0) msys-2.0.dll+0x1FE8E
0007FFFFBBD0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBEA8) msys-2.0.dll+0x67F9
0007FFFFBBD0  000210046832 (000210286019, 0007FFFFBA88, 0007FFFFBBD0, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBD0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBBD0  000210068E24 (0007FFFFBBE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBEB0  00021006A225 (0007FFFFBBE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD7F4A0000 ntdll.dll
7FFD7E880000 KERNEL32.DLL
7FFD7C870000 KERNELBASE.dll
7FFD7D700000 USER32.dll
7FFD7D010000 win32u.dll
7FFD7E950000 GDI32.dll
7FFD7CC30000 gdi32full.dll
7FFD7D250000 msvcp_win.dll
7FFD7D040000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD7F3A0000 advapi32.dll
7FFD7EC00000 msvcrt.dll
7FFD7D650000 sechost.dll
7FFD7D960000 RPCRT4.dll
7FFD7BF40000 CRYPTBASE.DLL
7FFD7CEE0000 bcryptPrimitives.dll
7FFD7ED60000 IMM32.DLL
