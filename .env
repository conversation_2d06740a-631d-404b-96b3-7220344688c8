# 环境配置
NODE_ENV=development

# Redis 配置
REDIS_HOST=***************
REDIS_PORT=6379
REDIS_PASSWORD=123456

# MongoDB 配置
# A：如果用户在 admin 数据库中认证，但要访问 cluster 数据库
# MONGODB_URI=****************************:port/cluster?authSource=admin
# B：如果用户就在 cluster 数据库中认证和访问
# MONGODB_URI=****************************:port/cluster
# C：为认证服务使用专门的数据库
# MONGODB_URI=**************************************:port/auth?authSource=admin
MONGODB_URI=*************************************************************************************

# 新增微服务专用数据库配置
# 认证服务数据库
AUTH_MONGODB_URI=**************************************************************

# 角色服务数据库
CHARACTER_MONGODB_URI=************************************************************************

# 经济服务数据库
ECONOMY_MONGODB_URI=********************************************************************

# 社交服务数据库
SOCIAL_MONGODB_URI=******************************************************************

# 活动服务数据库
ACTIVITY_MONGODB_URI=**********************************************************************

# 比赛服务数据库
MATCH_MONGODB_URI=****************************************************************

# 旧项目数据库（用于数据迁移）
OLD_MONGODB_URI=**********************************************************************

# MongoDB 连接选项（可选）
MONGODB_MAX_POOL_SIZE=10
MONGODB_SERVER_SELECTION_TIMEOUT=5000
MONGODB_SOCKET_TIMEOUT=45000
MONGODB_CONNECT_TIMEOUT=10000

# MongoDB 高级选项（可选）
MONGODB_READ_PREFERENCE=primary
# MONGODB_REPLICA_SET=myReplicaSet
# MONGODB_TLS=false

# 动态端口分配配置
# 基础端口配置（用于动态端口计算）
GATEWAY_BASE_PORT=3000
AUTH_BASE_PORT=3100
CHARACTER_BASE_PORT=3200
ECONOMY_BASE_PORT=3300
SOCIAL_BASE_PORT=3400
ACTIVITY_BASE_PORT=3500
MATCH_BASE_PORT=3600
NOTIFICATION_BASE_PORT=3700

# 端口分配策略配置
PORT_ALLOCATION_STRATEGY=dynamic
PORT_RANGE_SIZE=10
VALIDATE_PORTS_ON_STARTUP=true
AUTO_RETRY_PORT_ON_CONFLICT=false

# 区服和实例配置（动态端口必需）
SERVER_ID=server_001
INSTANCE_ID=0

# ✅ 微服务URL配置已完全废弃
# 原因：已迁移到ServiceMesh动态服务发现 + 动态端口分配
# 影响：GraphQL Gateway已更新为使用动态端口
# 状态：可安全删除，无代码依赖

# 服务发现配置
SERVICE_DISCOVERY_ENABLED=true
SERVICE_DISCOVERY_PROVIDER=static

# 全局服务健康检查配置（优化后）
GLOBAL_SERVICE_HEALTH_CHECK_INTERVAL=10000  # 10秒（原30秒）
GLOBAL_SERVICE_INSTANCE_TIMEOUT=30000       # 30秒（原90秒）
GLOBAL_SERVICE_HEARTBEAT_INTERVAL=5000      # 5秒（原15秒）

# JWT 配置
JWT_SECRET=your-secret-key-change-in-production
JWT_ACCESS_TOKEN_TTL=15m
JWT_REFRESH_TOKEN_TTL=7d
JWT_EXPIRES_IN=24h

# 角色级Token配置（符合设计文档规范）
CHARACTER_JWT_SECRET=your-character-jwt-secret-change-in-production
CHARACTER_JWT_EXPIRES_IN=4h
CHARACTER_JWT_ALGORITHM=HS256
CHARACTER_JWT_ISSUER=football-manager-game
CHARACTER_JWT_AUDIENCE=football-manager-app

# 多服务器配置（新增）
MULTI_SERVER_ENABLED=true

DEFAULT_SERVER_ID=server_001
MAX_SERVERS=10
CHARACTER_SESSION_TIMEOUT=14400

# 区服配置（新增）
SERVER_001_ID=server_001
SERVER_001_NAME=开发服
SERVER_001_STATUS=active
SERVER_001_MAX_PLAYERS=10000

SERVER_002_ID=server_002
SERVER_002_NAME=测试服
SERVER_002_STATUS=active
SERVER_002_MAX_PLAYERS=10000

# CORS 配置
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# WebSocket 配置
WS_PING_TIMEOUT=60000
WS_PING_INTERVAL=25000

# 限流配置
THROTTLE_TTL=60000
THROTTLE_LIMIT=100

# 日志配置
LOG_LEVEL=debug

# 系统路由配置
# 注意：移除 /user，因为网关有真实的 UserController；/api/docs 等会通过回退机制处理
SYSTEM_ROUTE_PREFIXES=/health,/metrics,/api/docs,/api-json
STATIC_RESOURCE_PATHS=/favicon.ico,/robots.txt,/sitemap.xml

# Gateway配置
GATEWAY_PORT=3000
GATEWAY_GLOBAL_PREFIX=api

# Gateway区服发现配置
SERVER_CACHE_TIMEOUT=300
STATUS_CHECK_INTERVAL=60
HEALTH_CHECK_INTERVAL=300
RECOMMENDATION_CACHE_TIMEOUT=1800
