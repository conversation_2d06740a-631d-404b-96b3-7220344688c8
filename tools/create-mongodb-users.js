// ==================== 足球经理游戏微服务数据库用户创建脚本 V3.0 ====================
//
// 🎯 核心特性：
// 1. 区服独立用户模式，完美权限隔离
// 2. 智能参数配置，支持多种使用方式
// 3. 丰富的功能选项和错误处理
// 4. 详细的统计报告和连接字符串生成
// 5. 支持测试模式和批量操作
//
// 📖 使用方法：
// 1. 默认创建（3个区服）：
//    mongosh --host *************** --port 27017 -u admin -p FslcxeE2025 --file tools/create-mongodb-users.js
//
// 2. 指定区服：
//    mongosh --host *************** --port 27017 -u admin -p FslcxeE2025 --eval "var SERVER_IDS=['server_001','server_002'];" --file tools/create-mongodb-users.js
//
// 3. 指定服务：
//    mongosh --host *************** --port 27017 -u admin -p FslcxeE2025 --eval "var SERVICES=['character','hero'];" --file tools/create-mongodb-users.js
//
// 4. 测试模式（不实际创建）：
//    mongosh --host *************** --port 27017 -u admin -p FslcxeE2025 --eval "var DRY_RUN=true;" --file tools/create-mongodb-users.js
//
// 5. 自定义密码：
//    mongosh --host *************** --port 27017 -u admin -p FslcxeE2025 --eval "var DEFAULT_PASSWORD='NewPassword123';" --file tools/create-mongodb-users.js
//
// 6. 组合使用：
//    mongosh --host *************** --port 27017 -u admin -p FslcxeE2025 --eval "var SERVER_IDS=['server_001']; var SERVICES=['character','hero']; var DRY_RUN=true;" --file tools/create-mongodb-users.js

// ==================== 智能配置参数 ====================

// 区服列表（可通过--eval参数覆盖）
if (typeof SERVER_IDS === 'undefined') {
  var SERVER_IDS = ['server_001', 'server_002', 'server_003'];
}

// 服务列表（可通过--eval参数覆盖，支持部分创建）
if (typeof SERVICES === 'undefined') {
  var SERVICES = ['auth', 'character', 'economy', 'social', 'activity', 'match'];
}

// 测试模式（可通过--eval参数覆盖）
if (typeof DRY_RUN === 'undefined') {
  var DRY_RUN = false;
}

// 默认密码（可通过--eval参数覆盖）
if (typeof DEFAULT_PASSWORD === 'undefined') {
  var DEFAULT_PASSWORD = 'FslcxeE2025';
}

// 详细输出模式（可通过--eval参数覆盖）
if (typeof VERBOSE === 'undefined') {
  var VERBOSE = true;
}

// 完整服务配置映射
var SERVICE_CONFIG = {
  'auth': { name: 'auth', global: true, description: '认证服务（全局）' },
  'character': { name: 'character', global: false, description: '角色服务（区服）' },
  'economy': { name: 'economy', global: false, description: '经济服务（区服）' },
  'social': { name: 'social', global: false, description: '社交服务（区服）' },
  'activity': { name: 'activity', global: false, description: '活动服务（区服）' },
  'match': { name: 'match', global: false, description: '比赛服务（区服）' }
};

// 根据SERVICES参数过滤服务配置
var FILTERED_SERVICES = SERVICES.map(function(serviceName) {
  var config = SERVICE_CONFIG[serviceName];
  if (!config) {
    print("⚠️  未知服务: " + serviceName + "，将跳过");
    return null;
  }
  return config;
}).filter(function(config) {
  return config !== null;
});

// 增强统计信息
var STATS = {
  created: 0,
  existed: 0,
  failed: 0,
  dryRun: 0,
  total: 0,
  connections: [],
  startTime: new Date()
};

// ==================== 工具函数 ====================

/**
 * 生成数据库名称
 */
function generateDatabaseName(serviceName, serverId, isGlobal) {
  if (isGlobal) {
    return serviceName + '_db';
  } else {
    return serviceName + '_db_' + serverId;
  }
}

/**
 * 生成用户名
 */
function generateUsername(serviceName, serverId, isGlobal) {
  if (isGlobal) {
    return serviceName + '-admin';
  } else {
    return serviceName + '-admin-' + serverId;
  }
}

/**
 * 检查用户是否存在
 */
function userExists(database, username) {
  try {
    return database.getUser(username) !== null;
  } catch (e) {
    return false;
  }
}

/**
 * 创建服务用户（增强版）
 */
function createServiceUser(serviceName, serverId, isGlobal, description) {
  var dbName = generateDatabaseName(serviceName, serverId, isGlobal);
  var username = generateUsername(serviceName, serverId, isGlobal);
  var host = db.getMongo().host || "***************:27017";
  var connectionString = "mongodb://" + username + ":" + DEFAULT_PASSWORD + "@" + host;

  if (VERBOSE) {
    print("🔧 处理: " + description + " | 用户: " + username + " | 数据库: " + dbName);
  }

  try {
    var database = db.getSiblingDB(dbName);

    if (userExists(database, username)) {
      if (VERBOSE) {
        print("ℹ️  用户已存在: " + username + " -> " + dbName);
      }
      STATS.existed++;
      STATS.connections.push({
        service: serviceName,
        serverId: serverId,
        username: username,
        dbName: dbName,
        connectionString: connectionString,
        status: 'existed'
      });
      return true;
    }

    if (DRY_RUN) {
      print("🧪 [测试模式] 将创建用户: " + username + " -> " + dbName);
      STATS.dryRun++;
      STATS.connections.push({
        service: serviceName,
        serverId: serverId,
        username: username,
        dbName: dbName,
        connectionString: connectionString,
        status: 'dry_run'
      });
      return true;
    }

    // 创建用户
    database.createUser({
      user: username,
      pwd: DEFAULT_PASSWORD,
      roles: [
        { role: "dbOwner", db: dbName },
        { role: "readWrite", db: dbName }
      ]
    });

    print("✅ " + description + "用户创建成功: " + username + " -> " + dbName);
    STATS.created++;
    STATS.connections.push({
      service: serviceName,
      serverId: serverId,
      username: username,
      dbName: dbName,
      connectionString: connectionString,
      status: 'created'
    });
    return true;

  } catch (error) {
    print("❌ " + description + "用户创建失败: " + username + " -> " + dbName + " | 错误: " + error.message);
    STATS.failed++;
    STATS.connections.push({
      service: serviceName,
      serverId: serverId,
      username: username,
      dbName: dbName,
      connectionString: connectionString,
      status: 'failed',
      error: error.message
    });
    return false;
  }
}

/**
 * 验证用户权限
 */
function validateUserPermissions(username, dbName, password) {
  try {
    var database = db.getSiblingDB(dbName);
    database.auth(username, password);

    // 测试基本操作
    database.test_validation.insertOne({test: true, timestamp: new Date()});
    var result = database.test_validation.findOne({test: true});
    database.test_validation.deleteMany({test: true});

    return result !== null;
  } catch (error) {
    return false;
  }
}

// ==================== 管理员用户创建 ====================

// ==================== 启动信息显示 ====================

print("🚀 MongoDB用户创建脚本 V3.0 启动");
print("=".repeat(60));
print("📊 执行配置:");
print("   - 执行模式: " + (DRY_RUN ? "测试模式（不实际创建）" : "正常模式"));
print("   - 区服列表: " + SERVER_IDS.join(', ') + " (" + SERVER_IDS.length + "个)");
print("   - 服务列表: " + SERVICES.join(', ') + " (" + FILTERED_SERVICES.length + "个)");
print("   - 默认密码: " + (DEFAULT_PASSWORD.length > 8 ? DEFAULT_PASSWORD.substring(0, 8) + "..." : DEFAULT_PASSWORD));
print("   - 详细输出: " + (VERBOSE ? "开启" : "关闭"));

var globalServices = FILTERED_SERVICES.filter(s => s.global).length;
var serverServices = FILTERED_SERVICES.filter(s => !s.global).length;
var totalUsers = 1 + globalServices + (serverServices * SERVER_IDS.length);
print("   - 预计用户数: " + totalUsers + " (管理员:1 + 全局:" + globalServices + " + 区服:" + (serverServices * SERVER_IDS.length) + ")");

if (DRY_RUN) {
  print("\n🧪 注意：当前为测试模式，不会实际创建用户");
}
print("");

var adminDB = db.getSiblingDB('admin');

// 检查管理员用户是否已存在，若不存在则创建
try {
  if (!adminDB.getUser("admin")) {
    adminDB.createUser({
      user: "admin",
      pwd: DEFAULT_PASSWORD,
      roles: [
        { role: "root", db: "admin" }
      ]
    });
    print("✅ 管理员用户创建成功");
    STATS.created++;
  } else {
    print("ℹ️  管理员用户已存在");
    STATS.existed++;
  }
} catch (e) {
  print("ℹ️  管理员用户可能已存在或无权限检查，继续执行...");
  STATS.existed++;
}

STATS.total++;

// ==================== 服务用户创建（区服独立模式） ====================

print("\n🔧 开始创建服务用户...");

// 创建全局服务用户（如认证服务）
var globalServices = FILTERED_SERVICES.filter(service => service.global);
if (globalServices.length > 0) {
  print("📋 创建全局服务用户...");
  globalServices.forEach(function(service) {
    if (VERBOSE) {
      print("\n🌐 处理全局服务: " + service.name);
    }
    createServiceUser(service.name, null, service.global, service.description);
    STATS.total++;
  });
}

// 创建区服服务用户
var serverServices = FILTERED_SERVICES.filter(service => !service.global);
if (serverServices.length > 0) {
  print("\n📋 创建区服服务用户...");
  serverServices.forEach(function(service) {
    if (VERBOSE) {
      print("\n🏢 处理区服服务: " + service.name);
    }

    SERVER_IDS.forEach(function(serverId) {
      createServiceUser(service.name, serverId, service.global, service.description);
      STATS.total++;
    });
  });
}

// ==================== 详细统计报告 ====================

var endTime = new Date();
var duration = Math.round((endTime - STATS.startTime) / 1000 * 100) / 100;

print("\n" + "=".repeat(60));
print("📊 执行结果统计报告");
print("=".repeat(60));
print("执行时间: " + duration + " 秒");
print("总用户数: " + STATS.total);

if (DRY_RUN) {
  print("测试模式: " + STATS.dryRun + " 个用户将被创建");
} else {
  print("创建成功: " + STATS.created + " 个");
  print("已存在: " + STATS.existed + " 个");
  print("创建失败: " + STATS.failed + " 个");
  print("成功率: " + Math.round((STATS.created + STATS.existed) / STATS.total * 100) + "%");
}

// 显示失败详情
if (STATS.failed > 0) {
  print("\n❌ 失败的用户:");
  STATS.connections.filter(c => c.status === 'failed').forEach(function(conn) {
    print("  - " + conn.username + " -> " + conn.dbName + " | 错误: " + conn.error);
  });
}

// 执行结果总结
if (DRY_RUN) {
  print("\n🧪 测试模式完成！所有操作均为模拟，未实际创建用户。");
} else if (STATS.failed > 0) {
  print("\n⚠️  部分用户创建失败，请检查错误信息并重新执行");
} else {
  print("\n🎉 所有用户创建完成！权限隔离配置成功！");
}

// ==================== 连接字符串生成 ====================

print("\n🔗 生成的连接字符串示例:");
print("=".repeat(60));

// 全局服务连接字符串
FILTERED_SERVICES.filter(service => service.global).forEach(function(service) {
  var dbName = generateDatabaseName(service.name, null, service.global);
  var username = generateUsername(service.name, null, service.global);
  var connectionString = "mongodb://" + username + ":" + DEFAULT_PASSWORD + "@***************:27017/" + dbName;
  print(service.name.toUpperCase() + "_MONGODB_URI=" + connectionString);
});

// 区服服务连接字符串（仅显示第一个区服作为示例）
if (SERVER_IDS.length > 0) {
  var exampleServerId = SERVER_IDS[0];
  print("\n# 区服服务连接字符串示例（" + exampleServerId + "）:");

  FILTERED_SERVICES.filter(service => !service.global).forEach(function(service) {
    var dbName = generateDatabaseName(service.name, exampleServerId, service.global);
    var username = generateUsername(service.name, exampleServerId, service.global);
    var connectionString = "mongodb://" + username + ":" + DEFAULT_PASSWORD + "@***************:27017/" + dbName;
    print(service.name.toUpperCase() + "_MONGODB_URI=" + connectionString);
  });
}

// ==================== 使用说明 ====================

print("\n📖 使用说明:");
print("=".repeat(60));
print("1. 将上述连接字符串复制到对应服务的 .env.database 文件中");
print("2. 设置 SERVER_ID 环境变量来指定区服（如：SERVER_ID=server_001）");
print("3. 每个服务实例只需要配置自己的连接字符串");
print("4. 认证服务使用全局数据库，不受 SERVER_ID 影响");

print("\n🎯 优化效果:");
print("- 完美的权限隔离：每个区服用户只能访问自己的数据库");
print("- 线性扩展能力：新增区服只需创建对应用户");
print("- 安全性最佳：符合最小权限原则");
print("- 运维简单：用户创建完全自动化");

// ==================== 连接字符串生成 ====================

if (!DRY_RUN && (STATS.created > 0 || STATS.existed > 0)) {
  print("\n🔗 生成的连接字符串:");
  print("=".repeat(60));

  // 按服务分组显示连接字符串
  var serviceGroups = {};
  STATS.connections.filter(c => c.status === 'created' || c.status === 'existed').forEach(function(conn) {
    if (!serviceGroups[conn.service]) {
      serviceGroups[conn.service] = [];
    }
    serviceGroups[conn.service].push(conn);
  });

  Object.keys(serviceGroups).forEach(function(serviceName) {
    var connections = serviceGroups[serviceName];
    print("\n# " + serviceName.toUpperCase() + " 服务:");

    connections.forEach(function(conn) {
      var envVar = serviceName.toUpperCase() + "_MONGODB_URI";
      if (conn.serverId) {
        print("# " + conn.serverId + " 实例:");
      }
      print(envVar + "=" + conn.connectionString);
    });
  });

  print("\n📖 使用说明:");
  print("1. 将上述连接字符串复制到对应服务的 .env.database 文件中");
  print("2. 每个区服实例使用对应的连接字符串");
  print("3. 设置 SERVER_ID 环境变量来指定区服（如：SERVER_ID=server_001）");
  print("4. 认证服务使用全局连接字符串，不受 SERVER_ID 影响");
}

// ==================== 权限验证建议 ====================

if (!DRY_RUN && STATS.created > 0) {
  print("\n🔍 权限验证建议:");
  print("=".repeat(60));
  print("执行以下命令验证权限隔离:");

  // 提供验证命令示例
  if (SERVER_IDS.length >= 2) {
    var firstServer = SERVER_IDS[0];
    var secondServer = SERVER_IDS[1];
    var testService = FILTERED_SERVICES.find(s => !s.global);

    if (testService) {
      var username1 = generateUsername(testService.name, firstServer, false);
      var dbName1 = generateDatabaseName(testService.name, firstServer, false);
      var dbName2 = generateDatabaseName(testService.name, secondServer, false);

      print("\n# 测试正常访问（应该成功）:");
      var host = db.getMongo().host || "***************:27017";
      print('mongosh "mongodb://' + username1 + ':' + DEFAULT_PASSWORD + '@' + host + '/' + dbName1 + '" --eval "db.test.find()"');

      print("\n# 测试跨区服访问（应该失败）:");
      print('mongosh "mongodb://' + username1 + ':' + DEFAULT_PASSWORD + '@' + host + '/' + dbName2 + '" --eval "db.test.find()"');
    }
  }
}

print("\n🎯 优化效果总结:");
print("=".repeat(60));
print("✅ 完美的权限隔离：每个区服用户只能访问自己的数据库");
print("✅ 线性扩展能力：新增区服只需创建对应用户");
print("✅ 自动化管理：支持批量创建和智能配置");
print("✅ 安全性最佳：符合最小权限原则");
print("✅ 运维简单：一键创建，无需手动配置");

print("\n🚀 脚本执行完成！感谢使用 MongoDB用户管理脚本 V3.0");
print("=".repeat(60));
