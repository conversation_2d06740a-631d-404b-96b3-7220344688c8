#!/usr/bin/env node

/**
 * GameConfigFacade生成脚本
 * 基于已生成的接口文件，自动生成统一的配置访问门面
 * 
 * 功能：
 * - 扫描所有接口文件
 * - 生成装饰器和类型定义
 * - 生成GameConfigFacade类
 * - 生成业务增强方法
 * - 生成完整的TypeScript类型声明
 */

const path = require('path');
const chalk = require('chalk');
const ora = require('ora');

// 设置工作目录为项目根目录
process.chdir(path.resolve(__dirname, '../..'));

const FacadeGenerator = require('./libs/facade-generator');

async function main() {
  console.log(chalk.blue('🚀 Starting GameConfigFacade generation...'));
  
  const spinner = ora('Generating GameConfigFacade...').start();
  
  try {
    const generator = new FacadeGenerator();
    await generator.generateFacade();
    
    spinner.succeed(chalk.green('✅ GameConfigFacade generated successfully!'));
    
    console.log(chalk.blue('\n📊 Generation Summary:'));
    console.log(chalk.gray('   📁 Generated files:'));
    console.log(chalk.gray('     - libs/game-config/src/facades/game-config.facade.ts'));
    console.log(chalk.gray('     - libs/game-config/src/facades/business-types.ts'));

    console.log(chalk.blue('\n🎯 Features:'));
    console.log(chalk.gray('   ✅ 158个配置表自动生成访问器'));
    console.log(chalk.gray('   ✅ 完整的TypeScript类型安全'));
    console.log(chalk.gray('   ✅ 统一的CRUD操作（get, getAll, getBatch, search）'));
    console.log(chalk.gray('   ✅ 编译时静态生成，性能最优'));

    console.log(chalk.blue('\n💡 Usage:'));
    console.log(chalk.gray('   // 在业务代码中使用'));
    console.log(chalk.gray('   const hero = await this.gameConfig.hero.get(123);'));
    console.log(chalk.gray('   const items = await this.gameConfig.item.getBatch([1,2,3]);'));
    console.log(chalk.gray('   const allHeroes = await this.gameConfig.hero.getAll();'));
    
    console.log(chalk.blue('\n🔄 Next steps:'));
    console.log(chalk.gray('   1. 实现ConfigManager核心服务'));
    console.log(chalk.gray('   2. 在GameConfigModule中注册GameConfigFacade'));
    console.log(chalk.gray('   3. 在业务服务中注入使用'));
    
  } catch (error) {
    spinner.fail(chalk.red('❌ Failed to generate GameConfigFacade'));
    console.error(chalk.red('\n💥 Error Details:'));
    console.error(chalk.red('   Message:'), error.message);
    
    if (error.stack) {
      console.error(chalk.red('   Stack:'));
      console.error(chalk.gray(error.stack));
    }
    
    console.error(chalk.red('\n🔧 Troubleshooting:'));
    console.error(chalk.gray('   1. 确保已运行 npm run config:generate'));
    console.error(chalk.gray('   2. 检查 libs/game-config/src/interfaces/ 目录是否存在'));
    console.error(chalk.gray('   3. 确保接口文件格式正确'));
    
    process.exit(1);
  }
}

// 检查前置条件
async function checkPrerequisites() {
  const fs = require('fs-extra');
  
  // 检查接口目录是否存在
  const interfacesPath = 'libs/game-config/src/interfaces';
  if (!await fs.pathExists(interfacesPath)) {
    console.error(chalk.red('❌ 接口目录不存在，请先运行: npm run config:generate'));
    process.exit(1);
  }
  
  // 检查是否有接口文件
  const files = await fs.readdir(interfacesPath);
  const interfaceFiles = files.filter(f => f.endsWith('.interface.ts'));
  
  if (interfaceFiles.length === 0) {
    console.error(chalk.red('❌ 未找到接口文件，请先运行: npm run config:generate'));
    process.exit(1);
  }
  
  console.log(chalk.green(`✅ 找到 ${interfaceFiles.length} 个接口文件`));
}

// 主函数
async function run() {
  try {
    await checkPrerequisites();
    await main();
  } catch (error) {
    console.error(chalk.red('💥 Unexpected error:'), error.message);
    process.exit(1);
  }
}

run();
