/**
 * 核心模块单元测试
 */

const WebSocketManager = require('../../src/core/websocket-manager');
const SessionManager = require('../../src/core/session-manager');
const configManager = require('../../src/config/config-manager');

describe('核心模块测试', () => {
  describe('SessionManager', () => {
    let sessionManager;
    
    beforeEach(() => {
      sessionManager = new SessionManager();
    });
    
    test('应该能够设置和获取令牌', () => {
      const token = 'test-token-123';
      sessionManager.setToken(token);
      expect(sessionManager.getToken()).toBe(token);
      expect(sessionManager.isValid()).toBe(false); // 还需要userId
    });
    
    test('应该能够设置用户ID', () => {
      const userId = 'user-123';
      sessionManager.setUserId(userId);
      expect(sessionManager.getUserId()).toBe(userId);
    });
    
    test('应该能够管理游戏状态', () => {
      const characterData = {
        id: 'char-123',
        name: '测试角色',
        level: 5
      };
      
      sessionManager.updateGameState('character', characterData);
      expect(sessionManager.getGameState('character')).toEqual(characterData);
    });
    
    test('应该支持嵌套状态访问', () => {
      sessionManager.updateGameState('resources.gold', 1000);
      expect(sessionManager.getGameState('resources.gold')).toBe(1000);
    });
    
    test('应该能够导出和导入会话', () => {
      sessionManager.setToken('test-token');
      sessionManager.setUserId('user-123');
      sessionManager.updateGameState('character', { name: '测试' });
      
      const exported = sessionManager.export();
      
      const newSession = new SessionManager();
      newSession.import(exported);
      
      expect(newSession.getToken()).toBe('test-token');
      expect(newSession.getUserId()).toBe('user-123');
      expect(newSession.getGameState('character.name')).toBe('测试');
    });
  });
  
  describe('ConfigManager', () => {
    test('应该能够获取配置值', () => {
      const gatewayUrl = configManager.get('gateway.url');
      expect(typeof gatewayUrl).toBe('string');
      expect(gatewayUrl.length).toBeGreaterThan(0);
    });
    
    test('应该能够设置配置值', () => {
      configManager.set('test.value', 'test-123');
      expect(configManager.get('test.value')).toBe('test-123');
    });
    
    test('应该返回默认值', () => {
      const defaultValue = 'default-test';
      const value = configManager.get('non.existent.key', defaultValue);
      expect(value).toBe(defaultValue);
    });
    
    test('应该能够获取环境信息', () => {
      const envInfo = configManager.getEnvironmentInfo();
      expect(envInfo).toHaveProperty('nodeVersion');
      expect(envInfo).toHaveProperty('platform');
      expect(envInfo).toHaveProperty('configLoaded');
      expect(envInfo.configLoaded).toBe(true);
    });
  });
  
  describe('WebSocketManager', () => {
    let wsManager;
    
    beforeEach(() => {
      wsManager = new WebSocketManager('ws://localhost:3000');
    });
    
    afterEach(() => {
      if (wsManager) {
        wsManager.disconnect();
      }
    });
    
    test('应该能够创建WebSocket管理器', () => {
      expect(wsManager).toBeDefined();
      expect(wsManager.url).toBe('ws://localhost:3000');
      expect(wsManager.isConnected).toBe(false);
    });
    
    test('应该能够获取连接状态', () => {
      const status = wsManager.getStatus();
      expect(status).toHaveProperty('connected');
      expect(status).toHaveProperty('url');
      expect(status).toHaveProperty('pendingRequests');
      expect(status).toHaveProperty('queuedMessages');
      expect(status.connected).toBe(false);
    });
    
    test('应该能够处理消息队列', () => {
      // 在未连接状态下发送消息应该加入队列
      wsManager.sendMessage('test.command', { data: 'test' }).catch(() => {
        // 预期会失败，因为没有实际连接
      });
      
      const status = wsManager.getStatus();
      expect(status.queuedMessages).toBeGreaterThan(0);
    });
  });
});
