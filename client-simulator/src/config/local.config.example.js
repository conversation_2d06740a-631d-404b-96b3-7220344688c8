/**
 * 本地配置文件示例
 * 
 * 复制此文件为 local.config.js 并根据需要修改配置
 */

module.exports = {
  // 网关配置
  gateway: {
    url: 'http://127.0.0.1:3000',  // 修改为实际的网关地址
    timeout: 30000,
    reconnect: true,
    maxRetries: 5
  },

  // 认证配置
  auth: {
    url: 'http://127.0.0.1:3001',  // 修改为实际的认证服务地址
    timeout: 30000,
    retries: 3
  },
  
  // 日志配置
  logging: {
    level: 'debug',  // 开发时使用debug级别
    console: true,
    file: true
  },
  
  // 性能配置
  performance: {
    concurrent: 5,   // 开发时减少并发数
    delay: 2000,     // 增加操作间延迟
    timeout: 60000
  },
  
  // 测试配置
  test: {
    environment: 'development',
    cleanup: false,  // 开发时不自动清理数据
    mockData: false
  },
  
  // 游戏配置
  game: {
    defaultServerId: 'server_001',
    defaultPassword: 'Test123456!',
    userPrefix: 'dev_test_',
    characterPrefix: '开发测试_'
  }
};
