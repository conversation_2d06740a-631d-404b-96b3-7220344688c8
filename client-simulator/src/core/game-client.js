/**
 * 游戏客户端
 * 
 * 功能：
 * 1. 游戏服务器连接管理
 * 2. 操作执行和调度
 * 3. 场景运行管理
 * 4. API调用封装
 */

const axios = require('axios');
const WebSocketManager = require('./websocket-manager');
const SessionManager = require('./session-manager');
const ActionRegistry = require('./action-registry');
const configManager = require('../config/config-manager');
const logger = require('../utils/logger');

class GameClient {
  constructor(options = {}) {
    // 合并配置
    this.config = {
      ...configManager.getAll(),
      ...options
    };
    
    // 初始化核心组件
    this.websocket = new WebSocketManager(
      this.config.gateway.url,
      this.config.gateway
    );
    
    this.session = new SessionManager();
    this.actionRegistry = new ActionRegistry();
    
    // 状态管理
    this.isConnected = false;
    this.currentScenario = null;
    this.executionHistory = [];
    
    // 绑定事件
    this.setupEventHandlers();
    
    logger.info('🎮 游戏客户端已初始化');
  }

  /**
   * 设置事件处理器
   */
  setupEventHandlers() {
    this.websocket.on('connected', () => {
      this.isConnected = true;
      logger.success('游戏客户端已连接');
    });
    
    this.websocket.on('disconnected', () => {
      this.isConnected = false;
      logger.warn('游戏客户端连接断开');
    });
    
    this.websocket.on('error', (error) => {
      logger.failure('游戏客户端连接错误', error);
    });
    
    this.websocket.on('message', (message) => {
      this.handleServerMessage(message);
    });
  }

  /**
   * HTTP注册用户
   */
  async register(userData) {
    try {
      const registerUrl = `${this.config.gateway.url}/api/auth/auth/register`;

      logger.info('📝 正在注册用户...', { username: userData.username });

      // 构建完整的注册数据
      const registerData = {
        username: userData.username,
        email: userData.email,
        password: userData.password,
        confirmPassword: userData.password,
        acceptTerms: true,
        profile: {
          firstName: 'Test',
          lastName: 'User'
        }
      };

      const response = await axios.post(registerUrl, registerData, {
        timeout: this.config.gateway.timeout,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        logger.success('✅ 用户注册成功', { userId: response.data.data?.userId });
        return response.data;
      } else {
        throw new Error(response.data.message || '注册失败');
      }
    } catch (error) {
      logger.failure('❌ 用户注册失败', error.message);
      if (error.response) {
        logger.error('响应状态:', error.response.status);
        logger.error('响应数据:', error.response.data);
        logger.error('响应头:', error.response.headers);
      }
      throw error;
    }
  }

  /**
   * HTTP登录获取token
   */
  async login(credentials) {
    try {
      const loginUrl = `${this.config.gateway.url}/api/auth/auth/login`;

      logger.info('🔑 正在登录...', { username: credentials.username });

      // 构建登录数据
      const loginData = {
        identifier: credentials.username,  // 使用identifier而不是username
        password: credentials.password
      };

      const response = await axios.post(loginUrl, loginData, {
        timeout: this.config.gateway.timeout,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.status === 200) {
        const token = response.data.data?.tokens?.accessToken;
        if (token) {
          this.session.setToken(token);
          this.session.setUserId(credentials.username);  // 设置userId
          this.websocket.setToken(token);
          logger.success('✅ 登录成功，已获取token');
          return response.data;
        } else {
          throw new Error('登录响应中未包含token');
        }
      } else {
        throw new Error(response.data.message || '登录失败');
      }
    } catch (error) {
      logger.failure('❌ 登录失败', error.message);
      throw error;
    }
  }

  /**
   * 连接到游戏服务器
   */
  async connect() {
    try {
      logger.info('🔌 正在连接游戏服务器...');
      await this.websocket.connect();
      this.isConnected = true;
      logger.success('游戏服务器连接成功');
    } catch (error) {
      this.isConnected = false;
      logger.failure('游戏服务器连接失败', error);
      throw error;
    }
  }

  /**
   * 断开连接
   */
  async disconnect() {
    try {
      this.websocket.disconnect();
      this.isConnected = false;
      logger.info('🔌 已断开游戏服务器连接');
    } catch (error) {
      logger.failure('断开连接失败', error);
      throw error;
    }
  }

  /**
   * 检查连接状态
   */
  isConnected() {
    return this.isConnected && this.websocket.isConnected;
  }

  /**
   * 执行游戏操作
   */
  async performAction(actionName, params = {}) {
    const startTime = Date.now();
    
    try {
      // 获取操作实例
      const action = this.actionRegistry.getAction(actionName);
      if (!action) {
        throw new Error(`未找到操作: ${actionName}`);
      }
      
      logger.action(actionName, '开始执行操作', { params });
      
      // 执行操作
      const result = await action.execute(this, params);
      
      // 记录执行历史
      this.recordExecution(actionName, params, result, Date.now() - startTime);
      
      return result;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.failure(`操作执行失败: ${actionName}`, error);
      
      const errorResult = {
        success: false,
        error: {
          message: error.message,
          code: error.code || 'EXECUTION_ERROR'
        },
        duration,
        timestamp: new Date().toISOString()
      };
      
      this.recordExecution(actionName, params, errorResult, duration);
      
      return errorResult;
    }
  }

  /**
   * 直接调用游戏API
   */
  async callAPI(command, data = {}) {
    try {
      // 添加会话信息
      const requestData = {
        ...data,
        token: this.session.getToken(),
        characterId: this.session.getCharacterId()
      };
      
      logger.network('send', command, requestData);
      
      const response = await this.websocket.sendMessage(command, requestData);
      
      logger.network('receive', command, response);
      
      return response;
      
    } catch (error) {
      logger.failure(`API调用失败: ${command}`, error);
      throw error;
    }
  }

  /**
   * 执行游戏场景
   */
  async playScenario(scenarioName, playerData = {}) {
    try {
      logger.info(`🎬 开始执行场景: ${scenarioName}`);
      
      // 加载场景
      const scenario = await this.loadScenario(scenarioName);
      
      // 创建虚拟玩家
      const VirtualPlayer = require('./virtual-player');
      const player = new VirtualPlayer(this, playerData);
      
      // 设置当前场景
      this.currentScenario = {
        name: scenarioName,
        player,
        startTime: new Date(),
        status: 'running'
      };
      
      // 执行场景
      const result = await player.playScenario(scenario);
      
      // 更新场景状态
      this.currentScenario.status = result.success ? 'completed' : 'failed';
      this.currentScenario.endTime = new Date();
      this.currentScenario.result = result;
      
      logger.success(`场景执行完成: ${scenarioName}`);
      
      return result;
      
    } catch (error) {
      if (this.currentScenario) {
        this.currentScenario.status = 'error';
        this.currentScenario.endTime = new Date();
        this.currentScenario.error = error;
      }
      
      logger.failure(`场景执行失败: ${scenarioName}`, error);
      throw error;
    }
  }

  /**
   * 加载场景配置
   */
  async loadScenario(scenarioName) {
    const ScenarioLoader = require('../utils/scenario-loader');
    const loader = new ScenarioLoader();
    return await loader.load(scenarioName);
  }

  /**
   * 处理服务器消息
   */
  handleServerMessage(message) {
    logger.debug('📥 收到服务器消息:', message);
    
    // 处理服务器主动推送的消息
    if (message.type === 'notification') {
      this.handleNotification(message.data);
    } else if (message.type === 'update') {
      this.handleGameUpdate(message.data);
    }
  }

  /**
   * 处理通知消息
   */
  handleNotification(data) {
    logger.info(`📢 服务器通知: ${data.message}`);
  }

  /**
   * 处理游戏更新
   */
  handleGameUpdate(data) {
    // 自动更新游戏状态
    if (data.character) {
      this.session.updateCharacter(data.character);
    }
    if (data.heroes) {
      this.session.updateHeroes(data.heroes);
    }
    if (data.guild) {
      this.session.updateGuild(data.guild);
    }
    
    logger.debug('🔄 游戏状态已更新');
  }

  /**
   * 记录执行历史
   */
  recordExecution(actionName, params, result, duration) {
    const record = {
      actionName,
      params,
      result,
      duration,
      timestamp: new Date().toISOString(),
      sessionId: this.session.getUserId()
    };
    
    this.executionHistory.push(record);
    
    // 限制历史记录数量
    if (this.executionHistory.length > 1000) {
      this.executionHistory = this.executionHistory.slice(-500);
    }
  }

  /**
   * 获取执行历史
   */
  getExecutionHistory(limit = 50) {
    return this.executionHistory.slice(-limit);
  }

  /**
   * 获取当前场景状态
   */
  getCurrentScenario() {
    return this.currentScenario;
  }

  /**
   * 获取客户端状态
   */
  getStatus() {
    return {
      connected: this.isConnected,
      session: this.session.getSummary(),
      websocket: this.websocket.getStatus(),
      currentScenario: this.currentScenario ? {
        name: this.currentScenario.name,
        status: this.currentScenario.status,
        startTime: this.currentScenario.startTime
      } : null,
      executionHistory: this.executionHistory.length,
      availableActions: this.actionRegistry.getStatistics()
    };
  }

  /**
   * 获取可用操作列表
   */
  getAvailableActions(category = null) {
    if (category) {
      return this.actionRegistry.getActionsByCategory(category);
    }
    return this.actionRegistry.getAllActions();
  }

  /**
   * 搜索操作
   */
  searchActions(query) {
    return this.actionRegistry.searchActions(query);
  }

  /**
   * 获取操作帮助
   */
  getActionHelp(actionName) {
    const action = this.actionRegistry.getAction(actionName);
    if (!action) {
      return null;
    }
    return action.getHelp();
  }
}

module.exports = GameClient;
