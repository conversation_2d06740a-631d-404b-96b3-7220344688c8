/**
 * 球员升级
 * 
 * 微服务: hero
 * 模块: hero
 * Controller: hero
 * Pattern: hero.levelUp
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.531Z
 */

const BaseAction = require('../../../core/base-action');

class HerolevelUpAction extends BaseAction {
  static metadata = {
    name: '球员升级',
    description: '球员升级',
    category: 'hero',
    serviceName: 'hero',
    module: 'hero',
    actionName: 'hero.levelUp',
    prerequisites: ["login","character"],
    params: {
      "levelUpDto": {
            "type": "object",
            "required": true,
            "description": "levelUpDto参数",
            "properties": {
                  "heroId": {
                        "type": "string",
                        "required": true,
                        "description": "heroId参数"
                  },
                  "targetLevel": {
                        "type": "number",
                        "required": false,
                        "description": "targetLevel参数"
                  },
                  "useExpItems": {
                        "type": "boolean",
                        "required": false,
                        "description": "useExpItems参数"
                  }
            },
            "example": {
                  "heroId": "示例heroId",
                  "targetLevel": 1,
                  "useExpItems": true
            }
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { levelUpDto, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      levelUpDto,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '球员升级成功'
      };
    } else {
      throw new Error(`球员升级失败: ${response.message}`);
    }
  }
}

module.exports = HerolevelUpAction;