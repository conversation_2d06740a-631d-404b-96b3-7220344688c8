/**
 * 根据位置获取球员配置
 * 
 * 微服务: hero
 * 模块: hero
 * Controller: hero
 * Pattern: hero.getConfigByPosition
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.518Z
 */

const BaseAction = require('../../../core/base-action');

class HerogetConfigByPositionAction extends BaseAction {
  static metadata = {
    name: '根据位置获取球员配置',
    description: '根据位置获取球员配置',
    category: 'hero',
    serviceName: 'hero',
    module: 'hero',
    actionName: 'hero.getConfigByPosition',
    prerequisites: ["login","character"],
    params: {
      "position": {
            "type": "object",
            "required": true,
            "description": "position参数",
            "properties": {
                  "data": {
                        "type": "object",
                        "required": true,
                        "description": "HeroPosition类型的数据对象"
                  }
            },
            "example": {
                  "data": {}
            }
      },
      "limit": {
            "type": "number",
            "required": false,
            "description": "limit参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { position, limit } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      position,
      limit
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '根据位置获取球员配置成功'
      };
    } else {
      throw new Error(`根据位置获取球员配置失败: ${response.message}`);
    }
  }
}

module.exports = HerogetConfigByPositionAction;