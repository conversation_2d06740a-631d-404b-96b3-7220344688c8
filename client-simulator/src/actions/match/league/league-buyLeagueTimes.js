/**
 * 购买联赛次数 基于old项目的购买次数逻辑
 * 
 * 微服务: match
 * 模块: league
 * Controller: league
 * Pattern: league.buyLeagueTimes
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.656Z
 */

const BaseAction = require('../../../core/base-action');

class LeaguebuyLeagueTimesAction extends BaseAction {
  static metadata = {
    name: '购买联赛次数 基于old项目的购买次数逻辑',
    description: '购买联赛次数 基于old项目的购买次数逻辑',
    category: 'match',
    serviceName: 'match',
    module: 'league',
    actionName: 'league.buyLeagueTimes',
    prerequisites: ["login","character"],
    params: {
      "buyLeagueTimesDto": {
            "type": "object",
            "required": true,
            "description": "buyLeagueTimesDto参数",
            "properties": {
                  "characterId": {
                        "type": "string",
                        "required": true,
                        "description": "characterId参数"
                  },
                  "times": {
                        "type": "number",
                        "required": true,
                        "description": "times参数"
                  },
                  "serverId": {
                        "type": "string",
                        "required": false,
                        "description": "serverId参数"
                  }
            },
            "example": {
                  "characterId": "示例characterId",
                  "times": 1,
                  "serverId": "示例serverId"
            }
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { buyLeagueTimesDto } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      buyLeagueTimesDto
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '购买联赛次数 基于old项目的购买次数逻辑成功'
      };
    } else {
      throw new Error(`购买联赛次数 基于old项目的购买次数逻辑失败: ${response.message}`);
    }
  }
}

module.exports = LeaguebuyLeagueTimesAction;