/**
 * 创建物品实例（内部方法，主要用于测试） 对应old项目: newItem方法，优化API命名
 * 
 * 微服务: character
 * 模块: item
 * Controller: item
 * Pattern: item.createItemInstance
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.385Z
 */

const BaseAction = require('../../../core/base-action');

class ItemcreateItemInstanceAction extends BaseAction {
  static metadata = {
    name: '创建物品实例（内部方法，主要用于测试） 对应old项目: newItem方法，优化API命名',
    description: '创建物品实例（内部方法，主要用于测试） 对应old项目: newItem方法，优化API命名',
    category: 'character',
    serviceName: 'character',
    module: 'item',
    actionName: 'item.createItemInstance',
    prerequisites: ["login"],
    params: {
      "configId": {
            "type": "number",
            "required": true,
            "description": "configId参数"
      },
      "quantity": {
            "type": "number",
            "required": true,
            "description": "quantity参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { configId, quantity } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      configId,
      quantity
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '创建物品实例（内部方法，主要用于测试） 对应old项目: newItem方法，优化API命名成功'
      };
    } else {
      throw new Error(`创建物品实例（内部方法，主要用于测试） 对应old项目: newItem方法，优化API命名失败: ${response.message}`);
    }
  }
}

module.exports = ItemcreateItemInstanceAction;