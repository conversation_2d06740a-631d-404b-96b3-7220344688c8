/**
 * 创建角色 对应old项目: game.player.createRole
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.createRole
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.329Z
 */

const BaseAction = require('../../../core/base-action');

class CharactercreateRoleAction extends BaseAction {
  static metadata = {
    name: '创建角色 对应old项目: game.player.createRole',
    description: '创建角色 对应old项目: game.player.createRole',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.createRole',
    prerequisites: ["login"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "qualified": {
            "type": "number",
            "required": true,
            "description": "qualified参数"
      },
      "name": {
            "type": "string",
            "required": true,
            "description": "name参数"
      },
      "faceIcon": {
            "type": "number",
            "required": true,
            "description": "faceIcon参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, qualified, name, faceIcon, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      qualified,
      name,
      faceIcon,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '创建角色 对应old项目: game.player.createRole成功'
      };
    } else {
      throw new Error(`创建角色 对应old项目: game.player.createRole失败: ${response.message}`);
    }
  }
}

module.exports = CharactercreateRoleAction;