/**
 * 整理背包 对应old项目: sortBag方法
 * 
 * 微服务: character
 * 模块: inventory
 * Controller: inventory
 * Pattern: inventory.sortBag
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.366Z
 */

const BaseAction = require('../../../core/base-action');

class InventorysortBagAction extends BaseAction {
  static metadata = {
    name: '整理背包 对应old项目: sortBag方法',
    description: '整理背包 对应old项目: sortBag方法',
    category: 'character',
    serviceName: 'character',
    module: 'inventory',
    actionName: 'inventory.sortBag',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "bookMarkId": {
            "type": "number",
            "required": true,
            "description": "bookMarkId参数"
      },
      "sortType": {
            "type": "string",
            "required": false,
            "description": "sortType参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, bookMarkId, sortType, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      bookMarkId,
      sortType,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '整理背包 对应old项目: sortBag方法成功'
      };
    } else {
      throw new Error(`整理背包 对应old项目: sortBag方法失败: ${response.message}`);
    }
  }
}

module.exports = InventorysortBagAction;