/**
 * 更新金牌教练任务进度 对应old项目act.js中的updateGoldCoachTaskProgress方法
 * 
 * 微服务: activity
 * 模块: task
 * Controller: task
 * Pattern: task.updateGoldCoachProgress
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.283Z
 */

const BaseAction = require('../../../core/base-action');

class TaskupdateGoldCoachProgressAction extends BaseAction {
  static metadata = {
    name: '更新金牌教练任务进度 对应old项目act.js中的updateGoldCoachTaskProgress方法',
    description: '更新金牌教练任务进度 对应old项目act.js中的updateGoldCoachTaskProgress方法',
    category: 'activity',
    serviceName: 'activity',
    module: 'task',
    actionName: 'task.updateGoldCoachProgress',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "coachAction": {
            "type": "string",
            "required": true,
            "description": "coachAction参数"
      },
      "param": {
            "type": "any",
            "required": false,
            "description": "param参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, coachAction, param } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      coachAction,
      param
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '更新金牌教练任务进度 对应old项目act.js中的updateGoldCoachTaskProgress方法成功'
      };
    } else {
      throw new Error(`更新金牌教练任务进度 对应old项目act.js中的updateGoldCoachTaskProgress方法失败: ${response.message}`);
    }
  }
}

module.exports = TaskupdateGoldCoachProgressAction;