/**
 * 重置引导
 * 
 * 微服务: activity
 * 模块: guide
 * Controller: guide
 * Pattern: guide.reset
 * 
 * 使用AST自动生成于 2025-08-16T13:11:19.237Z
 */

const BaseAction = require('../../../core/base-action');

class GuideresetAction extends BaseAction {
  static metadata = {
    name: '重置引导',
    description: '重置引导',
    category: 'activity',
    serviceName: 'activity',
    module: 'guide',
    actionName: 'guide.reset',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '重置引导成功'
      };
    } else {
      throw new Error(`重置引导失败: ${response.message}`);
    }
  }
}

module.exports = GuideresetAction;