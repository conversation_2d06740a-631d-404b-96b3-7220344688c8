# Redis分布式锁完整指南

## 概述

Redis分布式锁是一种基于Redis实现的分布式同步机制，用于在分布式系统中确保同一时间只有一个进程能够访问共享资源。本系统提供了完整的分布式锁解决方案，包括基础锁、可重入锁和业务专用锁。

## 架构设计

### 核心组件

```
┌─────────────────────────────────────────────────────────────┐
│                    Redis分布式锁架构                          │
├─────────────────────────────────────────────────────────────┤
│  应用层                                                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ 网关服务     │  │ 认证服务     │  │ 游戏服务     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  服务层                                                      │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              RedisLockService                           │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │ │
│  │  │ 基础锁       │ │ 可重入锁     │ │ 业务锁       │      │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘      │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  存储层                                                      │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   Redis集群                             │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │ │
│  │  │ 主节点       │ │ 从节点1      │ │ 从节点2      │      │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘      │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 设计原则

1. **原子性保证**: 使用Lua脚本确保锁操作的原子性
2. **超时机制**: 所有锁都有TTL，防止死锁
3. **所有权验证**: 只有锁的拥有者才能释放锁
4. **可重入支持**: 支持同一客户端多次获取同一锁
5. **高可用性**: 支持Redis集群和故障转移

## 锁类型详解

### 1. 基础分布式锁

**特点**:
- 互斥性：同一时间只有一个客户端能获取锁
- 超时自动释放：防止死锁
- 所有权验证：只有锁拥有者能释放锁

**实现原理**:
```lua
-- 获取锁的Lua脚本
if redis.call("get", KEYS[1]) == false then
  redis.call("set", KEYS[1], ARGV[1], "EX", ARGV[2])
  return 1
else
  return 0
end
```

### 2. 可重入锁

**特点**:
- 同一客户端可多次获取同一锁
- 维护获取计数器
- 必须释放相同次数才能完全释放锁

**实现原理**:
```lua
-- 可重入锁获取脚本
local current = redis.call("get", KEYS[1])
if current == false or current == ARGV[1] then
  redis.call("set", KEYS[1], ARGV[1], "EX", ARGV[2])
  local count = redis.call("incr", KEYS[2])
  redis.call("expire", KEYS[2], ARGV[2])
  return count
else
  return 0
end
```

### 3. 业务专用锁

针对不同业务场景优化的锁：

- **球员转会锁**: 防止同一球员被多个俱乐部同时转会
- **俱乐部操作锁**: 确保俱乐部操作的一致性
- **比赛模拟锁**: 防止比赛被重复模拟
- **用户操作锁**: 防止用户并发操作冲突

## 使用方法

### 基础用法

```typescript
import { RedisLockService } from '@libs/redis';

@Injectable()
export class GameService {
  constructor(private readonly lockService: RedisLockService) {}

  async processPlayerTransfer(playerId: string) {
    // 获取球员转会锁
    const lockId = await this.lockService.acquireTransferLock(playerId, {
      ttl: 300, // 5分钟
      maxRetries: 3,
      retryDelay: 1000
    });

    if (!lockId) {
      throw new Error('无法获取球员转会锁，请稍后重试');
    }

    try {
      // 执行转会逻辑
      await this.executeTransfer(playerId);
    } finally {
      // 确保释放锁
      await this.lockService.releaseLock(`transfer:player:${playerId}`, lockId);
    }
  }
}
```

### 可重入锁用法

```typescript
async processNestedOperation(resourceId: string) {
  const lockId = await this.lockService.acquireReentrantLock(`resource:${resourceId}`, {
    ttl: 60,
    identifier: 'client-123'
  });

  if (lockId) {
    try {
      // 第一层操作
      await this.firstOperation();
      
      // 可以再次获取同一锁（可重入）
      const lockId2 = await this.lockService.acquireReentrantLock(`resource:${resourceId}`, {
        ttl: 60,
        identifier: 'client-123'
      });
      
      if (lockId2) {
        try {
          // 第二层操作
          await this.secondOperation();
        } finally {
          await this.lockService.releaseReentrantLock(`resource:${resourceId}`, lockId2);
        }
      }
    } finally {
      await this.lockService.releaseReentrantLock(`resource:${resourceId}`, lockId);
    }
  }
}
```

### 自动释放锁用法

```typescript
async processWithAutoRelease(resourceId: string) {
  return await this.lockService.withLock(
    `resource:${resourceId}`,
    async () => {
      // 业务逻辑
      return await this.businessLogic();
    },
    {
      ttl: 30,
      maxRetries: 5,
      retryDelay: 500
    }
  );
}
```

## 使用场景

### 1. 数据一致性保护

**场景**: 防止并发修改导致的数据不一致

```typescript
async updatePlayerStats(playerId: string, stats: PlayerStats) {
  const lockId = await this.lockService.acquireUserLock(playerId, 'update_stats');
  
  if (lockId) {
    try {
      const currentStats = await this.getPlayerStats(playerId);
      const newStats = this.calculateNewStats(currentStats, stats);
      await this.savePlayerStats(playerId, newStats);
    } finally {
      await this.lockService.releaseLock(`user:${playerId}:update_stats`, lockId);
    }
  }
}
```

### 2. 资源竞争控制

**场景**: 限制对稀缺资源的并发访问

```typescript
async simulateMatch(matchId: string) {
  const lockId = await this.lockService.acquireMatchLock(matchId);
  
  if (!lockId) {
    throw new Error('比赛正在进行中，请稍后重试');
  }

  try {
    await this.runMatchSimulation(matchId);
  } finally {
    await this.lockService.releaseLock(`match:simulation:${matchId}`, lockId);
  }
}
```

### 3. 防重复提交

**场景**: 防止用户重复提交表单或操作

```typescript
async submitTransferOffer(userId: string, offerId: string) {
  const lockKey = `transfer:offer:${userId}:${offerId}`;
  const lockId = await this.lockService.acquireLock(lockKey, { ttl: 10 });
  
  if (!lockId) {
    throw new Error('请勿重复提交');
  }

  try {
    await this.processTransferOffer(offerId);
  } finally {
    await this.lockService.releaseLock(lockKey, lockId);
  }
}
```

### 4. 排行榜更新

**场景**: 确保排行榜更新的原子性

```typescript
async updateLeaderboard(type: string, userId: string, score: number) {
  const lockId = await this.lockService.acquireLeaderboardLock(type);
  
  if (lockId) {
    try {
      await this.recalculateLeaderboard(type, userId, score);
    } finally {
      await this.lockService.releaseLock(`leaderboard:update:${type}`, lockId);
    }
  }
}
```

## 最佳实践

### 1. 锁粒度控制

```typescript
// ✅ 好的做法：细粒度锁
await this.lockService.acquireLock(`player:${playerId}:transfer`);

// ❌ 避免：粗粒度锁
await this.lockService.acquireLock('all_transfers');
```

### 2. 超时时间设置

```typescript
// ✅ 根据业务场景设置合适的TTL
const lockId = await this.lockService.acquireLock(key, {
  ttl: 30,      // 快速操作：30秒
  ttl: 300,     // 中等操作：5分钟
  ttl: 1800,    // 长时间操作：30分钟
});
```

### 3. 错误处理

```typescript
async processWithLock(resourceId: string) {
  const lockId = await this.lockService.acquireLock(`resource:${resourceId}`);
  
  if (!lockId) {
    // 处理获取锁失败的情况
    throw new ConflictException('资源正在被其他进程使用');
  }

  try {
    await this.businessLogic();
  } catch (error) {
    // 记录错误但仍要释放锁
    this.logger.error(`业务逻辑执行失败: ${error.message}`);
    throw error;
  } finally {
    // 确保锁被释放
    try {
      await this.lockService.releaseLock(`resource:${resourceId}`, lockId);
    } catch (releaseError) {
      this.logger.error(`释放锁失败: ${releaseError.message}`);
    }
  }
}
```

### 4. 监控和统计

```typescript
// 获取锁统计信息
const stats = this.lockService.getStats();
console.log('锁统计:', {
  totalAcquired: stats.totalAcquired,
  totalReleased: stats.totalReleased,
  activeLocks: stats.activeLocks,
  averageHoldTime: stats.averageHoldTime
});

// 获取活跃锁列表
const activeLocks = this.lockService.getActiveLocks();
console.log('活跃锁:', activeLocks);
```

## 性能优化

### 1. 连接池优化

```typescript
// Redis连接池配置
const redisConfig = {
  host: '***************',
  port: 6379,
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  lazyConnect: true,
  keepAlive: 30000,
  family: 4,
  // 连接池配置
  maxConnections: 20,
  minConnections: 5
};
```

### 2. 批量操作

```typescript
// 批量获取多个锁
async acquireMultipleLocks(keys: string[]) {
  const promises = keys.map(key => 
    this.lockService.acquireLock(key, { ttl: 30 })
  );
  
  return await Promise.allSettled(promises);
}
```

### 3. 锁降级策略

```typescript
async processWithFallback(resourceId: string) {
  const lockId = await this.lockService.acquireLock(`resource:${resourceId}`, {
    ttl: 30,
    maxRetries: 1
  });
  
  if (lockId) {
    // 获取锁成功，执行完整逻辑
    return await this.fullProcess();
  } else {
    // 获取锁失败，执行降级逻辑
    return await this.fallbackProcess();
  }
}
```

## 故障处理

### 1. 死锁检测

```typescript
// 定期清理过期锁
@Cron('0 */5 * * * *') // 每5分钟执行一次
async cleanupExpiredLocks() {
  await this.lockService.cleanupExpiredLocks();
}
```

### 2. 强制释放锁

```typescript
// 管理员功能：强制释放锁
async forceReleaseLock(lockKey: string) {
  const released = await this.lockService.forceReleaseLock(lockKey);
  if (released) {
    this.logger.warn(`强制释放锁: ${lockKey}`);
  }
}
```

### 3. 健康检查

```typescript
@Get('/health/locks')
async checkLockHealth() {
  const stats = this.lockService.getStats();
  const activeLocks = this.lockService.getActiveLocks();
  
  return {
    status: 'healthy',
    stats,
    activeLockCount: activeLocks.length,
    timestamp: new Date()
  };
}
```

## 注意事项

1. **避免长时间持有锁**: 锁的持有时间应该尽可能短
2. **合理设置重试策略**: 避免无限重试导致系统负载过高
3. **监控锁的使用情况**: 定期检查锁的获取和释放情况
4. **处理网络分区**: 考虑Redis集群的网络分区情况
5. **测试锁的行为**: 在不同场景下测试锁的正确性

## 相关文档

- [Redis缓存模式指南](./cache-patterns-guide.md)
- [Redis部署指南](./deployment-guide.md)
- [Redis设计原则](./design-principles.md)
