import { Injectable, Logger } from '@nestjs/common';
import { DataSource } from './cache.interfaces';

/**
 * 抽象数据源基类
 */
export abstract class BaseDataSource<T> implements DataSource<T> {
  protected readonly logger = new Logger(this.constructor.name);

  abstract load(key: string, ...args: any[]): Promise<T | null>;
  
  async save?(key: string, data: T, ...args: any[]): Promise<void> {
    this.logger.warn(`Save operation not implemented for ${this.constructor.name}`);
  }

  async delete?(key: string, ...args: any[]): Promise<void> {
    this.logger.warn(`Delete operation not implemented for ${this.constructor.name}`);
  }

  async exists?(key: string, ...args: any[]): Promise<boolean> {
    const data = await this.load(key);
    return data !== null;
  }
}

/**
 * HTTP API 数据源
 */
@Injectable()
export class HttpDataSource<T> extends BaseDataSource<T> {
  constructor(
    private readonly baseUrl: string,
    private readonly httpClient: any, // 这里应该是实际的 HTTP 客户端
  ) {
    super();
  }

  async load(key: string): Promise<T | null> {
    try {
      const response = await this.httpClient.get(`${this.baseUrl}/${key}`);
      return response.data;
    } catch (error) {
      if (error.status === 404) {
        return null;
      }
      this.logger.error(`HTTP load failed for key ${key}: ${error.message}`);
      throw error;
    }
  }

  async save(key: string, data: T): Promise<void> {
    try {
      await this.httpClient.put(`${this.baseUrl}/${key}`, data);
    } catch (error) {
      this.logger.error(`HTTP save failed for key ${key}: ${error.message}`);
      throw error;
    }
  }

  async delete(key: string): Promise<void> {
    try {
      await this.httpClient.delete(`${this.baseUrl}/${key}`);
    } catch (error) {
      this.logger.error(`HTTP delete failed for key ${key}: ${error.message}`);
      throw error;
    }
  }
}

/**
 * 数据库数据源
 */
@Injectable()
export class DatabaseDataSource<T> extends BaseDataSource<T> {
  constructor(
    private readonly repository: any, // 这里应该是实际的数据库仓库
    private readonly entityName: string,
  ) {
    super();
  }

  async load(key: string): Promise<T | null> {
    try {
      const entity = await this.repository.findOne({ where: { id: key } });
      return entity || null;
    } catch (error) {
      this.logger.error(`Database load failed for key ${key}: ${error.message}`);
      throw error;
    }
  }

  async save(key: string, data: T): Promise<void> {
    try {
      await this.repository.save({ id: key, ...data });
    } catch (error) {
      this.logger.error(`Database save failed for key ${key}: ${error.message}`);
      throw error;
    }
  }

  async delete(key: string): Promise<void> {
    try {
      await this.repository.delete({ id: key });
    } catch (error) {
      this.logger.error(`Database delete failed for key ${key}: ${error.message}`);
      throw error;
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const count = await this.repository.count({ where: { id: key } });
      return count > 0;
    } catch (error) {
      this.logger.error(`Database exists check failed for key ${key}: ${error.message}`);
      return false;
    }
  }
}

/**
 * 内存数据源（用于测试）
 */
@Injectable()
export class MemoryDataSource<T> extends BaseDataSource<T> {
  private readonly data = new Map<string, T>();

  async load(key: string): Promise<T | null> {
    return this.data.get(key) || null;
  }

  async save(key: string, data: T): Promise<void> {
    this.data.set(key, data);
  }

  async delete(key: string): Promise<void> {
    this.data.delete(key);
  }

  async exists(key: string): Promise<boolean> {
    return this.data.has(key);
  }

  clear(): void {
    this.data.clear();
  }

  size(): number {
    return this.data.size;
  }
}

/**
 * 组合数据源（多级数据源）
 */
@Injectable()
export class CompositeDataSource<T> extends BaseDataSource<T> {
  constructor(private readonly dataSources: DataSource<T>[]) {
    super();
  }

  async load(key: string, ...args: any[]): Promise<T | null> {
    for (const dataSource of this.dataSources) {
      try {
        const data = await dataSource.load(key, ...args);
        if (data !== null) {
          return data;
        }
      } catch (error) {
        this.logger.warn(`Data source failed for key ${key}: ${error.message}`);
        // 继续尝试下一个数据源
      }
    }
    return null;
  }

  async save(key: string, data: T, ...args: any[]): Promise<void> {
    const errors: Error[] = [];
    
    for (const dataSource of this.dataSources) {
      if (dataSource.save) {
        try {
          await dataSource.save(key, data, ...args);
        } catch (error) {
          errors.push(error);
        }
      }
    }

    if (errors.length === this.dataSources.length) {
      throw new Error(`All data sources failed to save key ${key}`);
    }
  }

  async delete(key: string, ...args: any[]): Promise<void> {
    for (const dataSource of this.dataSources) {
      if (dataSource.delete) {
        try {
          await dataSource.delete(key, ...args);
        } catch (error) {
          this.logger.warn(`Data source delete failed for key ${key}: ${error.message}`);
        }
      }
    }
  }
}

/**
 * 延迟加载数据源
 */
@Injectable()
export class LazyDataSource<T> extends BaseDataSource<T> {
  private loadPromises = new Map<string, Promise<T | null>>();

  constructor(private readonly actualDataSource: DataSource<T>) {
    super();
  }

  async load(key: string, ...args: any[]): Promise<T | null> {
    // 如果已经有正在进行的加载，返回同一个 Promise
    if (this.loadPromises.has(key)) {
      return this.loadPromises.get(key)!;
    }

    const loadPromise = this.actualDataSource.load(key, ...args)
      .finally(() => {
        // 加载完成后清理 Promise
        this.loadPromises.delete(key);
      });

    this.loadPromises.set(key, loadPromise);
    return loadPromise;
  }

  async save(key: string, data: T, ...args: any[]): Promise<void> {
    if (this.actualDataSource.save) {
      return this.actualDataSource.save(key, data, ...args);
    }
  }

  async delete(key: string, ...args: any[]): Promise<void> {
    // 取消正在进行的加载
    this.loadPromises.delete(key);
    
    if (this.actualDataSource.delete) {
      return this.actualDataSource.delete(key, ...args);
    }
  }
}

/**
 * 重试数据源
 */
@Injectable()
export class RetryDataSource<T> extends BaseDataSource<T> {
  constructor(
    private readonly actualDataSource: DataSource<T>,
    private readonly maxRetries: number = 3,
    private readonly retryDelay: number = 1000,
  ) {
    super();
  }

  async load(key: string, ...args: any[]): Promise<T | null> {
    return this.withRetry(() => this.actualDataSource.load(key, ...args), key, 'load');
  }

  async save(key: string, data: T, ...args: any[]): Promise<void> {
    if (this.actualDataSource.save) {
      return this.withRetry(() => this.actualDataSource.save!(key, data, ...args), key, 'save');
    }
  }

  async delete(key: string, ...args: any[]): Promise<void> {
    if (this.actualDataSource.delete) {
      return this.withRetry(() => this.actualDataSource.delete!(key, ...args), key, 'delete');
    }
  }

  private async withRetry<R>(
    operation: () => Promise<R>,
    key: string,
    operationType: string,
  ): Promise<R> {
    let lastError: Error;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        
        if (attempt === this.maxRetries) {
          this.logger.error(`${operationType} failed after ${this.maxRetries} attempts for key ${key}: ${error.message}`);
          throw error;
        }

        this.logger.warn(`${operationType} attempt ${attempt} failed for key ${key}, retrying in ${this.retryDelay}ms`);
        await this.sleep(this.retryDelay * attempt); // 指数退避
      }
    }

    throw lastError!;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 数据源工厂
 */
@Injectable()
export class DataSourceFactory {
  createHttpDataSource<T>(baseUrl: string, httpClient: any): HttpDataSource<T> {
    return new HttpDataSource<T>(baseUrl, httpClient);
  }

  createDatabaseDataSource<T>(repository: any, entityName: string): DatabaseDataSource<T> {
    return new DatabaseDataSource<T>(repository, entityName);
  }

  createMemoryDataSource<T>(): MemoryDataSource<T> {
    return new MemoryDataSource<T>();
  }

  createCompositeDataSource<T>(dataSources: DataSource<T>[]): CompositeDataSource<T> {
    return new CompositeDataSource<T>(dataSources);
  }

  createLazyDataSource<T>(dataSource: DataSource<T>): LazyDataSource<T> {
    return new LazyDataSource<T>(dataSource);
  }

  createRetryDataSource<T>(
    dataSource: DataSource<T>,
    maxRetries?: number,
    retryDelay?: number,
  ): RetryDataSource<T> {
    return new RetryDataSource<T>(dataSource, maxRetries, retryDelay);
  }
}

