/**
 * 基础Schema装饰器与通用插件（base-new）
 * 目标：
 *  - 统一Schema默认配置（strict/strictQuery/toJSON/toObject）
 *  - 提供通用实例/静态方法（toSafeObject/equals/findByIdSafe等）
 *  - 仅保留与业务强相关、可落地的能力，降低心智负担
 */

import { Schema as NestSchema } from '@nestjs/mongoose';
import {
  Schema as MongooseSchema,
  Document,
  SchemaOptions,
  Types
} from 'mongoose';

/**
 * 通用Schema配置
 */
export interface BaseSchemaOptions {
  collection?: string;
  timestamps?: boolean; // 默认由具体子模块决定（本套基类推荐：时间戳用毫秒Number自管）
  versionKey?: boolean | string; // 默认关闭版本键
  strict?: boolean;
  strictQuery?: boolean;
  toJSON?: any;
  toObject?: any;
}

/**
 * 基础文档接口（所有Model文档推荐继承）
 */
export interface BaseDoc extends Document {
  _id: Types.ObjectId | string;
  toJSON(): any;
  toObject(): any;
}

/**
 * 创建统一默认选项的Schema装饰器
 * - JSON序列化时：输出 id，隐藏 _id/__v
 * - 开启 strict/strictQuery，保证模型规范
 */
export function createBaseSchema(options: BaseSchemaOptions = {}): ClassDecorator {
  const defaultOptions: SchemaOptions = {
    timestamps: false, // 统一交由具体时间戳插件/类管理
    versionKey: false,
    strict: true,
    strictQuery: true,
    toJSON: {
      virtuals: true,
      transform: (_doc: any, ret: any) => {
        ret.id = ret._id?.toString?.() ?? ret._id;
        delete ret._id;
        delete ret.__v;
        return ret;
      }
    },
    toObject: {
      virtuals: true,
      transform: (_doc: any, ret: any) => {
        ret.id = ret._id?.toString?.() ?? ret._id;
        delete ret._id;
        delete ret.__v;
        return ret;
      }
    },
    ...options
  } as SchemaOptions;

  return NestSchema(defaultOptions as any);
}

/**
 * 通用实例/静态方法插件
 * - 不做过度封装，仅提供实际业务常用的能力
 */
export function baseNewPlugin(schema: MongooseSchema) {
  // 实例方法：安全对象输出
  schema.methods.toSafeObject = function () {
    const obj = this.toObject();
    obj.id = obj._id?.toString?.() ?? obj._id;
    delete obj._id;
    delete obj.__v;
    return obj;
  };

  // 实例方法：等值比较（基于_id）
  schema.methods.equals = function (other: any) {
    if (!other) return false;
    const a = this._id?.toString?.() ?? this._id;
    const b = other._id?.toString?.() ?? other._id;
    return a === b;
  };

  // 实例方法：集合名/模型名
  schema.methods.getCollectionName = function () {
    return this?.collection?.name || this.constructor?.modelName || '';
  };
  schema.methods.getSchemaName = function () {
    return this.constructor?.modelName || '';
  };

  // 静态方法：安全按ID查找
  schema.statics.findByIdSafe = async function (id: string) {
    if (!id) return null;
    try {
      // 兼容字符串/ObjectId
      const valid = Types.ObjectId.isValid(id);
      return await this.findById(valid ? new Types.ObjectId(id) : id);
    } catch {
      return null;
    }
  };

  // 静态方法：是否存在
  schema.statics.existsById = async function (id: string) {
    if (!id) return false;
    try {
      const valid = Types.ObjectId.isValid(id);
      const count = await this.countDocuments({ _id: valid ? new Types.ObjectId(id) : id });
      return count > 0;
    } catch {
      return false;
    }
  };
}

/**
 * 应用基础插件
 */
export function applyBaseNewPlugin(schema: MongooseSchema) {
  baseNewPlugin(schema);
}
