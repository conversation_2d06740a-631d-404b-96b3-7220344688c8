/**
 * Indexed 索引工具（轻量）
 * - 提供常用索引模式封装
 * - 提供在 Schema 上批量应用索引的方法
 */

import { Schema as MongooseSchema, IndexOptions } from 'mongoose';

export type IndexDirection = 1 | -1 | 'text' | '2d' | '2dsphere' | 'hashed';

export interface SingleIndexDef {
  field: string;
  direction: IndexDirection;
  options?: IndexOptions & { name?: string; background?: boolean; sparse?: boolean; unique?: boolean };
}

export interface CompoundIndexDef {
  fields: Record<string, IndexDirection>;
  options?: IndexOptions & { name?: string; background?: boolean; sparse?: boolean; unique?: boolean };
}

export interface TTLIndexDef {
  field: string;
  expireAfterSeconds: number;
  options?: IndexOptions & { name?: string; background?: boolean };
}

export type IndexDef = SingleIndexDef | CompoundIndexDef | TTLIndexDef;

/**
 * 常用索引模式
 */
export const IndexPatterns = {
  ascending(field: string, options?: SingleIndexDef['options']): SingleIndexDef {
    return { field, direction: 1, options };
  },
  descending(field: string, options?: SingleIndexDef['options']): SingleIndexDef {
    return { field, direction: -1, options };
  },
  unique(field: string, options?: SingleIndexDef['options']): SingleIndexDef {
    return { field, direction: 1, options: { ...(options || {}), unique: true } };
  },
  compound(fields: Record<string, IndexDirection>, options?: CompoundIndexDef['options']): CompoundIndexDef {
    return { fields, options };
  },
  text(fields: string[], weights?: Record<string, number>, options?: CompoundIndexDef['options']): CompoundIndexDef {
    const map: Record<string, IndexDirection> = {};
    fields.forEach((f) => (map[f] = 'text'));
    const opts: any = { ...(options || {}) };
    if (weights) opts.weights = weights;
    return { fields: map, options: opts };
  },
  ttl(field: string, seconds: number, options?: TTLIndexDef['options']): TTLIndexDef {
    return { field, expireAfterSeconds: seconds, options };
  }
};

/**
 * 将索引定义应用到Schema
 */
export function applyIndexes(schema: MongooseSchema, defs: IndexDef[], background = true) {
  defs.forEach((def) => {
    if ((def as TTLIndexDef).expireAfterSeconds !== undefined && (def as any).field) {
      const d = def as TTLIndexDef;
      schema.index({ [d.field]: 1 }, { ...(d.options || {}), background, expireAfterSeconds: d.expireAfterSeconds });
      return;
    }

    if ((def as SingleIndexDef).field) {
      const d = def as SingleIndexDef;
      schema.index({ [d.field]: d.direction }, { ...(d.options || {}), background });
      return;
    }

    const c = def as CompoundIndexDef;
    schema.index(c.fields, { ...(c.options || {}), background });
  });
}
