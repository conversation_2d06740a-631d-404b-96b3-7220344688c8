/**
 * Versioned 基类（轻量版本控制）
 * - version: number（默认1）
 * - lastModifiedBy?: string（可选）
 * - 可选 versionHistory: {version, changedAt, changedBy?, changes?}[]
 * - 自动在保存/更新时递增版本（可配置开关）
 */

import { Prop } from '@nestjs/mongoose';
import { Schema as MongooseSchema } from 'mongoose';
import { createBaseSchema } from './base.schema';

export interface VersionInfo {
  version: number;
  changedAt: number; // 毫秒
  changedBy?: string;
  changes?: string[];
}

export interface IVersionedDoc {
  version: number;
  lastModifiedBy?: string;
  versionHistory?: VersionInfo[];

  getVersion(): number;
  getVersionHistory(): VersionInfo[];
  createVersion(changes?: string[], changedBy?: string): Promise<void>;
}

export interface VersionedPluginOptions {
  autoIncrement?: boolean; // 默认 true
  trackHistory?: boolean;  // 默认 true
  maxHistory?: number;     // 默认 10
  trackChangedBy?: boolean; // 默认 true
  trackChanges?: boolean;   // 默认 false
  versionField?: string;    // 默认 'version'
}

@createBaseSchema({ timestamps: false })
export class VersionedSchemaBase implements IVersionedDoc {
  @Prop({ type: Number, default: 1, index: true })
  version: number;

  @Prop({ type: String, required: false, index: true })
  lastModifiedBy?: string;

  @Prop({ type: [{ version: Number, changedAt: Number, changedBy: String, changes: [String] }], default: [] })
  versionHistory?: VersionInfo[];

  getVersion!: () => number;
  getVersionHistory!: () => VersionInfo[];
  createVersion!: (changes?: string[], changedBy?: string) => Promise<void>;
}

export function versionedNewPlugin(schema: MongooseSchema, options?: VersionedPluginOptions) {
  const opt: Required<VersionedPluginOptions> = {
    autoIncrement: options?.autoIncrement ?? true,
    trackHistory: options?.trackHistory ?? true,
    maxHistory: options?.maxHistory ?? 10,
    trackChangedBy: options?.trackChangedBy ?? true,
    trackChanges: options?.trackChanges ?? false,
    versionField: options?.versionField ?? 'version'
  } as any;

  // 保存前：处理版本递增与历史
  schema.pre('save', function (next) {
    const doc: any = this;
    const isNew = this.isNew;
    const vf = opt.versionField as string;

    if (isNew) {
      doc[vf] = 1;
      if (opt.trackHistory) {
        doc.versionHistory = doc.versionHistory || [];
        doc.versionHistory.push({
          version: 1,
          changedAt: Date.now(),
          ...(opt.trackChangedBy && doc.lastModifiedBy ? { changedBy: doc.lastModifiedBy } : {}),
          ...(opt.trackChanges ? { changes: ['created'] } : {})
        });
      }
    } else if (opt.autoIncrement) {
      const modified = this.modifiedPaths?.() || [];
      doc[vf] = (doc[vf] || 1) + 1;

      if (opt.trackHistory) {
        const entry: any = {
          version: doc[vf],
          changedAt: Date.now()
        };
        if (opt.trackChangedBy && doc.lastModifiedBy) entry.changedBy = doc.lastModifiedBy;
        if (opt.trackChanges && modified.length) {
          entry.changes = modified.filter((f: string) => f !== vf && f !== 'versionHistory');
        }
        doc.versionHistory = doc.versionHistory || [];
        doc.versionHistory.push(entry);
        if (opt.maxHistory && doc.versionHistory.length > opt.maxHistory) {
          doc.versionHistory = doc.versionHistory.slice(-opt.maxHistory);
        }
      }
    }

    next();
  });

  // 更新前：在 update 语义中递增版本
  schema.pre(['updateOne', 'updateMany', 'findOneAndUpdate'], function () {
    if (!opt.autoIncrement) return;
    const update = this.getUpdate() as any;
    const vf = opt.versionField as string;

    if (!update.$inc) update.$inc = {};
    update.$inc[vf] = 1;

    if (opt.trackHistory) {
      const pushEntry: any = {
        version: 0, // 无法在此时准确设置，保持兼容占位
        changedAt: Date.now()
      };
      if (!update.$push) update.$push = {};
      update.$push.versionHistory = pushEntry;
    }
  });

  // 实例方法
  schema.methods.getVersion = function (): number {
    const vf = opt.versionField as string;
    return this[vf] || 1;
  };

  schema.methods.getVersionHistory = function (): VersionInfo[] {
    return (this.versionHistory || []) as VersionInfo[];
  };

  schema.methods.createVersion = async function (changes?: string[], changedBy?: string) {
    const vf = opt.versionField as string;
    this[vf] = (this[vf] || 1) + 1;
    if (changedBy && opt.trackChangedBy) this.lastModifiedBy = changedBy;

    if (opt.trackHistory) {
      const entry: VersionInfo = {
        version: this[vf],
        changedAt: Date.now(),
        ...(changedBy && opt.trackChangedBy ? { changedBy } : {}),
        ...(changes && opt.trackChanges ? { changes } : {})
      } as any;
      this.versionHistory = this.versionHistory || [];
      this.versionHistory.push(entry);
      if (opt.maxHistory && this.versionHistory.length > opt.maxHistory) {
        this.versionHistory = this.versionHistory.slice(-opt.maxHistory);
      }
    }

    await this.save();
  };
}
