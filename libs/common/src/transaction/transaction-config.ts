import { ConfigService } from '@nestjs/config';

/**
 * 事务执行时的配置选项
 * 注意：这些不是连接选项，而是执行事务时使用的选项
 */
export interface TransactionExecutionOptions {
  readPreference?: 'primary' | 'secondary' | 'primaryPreferred' | 'secondaryPreferred' | 'nearest';
  readConcern?: { level: 'local' | 'available' | 'majority' | 'linearizable' | 'snapshot' };
  writeConcern?: { w: 'majority' | number; j?: boolean; wtimeout?: number };
  maxCommitTimeMS?: number;
}

/**
 * 事务配置管理器
 * 为不同服务提供合适的事务执行配置
 */
export class TransactionConfigManager {
  
  /**
   * 获取服务的事务执行配置
   * @param serviceName 服务名称
   * @param configService 配置服务
   * @returns 事务执行配置
   */
  static getTransactionConfig(
    serviceName: string,
    configService?: ConfigService
  ): TransactionExecutionOptions {
    const criticalServices = ['auth', 'economy', 'match'];
    const isCritical = criticalServices.includes(serviceName);
    
    if (isCritical) {
      // 关键服务：强一致性配置
      return {
        readPreference: 'primary',
        readConcern: { level: 'majority' },
        writeConcern: { w: 'majority', j: true },
        maxCommitTimeMS: 30000
      };
    } else {
      // 一般服务：平衡性能和一致性
      return {
        readPreference: 'primaryPreferred',
        readConcern: { level: 'local' },
        writeConcern: { w: 1 },
        maxCommitTimeMS: 15000
      };
    }
  }

  /**
   * 获取默认事务配置
   */
  static getDefaultConfig(): TransactionExecutionOptions {
    return {
      readPreference: 'primary',
      readConcern: { level: 'local' },
      writeConcern: { w: 1 },
      maxCommitTimeMS: 30000
    };
  }
}
