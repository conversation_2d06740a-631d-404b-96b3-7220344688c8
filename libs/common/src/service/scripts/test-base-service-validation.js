/**
 * BaseService业务验证方法测试脚本
 * 
 * 测试libs/common/src/service/base-service.ts中新增的业务验证方法
 */

// 模拟BaseService和相关依赖
class MockLogger {
  debug(message, context) {
    console.log(`[DEBUG] ${message}`, context ? JSON.stringify(context) : '');
  }
  
  error(message, context) {
    console.error(`[ERROR] ${message}`, context ? JSON.stringify(context) : '');
  }
}

// 模拟XResultUtils
const XResultUtils = {
  ok: (data) => ({ success: true, data }),
  error: (message, code) => ({ success: false, message, code }),
  isFailure: (result) => !result.success,
};

// 测试用的BaseService实现
class TestBaseService {
  constructor() {
    this.serviceName = 'TestService';
    this.logger = new MockLogger();
  }

  // 复制BaseService中的验证方法（简化版）
  async validateResourceExists(resourceId, resourceType, checkFunction) {
    if (!resourceId) {
      return XResultUtils.error(`${resourceType}ID不能为空`, 'RESOURCE_ID_REQUIRED');
    }

    const result = await checkFunction(resourceId);
    if (XResultUtils.isFailure(result)) {
      return XResultUtils.error(`${resourceType}不存在`, 'RESOURCE_NOT_FOUND');
    }

    return result;
  }

  async validateUserPermission(userId, resourceId, permission, checkFunction) {
    if (!userId) {
      return XResultUtils.error('用户ID不能为空', 'USER_ID_REQUIRED');
    }

    try {
      const hasPermission = await checkFunction(userId, resourceId, permission);
      if (!hasPermission) {
        return XResultUtils.error('没有权限执行此操作', 'INSUFFICIENT_PERMISSION');
      }

      return XResultUtils.ok(undefined);
    } catch (error) {
      this.logger.error('权限验证失败', {
        userId,
        resourceId,
        permission,
        error: error.message,
      });
      return XResultUtils.error('权限验证失败', 'PERMISSION_CHECK_FAILED');
    }
  }

  validateStatusTransition(currentStatus, targetStatus, allowedTransitions) {
    if (!currentStatus || !targetStatus) {
      return XResultUtils.error('状态不能为空', 'STATUS_REQUIRED');
    }

    if (currentStatus === targetStatus) {
      return XResultUtils.error('目标状态与当前状态相同', 'STATUS_UNCHANGED');
    }

    const allowedTargets = allowedTransitions[currentStatus];
    if (!allowedTargets || !allowedTargets.includes(targetStatus)) {
      return XResultUtils.error(
        `不能从状态 ${currentStatus} 转换到 ${targetStatus}`,
        'INVALID_STATUS_TRANSITION'
      );
    }

    return XResultUtils.ok(undefined);
  }

  validateNumberRange(value, fieldName, min, max) {
    if (typeof value !== 'number' || isNaN(value)) {
      return XResultUtils.error(`${fieldName}必须是有效数字`, 'INVALID_NUMBER');
    }

    if (min !== undefined && value < min) {
      return XResultUtils.error(`${fieldName}不能小于${min}`, 'VALUE_TOO_SMALL');
    }

    if (max !== undefined && value > max) {
      return XResultUtils.error(`${fieldName}不能大于${max}`, 'VALUE_TOO_LARGE');
    }

    return XResultUtils.ok(undefined);
  }

  validateStringLength(value, fieldName, minLength, maxLength) {
    if (typeof value !== 'string') {
      return XResultUtils.error(`${fieldName}必须是字符串`, 'INVALID_STRING');
    }

    if (minLength !== undefined && value.length < minLength) {
      return XResultUtils.error(`${fieldName}长度不能少于${minLength}个字符`, 'STRING_TOO_SHORT');
    }

    if (maxLength !== undefined && value.length > maxLength) {
      return XResultUtils.error(`${fieldName}长度不能超过${maxLength}个字符`, 'STRING_TOO_LONG');
    }

    return XResultUtils.ok(undefined);
  }

  async validateUniqueness(value, fieldName, checkFunction, excludeId) {
    if (!value) {
      return XResultUtils.error(`${fieldName}不能为空`, 'VALUE_REQUIRED');
    }

    try {
      const exists = await checkFunction(value, excludeId);
      if (exists) {
        return XResultUtils.error(`${fieldName} "${value}" 已存在`, 'VALUE_ALREADY_EXISTS');
      }

      return XResultUtils.ok(undefined);
    } catch (error) {
      this.logger.error('唯一性验证失败', {
        value,
        fieldName,
        excludeId,
        error: error.message,
      });
      return XResultUtils.error('唯一性验证失败', 'UNIQUENESS_CHECK_FAILED');
    }
  }

  async validateBatch(validations) {
    const errors = [];

    for (const validation of validations) {
      try {
        const result = await validation();
        if (XResultUtils.isFailure(result)) {
          errors.push(result.message);
        }
      } catch (error) {
        errors.push(`验证异常: ${error.message}`);
      }
    }

    if (errors.length > 0) {
      return XResultUtils.error(`验证失败: ${errors.join('; ')}`, 'BATCH_VALIDATION_FAILED');
    }

    return XResultUtils.ok(undefined);
  }

  async validateComplexBusinessRules(rules) {
    for (const rule of rules) {
      try {
        const passed = await rule.check();
        if (!passed) {
          return XResultUtils.error(rule.errorMessage, rule.errorCode || 'BUSINESS_RULE_VIOLATION');
        }
      } catch (error) {
        this.logger.error('业务规则验证异常', {
          ruleName: rule.name,
          error: error.message,
        });
        return XResultUtils.error(
          `业务规则 "${rule.name}" 验证失败: ${error.message}`,
          'BUSINESS_RULE_CHECK_FAILED'
        );
      }
    }

    return XResultUtils.ok(undefined);
  }
}

// 测试类
class BaseServiceValidationTester {
  constructor() {
    this.service = new TestBaseService();
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0,
      details: []
    };
  }

  async runAllTests() {
    console.log('🧪 开始BaseService业务验证方法测试');
    console.log('=' .repeat(60));

    await this.testValidateResourceExists();
    await this.testValidateUserPermission();
    await this.testValidateStatusTransition();
    await this.testValidateNumberRange();
    await this.testValidateStringLength();
    await this.testValidateUniqueness();
    await this.testValidateBatch();
    await this.testValidateComplexBusinessRules();

    this.printSummary();
  }

  async testValidateResourceExists() {
    console.log('\n📋 测试1: validateResourceExists');
    console.log('-'.repeat(40));

    // 成功案例
    await this.runTest(
      'resource-exists-success',
      async () => {
        const result = await this.service.validateResourceExists(
          'char_123',
          '角色',
          async (id) => XResultUtils.ok({ id, name: 'TestCharacter' })
        );
        return result.success && result.data.id === 'char_123';
      }
    );

    // 失败案例：资源不存在
    await this.runTest(
      'resource-not-exists',
      async () => {
        const result = await this.service.validateResourceExists(
          'char_999',
          '角色',
          async (id) => XResultUtils.error('未找到', 'NOT_FOUND')
        );
        return !result.success && result.message === '角色不存在';
      }
    );

    // 失败案例：ID为空
    await this.runTest(
      'resource-empty-id',
      async () => {
        const result = await this.service.validateResourceExists(
          '',
          '角色',
          async (id) => XResultUtils.ok({ id })
        );
        return !result.success && result.message === '角色ID不能为空';
      }
    );
  }

  async testValidateUserPermission() {
    console.log('\n📋 测试2: validateUserPermission');
    console.log('-'.repeat(40));

    // 成功案例
    await this.runTest(
      'permission-success',
      async () => {
        const result = await this.service.validateUserPermission(
          'user_123',
          'char_123',
          'character:update',
          async (uid, rid, perm) => true
        );
        return result.success;
      }
    );

    // 失败案例：权限不足
    await this.runTest(
      'permission-denied',
      async () => {
        const result = await this.service.validateUserPermission(
          'user_123',
          'char_456',
          'character:update',
          async (uid, rid, perm) => false
        );
        return !result.success && result.message === '没有权限执行此操作';
      }
    );
  }

  async testValidateStatusTransition() {
    console.log('\n📋 测试3: validateStatusTransition');
    console.log('-'.repeat(40));

    const transitions = {
      'ACTIVE': ['INACTIVE', 'TRAINING'],
      'INACTIVE': ['ACTIVE'],
      'TRAINING': ['ACTIVE']
    };

    // 成功案例
    await this.runTest(
      'status-transition-success',
      async () => {
        const result = this.service.validateStatusTransition('ACTIVE', 'TRAINING', transitions);
        return result.success;
      }
    );

    // 失败案例：无效转换
    await this.runTest(
      'status-transition-invalid',
      async () => {
        const result = this.service.validateStatusTransition('INACTIVE', 'TRAINING', transitions);
        return !result.success && result.message.includes('不能从状态 INACTIVE 转换到 TRAINING');
      }
    );
  }

  async testValidateNumberRange() {
    console.log('\n📋 测试4: validateNumberRange');
    console.log('-'.repeat(40));

    // 成功案例
    await this.runTest(
      'number-range-success',
      async () => {
        const result = this.service.validateNumberRange(25, '年龄', 18, 100);
        return result.success;
      }
    );

    // 失败案例：超出范围
    await this.runTest(
      'number-range-too-large',
      async () => {
        const result = this.service.validateNumberRange(150, '年龄', 18, 100);
        return !result.success && result.message === '年龄不能大于100';
      }
    );
  }

  async testValidateStringLength() {
    console.log('\n📋 测试5: validateStringLength');
    console.log('-'.repeat(40));

    // 成功案例
    await this.runTest(
      'string-length-success',
      async () => {
        const result = this.service.validateStringLength('TestUser', '用户名', 3, 20);
        return result.success;
      }
    );

    // 失败案例：太短
    await this.runTest(
      'string-length-too-short',
      async () => {
        const result = this.service.validateStringLength('ab', '用户名', 3, 20);
        return !result.success && result.message === '用户名长度不能少于3个字符';
      }
    );
  }

  async testValidateUniqueness() {
    console.log('\n📋 测试6: validateUniqueness');
    console.log('-'.repeat(40));

    // 成功案例：不存在
    await this.runTest(
      'uniqueness-success',
      async () => {
        const result = await this.service.validateUniqueness(
          'newuser',
          '用户名',
          async (value) => false // 不存在
        );
        return result.success;
      }
    );

    // 失败案例：已存在
    await this.runTest(
      'uniqueness-exists',
      async () => {
        const result = await this.service.validateUniqueness(
          'existinguser',
          '用户名',
          async (value) => true // 已存在
        );
        return !result.success && result.message === '用户名 "existinguser" 已存在';
      }
    );
  }

  async testValidateBatch() {
    console.log('\n📋 测试7: validateBatch');
    console.log('-'.repeat(40));

    // 成功案例：所有验证通过
    await this.runTest(
      'batch-success',
      async () => {
        const result = await this.service.validateBatch([
          () => this.service.validateNumberRange(25, '年龄', 18, 100),
          () => this.service.validateStringLength('TestUser', '用户名', 3, 20),
        ]);
        return result.success;
      }
    );

    // 失败案例：部分验证失败
    await this.runTest(
      'batch-partial-failure',
      async () => {
        const result = await this.service.validateBatch([
          () => this.service.validateNumberRange(15, '年龄', 18, 100), // 失败
          () => this.service.validateStringLength('TestUser', '用户名', 3, 20), // 成功
        ]);
        return !result.success && result.message.includes('年龄不能小于18');
      }
    );
  }

  async testValidateComplexBusinessRules() {
    console.log('\n📋 测试8: validateComplexBusinessRules');
    console.log('-'.repeat(40));

    // 成功案例：所有规则通过
    await this.runTest(
      'business-rules-success',
      async () => {
        const result = await this.service.validateComplexBusinessRules([
          {
            name: '等级检查',
            check: () => true,
            errorMessage: '等级不足',
          },
          {
            name: '资源检查',
            check: async () => Promise.resolve(true),
            errorMessage: '资源不足',
          }
        ]);
        return result.success;
      }
    );

    // 失败案例：规则失败
    await this.runTest(
      'business-rules-failure',
      async () => {
        const result = await this.service.validateComplexBusinessRules([
          {
            name: '等级检查',
            check: () => false,
            errorMessage: '等级不足',
            errorCode: 'LEVEL_TOO_LOW'
          }
        ]);
        return !result.success && result.message === '等级不足' && result.code === 'LEVEL_TOO_LOW';
      }
    );
  }

  async runTest(testName, testFunction) {
    this.testResults.total++;
    
    try {
      const passed = await testFunction();
      if (passed) {
        console.log(`✅ ${testName}`);
        this.testResults.passed++;
        this.testResults.details.push({ name: testName, status: 'PASSED' });
      } else {
        console.log(`❌ ${testName} - 验证失败`);
        this.testResults.failed++;
        this.testResults.details.push({ name: testName, status: 'FAILED', reason: 'Validation failed' });
      }
    } catch (error) {
      console.log(`❌ ${testName} - 异常: ${error.message}`);
      this.testResults.failed++;
      this.testResults.details.push({ name: testName, status: 'ERROR', error: error.message });
    }
  }

  printSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 BaseService业务验证方法测试结果');
    console.log('='.repeat(60));
    console.log(`总测试数: ${this.testResults.total}`);
    console.log(`通过: ${this.testResults.passed} ✅`);
    console.log(`失败: ${this.testResults.failed} ❌`);
    console.log(`成功率: ${((this.testResults.passed / this.testResults.total) * 100).toFixed(1)}%`);
    
    if (this.testResults.failed > 0) {
      console.log('\n❌ 失败的测试:');
      this.testResults.details
        .filter(detail => detail.status !== 'PASSED')
        .forEach(detail => {
          console.log(`  - ${detail.name}: ${detail.reason || detail.error}`);
        });
    }
    
    console.log('\n🎉 BaseService业务验证方法测试完成！');
  }
}

// 运行测试
async function main() {
  const tester = new BaseServiceValidationTester();
  await tester.runAllTests();
}

main().catch(console.error);
