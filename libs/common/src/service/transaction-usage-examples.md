# BaseService 事务操作使用指南

## 概述

BaseService 现在提供了完整的事务支持，通过 `executeTransaction` 方法可以轻松实现 ACID 特性的复杂业务操作。该方法特别适用于 **Orchestrator 编排服务**，支持跨微服务的复杂业务流程。

## 初始化

在应用启动时需要初始化事务管理器：

```typescript
// main.ts
import mongoose from 'mongoose';
import { BaseService } from '@libs/common/service';

async function bootstrap() {
  // 连接数据库
  await mongoose.connect(process.env.MONGODB_URI);

  // 初始化事务管理器
  BaseService.initializeTransaction(mongoose);

  // 启动应用
  const app = await NestFactory.create(AppModule);
  await app.listen(3000);
}
```

## 使用场景

### 1. Repository层事务（传统数据库事务）

适用于单个微服务内的多个Repository操作：

```typescript
@Injectable()
export class CharacterService extends BaseService {
  constructor(
    private readonly characterRepository: CharacterRepository,
    private readonly inventoryRepository: InventoryRepository
  ) {
    super('CharacterService');
  }

  async upgradeCharacter(characterId: string, cost: number): Promise<XResult<Character>> {
    return this.executeTransaction(async (session) => {
      // 所有Repository操作都传递session参数
      const characterResult = await this.characterRepository.findById(characterId, { session });
      if (XResultUtils.isFailure(characterResult)) return characterResult;

      const character = characterResult.data;
      if (!character || character.gold < cost) {
        return XResultUtils.error('金币不足', 'INSUFFICIENT_GOLD');
      }

      // 扣除金币并升级
      const updateResult = await this.characterRepository.updateById(
        characterId,
        {
          $inc: { level: 1, gold: -cost },
          $set: { lastUpgradeTime: new Date() }
        },
        session
      );

      return updateResult;
    });
  }
}
```

### 2. Orchestrator编排事务（跨微服务事务）

这是最重要的使用场景，适用于需要调用多个微服务的复杂业务流程：

```typescript
@Injectable()
export class CharacterOrchestratorService extends BaseService {
  constructor(
    private readonly characterRepository: CharacterRepository,
    microserviceClient: MicroserviceClientService
  ) {
    super('CharacterOrchestratorService', microserviceClient);
  }

  /**
   * 角色初始化编排 - 典型的Orchestrator场景
   * 需要初始化角色基础数据，然后调用formation、inventory、hero等服务
   */
  async initializeCharacter(dto: InitializeCharacterDto): Promise<XResult<InitializeResult>> {
    return this.executeTransaction(async (session) => {
      // 1. 数据库操作：创建角色基础数据（传递session）
      const characterResult = await this.characterRepository.createOne({
        userId: dto.userId,
        name: dto.name,
        serverId: dto.serverId,
        level: 1,
        gold: 1000,
        createdAt: new Date()
      }, session);
      if (XResultUtils.isFailure(characterResult)) return characterResult;

      const characterId = characterResult.data._id.toString();

      // 2. 微服务调用：初始化阵容系统（不传递session）
      const formationResult = await this.callMicroservice('character', 'formation.initialize', {
        characterId,
        defaultFormationType: '4-4-2'
      });
      if (XResultUtils.isFailure(formationResult)) return formationResult;

      // 3. 微服务调用：初始化背包系统
      const inventoryResult = await this.callMicroservice('character', 'inventory.initialize', {
        characterId,
        initialItems: dto.initialItems || []
      });
      if (XResultUtils.isFailure(inventoryResult)) return inventoryResult;

      // 4. 微服务调用：初始化英雄系统
      const heroResult = await this.callMicroservice('hero', 'hero.initializeForCharacter', {
        characterId,
        initialHeroes: dto.initialHeroes || []
      });
      if (XResultUtils.isFailure(heroResult)) return heroResult;

      // 5. 数据库操作：更新角色初始化状态（传递session）
      const updateResult = await this.characterRepository.updateById(
        characterId,
        {
          $set: {
            isInitialized: true,
            initializedAt: new Date()
          }
        },
        session
      );
      if (XResultUtils.isFailure(updateResult)) return updateResult;

      return XResultUtils.ok({
        characterId,
        character: characterResult.data,
        formation: formationResult.data,
        inventory: inventoryResult.data,
        heroes: heroResult.data,
        initializationTime: new Date()
      });
    }, {
      maxRetries: 2, // 编排服务减少重试次数
      timeout: 60000, // 编排服务需要更长的超时时间
      operationDescription: '角色初始化编排',
      enableDetailedLogging: true
    });
  }
}
```

### 3. 混合事务（数据库+微服务）

适用于既有数据库操作又有微服务调用的场景：

```typescript
async processItemPurchase(
  characterId: string,
  itemId: string,
  price: number
): Promise<XResult<PurchaseResult>> {
  return this.executeTransaction(async (session) => {
    // 1. 数据库操作：扣除金币（传递session）
    const deductResult = await this.characterRepository.updateOne(
      { _id: characterId, gold: { $gte: price } },
      { $inc: { gold: -price } },
      session
    );
    if (XResultUtils.isFailure(deductResult)) return deductResult;

    if (deductResult.data.modifiedCount === 0) {
      return XResultUtils.error('金币不足', 'INSUFFICIENT_GOLD');
    }

    // 2. 微服务调用：添加物品到背包（不传递session）
    const addItemResult = await this.callMicroservice('character', 'inventory.addItem', {
      characterId,
      itemId,
      quantity: 1,
      reason: 'purchase'
    });
    if (XResultUtils.isFailure(addItemResult)) return addItemResult;

    // 3. 微服务调用：记录购买日志
    const logResult = await this.callMicroservice('economy', 'purchase.log', {
      characterId,
      itemId,
      price,
      timestamp: new Date()
    });
    if (XResultUtils.isFailure(logResult)) return logResult;

    return XResultUtils.ok({
      characterId,
      itemId,
      price,
      purchaseTime: new Date()
    });
  }, {
    operationDescription: '物品购买处理'
  });
}
```

## 配置选项

```typescript
// 自定义事务配置
const result = await this.executeTransaction(async (session) => {
  // 事务操作
}, {
  maxRetries: 5,        // 最大重试次数
  retryDelay: 2000,     // 重试延迟（毫秒）
  timeout: 60000,       // 超时时间（毫秒）
  readPreference: 'primary',
  readConcern: 'majority',
  writeConcern: 'majority'
});
```

## 最佳实践

### 1. 错误处理模式

```typescript
return this.executeTransaction(async (session) => {
  // ✅ 正确：检查每个Repository调用的结果
  const result1 = await this.repo1.operation(session);
  if (XResultUtils.isFailure(result1)) return result1;

  const result2 = await this.repo2.operation(session);
  if (XResultUtils.isFailure(result2)) return result2;

  return XResultUtils.ok(finalResult);
});
```

### 2. 避免长时间操作

```typescript
// ❌ 错误：在事务中进行长时间的外部API调用
return this.executeTransaction(async (session) => {
  const result = await this.repository.update(data, session);
  await this.externalApiCall(); // 这会延长事务时间
  return result;
});

// ✅ 正确：先完成事务，再进行外部调用
const transactionResult = await this.executeTransaction(async (session) => {
  return await this.repository.update(data, session);
});

if (XResultUtils.isSuccess(transactionResult)) {
  await this.externalApiCall(); // 事务外进行
}
```

### 3. 合理的超时设置

```typescript
// 简单操作：较短超时
await this.executeTransaction(operation, { timeout: 10000 });

// 复杂操作：较长超时
await this.executeTransaction(complexOperation, { timeout: 60000 });
```

## 注意事项

1. **必须传递session**：事务内的所有Repository操作都必须传递session参数
2. **返回XResult**：事务操作函数必须返回XResult<T>类型
3. **自动回滚**：任何XResult失败都会自动回滚事务
4. **重试机制**：系统会自动重试可重试的错误（如并发冲突）
5. **资源管理**：session的创建和清理由框架自动管理
6. **事务支持检测**：系统会自动检测MongoDB是否支持事务

## 错误码说明

- `MONGOOSE_NOT_INITIALIZED`: Mongoose未初始化
- `TRANSACTION_CONFLICT`: 事务冲突（可重试）
- `WRITE_CONFLICT`: 写冲突（可重试）
- `DUPLICATE_KEY`: 重复键错误（可重试）
- `TRANSACTION_EXCEPTION`: 事务执行异常
- `TRANSACTION_FAILED`: 事务执行失败
