import { Injectable, Logger } from '@nestjs/common';
import { XResult, XResultUtils, ServiceResultHandler } from '../types/result.type';
import { ClientSession } from 'mongoose';

/**
 * 业务操作选项
 */
export interface BusinessOperationOptions {
  /** 操作原因/来源 */
  reason?: string;
  /** 是否跳过验证 */
  skipValidation?: boolean;
  /** 额外的元数据 */
  metadata?: Record<string, any>;
  /** 操作超时时间（毫秒） */
  timeout?: number;
}

/**
 * 微服务调用选项
 */
export interface MicroserviceCallOptions {
  /** 超时时间（毫秒） */
  timeout?: number;
  /** 重试次数 */
  retries?: number;
  /** 是否记录调用日志 */
  logCall?: boolean;
}

/**
 * 微服务客户端接口（抽象接口，避免具体依赖）
 */
export interface IMicroserviceClient {
  call<T = any>(serviceName: string, pattern: string, payload: any): Promise<any>;
}

/**
 * 事务配置选项
 */
export interface TransactionOptions {
  /** 最大重试次数，默认3次 */
  maxRetries?: number;
  /** 重试延迟基数（毫秒），默认1000ms */
  retryDelay?: number;
  /** 事务超时时间（毫秒），默认30秒 */
  timeout?: number;
  /** 读偏好 */
  readPreference?: 'primary' | 'secondary';
  /** 读关注级别 */
  readConcern?: 'local' | 'majority' | 'snapshot';
  /** 写关注级别 */
  writeConcern?: 'majority' | number;
}

/**
 * 事务操作函数类型
 */
export type TransactionOperation<T> = (session: ClientSession) => Promise<XResult<T>>;

/**
 * 简化的字段规则配置类型
 */
export type FieldRulesConfig<TSource> = {
  [K in keyof TSource]?:
    | ((value: TSource[K]) => boolean)  // 简单验证函数
    | {
        validate?: (value: TSource[K]) => boolean;  // 验证函数
        transform?: (value: TSource[K]) => any;     // 转换函数
      };
};

/**
 * 分页查询选项
 */
export interface ServicePaginationOptions {
  /** 页码（从1开始） */
  page: number;
  /** 每页记录数 */
  limit: number;
  /** 排序字段 */
  sortBy?: string;
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc';
  /** 搜索关键词 */
  search?: string;
  /** 额外的过滤条件 */
  filters?: Record<string, any>;
}

/**
 * 通用BaseService基类
 * 
 * 🎯 设计目标：
 * - 统一的业务逻辑处理模式
 * - 标准化的微服务调用
 * - 完整的Result模式集成
 * - 通用的错误处理和日志记录
 * - 缓存集成支持
 * - 业务验证框架
 * 
 * 🚀 核心特性：
 * - 统一的Result模式错误处理
 * - 标准化的微服务通信
 * - 通用的业务操作模式
 * - 完整的日志记录
 * - 性能监控支持
 * - 缓存集成框架
 * 
 * 📖 使用示例：
 * ```typescript
 * @Injectable()
 * export class CharacterService extends BaseService {
 *   constructor(
 *     private readonly characterRepository: CharacterRepository,
 *     microserviceClient: MicroserviceClientService
 *   ) {
 *     super('CharacterService', microserviceClient);
 *   }
 * 
 *   async createCharacter(createDto: CreateCharacterDto): Promise<XResult<Character>> {
 *     return this.executeBusinessOperation(async () => {
 *       // 业务逻辑实现
 *       const result = await this.characterRepository.createOne(createDto);
 *       return this.handleRepositoryResult(result);
 *     }, { reason: 'create_character' });
 *   }
 * }
 * ```
 */
@Injectable()
export abstract class BaseService {
  protected readonly logger: Logger;
  private static mongoose: any;
  private static transactionSupportCache = new Map<string, boolean>();

  constructor(
    protected readonly serviceName: string,
    protected readonly microserviceClient?: IMicroserviceClient
  ) {
    this.logger = new Logger(serviceName);
  }

  /**
   * 初始化事务管理器（静态方法，全局调用一次）
   *
   * 使用场景：
   * - 在应用启动时调用，通常在main.ts中
   * - 传入mongoose实例以支持事务功能
   *
   * 使用示例：
   * ```typescript
   * // main.ts
   * import mongoose from 'mongoose';
   * import { BaseService } from '@libs/common/service';
   *
   * async function bootstrap() {
   *   await mongoose.connect(mongoUrl);
   *   BaseService.initializeTransaction(mongoose);
   *   // ... 其他初始化代码
   * }
   * ```
   */
  static initializeTransaction(mongooseInstance: any): void {
    this.mongoose = mongooseInstance;
    this.transactionSupportCache.clear();
  }

  // ========== 事务管理方法 ==========

  /**
   * 执行事务操作（核心方法）
   *
   * 🎯 **使用场景**：
   * - 需要多个数据库操作保持ACID特性
   * - 跨多个Repository的复杂业务操作
   * - 需要回滚机制的关键业务流程
   * - 涉及资源转移、状态变更的操作
   *
   * 🚀 **核心特性**：
   * - 自动资源管理（session创建和清理）
   * - 智能重试机制（处理并发冲突）
   * - 完整的Result模式集成
   * - 自动事务支持检测
   * - 详细的错误处理和日志记录
   *
   * 📖 **使用示例**：
   * ```typescript
   * // 示例1：角色转会操作
   * async transferCharacter(characterId: string, newGuildId: string): Promise<XResult<TransferResult>> {
   *   return this.executeTransaction(async (session) => {
   *     // 1. 检查角色存在
   *     const characterResult = await this.characterRepository.findById(characterId, session);
   *     if (XResultUtils.isFailure(characterResult)) return characterResult;
   *
   *     const character = characterResult.data;
   *     if (!character) {
   *       return XResultUtils.error('角色不存在', 'CHARACTER_NOT_FOUND');
   *     }
   *
   *     // 2. 检查新公会容量
   *     const guildResult = await this.guildRepository.findById(newGuildId, session);
   *     if (XResultUtils.isFailure(guildResult)) return guildResult;
   *
   *     // 3. 执行转会操作
   *     const updateResult = await this.characterRepository.updateOne(
   *       { _id: characterId },
   *       { $set: { guildId: newGuildId, transferTime: new Date() } },
   *       session
   *     );
   *     if (XResultUtils.isFailure(updateResult)) return updateResult;
   *
   *     // 4. 更新公会成员数
   *     const guildUpdateResult = await this.guildRepository.increment(
   *       { _id: newGuildId },
   *       { memberCount: 1 },
   *       session
   *     );
   *     if (XResultUtils.isFailure(guildUpdateResult)) return guildUpdateResult;
   *
   *     return XResultUtils.ok({
   *       characterId,
   *       oldGuildId: character.guildId,
   *       newGuildId,
   *       transferTime: new Date()
   *     });
   *   }, {
   *     maxRetries: 3,
   *     timeout: 30000
   *   });
   * }
   *
   * // 示例2：物品交易操作
   * async tradeItems(
   *   fromCharacterId: string,
   *   toCharacterId: string,
   *   itemId: string,
   *   quantity: number
   * ): Promise<XResult<TradeResult>> {
   *   return this.executeTransaction(async (session) => {
   *     // 验证发送方物品数量
   *     const fromInventoryResult = await this.inventoryRepository.findByCharacterId(fromCharacterId, session);
   *     if (XResultUtils.isFailure(fromInventoryResult)) return fromInventoryResult;
   *
   *     // 扣除发送方物品
   *     const deductResult = await this.inventoryRepository.removeItem(fromCharacterId, itemId, quantity, session);
   *     if (XResultUtils.isFailure(deductResult)) return deductResult;
   *
   *     // 添加到接收方
   *     const addResult = await this.inventoryRepository.addItem(toCharacterId, itemId, quantity, session);
   *     if (XResultUtils.isFailure(addResult)) return addResult;
   *
   *     return XResultUtils.ok({
   *       fromCharacterId,
   *       toCharacterId,
   *       itemId,
   *       quantity,
   *       tradeTime: new Date()
   *     });
   *   });
   * }
   * ```
   *
   * ⚠️ **重要注意事项**：
   * - 事务内的所有Repository操作都必须传递session参数
   * - 事务操作函数必须返回XResult<T>类型
   * - 任何XResult失败都会自动回滚事务
   * - 不要在事务内进行长时间的外部API调用
   * - 事务超时默认30秒，可根据业务需要调整
   *
   * @param operation 事务操作函数，接收ClientSession参数，返回XResult<T>
   * @param options 事务配置选项，包含重试次数、超时时间等
   * @returns Promise<XResult<T>> 事务执行结果
   */
  protected async executeTransaction<T>(
    operation: TransactionOperation<T>,
    options: TransactionOptions = {}
  ): Promise<XResult<T>> {
    const config = this.mergeDefaultTransactionOptions(options);
    const operationId = this.generateOperationId();

    this.logger.log(`开始执行事务: ${operationId}`, {
      maxRetries: config.maxRetries,
      timeout: config.timeout
    });

    for (let attempt = 1; attempt <= config.maxRetries; attempt++) {
      const attemptStartTime = Date.now();

      try {
        const result = await this.executeTransactionOnce(operation, config, operationId, attempt);

        const duration = Date.now() - attemptStartTime;
        if (XResultUtils.isSuccess(result)) {
          this.logger.log(`事务执行成功: ${operationId} (尝试${attempt}/${config.maxRetries}) - 耗时${duration}ms`);
          return result;
        }

        // 检查是否可重试
        if (!this.isRetryableTransactionError(result) || attempt === config.maxRetries) {
          this.logger.warn(`事务执行失败: ${operationId} (尝试${attempt}/${config.maxRetries}) - 耗时${duration}ms`, {
            code: result.code,
            message: result.message,
            canRetry: this.isRetryableTransactionError(result)
          });
          return result;
        }

        // 等待后重试
        const retryDelay = config.retryDelay * attempt;
        this.logger.warn(`事务失败，${retryDelay}ms后重试: ${operationId} (尝试${attempt}/${config.maxRetries})`, {
          code: result.code,
          message: result.message
        });
        await this.delay(retryDelay);

      } catch (error: any) {
        const duration = Date.now() - attemptStartTime;
        this.logger.error(`事务执行异常: ${operationId} (尝试${attempt}/${config.maxRetries}) - 耗时${duration}ms`, {
          error: error.message,
          stack: error.stack
        });

        if (attempt === config.maxRetries) {
          return XResultUtils.error(
            `事务执行异常: ${error.message}`,
            'TRANSACTION_EXCEPTION'
          );
        }

        await this.delay(config.retryDelay * attempt);
      }
    }

    return XResultUtils.error('事务执行失败', 'TRANSACTION_FAILED');
  }

  // ========== 核心业务操作方法 ==========

  /**
   * 执行复杂业务操作（带性能监控和错误处理）
   *
   * 🎯 **使用场景**：
   * - 多步骤业务流程（需要多个Repository调用）
   * - 事务操作（需要保证ACID特性）
   * - 微服务调用（需要处理网络异常）
   * - 复杂验证逻辑（需要多重业务规则检查）
   *
   * ⚠️ **重要提醒**：
   * - 简单的单一Repository调用请直接返回，不要使用此方法
   * - Repository层已将异常转换为XResult，catch块主要处理业务逻辑异常
   * - 此方法会增加调用栈，仅在确实需要性能监控和日志时使用
   *
   * 📋 **使用决策**：
   * ```
   * 是否使用executeBusinessOperation？
   * ├─ 单一Repository调用？ → ❌ 直接返回repository.method()
   * ├─ 需要多步骤验证？ → ✅ 使用executeBusinessOperation
   * ├─ 涉及事务操作？ → ✅ 使用executeBusinessOperation
   * ├─ 调用微服务？ → ✅ 使用executeBusinessOperation
   * └─ 需要特殊监控？ → ✅ 使用executeBusinessOperation
   * ```
   *
   * 📖 **正确使用示例**：
   * ```typescript
   * // ✅ 适合场景：复杂的物品转移业务
   * async transferItemBetweenPlayers(
   *   fromCharacterId: string,
   *   toCharacterId: string,
   *   itemId: string
   * ): Promise<XResult<TransferResult>> {
   *   return this.executeBusinessOperation(async () => {
   *     // 1. 多重验证
   *     const validation = await this.validateTransferRules(fromCharacterId, toCharacterId, itemId);
   *     if (XResultUtils.isFailure(validation)) return validation;
   *
   *     // 2. 多个Repository调用
   *     const fromResult = await this.inventoryRepository.findById(fromCharacterId);
   *     if (XResultUtils.isFailure(fromResult)) return fromResult;
   *
   *     // 3. 微服务调用
   *     const feeResult = await this.callMicroservice('economy', 'checkTransferFee', {...});
   *     if (XResultUtils.isFailure(feeResult)) return feeResult;
   *
   *     // 4. 事务操作
   *     return await this.executeTransferTransaction(fromCharacterId, toCharacterId, itemId);
   *   }, { reason: 'transfer_item' });
   * }
   *
   * // ❌ 不适合场景：简单的数据获取
   * async getCharacterWrong(id: string): Promise<XResult<Character>> {
   *   return this.executeBusinessOperation(async () => {  // 过度包装！
   *     return this.characterRepository.findById(id);
   *   });
   * }
   *
   * // ✅ 正确做法：简单调用直接返回
   * async getCharacter(id: string): Promise<XResult<Character>> {
   *   const result = await this.characterRepository.findById(id);
   *   return result;
   * }
   * ```
   *
   * @param operation 业务操作函数，返回XResult<T>
   * @param options 操作选项，包含原因、元数据等
   * @returns Promise<XResult<T>> 业务操作结果
   */
  protected async executeBusinessOperation<T>(
    operation: () => Promise<XResult<T>>,
    options: BusinessOperationOptions = {}
  ): Promise<XResult<T>> {
    const startTime = Date.now();
    const operationId = this.generateOperationId();

    try {
      this.logOperationStart(operationId, options);

      const result = await operation();

      this.logOperationEnd(operationId, startTime, XResultUtils.isSuccess(result));
      return result;

    } catch (error) {
      this.logOperationError(operationId, startTime, error);
      return XResultUtils.error(
        `业务操作失败: ${error.message}`,
        'BUSINESS_OPERATION_ERROR'
      );
    }
  }

  /**
   * 处理Repository结果并转换错误消息
   *
   * 🎯 **使用场景**：
   * - 需要将Repository错误转换为更友好的业务错误消息
   * - 需要统一错误格式和错误码
   * - 需要在Service层添加上下文信息
   *
   * 📋 **使用决策**：
   * ```
   * 何时使用handleRepositoryResult？
   * ├─ Repository结果需要错误消息转换？ → ✅ 使用handleRepositoryResult
   * ├─ 需要添加业务上下文？ → ✅ 使用handleRepositoryResult
   * ├─ 直接传递Repository错误？ → ❌ 直接返回或使用链式调用
   * └─ 需要统一错误格式？ → ✅ 使用handleRepositoryResult
   * ```
   *
   * 📖 **使用示例**：
   * ```typescript
   * // ✅ 适合场景：需要转换错误消息
   * async getCharacterInfo(characterId: string): Promise<XResult<Character>> {
   *   const result = await this.characterRepository.findById(characterId);
   *   if (XResultUtils.isFailure(result)) {
   *     return this.handleRepositoryResult(result, '获取角色信息失败，请稍后重试');
   *   }
   *
   *   if (!result.data) {
   *     return XResultUtils.error('角色不存在或已被删除', 'CHARACTER_NOT_FOUND');
   *   }
   *
   *   return XResultUtils.ok(result.data);
   * }
   *
   * // ❌ 不适合场景：直接传递错误
   * async getCharacterWrong(characterId: string): Promise<XResult<Character>> {
   *   const result = await this.characterRepository.findById(characterId);
   *   return this.handleRepositoryResult(result); // 没有添加价值
   * }
   * ```
   * 
   * // ✅ 正确做法：简单调用直接返回
   * async getCharacter(id: string): Promise<XResult<Character>> {
   *   const result = await this.characterRepository.findById(id);
   *   return result;
   * }
   * ```
   * 
   * @param repositoryResult Repository返回的结果
   * @param errorMessage 自定义错误消息（可选）
   * @returns 处理后的结果，失败时包含自定义错误消息
   */
  protected handleRepositoryResult<T>(
    repositoryResult: XResult<T>,
    errorMessage?: string
  ): XResult<T> {
    if (XResultUtils.isFailure(repositoryResult)) {
      const message = errorMessage || `数据访问失败: ${repositoryResult.message}`;
      this.logger.error(message, { originalError: repositoryResult });
      return XResultUtils.error(message, repositoryResult.code);
    }

    return repositoryResult;
  }

  /**
   * 验证业务规则
   * @param validations 验证函数数组
   */
  protected async validateBusinessRules(
    validations: Array<() => Promise<XResult<void>> | XResult<void>>
  ): Promise<XResult<void>> {
    for (const validation of validations) {
      const result = await validation();
      if (XResultUtils.isFailure(result)) {
        return result;
      }
    }
    return XResultUtils.ok(undefined);
  }

  // ========== 微服务调用方法 ==========

  /**
   * 调用微服务（带错误处理和日志）
   * @param serviceName 服务名称
   * @param pattern 消息模式
   * @param payload 请求数据
   * @param options 调用选项
   */
  protected async callMicroservice<T = any>(
    serviceName: string,
    pattern: string,
    payload: any,
    options: MicroserviceCallOptions = {}
  ): Promise<XResult<T>> {
    if (!this.microserviceClient) {
      return XResultUtils.error('微服务客户端未初始化', 'MICROSERVICE_CLIENT_NOT_INITIALIZED');
    }

    const startTime = Date.now();
    const callId = this.generateCallId();

    try {
      if (options.logCall !== false) {
        this.logger.log(`微服务调用开始: ${callId} - ${serviceName}.${pattern}`, {
          payload: this.sanitizePayload(payload)
        });
      }

      const result = await this.microserviceClient.call(serviceName, pattern, payload);

      const duration = Date.now() - startTime;
      if (options.logCall !== false) {
        this.logger.log(`微服务调用完成: ${callId} - 耗时 ${duration}ms`, {
          success: result?.code === 0
        });
      }

      // 转换微服务响应为Result格式
      if (result && result.code === 0) {
        return XResultUtils.ok(result.data);
      } else {
        const errorMessage = result?.message || '微服务调用失败';
        return XResultUtils.error(errorMessage, `MICROSERVICE_ERROR_${result?.code || 'UNKNOWN'}`);
      }

    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`微服务调用失败: ${callId} - 耗时 ${duration}ms`, error);
      return XResultUtils.error(
        `微服务调用异常: ${error.message}`,
        'MICROSERVICE_CALL_EXCEPTION'
      );
    }
  }

  /**
   * 批量调用微服务
   * @param calls 调用配置数组
   */
  protected async batchCallMicroservices<T = any>(
    calls: Array<{
      serviceName: string;
      pattern: string;
      payload: any;
      key?: string;
    }>
  ): Promise<XResult<Record<string, T>>> {
    try {
      const promises = calls.map(async (call, index) => {
        const key = call.key || `call_${index}`;
        const result = await this.callMicroservice(call.serviceName, call.pattern, call.payload);
        return { key, result };
      });

      const results = await Promise.all(promises);
      const successResults: Record<string, T> = {};
      const errors: string[] = [];

      for (const { key, result } of results) {
        if (XResultUtils.isSuccess(result)) {
          successResults[key] = result.data;
        } else {
          errors.push(`${key}: ${result.message}`);
        }
      }

      if (errors.length > 0) {
        return XResultUtils.error(
          `批量调用部分失败: ${errors.join(', ')}`,
          'BATCH_CALL_PARTIAL_FAILURE'
        );
      }

      return XResultUtils.ok(successResults);

    } catch (error) {
      this.logger.error('批量微服务调用失败', error);
      return XResultUtils.error(
        `批量调用异常: ${error.message}`,
        'BATCH_CALL_EXCEPTION'
      );
    }
  }

  // ========== 通用业务方法 ==========

  /**
   * 获取角色信息（通用方法）
   * @param characterId 角色ID
   */
  /*protected async getCharacterInfo(characterId: string): Promise<XResult<any>> {
    return this.callMicroservice(
      'character',
      'character.getInfo',
      { characterId }
    );
  }*/

  /**
   * 检查角色资源是否足够
   * @param characterId 角色ID
   * @param resourceType 资源类型
   * @param amount 需要的数量
   */
  /*protected async checkCharacterResource(
    characterId: string,
    resourceType: string,
    amount: number
  ): Promise<XResult<boolean>> {
    const characterResult = await this.getCharacterInfo(characterId);
    if (XResultUtils.isFailure(characterResult)) {
      return characterResult as any;
    }

    const character = characterResult.data;
    const currentAmount = character[resourceType] || 0;
    const sufficient = currentAmount >= amount;

    if (!sufficient) {
      return XResultUtils.error(
        `${resourceType}不足，需要${amount}，当前${currentAmount}`,
        'INSUFFICIENT_RESOURCE'
      );
    }

    return XResultUtils.ok(true);
  }*/

  /**
   * 扣除角色资源
   * @param characterId 角色ID
   * @param resourceType 资源类型
   * @param amount 扣除数量
   * @param reason 扣除原因
   */
  /*protected async deductCharacterResource(
    characterId: string,
    resourceType: string,
    amount: number,
    reason: string
  ): Promise<XResult<any>> {
    return this.callMicroservice(
      'character',
      'character.deductCurrency',
      { characterId, currencyType: resourceType, amount, reason }
    );
  }*/

  /**
   * 添加角色资源
   * @param characterId 角色ID
   * @param resourceType 资源类型
   * @param amount 添加数量
   * @param reason 添加原因
   */
  /*protected async addCharacterResource(
    characterId: string,
    resourceType: string,
    amount: number,
    reason: string
  ): Promise<XResult<any>> {
    return this.callMicroservice(
      'character',
      'character.addCurrency',
      { characterId, currencyType: resourceType, amount, reason }
    );
  }*/

  // ========== 类型安全转换工具 ==========

  /**
   * 类型安全的字段提取器（简化版）
   * 从DTO中提取业务字段，自动过滤载荷字段，专注于业务逻辑验证
   *
   * 🎯 设计理念：
   * - Controller层已完成基础验证（类型、格式、必填项）
   * - UpdateQuery会自动过滤非Entity字段
   * - 本函数专注于业务规则验证和数据清理
   *
   * @param source 源对象（DTO）
   * @param fieldRules 字段规则配置
   * @returns 提取后的安全对象
   *
   * @example
   * ```typescript
   * // 简单提取 - 只要字段存在且不为undefined就提取
   * const updateData = this.extractSafeFields(dto, ['name', 'faceIcon', 'level']);
   *
   * // 带业务验证的提取
   * const updateData = this.extractSafeFields(dto, {
   *   name: (val) => val && val.length >= 2 && val.length <= 20,
   *   faceIcon: (val) => val >= 0 && val <= 999,
   *   level: (val) => val > 0 && val <= 100
   * });
   *
   * // 带数据清理的提取
   * const updateData = this.extractSafeFields(dto, {
   *   name: {
   *     validate: (val) => val && val.length >= 2,
   *     transform: (val) => val.trim().toLowerCase()
   *   }
   * });
   * ```
   */
  protected extractSafeFields<TSource extends Record<string, any>>(
    source: TSource,
    fieldRules: string[] | FieldRulesConfig<TSource>
  ): Record<string, any> {
    if (!source || typeof source !== 'object') {
      return {};
    }

    // 简单数组模式：直接提取指定字段
    if (Array.isArray(fieldRules)) {
      const result: Record<string, any> = {};
      for (const field of fieldRules) {
        const value = source[field];
        if (value !== undefined) {
          result[field] = value;
        }
      }
      return result;
    }

    // 规则对象模式：应用业务验证和转换
    const result: Record<string, any> = {};
    const processedFields: string[] = [];

    for (const [field, rule] of Object.entries(fieldRules)) {
      try {
        const value = source[field];

        // 跳过undefined值
        if (value === undefined) {
          continue;
        }

        // 应用规则
        const processedValue = this.applyFieldRule(value, rule, field);
        if (processedValue !== undefined) {
          result[field] = processedValue;
          processedFields.push(field);
        }

      } catch (error) {
        this.logger.warn(`字段处理失败: ${field}`, { error: error.message, value: source[field] });
      }
    }

    // 简化日志记录
    if (processedFields.length > 0) {
      this.logger.debug(`提取字段: [${processedFields.join(', ')}]`);
    }

    return result;
  }

  // ========== 工具方法 ==========

  /**
   * 生成操作ID
   */
  protected generateOperationId(): string {
    return `${this.serviceName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成调用ID
   */
  protected generateCallId(): string {
    return `call_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }

  // ========== 字段提取辅助方法 ==========

  /**
   * 应用字段规则
   */
  private applyFieldRule(value: any, rule: any, fieldName: string): any {
    // 函数规则：直接验证
    if (typeof rule === 'function') {
      try {
        return rule(value) ? value : undefined;
      } catch (error) {
        this.logger.warn(`字段验证失败: ${fieldName}`, { error: error.message });
        return undefined;
      }
    }

    // 对象规则：验证 + 转换
    if (typeof rule === 'object' && rule !== null) {
      // 先验证
      if (typeof rule.validate === 'function') {
        try {
          if (!rule.validate(value)) {
            return undefined;
          }
        } catch (error) {
          this.logger.warn(`字段验证失败: ${fieldName}`, { error: error.message });
          return undefined;
        }
      }

      // 再转换
      if (typeof rule.transform === 'function') {
        try {
          return rule.transform(value);
        } catch (error) {
          this.logger.warn(`字段转换失败: ${fieldName}`, { error: error.message });
          return value; // 转换失败时返回原值
        }
      }

      return value;
    }

    // 默认：直接返回值
    return value;
  }

  /**
   * 清理敏感数据
   * @param payload 请求数据
   */
  protected sanitizePayload(payload: any): any {
    if (!payload || typeof payload !== 'object') {
      return payload;
    }

    const sensitiveFields = ['password', 'token', 'secret', 'key'];
    const sanitized = { ...payload };

    for (const field of sensitiveFields) {
      if (field in sanitized) {
        sanitized[field] = '***';
      }
    }

    return sanitized;
  }

  /**
   * 记录操作开始
   */
  protected logOperationStart(operationId: string, options: BusinessOperationOptions): void {
    this.logger.log(`业务操作开始: ${operationId}`, {
      reason: options.reason,
      metadata: options.metadata
    });
  }

  /**
   * 记录操作结束
   */
  protected logOperationEnd(operationId: string, startTime: number, success: boolean): void {
    const duration = Date.now() - startTime;
    const level = success ? 'log' : 'warn';
    this.logger[level](`业务操作${success ? '成功' : '失败'}: ${operationId} - 耗时 ${duration}ms`);

    if (duration > 5000) {
      this.logger.warn(`慢操作检测: ${operationId} 耗时 ${duration}ms`);
    }
  }

  /**
   * 记录操作错误
   */
  protected logOperationError(operationId: string, startTime: number, error: any): void {
    const duration = Date.now() - startTime;
    this.logger.error(`业务操作异常: ${operationId} - 耗时 ${duration}ms`, {
      error: error.message,
      stack: error.stack
    });
  }

  /**
   * 构建分页查询条件
   * @param options 分页选项
   */
  protected buildPaginationQuery(options: ServicePaginationOptions): any {
    const query: any = {
      page: options.page,
      limit: options.limit
    };

    if (options.sortBy) {
      query.sortBy = options.sortBy;
      query.sortOrder = options.sortOrder || 'desc';
    }

    if (options.search) {
      query.search = options.search;
    }

    if (options.filters) {
      Object.assign(query, options.filters);
    }

    return query;
  }

  /**
   * 获取服务名称
   */
  protected getServiceName(): string {
    return this.serviceName;
  }

  // ==================== 实用业务验证方法 ====================

  /**
   * 验证唯一性约束（最常用）
   * @param value 要检查的值
   * @param fieldName 字段名称
   * @param checkExists 检查是否存在的函数，返回true表示已存在
   *
   * @example
   * ```typescript
   * // 验证角色名唯一性
   * const nameCheck = await this.validateUniqueness(
   *   createDto.name,
   *   '角色名称',
   *   (name) => this.characterRepository.existsByName(name, serverId)
   * );
   * if (XResultUtils.isFailure(nameCheck)) return nameCheck;
   *
   * // 验证邮箱唯一性
   * const emailCheck = await this.validateUniqueness(
   *   registerDto.email,
   *   '邮箱地址',
   *   (email) => this.userRepository.existsByEmail(email)
   * );
   * ```
   */
  protected async validateUniqueness(
    value: string,
    fieldName: string,
    checkExists: (value: string) => Promise<boolean>
  ): Promise<XResult<void>> {
    try {
      const exists = await checkExists(value);
      if (exists) {
        return XResultUtils.error(`${fieldName}已存在`, 'DUPLICATE_VALUE');
      }
      return XResultUtils.ok(undefined);
    } catch (error) {
      this.logger.error(`唯一性验证失败: ${fieldName}`, error);
      return XResultUtils.error(`${fieldName}验证失败`, 'VALIDATION_ERROR');
    }
  }

  /**
   * 验证资源所有权（权限验证简化版）
   * @param userId 用户ID
   * @param resourceOwnerId 资源所有者ID
   * @param resourceName 资源名称
   *
   * @example
   * ```typescript
   * // 验证角色所有权
   * const ownershipCheck = this.validateOwnership(
   *   userId,
   *   character.userId,
   *   '角色'
   * );
   * if (XResultUtils.isFailure(ownershipCheck)) return ownershipCheck;
   *
   * // 验证装备所有权
   * const equipmentCheck = this.validateOwnership(
   *   userId,
   *   equipment.ownerId,
   *   '装备'
   * );
   * ```
   */
  protected validateOwnership(
    userId: string,
    resourceOwnerId: string,
    resourceName: string
  ): XResult<void> {
    if (userId !== resourceOwnerId) {
      return XResultUtils.error(`无权操作此${resourceName}`, 'ACCESS_DENIED');
    }
    return XResultUtils.ok(undefined);
  }

  /**
   * 验证资源充足性（游戏中最常用）
   * @param currentAmount 当前数量
   * @param requiredAmount 需要数量
   * @param resourceName 资源名称
   *
   * @example
   * ```typescript
   * // 验证金币是否充足
   * const goldCheck = this.validateSufficientResource(
   *   character.gold,
   *   upgradeConfig.goldCost,
   *   '金币'
   * );
   * if (XResultUtils.isFailure(goldCheck)) return goldCheck;
   *
   * // 验证体力是否充足
   * const energyCheck = this.validateSufficientResource(
   *   character.energy,
   *   actionConfig.energyCost,
   *   '体力'
   * );
   * ```
   */
  protected validateSufficientResource(
    currentAmount: number,
    requiredAmount: number,
    resourceName: string
  ): XResult<void> {
    if (currentAmount < requiredAmount) {
      return XResultUtils.error(
        `${resourceName}不足，需要${requiredAmount}，当前${currentAmount}`,
        'INSUFFICIENT_RESOURCE'
      );
    }
    return XResultUtils.ok(undefined);
  }

  /**
   * 验证业务状态（简化版）
   * @param currentStatus 当前状态
   * @param allowedStatuses 允许的状态列表
   * @param operationName 操作名称
   *
   * @example
   * ```typescript
   * // 验证角色是否可以进行训练
   * const statusCheck = this.validateBusinessStatus(
   *   character.status,
   *   ['IDLE', 'RESTING'],
   *   '开始训练'
   * );
   * if (XResultUtils.isFailure(statusCheck)) return statusCheck;
   *
   * // 验证订单是否可以取消
   * const orderCheck = this.validateBusinessStatus(
   *   order.status,
   *   ['PENDING', 'CONFIRMED'],
   *   '取消订单'
   * );
   * ```
   */
  protected validateBusinessStatus(
    currentStatus: string,
    allowedStatuses: string[],
    operationName: string
  ): XResult<void> {
    if (!allowedStatuses.includes(currentStatus)) {
      return XResultUtils.error(
        `当前状态(${currentStatus})不允许${operationName}`,
        'INVALID_STATUS'
      );
    }
    return XResultUtils.ok(undefined);
  }

  /**
   * 验证时间限制（冷却时间、活动时间等）
   * @param lastActionTime 上次操作时间
   * @param cooldownMs 冷却时间（毫秒）
   * @param actionName 操作名称
   *
   * @example
   * ```typescript
   * // 验证签到冷却时间
   * const checkinCheck = this.validateTimeLimit(
   *   character.lastCheckinTime,
   *   24 * 60 * 60 * 1000, // 24小时
   *   '每日签到'
   * );
   * if (XResultUtils.isFailure(checkinCheck)) return checkinCheck;
   *
   * // 验证抽卡冷却时间
   * const drawCheck = this.validateTimeLimit(
   *   character.lastDrawTime,
   *   5 * 60 * 1000, // 5分钟
   *   '抽卡'
   * );
   * ```
   */
  protected validateTimeLimit(
    lastActionTime: number,
    cooldownMs: number,
    actionName: string
  ): XResult<void> {
    const now = Date.now();
    const timePassed = now - lastActionTime;

    if (timePassed < cooldownMs) {
      const remainingMs = cooldownMs - timePassed;
      const remainingMinutes = Math.ceil(remainingMs / (60 * 1000));
      return XResultUtils.error(
        `${actionName}冷却中，还需等待${remainingMinutes}分钟`,
        'COOLDOWN_ACTIVE'
      );
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 验证数量限制（每日限制、库存限制等）
   * @param currentCount 当前数量
   * @param maxCount 最大数量
   * @param limitName 限制名称
   *
   * @example
   * ```typescript
   * // 验证每日任务完成次数
   * const dailyTaskCheck = this.validateCountLimit(
   *   character.dailyTaskCount,
   *   10,
   *   '每日任务'
   * );
   * if (XResultUtils.isFailure(dailyTaskCheck)) return dailyTaskCheck;
   *
   * // 验证背包容量
   * const inventoryCheck = this.validateCountLimit(
   *   character.inventory.length,
   *   100,
   *   '背包容量'
   * );
   * ```
   */
  protected validateCountLimit(
    currentCount: number,
    maxCount: number,
    limitName: string
  ): XResult<void> {
    if (currentCount >= maxCount) {
      return XResultUtils.error(
        `${limitName}已达上限(${maxCount})`,
        'LIMIT_EXCEEDED'
      );
    }
    return XResultUtils.ok(undefined);
  }

  /**
   * 验证配置条件（等级、VIP等级等）
   * @param currentLevel 当前等级
   * @param requiredLevel 需要等级
   * @param levelType 等级类型
   *
   * @example
   * ```typescript
   * // 验证角色等级
   * const levelCheck = this.validateLevelRequirement(
   *   character.level,
   *   10,
   *   '角色等级'
   * );
   * if (XResultUtils.isFailure(levelCheck)) return levelCheck;
   *
   * // 验证VIP等级
   * const vipCheck = this.validateLevelRequirement(
   *   character.vipLevel,
   *   3,
   *   'VIP等级'
   * );
   * ```
   */
  protected validateLevelRequirement(
    currentLevel: number,
    requiredLevel: number,
    levelType: string
  ): XResult<void> {
    if (currentLevel < requiredLevel) {
      return XResultUtils.error(
        `${levelType}不足，需要${requiredLevel}级，当前${currentLevel}级`,
        'LEVEL_INSUFFICIENT'
      );
    }
    return XResultUtils.ok(undefined);
  }

  /**
   * 批量验证（组合多个验证）
   * @param validations 验证函数数组
   *
   * @example
   * 示例1：
   * ```typescript
   * // 批量验证多个条件
   * const validationResult = await this.validateBatch([
   *   () => this.validateUniqueness(name, '角色名', checkName),
   *   () => this.validateSufficientResource(gold, cost, '金币'),
   *   () => this.validateLevelRequirement(level, 10, '角色等级')
   * ]);
   * if (XResultUtils.isFailure(validationResult)) return validationResult;
   * ```
   * 示例2：
   * ```typescript
   * // 角色创建的批量验证
   * const batchResult = await this.validateBatch([
   *   // 验证用户是否存在
   *   () => this.validateResourceExists(userId, '用户', (id) => this.userRepository.findById(id)),
   *
   *   // 验证角色名称唯一性
   *   () => this.validateUniqueness(
   *     createDto.name,
   *     '角色名称',
   *     (name) => this.characterRepository.existsByName(name)
   *   ),
   *
   *   // 验证用户角色数量配额
   *   () => this.validateResourceQuota(
   *     await this.characterRepository.countByUserId(userId),
   *     5, // 最多5个角色
   *     '角色'
   *   ),
   *
   *   // 验证服务器容量
   *   async () => {
   *     const serverLoad = await this.getServerLoad(createDto.serverId);
   *     return serverLoad < 0.9
   *       ? XResultUtils.ok(undefined)
   *       : XResultUtils.error('服务器负载过高', 'SERVER_OVERLOADED');
   *   }
   * ]);
   *
   * if (XResultUtils.isFailure(batchResult)) {
   *   return batchResult; // 返回第一个失败的验证错误
   * }
   * ```
   */
  protected async validateBatch(
    validations: Array<() => Promise<XResult<any>> | XResult<any>>
  ): Promise<XResult<void>> {
    const errors: string[] = [];

    for (const validation of validations) {
      try {
        const result = await validation();
        if (XResultUtils.isFailure(result)) {
          errors.push(result.message);
        }
      } catch (error) {
        errors.push(`验证异常: ${error.message}`);
      }
    }

    if (errors.length > 0) {
      return XResultUtils.error(`验证失败: ${errors.join('; ')}`, 'BATCH_VALIDATION_FAILED');
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 验证数组长度
   * @param array 数组
   * @param fieldName 字段名称
   * @param minLength 最小长度
   * @param maxLength 最大长度
   */
  protected validateArrayLength<T>(
    array: T[],
    fieldName: string,
    minLength?: number,
    maxLength?: number
  ): XResult<void> {
    if (!Array.isArray(array)) {
      return XResultUtils.error(`${fieldName}必须是数组`, 'INVALID_ARRAY');
    }

    if (minLength !== undefined && array.length < minLength) {
      return XResultUtils.error(`${fieldName}至少需要${minLength}个元素`, 'ARRAY_TOO_SHORT');
    }

    if (maxLength !== undefined && array.length > maxLength) {
      return XResultUtils.error(`${fieldName}最多只能有${maxLength}个元素`, 'ARRAY_TOO_LONG');
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 验证外键约束
   * @param foreignId 外键ID
   * @param foreignType 外键类型
   * @param checkFunction 外键检查函数
   */
  protected async validateForeignKey(
    foreignId: string,
    foreignType: string,
    checkFunction: (id: string) => Promise<boolean>
  ): Promise<XResult<void>> {
    if (!foreignId) {
      return XResultUtils.error(`${foreignType}ID不能为空`, 'FOREIGN_KEY_REQUIRED');
    }

    try {
      const exists = await checkFunction(foreignId);
      if (!exists) {
        return XResultUtils.error(`关联的${foreignType}不存在`, 'FOREIGN_KEY_NOT_FOUND');
      }

      return XResultUtils.ok(undefined);
    } catch (error) {
      this.logger.error('外键验证失败', {
        foreignId,
        foreignType,
        error: error.message,
      });
      return XResultUtils.error('外键验证失败', 'FOREIGN_KEY_CHECK_FAILED');
    }
  }

  /**
   * 条件验证
   * @param condition 条件
   * @param validation 验证函数
   * @param errorMessage 错误消息
   * @param errorCode 错误代码
   */
  protected async validateIf(
    condition: boolean | (() => boolean | Promise<boolean>),
    validation: () => Promise<XResult<any>> | XResult<any>,
    errorMessage?: string,
    errorCode?: string
  ): Promise<XResult<void>> {
    try {
      const shouldValidate = typeof condition === 'function' ? await condition() : condition;

      if (!shouldValidate) {
        return XResultUtils.ok(undefined);
      }

      const result = await validation();
      if (XResultUtils.isFailure(result)) {
        return result;
      }

      return XResultUtils.ok(undefined);
    } catch (error) {
      return XResultUtils.error(
        errorMessage || `条件验证失败: ${error.message}`,
        errorCode || 'CONDITIONAL_VALIDATION_FAILED'
      );
    }
  }

  /**
   * 验证复杂业务规则
   * @param rules 业务规则数组
   *
   * @example
   * ```typescript
   * // 装备升级的复杂业务规则验证
   * const rulesResult = await this.validateComplexBusinessRules([
   *   {
   *     name: '等级要求',
   *     check: () => character.level >= 10,
   *     errorMessage: '角色等级必须达到10级才能升级装备',
   *     errorCode: 'LEVEL_TOO_LOW'
   *   },
   *   {
   *     name: '升级冷却时间',
   *     check: async () => {
   *       if (!equipment.lastUpgradeAt) return true;
   *       const cooldownHours = 2;
   *       const timeSinceLastUpgrade = Date.now() - equipment.lastUpgradeAt.getTime();
   *       return timeSinceLastUpgrade >= cooldownHours * 60 * 60 * 1000;
   *     },
   *     errorMessage: '升级冷却时间未到，请2小时后再试',
   *     errorCode: 'UPGRADE_COOLDOWN'
   *   },
   *   {
   *     name: '升级材料检查',
   *     check: async () => {
   *       const materials = await this.inventoryService.getMaterials(characterId);
   *       const required = this.getRequiredMaterials(equipment.level + 1);
   *       return Object.entries(required).every(([materialId, count]) =>
   *         (materials[materialId] || 0) >= count
   *       );
   *     },
   *     errorMessage: '升级材料不足',
   *     errorCode: 'INSUFFICIENT_MATERIALS'
   *   },
   *   {
   *     name: '成功率检查',
   *     check: () => {
   *       const successRate = this.calculateUpgradeSuccessRate(equipment.level);
   *       return successRate >= 0.1; // 至少10%成功率
   *     },
   *     errorMessage: '升级成功率过低，无法进行升级',
   *     errorCode: 'SUCCESS_RATE_TOO_LOW'
   *   }
   * ]);
   *
   * if (XResultUtils.isFailure(rulesResult)) {
   *   return rulesResult; // 返回第一个失败的业务规则错误
   * }
   * ```
   */
  protected async validateComplexBusinessRules(
    rules: Array<{
      name: string;
      check: () => Promise<boolean> | boolean;
      errorMessage: string;
      errorCode?: string;
    }>
  ): Promise<XResult<void>> {
    for (const rule of rules) {
      try {
        const passed = await rule.check();
        if (!passed) {
          return XResultUtils.error(rule.errorMessage, rule.errorCode || 'BUSINESS_RULE_VIOLATION');
        }
      } catch (error) {
        this.logger.error('业务规则验证异常', {
          ruleName: rule.name,
          error: error.message,
        });
        return XResultUtils.error(
          `业务规则 "${rule.name}" 验证失败: ${error.message}`,
          'BUSINESS_RULE_CHECK_FAILED'
        );
      }
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 验证时间范围
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @param fieldName 字段名称前缀
   */
  protected validateTimeRange(
    startTime: Date,
    endTime: Date,
    fieldName: string = '时间'
  ): XResult<void> {
    if (!startTime || !endTime) {
      return XResultUtils.error(`${fieldName}范围不能为空`, 'TIME_RANGE_REQUIRED');
    }

    if (!(startTime instanceof Date) || !(endTime instanceof Date)) {
      return XResultUtils.error(`${fieldName}必须是有效的日期`, 'INVALID_DATE');
    }

    if (startTime >= endTime) {
      return XResultUtils.error(`${fieldName}开始时间必须早于结束时间`, 'INVALID_TIME_RANGE');
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 验证资源配额
   * @param currentCount 当前数量
   * @param maxAllowed 最大允许数量
   * @param resourceType 资源类型
   */
  protected validateResourceQuota(
    currentCount: number,
    maxAllowed: number,
    resourceType: string
  ): XResult<void> {
    if (currentCount >= maxAllowed) {
      return XResultUtils.error(
        `${resourceType}数量已达上限 (${maxAllowed})`,
        'RESOURCE_QUOTA_EXCEEDED'
      );
    }

    return XResultUtils.ok(undefined);
  }

  // ========== 事务辅助方法（私有方法） ==========

  /**
   * 执行单次事务
   */
  private async executeTransactionOnce<T>(
    operation: TransactionOperation<T>,
    options: Required<TransactionOptions>,
    operationId: string,
    attempt: number
  ): Promise<XResult<T>> {
    if (!BaseService.mongoose) {
      return XResultUtils.error('Mongoose未初始化，请先调用BaseService.initializeTransaction()', 'MONGOOSE_NOT_INITIALIZED');
    }

    // 检查是否支持事务
    const supportsTransactions = await this.checkTransactionSupport();

    if (!supportsTransactions) {
      this.logger.warn(`MongoDB不支持事务，将在非事务模式下执行操作: ${operationId}`);
      try {
        const result = await operation(null as any);
        return result;
      } catch (error: any) {
        return XResultUtils.error(
          error.message || '操作执行异常',
          this.getTransactionErrorCode(error)
        );
      }
    }

    const session = await BaseService.mongoose.startSession();

    try {
      // 启动事务
      session.startTransaction({
        readPreference: options.readPreference,
        readConcern: { level: options.readConcern },
        writeConcern: { w: options.writeConcern }
      });

      // 设置超时
      const timeoutPromise = new Promise<XResult<T>>((_, reject) => {
        setTimeout(() => reject(new Error(`事务超时: ${operationId}`)), options.timeout);
      });

      // 执行操作
      const operationPromise = operation(session);
      const result = await Promise.race([operationPromise, timeoutPromise]);

      // 检查结果
      if (XResultUtils.isFailure(result)) {
        await session.abortTransaction();
        return result;
      }

      // 提交事务
      await session.commitTransaction();
      return result;

    } catch (error: any) {
      await session.abortTransaction();
      return XResultUtils.error(
        error.message || '事务执行异常',
        this.getTransactionErrorCode(error)
      );
    } finally {
      await session.endSession();
    }
  }

  /**
   * 检查MongoDB是否支持事务（带缓存）
   */
  private async checkTransactionSupport(): Promise<boolean> {
    const dbName = BaseService.mongoose.connection.db.databaseName;

    // 如果已经检测过这个数据库，直接返回缓存结果
    if (BaseService.transactionSupportCache.has(dbName)) {
      return BaseService.transactionSupportCache.get(dbName)!;
    }

    try {
      const admin = BaseService.mongoose.connection.db.admin();
      const result = await admin.command({ isMaster: 1 });

      // 检查是否为副本集或分片集群
      const supported = !!(result.setName || result.msg === 'isdbgrid');

      // 缓存结果
      BaseService.transactionSupportCache.set(dbName, supported);

      if (supported) {
        this.logger.debug(`数据库 ${dbName} 支持事务（副本集或分片集群）`);
      } else {
        this.logger.debug(`数据库 ${dbName} 不支持事务（单节点模式）`);
      }

      return supported;
    } catch (error) {
      this.logger.warn(`检查数据库 ${dbName} 事务支持时出错:`, error);
      BaseService.transactionSupportCache.set(dbName, false);
      return false;
    }
  }

  /**
   * 合并默认事务配置
   */
  private mergeDefaultTransactionOptions(options: TransactionOptions): Required<TransactionOptions> {
    return {
      maxRetries: options.maxRetries ?? 3,
      retryDelay: options.retryDelay ?? 1000,
      timeout: options.timeout ?? 30000,
      readPreference: options.readPreference ?? 'primary',
      readConcern: options.readConcern ?? 'local',
      writeConcern: options.writeConcern ?? 'majority'
    };
  }

  /**
   * 检查是否为可重试的事务错误
   */
  private isRetryableTransactionError(result: XResult<any>): boolean {
    if (XResultUtils.isSuccess(result)) return false;

    const retryableCodes = [
      'TRANSACTION_CONFLICT',
      'WRITE_CONFLICT',
      'DUPLICATE_KEY',
      'NETWORK_ERROR',
      'TRANSACTION_ABORTED'
    ];

    return retryableCodes.includes(result.code);
  }

  /**
   * 获取事务错误代码
   */
  private getTransactionErrorCode(error: any): string {
    const retryableErrorCodes = [11000, 112, 244, 251];

    if (retryableErrorCodes.includes(error.code)) {
      switch (error.code) {
        case 11000: return 'DUPLICATE_KEY';
        case 112: return 'WRITE_CONFLICT';
        case 244: return 'TRANSACTION_CONFLICT';
        case 251: return 'TRANSACTION_ABORTED';
        default: return 'RETRYABLE_ERROR';
      }
    }

    return 'TRANSACTION_ERROR';
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
