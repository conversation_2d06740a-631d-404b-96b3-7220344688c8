import { Injectable, Logger } from '@nestjs/common';
import { RedisPubSubService } from '@libs/redis';

/**
 * 推送客户端服务
 * 
 * 职责：
 * - 为业务服务提供统一的推送接口
 * - 将推送请求发送到Gateway
 * - 完全业务无关的纯推送工具
 */

/**
 * 推送数据接口
 */
export interface PushData {
  /** WebSocket事件名称 */
  eventName: string;
  /** 推送给客户端的数据 */
  payload: any;
  /** 业务类型（可选，用于区分业务模块） */
  businessType?: string;
  /** 推送选项 */
  options?: {
    requireAck?: boolean;
    priority?: 'low' | 'normal' | 'high';
    expireAt?: number;
  };
}

@Injectable()
export class PushClientService {
  private readonly logger = new Logger(PushClientService.name);

  constructor(
    private readonly redisPubSubService: RedisPubSubService,
  ) {}

  /**
   * 推送给单个用户
   */
  async pushToUser(userId: string, pushData: PushData): Promise<void> {
    try {
      await this.redisPubSubService.publishServerEvent('gateway:push', {
        pushType: 'user',
        targetUserId: userId,
        eventName: pushData.eventName,
        payload: pushData.payload,
        businessType: pushData.businessType,
        options: pushData.options
      });

      this.logger.debug(`📤 Sent push request to user: ${userId}, event: ${pushData.eventName}`);
    } catch (error) {
      this.logger.error(`Failed to send push request to user ${userId}: ${error.message}`);
    }
  }

  /**
   * 推送给多个用户
   */
  async pushToUsers(userIds: string[], pushData: PushData): Promise<void> {
    try {
      await this.redisPubSubService.publishServerEvent('gateway:push', {
        pushType: 'users',
        targetUserIds: userIds,
        eventName: pushData.eventName,
        payload: pushData.payload,
        businessType: pushData.businessType,
        options: pushData.options
      });

      this.logger.debug(`📤 Sent push request to ${userIds.length} users, event: ${pushData.eventName}`);
    } catch (error) {
      this.logger.error(`Failed to send push request to users: ${error.message}`);
    }
  }

  /**
   * 推送给游戏房间（比赛房间、俱乐部房间等）
   */
  async pushToRoom(roomId: string, pushData: PushData): Promise<void> {
    try {
      await this.redisPubSubService.publishServerEvent('gateway:push', {
        pushType: 'room',
        roomId: roomId,
        eventName: pushData.eventName,
        payload: pushData.payload,
        businessType: pushData.businessType,
        options: pushData.options
      });

      this.logger.debug(`📤 Sent push request to room: ${roomId}, event: ${pushData.eventName}`);
    } catch (error) {
      this.logger.error(`Failed to send push request to room ${roomId}: ${error.message}`);
    }
  }

  // ==================== 便捷方法 ====================

  /**
   * 发送简单通知给用户
   */
  async notifyUser(userId: string, eventName: string, data: any, businessType?: string): Promise<void> {
    await this.pushToUser(userId, {
      eventName,
      payload: data,
      businessType
    });
  }

  /**
   * 发送简单通知给多个用户
   */
  async notifyUsers(userIds: string[], eventName: string, data: any, businessType?: string): Promise<void> {
    await this.pushToUsers(userIds, {
      eventName,
      payload: data,
      businessType
    });
  }

  /**
   * 发送简单通知给房间
   */
  async notifyRoom(roomId: string, eventName: string, data: any, businessType?: string): Promise<void> {
    await this.pushToRoom(roomId, {
      eventName,
      payload: data,
      businessType
    });
  }

  /**
   * 发送房间通知（游戏房间专用）
   * 使用场景：比赛房间、俱乐部房间、私聊房间等
   */
  async notifyGameRoom(roomId: string, eventName: string, data: any, businessType?: string): Promise<void> {
    await this.pushToRoom(roomId, {
      eventName,
      payload: data,
      businessType
    });
  }
}
