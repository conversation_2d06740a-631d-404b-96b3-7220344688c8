/**
 * 游戏常量定义
 */

// 游戏基础常量
export const GAME_CONSTANTS = {
  // 角色相关
  CHARACTER: {
    MAX_LEVEL: 100, // 最大等级
    INITIAL_CASH: 10000, // 初始欧元数量
    INITIAL_GOLD: 0, // 初始金币数量
    INITIAL_ENERGY: 100, // 初始体力值
    INITIAL_NAME_PREFIX: 'Char_', // 初始化角色名前缀
    MAX_ENERGY: 100, // 最大体力值
    ENERGY_RECOVERY_RATE: 1, // 每分钟恢复1点体力
    ENERGY_RECOVERY_INTERVAL: 60000, // 60秒
    MAX_NAME_LENGTH: 20, // 最大角色名长度
    MIN_NAME_LENGTH: 2, // 最小角色名长度
    DEFAULT_FACE_ICON: 10600011, // 默认头像ID
    DEFAULT_FIELD_LEVEL: 0, // 默认球场等级
    DEFAULT_LEAGUE: 0, // 默认联赛
    DEFAULT_QUALIFIED: 0, // 默认新老手标志
  },

  // 球员相关
  HERO: {
    MAX_LEVEL: 100,
    MAX_STAR: 5,
    MIN_STAR: 1,
    MAX_SKILL_LEVEL: 10,
    INITIAL_ATTRIBUTES: {
      speed: 50,
      jumping: 50,
      strength: 50,
      stamina: 50,
      finishing: 50,
      dribbling: 50,
      passing: 50,
      heading: 50,
      standingTackle: 50,
      slidingTackle: 50,
      longPassing: 50,
      longShots: 50,
      penalties: 50,
      cornerKick: 50,
      freeKick: 50,
      explosiveForce: 50,
      attack: 50,
      volleys: 50,
      save: 50,
      resistanceDamage: 50,
    },
  },

  // 阵容相关
  FORMATION: {
    MAX_FORMATIONS: 5,
    REQUIRED_PLAYERS: 11,
    POSITIONS: {
      GK: 1,  // 门将数量
      FIELD_PLAYERS: 10, // 场上球员数量
    },
    MAX_NAME_LENGTH: 30,
  },

  // 背包相关
  INVENTORY: {
    DEFAULT_CAPACITY: 100,
    MAX_CAPACITY: 500,
    STACK_SIZE: 999,
  },

  // 比赛相关
  MATCH: {
    ENERGY_COST: {
      LEAGUE: 10,
      CUP: 15,
      FRIENDLY: 5,
      TOURNAMENT: 20,
    },
    COOLDOWN: {
      LEAGUE: 0,
      CUP: 3600000, // 1小时
      FRIENDLY: 0,
      TOURNAMENT: 7200000, // 2小时
    },
  },

  // 经济相关
  ECONOMY: {
    MAX_CASH: 999999999,
    MAX_GOLD: 999999999,
    DAILY_FREE_ENERGY: 3,
    VIP_BENEFITS: {
      ENERGY_BONUS: [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
      CASH_BONUS: [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50],
    },
  },

  // 社交相关
  SOCIAL: {
    MAX_FRIENDS: 100,
    MAX_GUILD_MEMBERS: 50,
    MAIL_EXPIRY_DAYS: 7,
    MAX_MAIL_CONTENT_LENGTH: 500,
  },

  // 活动相关
  ACTIVITY: {
    DAILY_TASK_RESET_HOUR: 0, // 每日0点重置
    WEEKLY_TASK_RESET_DAY: 1, // 每周一重置
    MAX_ACTIVE_TASKS: 20,
    SIGN_REWARD_DAYS: 7,
  },

  // 训练相关
  TRAINING: {
    MAX_DAILY_TRAINING: 10,
    TRAINING_COSTS: {
      SPEED: 1000,
      STRENGTH: 1000,
      TECHNIQUE: 1500,
      MENTAL: 2000,
    },
    SUCCESS_RATES: {
      NORMAL: 0.8,
      ADVANCED: 0.6,
      EXPERT: 0.4,
    },
  },

  // 技能相关
  SKILL: {
    MAX_ACTIVE_SKILLS: 3,
    UPGRADE_COSTS: [100, 200, 400, 800, 1600, 3200, 6400, 12800, 25600, 51200],
  },

  // 时间相关
  TIME: {
    MINUTE: 60 * 1000,
    HOUR: 60 * 60 * 1000,
    DAY: 24 * 60 * 60 * 1000,
    WEEK: 7 * 24 * 60 * 60 * 1000,
  },

  // 缓存相关
  CACHE: {
    TTL: {
      CHARACTER_INFO: 3600, // 1小时
      HERO_INFO: 1800,      // 30分钟
      FORMATION_INFO: 1800, // 30分钟
      CONFIG_DATA: 7200,    // 2小时
      RANKING_DATA: 300,    // 5分钟
    },
  },

  // API相关
  API: {
    DEFAULT_PAGE_SIZE: 20,
    MAX_PAGE_SIZE: 100,
    RATE_LIMIT: {
      REQUESTS_PER_MINUTE: 100,
      REQUESTS_PER_HOUR: 1000,
    },
  },

  // 文件相关
  FILE: {
    MAX_UPLOAD_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_IMAGE_TYPES: ['jpg', 'jpeg', 'png', 'gif'],
    ALLOWED_DOCUMENT_TYPES: ['pdf', 'doc', 'docx', 'txt'],
  },

  // 安全相关
  SECURITY: {
    PASSWORD_MIN_LENGTH: 6,
    PASSWORD_MAX_LENGTH: 50,
    TOKEN_EXPIRY: 24 * 60 * 60 * 1000, // 24小时
    REFRESH_TOKEN_EXPIRY: 7 * 24 * 60 * 60 * 1000, // 7天
    MAX_LOGIN_ATTEMPTS: 5,
    LOCKOUT_DURATION: 30 * 60 * 1000, // 30分钟
  },

  // 日志相关
  LOG: {
    MAX_LOG_SIZE: 100 * 1024 * 1024, // 100MB
    LOG_RETENTION_DAYS: 30,
    SENSITIVE_FIELDS: ['password', 'token', 'secret'],
  },
} as const;

// 导出类型
export type GameConstants = typeof GAME_CONSTANTS;
