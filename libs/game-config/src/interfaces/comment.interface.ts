// Auto-generated from Comment.json
// Generated at: 2025-07-20T12:55:59.695Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface CommentDefinition {
  groupId: number; // 唯一标识符 例: 10100, 10102 (原: GroupID)
  id: number; // 唯一标识符 例: 101000, 101001
  comment: string; // 字符串 例: #A1#向前传球, #A1#打算传球给队友 (原: Comment)
}

// 字段映射：新字段名 -> 原始字段名
export const CommentFieldMappings = {
  groupId: 'GroupID',
  comment: 'Comment',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const CommentReverseFieldMappings = {
  'GroupID': 'groupId',
  'Comment': 'comment',
} as const;

export const CommentMeta = {
  tableName: 'Comment',
  dataFileName: 'Comment.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 3,
  requiredFields: ['groupId', 'id', 'comment'],
  optionalFields: [],
  renamedFieldsCount: 2,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: CommentFieldMappings,
  reverseFieldMappings: CommentReverseFieldMappings,
} as const;

export type CommentConfigMeta = typeof CommentMeta;
export type CommentFieldMapping = typeof CommentFieldMappings;
export type CommentReverseFieldMapping = typeof CommentReverseFieldMappings;
