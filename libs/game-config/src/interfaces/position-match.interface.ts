// Auto-generated from PositionMatch.json
// Generated at: 2025-07-20T12:56:05.936Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface PositionMatchDefinition {
  id: number; // 唯一标识符 例: 1, 2
  AM: number; // 数值 例: 0.3, 0.4
  DC: number; // 数值 例: 0.3, 1
  DL: number; // 数值 例: 0.3, 0.8
  DM: number; // 数值 例: 0.3, 0.85
  DR: number; // 数值 例: 0.3, 0.8
  GK: number; // 数值 例: 1, 0.3
  MC: number; // 数值 例: 0.3, 0.7
  ML: number; // 数值 例: 0.3, 0.5
  MR: number; // 数值 例: 0.3, 0.5
  position: string; // 位置 例: 门将, 中后卫 (原: Position)
  ST: number; // 数值 例: 0.3, 0.4
  WL: number; // 数值 例: 0.3, 0.9
  WR: number; // 数值 例: 0.3, 0.9
}

// 字段映射：新字段名 -> 原始字段名
export const PositionMatchFieldMappings = {
  position: 'Position',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const PositionMatchReverseFieldMappings = {
  'Position': 'position',
} as const;

export const PositionMatchMeta = {
  tableName: 'PositionMatch',
  dataFileName: 'PositionMatch.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 14,
  requiredFields: ['id', 'AM', 'DC', 'DL', 'DM', 'DR', 'GK', 'MC', 'ML', 'MR', 'position', 'ST', 'WL', 'WR'],
  optionalFields: [],
  renamedFieldsCount: 1,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: PositionMatchFieldMappings,
  reverseFieldMappings: PositionMatchReverseFieldMappings,
} as const;

export type PositionMatchConfigMeta = typeof PositionMatchMeta;
export type PositionMatchFieldMapping = typeof PositionMatchFieldMappings;
export type PositionMatchReverseFieldMapping = typeof PositionMatchReverseFieldMappings;
