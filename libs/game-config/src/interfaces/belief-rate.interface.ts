// Auto-generated from BeliefRate.json
// Generated at: 2025-07-20T12:55:59.222Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface BeliefRateDefinition {
  id: number; // 唯一标识符 例: 1, 2
  maxValue: number; // 最大值 例: 10000, 20000 (原: Max)
  minValue: number; // 最小值 例: 0, 10001 (原: Min)
  rate: number; // 比率 例: 1, 0.9 (原: Rate)
}

// 字段映射：新字段名 -> 原始字段名
export const BeliefRateFieldMappings = {
  maxValue: 'Max',
  minValue: 'Min',
  rate: 'Rate',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const BeliefRateReverseFieldMappings = {
  'Max': 'maxValue',
  'Min': 'minValue',
  'Rate': 'rate',
} as const;

export const BeliefRateMeta = {
  tableName: 'BeliefRate',
  dataFileName: 'BeliefRate.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 4,
  requiredFields: ['id', 'maxValue', 'minValue', 'rate'],
  optionalFields: [],
  renamedFieldsCount: 3,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: BeliefRateFieldMappings,
  reverseFieldMappings: BeliefRateReverseFieldMappings,
} as const;

export type BeliefRateConfigMeta = typeof BeliefRateMeta;
export type BeliefRateFieldMapping = typeof BeliefRateFieldMappings;
export type BeliefRateReverseFieldMapping = typeof BeliefRateReverseFieldMappings;
