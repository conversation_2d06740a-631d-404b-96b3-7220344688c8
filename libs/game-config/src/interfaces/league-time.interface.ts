// Auto-generated from LeagueTime.json
// Generated at: 2025-07-20T12:56:04.939Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface LeagueTimeDefinition {
  groupId: number; // 唯一标识符 例: 1, 2 (原: GroupId)
  id: number; // 唯一标识符 例: 1, 2
  hour: number; // 数值 例: 15, 8 (原: Hour)
  minutes: number; // 最小 例: 0, 50 (原: Minutes)
  seconds: number; // 数值 例: 0, 30 (原: Seconds)
}

// 字段映射：新字段名 -> 原始字段名
export const LeagueTimeFieldMappings = {
  groupId: 'GroupId',
  hour: 'Hour',
  minutes: 'Minutes',
  seconds: 'Seconds',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const LeagueTimeReverseFieldMappings = {
  'GroupId': 'groupId',
  'Hour': 'hour',
  'Minutes': 'minutes',
  'Seconds': 'seconds',
} as const;

export const LeagueTimeMeta = {
  tableName: 'LeagueTime',
  dataFileName: 'LeagueTime.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 5,
  requiredFields: ['groupId', 'id', 'hour', 'minutes', 'seconds'],
  optionalFields: [],
  renamedFieldsCount: 4,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: LeagueTimeFieldMappings,
  reverseFieldMappings: LeagueTimeReverseFieldMappings,
} as const;

export type LeagueTimeConfigMeta = typeof LeagueTimeMeta;
export type LeagueTimeFieldMapping = typeof LeagueTimeFieldMappings;
export type LeagueTimeReverseFieldMapping = typeof LeagueTimeReverseFieldMappings;
