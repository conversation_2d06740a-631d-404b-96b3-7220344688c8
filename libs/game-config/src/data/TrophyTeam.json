[{"id": 1, "CopyType": 1, "CopyIcon": 0, "TeamName": "意大利之夏", "TeamRemarks": "随机获得意大利籍球员", "TeamOrder": 1, "TeamID": 91206, "Lv": 1, "Formation": 442201, "OffensiveID": 101, "DefenseID": 1101, "LimitNum": 1, "PurchaseType": 1, "PurchaseMaxNum": 2, "PurchaseParameters": 100, "ConsumeType": 3, "ConsumeValue": 7, "RewardType1": 0, "RewardIcon1": 10010, "RewardType2": 0, "RewardIcon2": 10011, "RewardType3": 0, "RewardIcon3": 10012, "FixedRewardId": 1000, "RandomRewardId1": 0, "Weight1": 9000, "RandomRewardId2": 2000, "Weight2": 1000}, {"id": 2, "CopyType": 1, "CopyIcon": 0, "TeamName": "荣耀之地", "TeamRemarks": "随机获得火星籍球员", "TeamOrder": 2, "TeamID": 91207, "Lv": 1, "Formation": 442101, "OffensiveID": 101, "DefenseID": 1101, "LimitNum": 2, "PurchaseType": 2, "PurchaseMaxNum": 3, "PurchaseParameters": 100, "ConsumeType": 3, "ConsumeValue": 7, "RewardType1": 0, "RewardIcon1": 10010, "RewardType2": 0, "RewardIcon2": 10011, "RewardType3": 0, "RewardIcon3": 10012, "FixedRewardId": 1000, "RandomRewardId1": 0, "Weight1": 9000, "RandomRewardId2": 2001, "Weight2": 1000}, {"id": 3, "CopyType": 1, "CopyIcon": 0, "TeamName": "生命之杯", "TeamRemarks": "随机获得不知道籍球员", "TeamOrder": 3, "TeamID": 91208, "Lv": 1, "Formation": 451401, "OffensiveID": 101, "DefenseID": 1101, "LimitNum": 2, "PurchaseType": 1, "PurchaseMaxNum": 4, "PurchaseParameters": 100, "ConsumeType": 3, "ConsumeValue": 7, "RewardType1": 0, "RewardIcon1": 10010, "RewardType2": 0, "RewardIcon2": 10011, "RewardType3": 0, "RewardIcon3": 10012, "FixedRewardId": 1000, "RandomRewardId1": 0, "Weight1": 9000, "RandomRewardId2": 2002, "Weight2": 1000}, {"id": 4, "CopyType": 1, "CopyIcon": 0, "TeamName": "团队之星", "TeamRemarks": "随机获得德国籍球员", "TeamOrder": 4, "TeamID": 91209, "Lv": 1, "Formation": 442101, "OffensiveID": 101, "DefenseID": 1101, "LimitNum": 2, "PurchaseType": 2, "PurchaseMaxNum": 5, "PurchaseParameters": 100, "ConsumeType": 3, "ConsumeValue": 7, "RewardType1": 0, "RewardIcon1": 10010, "RewardType2": 0, "RewardIcon2": 10011, "RewardType3": 0, "RewardIcon3": 10012, "FixedRewardId": 1000, "RandomRewardId1": 0, "Weight1": 9000, "RandomRewardId2": 2003, "Weight2": 1000}, {"id": 5, "CopyType": 1, "CopyIcon": 0, "TeamName": "飘扬的旗帜", "TeamRemarks": "随机获得法国籍球员", "TeamOrder": 5, "TeamID": 91210, "Lv": 1, "Formation": 442101, "OffensiveID": 101, "DefenseID": 1101, "LimitNum": 2, "PurchaseType": 1, "PurchaseMaxNum": 6, "PurchaseParameters": 100, "ConsumeType": 3, "ConsumeValue": 7, "RewardType1": 0, "RewardIcon1": 10010, "RewardType2": 0, "RewardIcon2": 10011, "RewardType3": 0, "RewardIcon3": 10012, "FixedRewardId": 1000, "RandomRewardId1": 0, "Weight1": 9000, "RandomRewardId2": 2004, "Weight2": 1000}, {"id": 6, "CopyType": 1, "CopyIcon": 0, "TeamName": "桑巴荣耀", "TeamRemarks": "随机获得巴西籍球员", "TeamOrder": 6, "TeamID": 91211, "Lv": 1, "Formation": 442201, "OffensiveID": 101, "DefenseID": 1101, "LimitNum": 2, "PurchaseType": 2, "PurchaseMaxNum": 7, "PurchaseParameters": 100, "ConsumeType": 3, "ConsumeValue": 7, "RewardType1": 0, "RewardIcon1": 10010, "RewardType2": 0, "RewardIcon2": 10011, "RewardType3": 0, "RewardIcon3": 10012, "FixedRewardId": 1000, "RandomRewardId1": 0, "Weight1": 9000, "RandomRewardId2": 2005, "Weight2": 1000}, {"id": 7, "CopyType": 1, "CopyIcon": 0, "TeamName": "决战西伯利亚", "TeamRemarks": "随机获得俄罗斯籍球员", "TeamOrder": 7, "TeamID": 91212, "Lv": 1, "Formation": 451401, "OffensiveID": 101, "DefenseID": 1101, "LimitNum": 2, "PurchaseType": 1, "PurchaseMaxNum": 8, "PurchaseParameters": 100, "ConsumeType": 3, "ConsumeValue": 7, "RewardType1": 0, "RewardIcon1": 10010, "RewardType2": 0, "RewardIcon2": 10011, "RewardType3": 0, "RewardIcon3": 10012, "FixedRewardId": 1000, "RandomRewardId1": 0, "Weight1": 9000, "RandomRewardId2": 2006, "Weight2": 1000}, {"id": 8, "CopyType": 2, "CopyIcon": 0, "TeamName": "职业联队", "TeamRemarks": "随机获得道具或球员奖励", "TeamOrder": 1, "TeamID": 91213, "Lv": 1, "Formation": 451401, "OffensiveID": 101, "DefenseID": 1101, "LimitNum": 2, "PurchaseType": 3, "PurchaseMaxNum": 9, "PurchaseParameters": 100, "ConsumeType": 2, "ConsumeValue": 100, "RewardType1": 0, "RewardIcon1": 10010, "RewardType2": 0, "RewardIcon2": 10011, "RewardType3": 0, "RewardIcon3": 10012, "FixedRewardId": 1000, "RandomRewardId1": 0, "Weight1": 9000, "RandomRewardId2": 2007, "Weight2": 1000}, {"id": 9, "CopyType": 2, "CopyIcon": 0, "TeamName": "巨星联队", "TeamRemarks": "随机获得道具或球员奖励", "TeamOrder": 2, "TeamID": 91214, "Lv": 1, "Formation": 451401, "OffensiveID": 101, "DefenseID": 1101, "LimitNum": 2, "PurchaseType": 3, "PurchaseMaxNum": 10, "PurchaseParameters": 100, "ConsumeType": 2, "ConsumeValue": 500, "RewardType1": 0, "RewardIcon1": 10010, "RewardType2": 0, "RewardIcon2": 10011, "RewardType3": 0, "RewardIcon3": 10012, "FixedRewardId": 1000, "RandomRewardId1": 0, "Weight1": 9000, "RandomRewardId2": 2008, "Weight2": 1000}, {"id": 10, "CopyType": 2, "CopyIcon": 0, "TeamName": "传奇联队", "TeamRemarks": "随机获得道具或球员奖励", "TeamOrder": 3, "TeamID": 91215, "Lv": 1, "Formation": 532101, "OffensiveID": 101, "DefenseID": 1101, "LimitNum": 2, "PurchaseType": 3, "PurchaseMaxNum": 11, "PurchaseParameters": 100, "ConsumeType": 2, "ConsumeValue": 1000, "RewardType1": 0, "RewardIcon1": 10010, "RewardType2": 0, "RewardIcon2": 10011, "RewardType3": 0, "RewardIcon3": 10012, "FixedRewardId": 1000, "RandomRewardId1": 0, "Weight1": 9000, "RandomRewardId2": 2009, "Weight2": 1000}]