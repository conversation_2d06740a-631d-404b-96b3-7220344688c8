[{"id": 1, "UserGroup": 1, "GroupId": 1, "GuideType": 1, "FrontId": 0, "RearId": 2, "canIgnore": 0, "CanComplete": 0, "Condition": 0, "ConditionId": 0, "TypeSort": 0, "Pattern": 0, "PatType": 0, "TypeId": 0, "Text": "", "VideoId": "123", "Module": "", "Btn": "", "Gift": 0, "Num": 0}, {"id": 2, "UserGroup": 2, "GroupId": 1, "GuideType": 1, "FrontId": 1, "RearId": 3, "canIgnore": 0, "CanComplete": 0, "Condition": 0, "ConditionId": 0, "TypeSort": 0, "Pattern": 0, "PatType": 0, "TypeId": 0, "Text": "欢迎来到足球世界！对于足球，请问您是新手还是老手？", "VideoId": "", "Module": "", "Btn": "", "Gift": 0, "Num": 0}, {"id": 3, "UserGroup": 2, "GroupId": 1, "GuideType": 1, "FrontId": 2, "RearId": 4, "canIgnore": 0, "CanComplete": 0, "Condition": 0, "ConditionId": 0, "TypeSort": 0, "Pattern": 0, "PatType": 0, "TypeId": 0, "Text": "请选择一名球员作为俱乐部核心", "VideoId": "", "Module": "", "Btn": "", "Gift": 0, "Num": 0}, {"id": 4, "UserGroup": 2, "GroupId": 2, "GuideType": 1, "FrontId": 3, "RearId": 5, "canIgnore": 0, "CanComplete": 0, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "", "VideoId": "", "Module": "game.MainPageMediator", "Btn": "0.1,200,OPEN_GIVE_MONEY,0", "Gift": 0, "Num": 0}, {"id": 5, "UserGroup": 2, "GroupId": 2, "GuideType": 1, "FrontId": 4, "RearId": 6, "canIgnore": 0, "CanComplete": 0, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 3, "PatType": 0, "TypeId": 0, "Text": "", "VideoId": "", "Module": "game.GiveMoneyMediator", "Btn": "1,500,ui,imgPack", "Gift": 0, "Num": 0}, {"id": 6, "UserGroup": 1, "GroupId": 3, "GuideType": 1, "FrontId": 5, "RearId": 7, "canIgnore": 0, "CanComplete": 0, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "球探能够为我们寻找优秀球员，我们去招募更多优秀球员吧！", "VideoId": "", "Module": "game.MainPageMediator", "Btn": "1,4500,ui,scoutBtn", "Gift": 0, "Num": 0}, {"id": 7, "UserGroup": 1, "GroupId": 3, "GuideType": 1, "FrontId": 6, "RearId": 8, "canIgnore": 0, "CanComplete": 0, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "球探级别越高，能够探索到的球员资质更好，来试试超级球探！", "VideoId": "", "Module": "game.ScoutMediator", "Btn": "2,0,ui,tab<PERSON><PERSON><PERSON>,2", "Gift": 0, "Num": 0}, {"id": 8, "UserGroup": 1, "GroupId": 3, "GuideType": 1, "FrontId": 7, "RearId": 9, "canIgnore": 0, "CanComplete": 0, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "哇！体力值充沛！现在招募试一试运气吧！", "VideoId": "", "Module": "game.ScoutMediator", "Btn": "1,0,ui,dispatchBtn", "Gift": 0, "Num": 0}, {"id": 9, "UserGroup": 1, "GroupId": 3, "GuideType": 1, "FrontId": 8, "RearId": 10, "canIgnore": 0, "CanComplete": 2, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "运气真棒！俱乐部正需要人才，签下这名球员！", "VideoId": "", "Module": "game.ScoutMediator", "Btn": "3,1500,<PERSON><PERSON><PERSON><PERSON>,signBtn,0,rewardGroup", "Gift": 0, "Num": 0}, {"id": 10, "UserGroup": 1, "GroupId": 4, "GuideType": 1, "FrontId": 9, "RearId": 11, "canIgnore": 0, "CanComplete": 0, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "获得了更优秀的球员，上场球员的安排需要更合理", "VideoId": "", "Module": "game.MainUIMediator", "Btn": "2,0,ui,menuGroup,1", "Gift": 0, "Num": 0}, {"id": 11, "UserGroup": 1, "GroupId": 4, "GuideType": 1, "FrontId": 10, "RearId": 12, "canIgnore": 1, "CanComplete": 0, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "把更优秀的球员替换到合适的位置上", "VideoId": "", "Module": "game.FormationMediator", "Btn": "1,500,ui,moreBtn", "Gift": 0, "Num": 0}, {"id": 12, "UserGroup": 1, "GroupId": 4, "GuideType": 1, "FrontId": 11, "RearId": 13, "canIgnore": 0, "CanComplete": 1, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "球员只有在合适的位置，才能发挥出全部能力", "VideoId": "", "Module": "game.FormationMediator", "Btn": "1,0,ui,autoForm", "Gift": 0, "Num": 0}, {"id": 13, "UserGroup": 1, "GroupId": 4, "GuideType": 1, "FrontId": 12, "RearId": 14, "canIgnore": 1, "CanComplete": 0, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "有了个很好的开始，我们去参加一次巡回赛吧", "VideoId": "", "Module": "game.MainUIMediator", "Btn": "2,0,ui,menuGroup,0", "Gift": 0, "Num": 0}, {"id": 14, "UserGroup": 1, "GroupId": 5, "GuideType": 1, "FrontId": 13, "RearId": 15, "canIgnore": 0, "CanComplete": 0, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "在这里，可以参加巡回赛", "VideoId": "", "Module": "game.MainUIMediator", "Btn": "2,0,ui,menuGroup,4", "Gift": 0, "Num": 0}, {"id": 15, "UserGroup": 1, "GroupId": 5, "GuideType": 1, "FrontId": 14, "RearId": 16, "canIgnore": 0, "CanComplete": 0, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "参加巡回赛，可以获得丰富的奖励，对俱乐部发展很有帮助", "VideoId": "", "Module": "game.StoryMapMediator", "Btn": "1,0,ui,btn_challenge", "Gift": 0, "Num": 0}, {"id": 16, "UserGroup": 1, "GroupId": 5, "GuideType": 1, "FrontId": 15, "RearId": 17, "canIgnore": 0, "CanComplete": 1, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "强化进攻，一举击溃对方吧！", "VideoId": "", "Module": "game.BattleMediator", "Btn": "1,9000,ui,btn_qhjg", "Gift": 0, "Num": 0}, {"id": 17, "UserGroup": 1, "GroupId": 6, "GuideType": 1, "FrontId": 0, "RearId": 18, "canIgnore": 0, "CanComplete": 1, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "太棒了！我们拿下了第一场比赛，真是场精彩的表演！", "VideoId": "", "Module": "game.BattleEndMediator", "Btn": "1,200,ui,btn_back", "Gift": 0, "Num": 0}, {"id": 18, "UserGroup": 1, "GroupId": 6, "GuideType": 1, "FrontId": 17, "RearId": 0, "canIgnore": 0, "CanComplete": 1, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "相信我们的俱乐部一定可以创造辉煌！现在开始探索吧！", "VideoId": "", "Module": "game.MainUIMediator", "Btn": "2,500,ui,menuGroup,0", "Gift": 0, "Num": 0}, {"id": 100, "UserGroup": 2, "GroupId": 8, "GuideType": 1, "FrontId": 0, "RearId": 101, "canIgnore": 0, "CanComplete": 0, "Condition": 3, "ConditionId": 30170, "TypeSort": 0, "Pattern": 0, "PatType": 0, "TypeId": 0, "Text": "（点击到任务的，）", "VideoId": "", "Module": "", "Btn": "", "Gift": 20001, "Num": 10}, {"id": 101, "UserGroup": 2, "GroupId": 8, "GuideType": 1, "FrontId": 100, "RearId": 102, "canIgnore": 0, "CanComplete": 0, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "提升战术可以大幅提升球队的效率，现在我们来尝试提升一次战术吧！", "VideoId": "", "Module": "game.FormationMediator", "Btn": "1,0,ui,moreBtn", "Gift": 0, "Num": 0}, {"id": 102, "UserGroup": 2, "GroupId": 8, "GuideType": 1, "FrontId": 101, "RearId": 103, "canIgnore": 0, "CanComplete": 0, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "点击战术调整", "VideoId": "", "Module": "game.FormationMediator", "Btn": "1,0,ui,formationTacticsBtn", "Gift": 0, "Num": 0}, {"id": 103, "UserGroup": 2, "GroupId": 8, "GuideType": 1, "FrontId": 102, "RearId": 104, "canIgnore": 1, "CanComplete": 1, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "点击战术的升级", "VideoId": "", "Module": "game.FormationMediator", "Btn": "1,500,<PERSON><PERSON><PERSON><PERSON>,btnUp", "Gift": 0, "Num": 0}, {"id": 104, "UserGroup": 2, "GroupId": 8, "GuideType": 1, "FrontId": 103, "RearId": 0, "canIgnore": 1, "CanComplete": 1, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 3, "PatType": 0, "TypeId": 0, "Text": "", "VideoId": "", "Module": "game.FormationMediator", "Btn": "0.2,1000,ui,use<PERSON>oodsPanel,sureBtn", "Gift": 0, "Num": 0}, {"id": 150, "UserGroup": 2, "GroupId": 9, "GuideType": 1, "FrontId": 0, "RearId": 151, "canIgnore": 0, "CanComplete": 0, "Condition": 3, "ConditionId": 30169, "TypeSort": 0, "Pattern": 0, "PatType": 0, "TypeId": 0, "Text": "", "VideoId": "", "Module": "", "Btn": "", "Gift": 10500, "Num": 10}, {"id": 151, "UserGroup": 2, "GroupId": 9, "GuideType": 1, "FrontId": 150, "RearId": 152, "canIgnore": 0, "CanComplete": 0, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "提升阵容可以大幅提升球队的效率，现在我们来尝试提升一次阵容吧！", "VideoId": "", "Module": "game.FormationMediator", "Btn": "1,0,ui,moreBtn", "Gift": 0, "Num": 0}, {"id": 152, "UserGroup": 2, "GroupId": 9, "GuideType": 1, "FrontId": 151, "RearId": 153, "canIgnore": 0, "CanComplete": 0, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "点击阵型调整", "VideoId": "", "Module": "game.FormationMediator", "Btn": "1,0,ui,formationLayoutBtn", "Gift": 0, "Num": 0}, {"id": 153, "UserGroup": 2, "GroupId": 9, "GuideType": 1, "FrontId": 152, "RearId": 154, "canIgnore": 0, "CanComplete": 1, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "点击提升阵容的升级", "VideoId": "", "Module": "game.FormationMediator", "Btn": "1,0,ui,formationUpgradeBtn", "Gift": 0, "Num": 0}, {"id": 154, "UserGroup": 2, "GroupId": 9, "GuideType": 1, "FrontId": 153, "RearId": 0, "canIgnore": 1, "CanComplete": 1, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 3, "PatType": 0, "TypeId": 0, "Text": "", "VideoId": "", "Module": "game.FormationMediator", "Btn": "0.2,1000,ui,use<PERSON>oodsPanel,sureBtn", "Gift": 0, "Num": 0}, {"id": 200, "UserGroup": 2, "GroupId": 10, "GuideType": 1, "FrontId": 0, "RearId": 201, "canIgnore": 0, "CanComplete": 0, "Condition": 3, "ConditionId": 30108, "TypeSort": 0, "Pattern": 0, "PatType": 0, "TypeId": 0, "Text": "", "VideoId": "", "Module": "", "Btn": "", "Gift": 4, "Num": 1}, {"id": 201, "UserGroup": 2, "GroupId": 10, "GuideType": 1, "FrontId": 200, "RearId": 202, "canIgnore": 0, "CanComplete": 0, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "培养球员，可以提升球员各项能力的属性", "VideoId": "", "Module": "game.HeroCultivateMediator", "Btn": "4,500,ui,list<PERSON><PERSON>ball<PERSON>,0,<PERSON><PERSON><PERSON>,0", "Gift": 0, "Num": 0}, {"id": 202, "UserGroup": 2, "GroupId": 10, "GuideType": 1, "FrontId": 201, "RearId": 203, "canIgnore": 0, "CanComplete": 1, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "选择培养", "VideoId": "", "Module": "game.HeroCultivateMediator", "Btn": "1,0,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,btnPY", "Gift": 0, "Num": 0}, {"id": 203, "UserGroup": 2, "GroupId": 10, "GuideType": 1, "FrontId": 202, "RearId": 204, "canIgnore": 0, "CanComplete": 1, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "点击培养", "VideoId": "", "Module": "game.HeroCultivateMediator", "Btn": "2,200,ui,list<PERSON><PERSON><PERSON><PERSON><PERSON>,2", "Gift": 0, "Num": 0}, {"id": 204, "UserGroup": 2, "GroupId": 10, "GuideType": 1, "FrontId": 203, "RearId": 0, "canIgnore": 1, "CanComplete": 1, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "点使用训练卡", "VideoId": "", "Module": "game.HeroCultivateMediator", "Btn": "1,500,ui,btnFiveTime", "Gift": 0, "Num": 0}, {"id": 250, "UserGroup": 2, "GroupId": 11, "GuideType": 1, "FrontId": 0, "RearId": 251, "canIgnore": 0, "CanComplete": 0, "Condition": 3, "ConditionId": 30144, "TypeSort": 0, "Pattern": 0, "PatType": 0, "TypeId": 0, "Text": "", "VideoId": "", "Module": "", "Btn": "", "Gift": 1, "Num": 200000}, {"id": 251, "UserGroup": 2, "GroupId": 11, "GuideType": 1, "FrontId": 250, "RearId": 252, "canIgnore": 0, "CanComplete": 0, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "突破，可以提升球员培养的属性上限，大幅提升球员的能力!", "VideoId": "", "Module": "game.HeroCultivateMediator", "Btn": "4,500,ui,list<PERSON><PERSON>ball<PERSON>,0,<PERSON><PERSON><PERSON>,0", "Gift": 0, "Num": 0}, {"id": 252, "UserGroup": 2, "GroupId": 11, "GuideType": 1, "FrontId": 251, "RearId": 253, "canIgnore": 0, "CanComplete": 1, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "选择培养", "VideoId": "", "Module": "game.HeroCultivateMediator", "Btn": "1,0,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,btnPY", "Gift": 0, "Num": 0}, {"id": 253, "UserGroup": 2, "GroupId": 11, "GuideType": 1, "FrontId": 252, "RearId": 254, "canIgnore": 0, "CanComplete": 1, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "点击突破", "VideoId": "", "Module": "game.HeroCultivateMediator", "Btn": "2,200,ui,list<PERSON><PERSON><PERSON><PERSON><PERSON>,3", "Gift": 0, "Num": 0}, {"id": 254, "UserGroup": 2, "GroupId": 11, "GuideType": 1, "FrontId": 253, "RearId": 0, "canIgnore": 1, "CanComplete": 1, "Condition": 0, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "突破一次", "VideoId": "", "Module": "game.HeroCultivateMediator", "Btn": "1,500,ui,btnPotComfirm", "Gift": 0, "Num": 0}, {"id": 300, "UserGroup": 2, "GroupId": 12, "GuideType": 1, "FrontId": 0, "RearId": 301, "canIgnore": 0, "CanComplete": 0, "Condition": 3, "ConditionId": 21264, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "", "VideoId": "", "Module": "", "Btn": "", "Gift": 0, "Num": 0}, {"id": 301, "UserGroup": 2, "GroupId": 12, "GuideType": 1, "FrontId": 300, "RearId": 302, "canIgnore": 0, "CanComplete": 1, "Condition": 3, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "点击头像", "VideoId": "", "Module": "game.MainUIMediator", "Btn": "1,0,ui,teamBtn", "Gift": 0, "Num": 0}, {"id": 302, "UserGroup": 2, "GroupId": 12, "GuideType": 1, "FrontId": 301, "RearId": 0, "canIgnore": 0, "CanComplete": 1, "Condition": 3, "ConditionId": 0, "TypeSort": 2, "Pattern": 2, "PatType": 0, "TypeId": 0, "Text": "游戏入口", "VideoId": "", "Module": "game.PlayerInfoMediator", "Btn": "1,500,ui,btn<PERSON><PERSON><PERSON>", "Gift": 0, "Num": 0}]