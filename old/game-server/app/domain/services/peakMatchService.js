/**
 * Idea and Persist
 * Created by <PERSON> on 2020/4/13.
 */
var logger = require('pomelo-logger').getLogger('pomelo', __filename);
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var Code = require('../../../../shared/code');
var utils = require('../../util/utils');
var TimeUtils = require('../../util/timeUtils');
var calcUtils = require('../../util/calc');

var async = require('async');
var dataApi = require('../../util/dataApi');
var Constant = require("../../../../shared/constant");
var commonEnum = require('../../../../shared/enum');
var debugConfig = require('../../../config/debugConfig');
var clusterConfig = require('../../../config/cluster');

module.exports.create = function(app, dbclient)
{
    return new peakMatchService(app, dbclient);
};

let peakMatchService = function(app, dbclient, cb)
{
    EventEmitter.call(this);
    this.app = app;

    this.honorSeasonId = 0;                      //荣誉墙赛季
    this.match32Num = 32;
    this.matchDao = require("../../dao/matchDao").create(dbclient);

    //Db init 初始化放在lifecycle文件里面

    //内存数据
    this.preBattleInfoMap = new Map();		//比赛个人比赛信息表 uid => {gid, name, faceUrl, pos, beliefId, backer:[{uid, num}]}

    //对战信息表
    this.dqCup32BattleList = [];			//32强对战信息
    this.dqCup16BattleList = [];			//16强对战信息
    this.dqCup8BattleList = [];				//8强对战信息
    this.dqCup4BattleList = [];				//4强对战信息
    this.dqCup2BattleList = [];				//2强对战信息

    this.coChairman = {};                   //联盟副主席
    this.chairman = {};		    		    //联盟主席    比赛第一名   {uid, gid, name, faceUrl, beliefId, pos}

    //战斗完成状态 (因为预选赛会打很多轮所以要依靠这个完成状态去判断, 不太需要)
    //this.isPreBattleFinish = false;

    this.noneUid = "noneUid";				//轮空Uid
    this.noneRoomUid = "noEnemy";			//轮空战斗

    //当前的比赛阶段 -- 根据数据库上一轮进行的阶段分析出当前进行的阶段
    this.matchPeriod = commonEnum.PEAK_MATCH_SCHEDULE.MATCH_INIT;	//该阶段完成后设置
    this.matchChampionRecord = [];		//每一期的懂球杯的冠军记录 [{time, chairman = {gid, name, faceUrl, pos, beliefId}, coChairman = {}}]
    this.finishPeriod = commonEnum.PEAK_MATCH_SCHEDULE.MATCH_INIT;

    //记录以通知过的玩家
    this.playerTips = new Map();
};

util.inherits(peakMatchService, EventEmitter);

peakMatchService.prototype.initByDB = function(cb)
{
    //获取数据库数据，初始化
    let self = this;
    this.matchDao.findPkMatchInfo(function (err, data) {
        logger.debug("findPkMatchInfo initByDB data:", data);
        if(!!data) {
            self.preBattleInfoMap = self.preBattleInfoArrayToMap(data.matchInfo.preBattleInfoMap);
            self.dqCup32BattleList = utils.cloneArray(data.matchInfo.dqCup32BattleList);
            self.dqCup16BattleList = utils.cloneArray(data.matchInfo.dqCup16BattleList);
            self.dqCup8BattleList = utils.cloneArray(data.matchInfo.dqCup8BattleList);
            self.dqCup4BattleList = utils.cloneArray(data.matchInfo.dqCup4BattleList);
            self.dqCup2BattleList = utils.cloneArray(data.matchInfo.dqCup2BattleList);
            self.coChairman = utils.deepCopy(data.matchInfo.coChairman);
            self.chairman = utils.deepCopy(data.matchInfo.chairman);
            if(data.matchInfo.matchPeriod > data.matchInfo.finishPeriod)
            {
                data.matchInfo.matchPeriod = data.matchInfo.finishPeriod;
            }
            self.matchPeriod = data.matchInfo.matchPeriod;
            self.matchChampionRecord = utils.cloneArray(data.matchInfo.matchChampionRecord);
            self.finishPeriod = data.matchInfo.finishPeriod || data.matchInfo.matchPeriod;
            self.honorSeasonId = data.matchInfo.honorSeasonId || 0;
        }
        //logger.debug("initByDB self.matchChampionRecord: ", self.matchChampionRecord);
        cb(null);
    });
};

peakMatchService.prototype.updateDB = function (cb) {
    if(cb && this.preBattleInfoMap.size === 0) {
        return cb(null);
    }
    let data = {};
    data.preBattleInfoMap = this.preBattleInfoMapToArray();
    data.dqCup32BattleList = utils.cloneArray(this.dqCup32BattleList);
    data.dqCup16BattleList = utils.cloneArray(this.dqCup16BattleList);
    data.dqCup8BattleList = utils.cloneArray(this.dqCup8BattleList);
    data.dqCup4BattleList = utils.cloneArray(this.dqCup4BattleList);
    data.dqCup2BattleList = utils.cloneArray(this.dqCup2BattleList);
    data.coChairman = utils.deepCopy(this.coChairman);
    data.chairman = utils.deepCopy(this.chairman);
    data.matchPeriod = this.matchPeriod;
    data.matchChampionRecord = utils.cloneArray(this.matchChampionRecord);
    data.finishPeriod = this.finishPeriod;
    data.honorSeasonId = this.honorSeasonId;
    /*
    if(debugConfig.isForTest) {
        data.preBattleRoundCursor = 0;
        data.preBattleList = [];
        data.dqCup32BattleList = [];
        data.dqCup16BattleList = [];
    }
    */
    this.matchDao.updatePkMatchInfoToDB(data, function (err) {
        if(cb) { cb(null); }
    });
};
//RPC处理
//界面信息
peakMatchService.prototype.getPeakMatchInfo = function(playerId, cb) {
    let nowTimePeriod = this.checkMatchPeriod();
    let nextPeriod = this.calcNextPeriod(nowTimePeriod);
    logger.debug("getDqMatchInfo nowTimePeriod, nextPeriod", nowTimePeriod, nextPeriod);
    let fightTimeHM = this.getSchduleConfigHourAndMinute(commonEnum.PEAK_MATCH_SCHEDULE.PUBLICATION_LIST);
    let endTimeHM = this.getSchduleConfigHourAndMinute(commonEnum.PEAK_MATCH_SCHEDULE.BET_ON);
    let nowDate = new Date();
    let openTimeDate = "";
    let fightTimeDate = "";
    let openTime = 0;
    let fightTime = 0;
    let timeArr = this.getWeekTime(-3).split("-");//本周三
    if(nowTimePeriod === commonEnum.PEAK_MATCH_SCHEDULE.MATCH_INIT)//获取下周开启时间
    {
        openTimeDate = TimeUtils.newDateByParam(timeArr[0], timeArr[1], timeArr[2], endTimeHM.hour, endTimeHM.min, 0);
        fightTimeDate = TimeUtils.newDateByParam(timeArr[0], timeArr[1], timeArr[2], fightTimeHM.hour, fightTimeHM.min, 0);
        openTime = openTimeDate.getTime() + 7 * 24 * 60 * 60 * 1000;
        fightTime = fightTimeDate.getTime() + 7 * 24 * 60 * 60 * 1000;
    }
    else//本周开启时间
    {
        openTimeDate = TimeUtils.newDateByParam(timeArr[0], timeArr[1], timeArr[2], endTimeHM.hour, endTimeHM.min, 0);
        fightTimeDate = TimeUtils.newDateByParam(timeArr[0], timeArr[1], timeArr[2], fightTimeHM.hour, fightTimeHM.min, 0);
        openTime = openTimeDate.getTime();
        fightTime = fightTimeDate.getTime();
        // if(this.matchPeriod === commonEnum.PEAK_MATCH_PERIOD.BET_ON)
        //     fightTime += 24 * 60 * 60 * 1000;
    }
    // logger.error("!!!!!!!!!!!!!!!!!!!!!!!!!", openTime, fightTime);
    let initial = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.PEAK_INITIAL].Param;   //初始值
    let teamNum = 0; //下注队伍数量
    //所有队伍下注总额
    let total = 0;
    //获取参赛者信息
    let list = [];
    for(let [k, v] of this.preBattleInfoMap)
    {
        teamNum += 1;
        list.push({uid: k, faceUrl: v.faceUrl || "test", name: v.name || "找不到名字", beliefId: v.beliefId});
        for(let i in v.backer)
        {
            total += v.backer[i].num;
        }
    }
    total += teamNum * initial;
    logger.debug("openTimeDate: ", openTimeDate);

    let winList = [];
    let chairman = {};
    let coChairman = {};
    if(!!this.chairman && !!this.chairman.uid)
    {
        winList.push({uid: this.chairman.uid, gid: this.chairman.gid});
        chairman.uid = this.chairman.uid;
        chairman.faceUrl = this.chairman.faceUrl;
        chairman.name = this.chairman.name;
        chairman.beliefId = this.chairman.beliefId;
    }
    if(!!this.coChairman && !!this.coChairman.uid)
    {
        winList.push({uid: this.coChairman.uid, gid: this.coChairman.gid});
        coChairman.uid = this.coChairman.uid;
        coChairman.faceUrl = this.coChairman.faceUrl;
        coChairman.name = this.coChairman.name;
        coChairman.beliefId = this.coChairman.beliefId;
    }
    let gid2Index = new Map();
    let index = 0;
    let playerList = [];
    for(let i=0,lens=winList.length;i<lens;i++) {
        if(gid2Index.has(winList[i].gid)) {
            playerList[gid2Index.get(winList[i].gid)].push(winList[i]);
        }else {
            playerList[index] = [];
            playerList[index].push(winList[i]);
            gid2Index.set(winList[i].gid, index);
            index++;
        }
    }
    let self = this;
    if(playerList.length > 0)
    {
        async.eachSeries(playerList, function (sameGameList, callback) {
                let session = {frontendId: self.app.getServerId(), toServerId: sameGameList[0].gid};
                let reMsg = {uidList: sameGameList};
                self.app.rpc.game.entryRemote.getActualStrengthList(session, reMsg, function (teamFormationList) {
                    for(let i in teamFormationList)
                    {
                        if(teamFormationList[i].uid === chairman.uid)
                        {
                            chairman.ActualStrength = teamFormationList[i].ActualStrength;
                        }
                        else if(teamFormationList[i].uid === coChairman.uid)
                        {
                            coChairman.ActualStrength = teamFormationList[i].ActualStrength;
                        }
                    }
                    callback(null);
                });},
            function(err) {
                let result = { matchPeriod: nowTimePeriod, openTime: openTime, participant: list,
                    match32List: self.getOutBattleListForClient(commonEnum.PEAK_MATCH_PERIOD.MATCH_32),
                    match16List: self.getOutBattleListForClient(commonEnum.PEAK_MATCH_PERIOD.MATCH_16),
                    match8List: self.getOutBattleListForClient(commonEnum.PEAK_MATCH_PERIOD.MATCH_8),
                    match4List: self.getOutBattleListForClient(commonEnum.PEAK_MATCH_PERIOD.MATCH_4),
                    match2List: self.getOutBattleListForClient(commonEnum.PEAK_MATCH_PERIOD.MATCH_2),
                    chairman: chairman, coChairman: coChairman, total: total, fightTime: fightTime};

                logger.debug("getDqMatchInfo result: ", result);
                cb(Code.OK, result);
            });
    }
    else
    {
        let result = { matchPeriod: nowTimePeriod, openTime: openTime, participant: list,
            match32List: self.getOutBattleListForClient(commonEnum.PEAK_MATCH_PERIOD.MATCH_32),
            match16List: self.getOutBattleListForClient(commonEnum.PEAK_MATCH_PERIOD.MATCH_16),
            match8List: self.getOutBattleListForClient(commonEnum.PEAK_MATCH_PERIOD.MATCH_8),
            match4List: self.getOutBattleListForClient(commonEnum.PEAK_MATCH_PERIOD.MATCH_4),
            match2List: self.getOutBattleListForClient(commonEnum.PEAK_MATCH_PERIOD.MATCH_2),
            chairman: chairman, coChairman: coChairman, total: total, fightTime: fightTime};
        logger.debug("getDqMatchInfo result: ", result);
        cb(Code.OK, result);
    }
};
//获取下阶段时间
peakMatchService.prototype.getNextTime = function() {
    let matchPeriod = this.matchPeriod + 1;
    if(matchPeriod === commonEnum.PEAK_MATCH_PERIOD.MATCH_INIT)
        matchPeriod = commonEnum.PEAK_MATCH_PERIOD.BET_ON;
    //下阶段时间
    let nextTimeHM = this.getSchduleConfigHourAndMinute(matchPeriod);
    let nowDate = new Date();
    let nextTimeDate = "";
    let nextTime = 0;
    let timeArr = this.getWeekTime(-2).split("-");//本周三
    if(this.matchPeriod + 1 === commonEnum.PEAK_MATCH_SCHEDULE.MATCH_INIT)//获取下周开启时间
    {
        nextTimeDate = TimeUtils.newDateByParam(timeArr[0], timeArr[1], timeArr[2], nextTimeHM.hour, nextTimeHM.min, 0);
        nextTime = nextTimeDate.getTime() + 7 * 24 * 60 * 60 * 1000;
    }
    else//本周开启时间
    {
        nextTimeDate = TimeUtils.newDateByParam(timeArr[0], timeArr[1], timeArr[2], nextTimeHM.hour, nextTimeHM.min, 0);
        nextTime = nextTimeDate.getTime();
        if(this.matchPeriod + 1 > commonEnum.PEAK_MATCH_PERIOD.BET_ON)
            nextTime += 24 * 60 * 60 * 1000;
    }
    return nextTime;
};
peakMatchService.prototype.getWeekTime = function(n) {
    let now = new Date();
    let year = now.getFullYear();
    let month = now.getMonth() + 1;
    let day = now.getDay(); //返回星期几的某一天;
    n = day === 0 ? n + 6 : n + (day - 1);
    now.setDate(now.getDate() - n);
    let date = now.getDate();
    return year + "-" + (month < 10 ? ('0' + month) : month) + "-" + (date < 10 ? ('0' + date) : date);
};
//获取某一阶段的比赛数据
peakMatchService.prototype.getOutBattleListForClient = function(period) {
    let listForClient = [];
    let battleList = this.getBattleListByPyPeriod(period);
    logger.debug("getOutBattleListForClient battleList, period:", battleList, period);
    for(let i=0, lens=battleList.length; i<lens; i++) {
        let oneInfo = {};
        if(battleList[i].home !== this.noneUid) {
            oneInfo.homeUid = battleList[i].home;
            let homeInfo = this.preBattleInfoMap.get(oneInfo.homeUid);
            if(!!homeInfo) {
                oneInfo.homeFaceUrl = homeInfo.faceUrl || "";
                if(!homeInfo.faceUrl) {
                    logger.error("wrong faceUrl, homeInfo:", homeInfo);
                }
                oneInfo.homeName = homeInfo.name || "找不到名字";
                oneInfo.homeScore = battleList[i].homeScore;
                oneInfo.winnerUid = battleList[i].winnerUid;
            }
        }
        else
        {
            oneInfo.homeUid = this.noneUid;
            oneInfo.homeFaceUrl = "";
            oneInfo.homeName = "轮空";
            oneInfo.homeScore = 0;
            oneInfo.winnerUid = battleList[i].winnerUid;
        }
        if(battleList[i].away !== this.noneUid) {
            oneInfo.awayUid = battleList[i].away;
            let awayInfo = this.preBattleInfoMap.get(oneInfo.awayUid);
            if(!!awayInfo) {
                if(!awayInfo.faceUrl) {
                    logger.error("getOutBattleListForClient awayInfo error", awayInfo, battleList[i]);
                }
                oneInfo.awayFaceUrl = awayInfo.faceUrl || "";
                if(!awayInfo.faceUrl) {
                    logger.debug("wrong faceUrl, awayInfo:", awayInfo);
                }
                oneInfo.awayName = awayInfo.name || "找不到名字";
                oneInfo.awayScore = battleList[i].awayScore;
                oneInfo.winnerUid = battleList[i].winnerUid;
            }
        }
        else
        {
            oneInfo.awayUid = this.noneUid;
            oneInfo.awayFaceUrl = "";
            oneInfo.awayName = "轮空";
            oneInfo.awayScore = 0;
            oneInfo.winnerUid = battleList[i].winnerUid;
        }
        if(battleList[i].roomUid !== this.noneRoomUid) {
            oneInfo.roomUid = battleList[i].roomUid;
            oneInfo.time = battleList[i].time;
        }
        listForClient.push(oneInfo);
    }
    return listForClient;
};

peakMatchService.prototype.getPeakMatchTopList = function(playerId, cb) {
    //统计历届冠军数据
    logger.debug("getDqMatchTopList this.matchChampionRecord: ", this.matchChampionRecord);
    let topList = [];
    let playerList = [];
    for(let i=0,lens=this.matchChampionRecord.length;i<lens;i++) {
        let data = {};
        // data.time = this.matchChampionRecord[i].time;
        // data.chairman = this.matchChampionRecord[i].chairman;
        // data.coChairman = this.matchChampionRecord[i].coChairman;
        // topList.push(utils.deepCopy(data));
        topList.push({time: this.matchChampionRecord[i].time, chairman: {uid: this.matchChampionRecord[i].chairman.uid, faceUrl: this.matchChampionRecord[i].chairman.faceUrl
                , name: this.matchChampionRecord[i].chairman.name, beliefId: this.matchChampionRecord[i].chairman.beliefId, ActualStrength: 0}});
        playerList.push({uid: this.matchChampionRecord[i].chairman.uid, gid: this.matchChampionRecord[i].chairman.gid});
    }
    //排序
    topList.sort(__rating_compare_func);
    //获取历届冠军实力
    let gid2Index = new Map();
    let index = 0;
    let winList = [];
    for(let i=0,lens=playerList.length;i<lens;i++) {
        if(gid2Index.has(playerList[i].gid)) {
            winList[gid2Index.get(playerList[i].gid)].push(playerList[i]);
        }else {
            winList[index] = [];
            winList[index].push(playerList[i]);
            gid2Index.set(playerList[i].gid, index);
            index++;
        }
    }
    let self = this;
    async.eachSeries(winList, function (sameGameList, callback) {
            let session = {frontendId: self.app.getServerId(), toServerId: sameGameList[0].gid};
            let reMsg = {uidList: sameGameList};
            self.app.rpc.game.entryRemote.getActualStrengthList(session, reMsg, function (teamFormationList) {
                for(let i in topList)
                {
                    for(let k in teamFormationList)
                    {
                        if(teamFormationList[k].uid === topList[i].chairman.uid)
                        {
                            topList[i].chairman.ActualStrength = teamFormationList[k].ActualStrength;
                        }
                    }
                }
                callback(null);
            });},
        function(err) {
            cb(Code.OK, topList);
        });
};

function __rating_compare_func(rankObj1, rankObj2) {
    if(rankObj1.time < rankObj2.time) {
        return 1;
    }else if(rankObj1.time > rankObj2.time) {
        return -1;
    }
    return 0;
}
//押注界面     [{teamUid, beliefId, isMax, playerBetOn}] 队伍uid 信仰id 是否达下注上限 0 没 1 已达上限 玩家下注金额
peakMatchService.prototype.getBetOnForClient = function(player, gold, beliefNum, cb){
    let teamsInfo = [];//{teamUid, isMax, playerBetOn}
    //获取参赛队伍信息
    for(let [k, v] of this.preBattleInfoMap)
    {
        let num = 0;
        for(let i in v.backer)
        {
            if(v.backer[i].uid === player)
            {
                num = v.backer[i].num;//自己在该队伍的下注额度
            }
        }
        //显示的下注额度不算初始值
        teamsInfo.push({uid: k, name: v.name || "找不到名字", beliefId: v.beliefId, beliefTotal: this.getBetOnFollowing(k), MAX: this.getBetOnMax(gold, beliefNum), playerBetOn: num});
    }
    cb(Code.OK, teamsInfo)
};
peakMatchService.prototype.preBattleInfoMapToArray = function() {
    // (playerId, {win: 0, lose: 0, draw: 0, gid: gid, roomUidList: [], faceUrl: faceUrl, name: name});
    let arr = [];
    for(let [k, v] of this.preBattleInfoMap) {
        let info = {};
        info.uid = k;
        info.beliefId = v.beliefId;
        info.gid = v.gid;
        info.win = v.win;
        // info.lose = v.lose;
        info.draw = v.draw;
        info.backer = utils.cloneArray(v.backer);
        // info.roomUidList = utils.cloneArray(v.roomUidList);
        info.faceUrl = v.faceUrl;
        info.name = v.name;
        info.level = v.level;
        info.pos = v.pos;
        info.rank = v.rank;
        arr.push(info);
    }
    return arr;
};

peakMatchService.prototype.preBattleInfoArrayToMap = function(arr) {
    let map = new Map();
    for(let i=0,lens=arr.length; i<lens; i++) {
        let info = {};
        info.beliefId = arr[i].beliefId;
        info.gid = arr[i].gid;
        info.win = arr[i].win;
        // info.lose = arr[i].lose;
        info.draw = arr[i].draw;
        info.backer = utils.cloneArray(arr[i].backer);
        // info.roomUidList = utils.cloneArray(arr[i].roomUidList);
        info.faceUrl = arr[i].faceUrl;
        info.name = arr[i].name;
        info.level = arr[i].level;
        info.pos = arr[i].pos;
        info.rank = arr[i].rank;
        map.set(arr[i].uid, info);
    }
    logger.debug("preBattleInfoArrayToMap arr: ", arr.length);
    return map;
};

peakMatchService.prototype.loopProcess = function() {
    //计算出当前的时间所属的阶段
    // logger.debug("loopProcess in ...");
    let nowTimePeriod = this.checkMatchPeriod();
    //根据已进行的状态和当前状态判定进行什么逻辑处理, 下一次状态
    let nextPeriod = this.calcNextPeriod(nowTimePeriod);
    this.dispatchProcess(nextPeriod);
    //存储数据
    this.updateDB();
};

peakMatchService.prototype.getSchduleConfigHourAndMinute = function (configId) {
    let timeArr = dataApi.allData.data["UltimateMatchTime"][configId].Time.split(":");
    return {hour: parseInt(timeArr[0], 10), min: parseInt(timeArr[1], 10)};
};

peakMatchService.prototype.checkMatchPeriod = function () {
    //检查状态
    let date = new Date();
    logger.debug("pkMatch checkMatchPeriod date: ", date);

    let period = commonEnum.PEAK_MATCH_SCHEDULE.MATCH_INIT;
    //0. 休赛期(周五晚上比赛结束到周一下一轮比赛开始)
    let betOnStartTime = this.getSchduleConfigHourAndMinute(commonEnum.PEAK_MATCH_SCHEDULE.BET_ON);
    let publicStartTime = this.getSchduleConfigHourAndMinute(commonEnum.PEAK_MATCH_SCHEDULE.PUBLICATION_LIST);
    let match32StartTime = this.getSchduleConfigHourAndMinute(commonEnum.PEAK_MATCH_SCHEDULE.MATCH_32);
    let match16StartTime = this.getSchduleConfigHourAndMinute(commonEnum.PEAK_MATCH_SCHEDULE.MATCH_16);
    let match8StartTime = this.getSchduleConfigHourAndMinute(commonEnum.PEAK_MATCH_SCHEDULE.MATCH_8);
    let match4StartTime = this.getSchduleConfigHourAndMinute(commonEnum.PEAK_MATCH_SCHEDULE.MATCH_4);
    let match2StartTime = this.getSchduleConfigHourAndMinute(commonEnum.PEAK_MATCH_SCHEDULE.MATCH_2);
    let sendRewardStartTime = this.getSchduleConfigHourAndMinute(commonEnum.PEAK_MATCH_SCHEDULE.SEND_REWARD);


    if(date.getDay()===4 && (date.getHours() > betOnStartTime.hour || (date.getHours()===betOnStartTime.hour && date.getMinutes()>=betOnStartTime.min)) &&
        (date.getHours() < publicStartTime.hour || (date.getHours()===publicStartTime.hour && date.getMinutes()<=publicStartTime.min))) {
        //押注阶段
        period = commonEnum.PEAK_MATCH_PERIOD.BET_ON;
    }else if(date.getDay()===4 && this.timeInPeriod(date, publicStartTime, match32StartTime)) {
        //公布参赛名单
        period = commonEnum.PEAK_MATCH_PERIOD.PUBLICATION_LIST;
    }else if(date.getDay()===4 && this.timeInPeriod(date, match32StartTime, match16StartTime)) {
        //32强比赛期
        period = commonEnum.PEAK_MATCH_PERIOD.MATCH_32;
    }else if(date.getDay()===4 && this.timeInPeriod(date, match16StartTime, match8StartTime)) {
        //16强比赛期
        period = commonEnum.PEAK_MATCH_PERIOD.MATCH_16;
    }else if(date.getDay()===4 && this.timeInPeriod(date, match8StartTime, match4StartTime)) {
        //8强比赛期
        period = commonEnum.PEAK_MATCH_PERIOD.MATCH_8;
    }else if(date.getDay()===4 && this.timeInPeriod(date, match4StartTime, match2StartTime)) {
        //4强比赛期
        period = commonEnum.PEAK_MATCH_PERIOD.MATCH_4;
    }else if(date.getDay()===4 && this.timeInPeriod(date, match2StartTime, sendRewardStartTime)) {
        //2强比赛期
        period = commonEnum.PEAK_MATCH_PERIOD.MATCH_2;
    }else if(date.getDay()===4 && (date.getHours()>sendRewardStartTime.hour || (date.getHours()===sendRewardStartTime.hour && date.getMinutes()>=sendRewardStartTime.min))) {
        //9. 奖励发放期
        period = commonEnum.PEAK_MATCH_PERIOD.SEND_REWARD;
        //状态循环
        if(this.finishPeriod === commonEnum.PEAK_MATCH_PERIOD.SEND_REWARD ||
            this.matchPeriod === commonEnum.PEAK_MATCH_PERIOD.MATCH_INIT) {
            period = commonEnum.PEAK_MATCH_PERIOD.MATCH_INIT;
            this.matchPeriod = commonEnum.PEAK_MATCH_PERIOD.MATCH_INIT;
            this.finishPeriod = commonEnum.PEAK_MATCH_PERIOD.MATCH_INIT;
        }
    }else {
        //比赛初始状态
        period = commonEnum.PEAK_MATCH_PERIOD.MATCH_INIT;
        // this.matchPeriod = commonEnum.PEAK_MATCH_PERIOD.MATCH_INIT;
        // this.finishPeriod = commonEnum.PEAK_MATCH_PERIOD.MATCH_INIT;
    }
    logger.debug("pkMatch checkMatchPeriod period", period);
    return period;
};

peakMatchService.prototype.dispatchProcess = function (nextPeriod) {
    logger.debug("dispatchProcess nextPeriod: ", nextPeriod);
    switch (nextPeriod) {
        case commonEnum.PEAK_MATCH_PERIOD.BET_ON://押注阶段
            this.matchPublicAtionList();//获取参赛者
            // this.matchPeriod = commonEnum.PEAK_MATCH_PERIOD.BET_ON;
            // this.finishPeriod = commonEnum.PEAK_MATCH_PERIOD.BET_ON;
            break;
        case commonEnum.PEAK_MATCH_PERIOD.PUBLICATION_LIST://公布参赛名单
            this.ReleasedAgainst();//设置32强对战列表
            break;
        case commonEnum.PEAK_MATCH_PERIOD.MATCH_32://16强
        case commonEnum.PEAK_MATCH_PERIOD.MATCH_16://8强
        case commonEnum.PEAK_MATCH_PERIOD.MATCH_8://4强
        case commonEnum.PEAK_MATCH_PERIOD.MATCH_4://半决赛
        case commonEnum.PEAK_MATCH_PERIOD.MATCH_2://决赛
            this.outBattleProcess();
            break;
        case commonEnum.PEAK_MATCH_PERIOD.SEND_REWARD://发奖阶段
            this.sendReward();
            break;
        default:
            logger.error("peakMatchService dispatchProcess error nextPeriod:", nextPeriod);
            break;
    }
};

peakMatchService.prototype.timeInPeriod = function(nowDate, startTimeObj, endTimeObj) {
    //是否在某个时间区间
    let result = false;
    logger.debug("timeInPeriod: ", nowDate.getHours(), nowDate.getMinutes(), startTimeObj, endTimeObj);
    if((nowDate.getHours()>startTimeObj.hour || (nowDate.getHours()===startTimeObj.hour && nowDate.getMinutes()>=startTimeObj.min))
        && (nowDate.getHours()<endTimeObj.hour || (nowDate.getHours()===endTimeObj.hour && nowDate.getMinutes()<endTimeObj.min))) {
        result = true;
    }
    return result;
};

//根据已进行的状态和当前状态计算下一次阶段
peakMatchService.prototype.calcNextPeriod = function(nowPeriod) {
    let nextPeriod = nowPeriod;
    logger.error("pkMatch calcNextPeriod nowPeriod, matchPeriod: ", nowPeriod, this.matchPeriod);
    if(this.finishPeriod === this.matchPeriod && nowPeriod === commonEnum.PEAK_MATCH_SCHEDULE.MATCH_INIT && (this.finishPeriod === commonEnum.PEAK_MATCH_SCHEDULE.SEND_REWARD || this.finishPeriod === commonEnum.PEAK_MATCH_SCHEDULE.MATCH_INIT)) {
        this.matchPeriod = commonEnum.PEAK_MATCH_SCHEDULE.MATCH_INIT;
        this.finishPeriod = this.matchPeriod;
    }
    if(this.matchPeriod !== nowPeriod) {
        if(this.finishPeriod === this.matchPeriod) {
            nextPeriod = this.matchPeriod+1;
        }
        else {
            nextPeriod = this.matchPeriod;
        }
    }
    return nextPeriod;
};

//公布参赛名单
peakMatchService.prototype.matchPublicAtionList = function () {
    //处于公布参赛名单阶段
    if(this.matchPeriod >= commonEnum.PEAK_MATCH_PERIOD.BET_ON) {
        logger.error("matchPublicAtionList finish. wait time over. period:", this.matchPeriod);
        return ;
    }
    //获取所有信仰的董事长和副董事长信息
    let map = new Map();
    let leftPlayerList = [];
    let playerInfoList = [];
    let self = this;
    self.emptyPkMatchData();//清空数据
    self.matchPeriod = commonEnum.PEAK_MATCH_PERIOD.BET_ON;
    let session = {frontendId: self.app.getServerId()};
    let msg = {};
    async.waterfall([
        function (callback) {
            self.app.rpc.datanode.dataNodeRemote.getAllBeliefLeader(session, msg, function (allLeader) {
                for(let i in allLeader)
                {
                    let date = {};
                    date.beliefId = allLeader[i].beliefId;
                    date.gid = allLeader[i].gid;
                    date.name = "";//allLeader[i].name;
                    date.faceUrl = "";//allLeader[i].faceUrl;
                    date.level = 0;
                    date.pos = allLeader[i].pos;
                    date.backer = [];//押注列表 {uid, gid, num}
                    // date.roomUidList = [];
                    date.win = 0;
                    date.draw = 0;
                    date.rank = 0;
                    map.set(allLeader[i].uid, utils.deepCopy(date));
                    leftPlayerList.push({uid:allLeader[i].uid, gid:allLeader[i].gid});
                }
                callback(null);
            });
        },
        function (callback) {
            let gidList = [];
            let tmpMap = new Map();
            // logger.error("获取玩家名，等级，头像", leftPlayerList);
            for(let i in leftPlayerList)
            {
                if(tmpMap.has(leftPlayerList[i].gid))
                {
                    tmpMap.get(leftPlayerList[i].gid).push({uid: leftPlayerList[i].uid, gid: leftPlayerList[i].gid});
                }
                else
                {
                    let list = [];
                    list.push({uid: leftPlayerList[i].uid, gid: leftPlayerList[i].gid});
                    tmpMap.set(leftPlayerList[i].gid, utils.cloneArray(list));
                }
            }
            for(let [k, v] of tmpMap)
            {
                gidList.push(v);
            }
            // logger.error("************", gidList.length, gidList);
            async.eachSeries(gidList, function (uidList, cb) {
                let session2 = {frontendId: self.app.getServerId(), toServerId: uidList[0].gid};
                let reMsg = {uidList: uidList};
                self.app.rpc.game.entryRemote.getPkMatchPlayerListSimpleDB(session2, reMsg, function (result) {
                    for(let i in result)
                    {
                        playerInfoList.push(result[i]);
                    }
                    cb(null);
                });
            },function (err) {
                // logger.error("11111111111111111111", playerInfoList);
                for(let i in playerInfoList)
                {
                    let tmp = map.get(playerInfoList[i].uid);
                    tmp.name = playerInfoList[i].Name || "找不到名字";
                    tmp.faceUrl = playerInfoList[i].FaceUrl || "test";
                    tmp.level = playerInfoList[i].level || 1;
                }
                // logger.error("获取玩家名，等级，头像完成", playerInfoList);
                callback(null);
            });
        },
        function (callback) {
            // logger.error("发邮件通知参赛者", map.size);
            self.preBattleInfoMap = map;
            //发邮件通知参赛者
            let playerList = [];
            for(let [k, v] of map)
            {
                playerList.push({uid: k, gid: v.gid});
            }
            self.sendInfoEmail(playerList, commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_OPEN, function () {
                // logger.error("发邮件通知参赛者完成");
                callback(null);
            });
        },
        function (callback) {
            //更新赛季
            self.honorSeasonId += 1;
            //更新参赛者参加次数
            self.preBattleInfoMap = map;
            let playerList = [];
            for(let [k, v] of map)
            {
                playerList.push({uid: k, gid: v.gid});
            }
            self.updataHonorData(playerList, commonEnum.HONOR_DISPOSE_TYPE.JOIN_NUM, function () {
                callback(null);
            });
        },
        function (callback) {
            // logger.error("在线玩家弹窗通知");
            let gameServers = self.app.getServersByType("game");
            async.eachSeries(gameServers, function (obj, cb) {
                let session3 = {frontendId: self.app.getServerId(), toServerId: obj.id};
                self.app.rpc.game.entryRemote.sendOnlinePeakMatchTips(session3, self.matchPeriod, function (err) {
                    cb(null);
                });
            },function (err) {
                // logger.error("在线玩家弹窗通知完成");
                callback(null);
            });
        }
    ],function (err) {
        // self.preBattleInfoMap = map;
        // self.preBattleTo32Grouping(leftPlayerList, self.match32Num);
        // logger.error("第一阶段完成");
        self.finishPeriod = self.matchPeriod;
    })
};

//押注阶段
peakMatchService.prototype.betOnProcess = function(msg, cb) {
    //是否押注阶段
    if(this.matchPeriod !== commonEnum.PEAK_MATCH_PERIOD.BET_ON)
    {
        return cb(Code.TIME_FAIL, 0);
    }
    let playerId = msg.punters; //下注者uid
    let gid = msg.gid;          //下注者gid
    let uid = msg.uid;          //下注对象uid
    let num = msg.num;          //下注数量
    if(!this.preBattleInfoMap.has(uid))
    {
        return cb(Code.FAIL, 0);
    }
    let initial = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.PEAK_INITIAL].Param;   //初始值
    let teamNum = 0; //下注队伍数量
    let participant = this.preBattleInfoMap.get(uid);
    // //已达下注上限
    // if(this.getBetOnFollowing(uid) + num > this.getBetOnMax(uid))
    // {
    //     return cb(Code.PASS_MAX_FALL, 0);//超过上限
    // }
    let flag = false;
    for(let i in participant.backer)
    {
        if(participant.backer[i].uid === playerId)
        {
            flag = true;
            participant.backer[i].num += num;
            break;
        }
    }
    if(!flag)
    {
        participant.backer.push({uid: playerId, gid: gid, num: num});
    }
    let total = 0;
    //所有队伍下注总额
    for(let [k, v] of this.preBattleInfoMap)
    {
        teamNum += 1;
        for(let i in v.backer)
        {
            total += v.backer[i].num;
        }
    }
    total += teamNum * initial;
    cb(Code.OK, total);
};
//得到该队伍已下注的总额
peakMatchService.prototype.getBetOnFollowing = function(teamId)
{
    let initial = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.PEAK_INITIAL].Param;   //初始值
    let beliefTotal = 0;   //初始值
    //得到下注信仰总数
    let backer = this.preBattleInfoMap.get(teamId).backer;
    for(let i in backer)
    {
        beliefTotal += backer[i].num;
    }
    beliefTotal += initial;
    return beliefTotal;
};
//得到该队伍的下注上限额       队伍uid
peakMatchService.prototype.getBetOnMax = function(gold, beliefNum)
{
    // let MAX = 0;
    // let teamNum = 0; //下注队伍数量
    // let initial = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.PEAK_INITIAL].Param;   //初始值
    // //得到下注其他信仰的总数
    // for(let [k, v] of this.preBattleInfoMap)
    // {
    //     if(k === teamId)
    //         continue;
    //     teamNum += 1;
    //     for(let i in v.backer)
    //     {
    //         MAX += v.backer[i].num;
    //     }
    // }
    // MAX += initial * teamNum;
    // return MAX;
    return gold * 10 + beliefNum;

};
//公布32强对战列表
peakMatchService.prototype.ReleasedAgainst = function()  {

    let nowTimePeriod = this.checkMatchPeriod();
    if(nowTimePeriod <= this.matchPeriod) {
        logger.error("ReleasedAgainst finish. wait time over. period:", nowTimePeriod);
        return ;
    }

    if(this.matchPeriod !== commonEnum.PEAK_MATCH_PERIOD.PUBLICATION_LIST)
    {
        let leftPlayerList = [];
        for(let [k, v] of this.preBattleInfoMap)
        {
            // let data = {};
            // data.uid = k;
            // data.gid = v.gid;
            // leftPlayerList.push(utils.deepCopy(data));
            leftPlayerList.push({uid: k, gid: v.gid, beliefId: v.beliefId});
        }
        this.preBattleTo32Grouping(leftPlayerList, this.match32Num);
        this.matchPeriod = commonEnum.PEAK_MATCH_PERIOD.PUBLICATION_LIST;
        this.finishPeriod = commonEnum.PEAK_MATCH_PERIOD.PUBLICATION_LIST;
    }
};
//淘汰赛处理
peakMatchService.prototype.outBattleProcess = function() {
    //检查该轮淘汰赛是否已经打完
    let nowTimePeriod = this.checkMatchPeriod();
    if(nowTimePeriod <= this.matchPeriod) {
        logger.error("outBattleProcess finish. wait time over. period:", nowTimePeriod);
        return ;
    }
    // if(this.matchPeriod === commonEnum.PEAK_MATCH_PERIOD.BET_ON)
    // {
    //     let leftPlayerList = [];
    //     for(let [k, v] of this.preBattleInfoMap)
    //     {
    //         // let data = {};
    //         // data.uid = k;
    //         // data.gid = v.gid;
    //         // leftPlayerList.push(utils.deepCopy(data));
    //         leftPlayerList.push({uid: k, gid: v.gid});
    //     }
    //     this.preBattleTo32Grouping(leftPlayerList, this.match32Num);
    // }

    let battleList = this.getBattleListByPyPeriod(this.matchPeriod + 1);
    // logger.error("11111111111111111", this.matchPeriod + 1, battleList);
    //没有人打的情况, 直接跳过
    if(battleList.length === 0) {
        this.matchPeriod++;
        this.finishPeriod = this.matchPeriod;
        return ;
    }
    //let roundNum = this.getNumByMatchPeriod(this.matchPeriod);
    this.matchPeriod++;
    //战斗
    let self = this;
    async.eachSeries(battleList, function (battleInfo, callback) {
        async.waterfall([
            function (cb) {
                logger.debug("outBattle start, battleInfo: ", battleInfo);
                if(battleInfo.home === self.noneUid && battleInfo.away === self.noneUid) {
                    battleInfo.homeScore += 1;
                    cb(null, battleInfo);
                }else if(battleInfo.home === self.noneUid) {
                    battleInfo.awayScore += 1;
                    cb(null, battleInfo);
                }else if(battleInfo.away === self.noneUid) {
                    battleInfo.homeScore += 1;
                    cb(null, battleInfo);
                }else {
                    let session = {frontendId: self.app.getServerId()};
                    let battleServers = self.app.getServersByType("battle");
                    let index = calcUtils.randRange(0, battleServers.length-1);
                    session.toServerId = battleServers[index].id;
                    self.app.rpc.battle.battleRemote.pvpPkMatchBattleReq(session, battleInfo, function (code, retMsg) {
                        if (code !== Code.OK) {
                            logger.error("pvpPkMatchBattleReq error", code, battleInfo);
                            callback(null);
                        } else {
                            //战斗结果
                            battleInfo.homeScore = retMsg.homeScore;
                            battleInfo.awayScore = retMsg.awayScore;

                            //战斗录像
                            if(!!retMsg.roomUid) {
                                battleInfo.roomUid = retMsg.roomUid;
                                battleInfo.time = TimeUtils.now();
                            }
                            cb(null, battleInfo);
                        }
                    });
                }
        },  //胜者邮件  //每轮发邮件通知
            function (battleInfo, cb) {
                let typeId = commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_ROUND_WIN;
                //如果是决赛 发决赛邮件
                if(battleList.length === 1 && self.matchPeriod === commonEnum.PEAK_MATCH_PERIOD.MATCH_2)
                {
                    typeId = commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_CHAMPION;
                }
                // if(battleInfo.home === self.noneUid || battleInfo.away === self.noneUid)
                //     return cb(null, battleInfo);
                let playerList = [];
                let uid = battleInfo.home;
                let gid = "";
                if(battleInfo.home !== self.noneUid && battleInfo.away !== self.noneUid)
                {
                    if(battleInfo.awayScore > battleInfo.homeScore)
                    {
                        uid = battleInfo.away;
                        gid = self.preBattleInfoMap.get(battleInfo.away).gid;
                    }
                    else if(battleInfo.awayScore < battleInfo.homeScore)
                    {
                        uid = battleInfo.home;
                        gid = self.preBattleInfoMap.get(battleInfo.home).gid;
                    }
                    playerList.push({uid: uid, gid: gid});
                    self.sendInfoEmail(playerList, typeId, function () {
                        cb(null, battleInfo);
                    })
                }
                else if(battleInfo.home !== self.noneUid || battleInfo.away !== self.noneUid)
                {
                    if(battleInfo.home === self.noneUid)
                    {
                        uid = battleInfo.away;
                        gid = self.preBattleInfoMap.get(battleInfo.away).gid;
                    }
                    else if(battleInfo.away === self.noneUid)
                    {
                        uid = battleInfo.home;
                        gid = self.preBattleInfoMap.get(battleInfo.home).gid;
                    }
                    playerList.push({uid: uid, gid: gid});
                    self.sendInfoEmail(playerList, typeId, function () {
                        cb(null, battleInfo);
                    })
                }else
                {
                    cb(null, battleInfo);
                }

        },  //败者邮件
            function (battleInfo, cb) {
                let typeId = commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_ROUND_LOSE;
                //如果是决赛 发决赛邮件
                if(battleList.length === 1 && self.matchPeriod === commonEnum.PEAK_MATCH_PERIOD.MATCH_2)
                {
                    typeId = commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_SECOND;
                }
                // if(battleInfo.home === self.noneUid || battleInfo.away === self.noneUid)
                //     return cb(null);
                let playerList = [];
                let uid = battleInfo.home;
                let gid = "";
                if(battleInfo.home !== self.noneUid && battleInfo.away !== self.noneUid)
                {
                    if(battleInfo.awayScore < battleInfo.homeScore)
                    {
                        uid = battleInfo.away;
                        gid = self.preBattleInfoMap.get(battleInfo.away).gid;
                    }
                    else if(battleInfo.awayScore > battleInfo.homeScore)
                    {
                        uid = battleInfo.home;
                        gid = self.preBattleInfoMap.get(battleInfo.home).gid;
                    }
                    playerList.push({uid: uid, gid: gid});
                    self.sendInfoEmail(playerList, typeId, function () {
                        cb(null);
                    })
                }
                else
                {
                    cb(null);
                }
            }], function (err) {
                callback(null);
        });
    }, function (err) {
        //只用打1轮淘汰赛
        self.finishPeriod = self.matchPeriod;
        let nextBattleList = self.getBattleListByPyPeriod(self.matchPeriod+1);
        let index = 0;
        //晋级下一轮结算
        for(let i=0,lens=battleList.length; i<lens; i++) {
            logger.debug("compare battleInfo: ", battleList[i]);
            //取胜者
            let winner = "home";
            //1. 总比分
            let homeAllScore = battleList[i].homeScore;
            let awayAllScore = battleList[i].awayScore;
            if(battleList[i].home !== self.noneUid && battleList[i].away === self.noneUid)
            {
                winner = "home";
            }
            else if(battleList[i].away !== self.noneUid && battleList[i].home === self.noneUid)
            {
                winner = "away";
            }
            else if(homeAllScore < awayAllScore) {
                winner = "away";
            }else if(homeAllScore > awayAllScore) {
                winner = "home";
            }else {
                //2. 押注者多
                if(self.preBattleInfoMap.get(battleList[i].home).backer.length  > self.preBattleInfoMap.get(battleList[i].away).backer.length) {
                    winner = "home";
                }else if(self.preBattleInfoMap.get(battleList[i].home).backer.length < self.preBattleInfoMap.get(battleList[i].away).backer.length) {
                    winner = "away";
                }else {
                    //3. 等级
                    if(self.preBattleInfoMap.get(battleList[i].home).level > self.preBattleInfoMap.get(battleList[i].away).level)
                    {
                        winner = "home";
                    }
                    else if(self.preBattleInfoMap.get(battleList[i].home).level < self.preBattleInfoMap.get(battleList[i].away).level)
                    {
                        winner = "away";
                    }
                    else
                    {
                        //4. 随机选一个
                        let randNum = utils.random(1, 100);
                        if(randNum <= 50) {
                            winner = "home";
                        }else {
                            winner = "away";
                        }
                    }

                }
            }
            //记录胜者
            battleList[i].winnerUid = battleList[i][winner];
            if(battleList[i][winner] !== self.noneUid)
            {
                self.preBattleInfoMap.get(battleList[i].winnerUid).win += 1;
            }
            logger.debug("index[%d] winner: %s", index, winner, i, battleList[i]);
            let groupIndex = Math.floor(index/2);
            if(!nextBattleList[groupIndex]) {
                nextBattleList[groupIndex] = {};
            }
            if(index%2 === 0) {
                if(!battleList[i][winner]) {
                    nextBattleList[groupIndex].home = self.noneUid;
                    nextBattleList[groupIndex].homeGid = "";
                }else {
                    nextBattleList[groupIndex].home = battleList[i][winner];
                    nextBattleList[groupIndex].homeGid = battleList[i][winner+"Gid"];
                }
                nextBattleList[groupIndex].homeScore = 0;
                // nextBattleList[groupIndex].homeAwayScore = 0;
            }else {
                if(!battleList[i][winner]) {
                    nextBattleList[groupIndex].away = self.noneUid;
                    nextBattleList[groupIndex].awayGid = "";
                }else {
                    nextBattleList[groupIndex].away = battleList[i][winner];
                    nextBattleList[groupIndex].awayGid = battleList[i][winner+"Gid"];
                }
                nextBattleList[groupIndex].awayScore = 0;
                // nextBattleList[groupIndex].awayAwayScore = 0;
            }
            index++;
        }
        logger.debug("nextBattleList: ", nextBattleList);
        if(battleList.length === 1 && self.finishPeriod === commonEnum.PEAK_MATCH_PERIOD.MATCH_2) {
            //决赛将胜者设置为董事长，败者设置为副董事长
            let loseUid = battleList[0].home;
            let winUid = battleList[0].winnerUid;
            if(winUid === battleList[0].home)
            {
                loseUid = battleList[0].away;
            }
            if(loseUid !== self.noneUid)
            {
                //设置副董事长
                self.coChairman = {uid: loseUid, gid: self.preBattleInfoMap.get(loseUid).gid, name: self.preBattleInfoMap.get(loseUid).name,
                    faceUrl: self.preBattleInfoMap.get(loseUid).faceUrl, beliefId: self.preBattleInfoMap.get(loseUid).beliefId, pos: self.preBattleInfoMap.get(loseUid).pos,};
            }
            if(winUid !== self.noneUid)
            {
                //设置董事长
                self.chairman = {uid: winUid, gid: self.preBattleInfoMap.get(winUid).gid, name: self.preBattleInfoMap.get(winUid).name,
                    faceUrl: self.preBattleInfoMap.get(winUid).faceUrl, beliefId: self.preBattleInfoMap.get(winUid).beliefId, pos: self.preBattleInfoMap.get(winUid).pos,};
            }
        }
    });
};
//设置32强对战列表             Math.floor（32/leftPlayerList.length） 中间间隔
peakMatchService.prototype.preBattleTo32Grouping = function (leftPlayerList, roundNum) {
    //一个人都没有参加
    if(leftPlayerList.length <= 0) {
        return;
    }
    //打乱顺序
    leftPlayerList.sort(function(){ return 0.5 - Math.random() });
    //清空分组
    this.dqCup32BattleList = [];
    //人数不足, 用轮空补全
    let noneNum = 0;
    if(leftPlayerList.length < roundNum) {
        noneNum = roundNum - leftPlayerList.length;
        logger.debug("preBattleTo32Grouping roundNum 111 roundNum, noneNum:", roundNum, noneNum);
    }
    for(let i = 0; i < roundNum/2; i++)
    {
        let data = {home: this.noneUid, homeGid: "", homeScore: 0,
            away: this.noneUid, awayGid: "",  awayScore: 0};
        this.dqCup32BattleList.push(utils.deepCopy(data));
    }
    let interval = Math.floor(16/leftPlayerList.length);//间隔
    let k = 0;
    let p = roundNum / 2 - 1;
    let rectify1 = k;
    let rectify2 = p;
    for(let i in leftPlayerList)
    {
        if(k <= 0 || k >= roundNum / 2 - 1 )
            k = rectify1;
        if(p >= roundNum / 2 - 1 || p <= 0)
            p = rectify2;
        if(i % 2 === 0)
        {
            this.dqCup32BattleList[k].home = leftPlayerList[i].uid;
            this.dqCup32BattleList[k].homeGid = leftPlayerList[i].gid;
            this.dqCup32BattleList[k].homeBeliefId = leftPlayerList[i].beliefId;
            this.dqCup32BattleList[k].homeScore = 0;
            rectify1 = k + 1;
            if(interval - 1 > 0)
            {
                k+=interval-1;
            }
            else
            {
                k+=1;
            }
        }
        else
        {
            this.dqCup32BattleList[p].away = leftPlayerList[i].uid;
            this.dqCup32BattleList[p].awayGid = leftPlayerList[i].gid;
            this.dqCup32BattleList[p].awayBeliefId = leftPlayerList[i].beliefId;
            this.dqCup32BattleList[p].awayScore = 0;
            rectify2 = p - 1;
            if(interval - 1 > 0)
            {
                p-=interval-1;
            }
            else
            {
                p-=1;
            }
        }
    }
    //第一轮对战错开相同信仰
    this.toAvoidFighting(this.dqCup32BattleList);
};
//将相同信仰对战的分开
peakMatchService.prototype.toAvoidFighting = function(battleList)
{
    let list = [];
    let beliefIdList = [];
    let battleAList = [];
    let battleBList = [];
  for(let i in battleList)
  {
      if(battleList[i].home !== this.noneUid && battleList[i].away !== this.noneUid)
      {
          let home = this.preBattleInfoMap.get(battleList[i].home);
          let away = this.preBattleInfoMap.get(battleList[i].away);
          if(home.beliefId === away.beliefId)
          {
              list.push(i);
              beliefIdList.push(battleList[i].homeBeliefId);
              beliefIdList.push(battleList[i].awayBeliefId);
              battleAList.push({uid: battleList[i].home, gid: battleList[i].homeGid, beliefId: battleList[i].homeBeliefId});
              battleBList.push({uid: battleList[i].away, gid: battleList[i].awayGid, beliefId: battleList[i].awayBeliefId});
          }
      }
  }

  if(list.length % 2 > 0)
  {
      //再找一队出来替换
      for(let i in battleList)
      {
          if((!battleList[i].homeBeliefId || beliefIdList.indexOf(battleList[i].homeBeliefId) < 0) || (!battleList[i].awayBeliefId || beliefIdList.indexOf(battleList[i].awayBeliefId) < 0))
          {
              list.push(i);
              if(battleList[i].home === this.noneUid)
              {
                  battleAList.push({uid: battleList[i].home})
              }
              else
              {
                  battleAList.push({uid: battleList[i].home, gid: battleList[i].homeGid, beliefId: battleList[i].homeBeliefId});
                  beliefIdList.push(battleList[i].homeBeliefId);
              }
              if(battleList[i].away === this.noneUid)
              {
                  battleBList.push({uid: battleList[i].away});
              }
              else
              {
                  battleBList.push({uid: battleList[i].away, gid: battleList[i].awayGid, beliefId: battleList[i].awayBeliefId});
                  beliefIdList.push(battleList[i].awayBeliefId);
              }
              break;
          }
      }
  }
  battleAList.sort(function(){ return 0.5 - Math.random() });
  battleBList.sort(function(){ return 0.5 - Math.random() });
  let k = 0;
  let n = 0;
  for(let i in list)
  {
      let p = list[i];
      if(i < battleAList.length/2)
      {
          battleList[p].home = battleAList[k].uid;
          if(battleList[p].home !== this.noneUid)
          {
              battleList[p].homeBeliefId = battleAList[k].beliefId;
              battleList[p].homeGid = battleAList[k].gid;
          }
          k++;
          battleList[p].away = battleAList[k].uid;
          if(battleList[p].away !== this.noneUid)
          {
              battleList[p].awayBeliefId = battleAList[k].beliefId;
              battleList[p].awayGid = battleAList[k].gid;
          }
          k++;
      }
      else {
          battleList[p].home = battleBList[n].uid;
          if(battleList[p].home !== this.noneUid)
          {
              battleList[p].homeBeliefId = battleBList[n].beliefId;
              battleList[p].homeGid = battleBList[n].gid;
          }
          n++;
          battleList[p].away = battleBList[n].uid;
          if(battleList[p].away !== this.noneUid)
          {
              battleList[p].awayBeliefId = battleBList[n].beliefId;
              battleList[p].awayGid = battleBList[n].gid;
          }
          n++
      }
  }
};
//获取对战表引用
peakMatchService.prototype.getBattleListByPyPeriod = function(period) {
    if(period === commonEnum.PEAK_MATCH_PERIOD.MATCH_32) {
        return this.dqCup32BattleList;
    }else if(period === commonEnum.PEAK_MATCH_PERIOD.MATCH_16) {
        return this.dqCup16BattleList;
    }else if(period === commonEnum.PEAK_MATCH_PERIOD.MATCH_8) {
        return this.dqCup8BattleList;
    }else if(period === commonEnum.PEAK_MATCH_PERIOD.MATCH_4) {
        return this.dqCup4BattleList;
    }else if(period === commonEnum.PEAK_MATCH_PERIOD.MATCH_2) {
        return this.dqCup2BattleList;
    }
    return [];
};
//每轮对战结束晋级的人发奖励
peakMatchService.prototype.periodSendReward = function (period) {
    let BattleList = [];
    let rank = 32;
    if(period === commonEnum.PEAK_MATCH_PERIOD.MATCH_32) {
        BattleList = this.dqCup16BattleList;
        rank = 16;
    }else if(period === commonEnum.PEAK_MATCH_PERIOD.MATCH_16) {
        BattleList = this.dqCup8BattleList;
        rank = 8;
    }else if(period === commonEnum.PEAK_MATCH_PERIOD.MATCH_8) {
        BattleList = this.dqCup4BattleList;
        rank = 4;
    }else if(period === commonEnum.PEAK_MATCH_PERIOD.MATCH_4) {
        BattleList = this.dqCup2BattleList;
        rank = 2;
    }else if(period === commonEnum.PEAK_MATCH_PERIOD.MATCH_2) {
        BattleList = [{home: this.chairman.uid, homeGid: this.chairman.gid}];
        rank = 1;
    }
    let uidList = [];
    for(let i in BattleList)
    {
        if(BattleList[i].home !== this.noneUid)
        {
            uidList.push({uid: BattleList[i].home, gid: BattleList[i].homeGid, rank: rank});
        }
        if(BattleList[i].away !== this.noneUid)
        {
            uidList.push({uid: BattleList[i].away, gid: BattleList[i].awayGid, rank: rank});
        }
    }
    if(uidList.length > 0)
    {
        //发奖励邮件
        this.sendRewardMailByPeriod(uidList,function () {
        });
    }

};
//发奖励
peakMatchService.prototype.sendReward = function () {
    let alreadySendMap = new Map();	//不重复发奖
    let self = this;
    let oneMatchRecord = [];	//记录每场比赛的名次
    let winners = [];           //记录中奖人uid和下注总数 {uid, num}
    if(self.matchPeriod === commonEnum.PEAK_MATCH_PERIOD.SEND_REWARD)
        return;
    logger.debug("sendReward - champion: ", self.chairman);
    self.matchPeriod = commonEnum.PEAK_MATCH_PERIOD.SEND_REWARD;
    async.waterfall([
        function (callback) {
            if(!!self.chairman.uid)
            {
                //冠军奖励
                let uidList = [{uid: self.chairman.uid, gid: self.chairman.gid, rank: 1}];
                alreadySendMap.set(self.chairman.uid, 1);
                self.preBattleInfoMap.get(self.chairman.uid).rank = 1;
                //冠军获得冠军奖池部分奖励
                self.sendInfoEmail(uidList, commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_RANK_GUESS_AWARD, function () {
                    callback(null);
                });
            }
            else
            {
                callback(null);
            }
        },
        function (callback) {
            //亚军奖励
            // let uidList = [{uid: self.coChairman.uid, gid: self.coChairman.gid, rank: 2}];
            // self.sendRewardMailByPeriod(uidList,function () {
            //     alreadySendMap.set(self.coChairman.uid, 1);
            //     oneMatchRecord.push({uid: self.coChairman.uid, name: self.coChairman.name, rank: 2});
            //     callback(null);
            // })
            if(!!self.coChairman.uid)
            {
                alreadySendMap.set(self.coChairman.uid, 1);
                self.preBattleInfoMap.get(self.coChairman.uid).rank = 2;
            }
            callback(null);
        },
        function (callback) {
            //4强奖励
            let playerList = self.getBattleListByPyPeriod(commonEnum.PEAK_MATCH_PERIOD.MATCH_4);
            let uidList = [];
            for(let i=0,lens=playerList.length;i<lens;i++) {
                if(!!playerList[i].home && (!alreadySendMap.has(playerList[i].home)) && playerList[i].home !== self.noneUid) {
                    uidList.push({uid: playerList[i].home, gid: playerList[i].homeGid});
                    alreadySendMap.set(playerList[i].home, 1);
                }
                if(!!playerList[i].away && (!alreadySendMap.has(playerList[i].away)) && playerList[i].away !== self.noneUid) {
                    uidList.push({uid: playerList[i].away, gid: playerList[i].awayGid});
                    alreadySendMap.set(playerList[i].away, 1);
                }
            }
            if(uidList.length === 0)
            {
                callback(null);
            }
            else
            {
                //对比球队实力，实力相同比攻防值 得到排名
                self.getbattleRank(uidList, 3, function (result) {
                    for(let i=0, lens=result.length; i<lens; i++) {
                        let uid = result[i].uid;
                        self.preBattleInfoMap.get(uid).rank = result[i].rank;
                    }
                    callback(null);
                });
            }
        },
        function (callback) {
            //8强奖励
            let playerList = self.getBattleListByPyPeriod(commonEnum.PEAK_MATCH_PERIOD.MATCH_8);
            let uidList = [];
            for(let i=0,lens=playerList.length;i<lens;i++) {
                if(!!playerList[i].home && (!alreadySendMap.has(playerList[i].home)) && playerList[i].home !== self.noneUid) {
                    uidList.push({uid: playerList[i].home, gid: playerList[i].homeGid});
                    alreadySendMap.set(playerList[i].home, 1);
                }
                if(!!playerList[i].away && (!alreadySendMap.has(playerList[i].away)) && playerList[i].away !== self.noneUid) {
                    uidList.push({uid: playerList[i].away, gid: playerList[i].awayGid});
                    alreadySendMap.set(playerList[i].away, 1);
                }
            }
            if(uidList.length === 0)
            {
                callback(null);
            }
            else
            {
                self.getbattleRank(uidList, 5, function (result) {
                    for(let i=0, lens=result.length; i<lens; i++) {
                        let uid = result[i].uid;
                        self.preBattleInfoMap.get(uid).rank = result[i].rank;
                    }
                    callback(null);
                });
            }
        },
        function (callback) {
            //16强奖励
            let playerList = self.getBattleListByPyPeriod(commonEnum.PEAK_MATCH_PERIOD.MATCH_16);
            let uidList = [];
            for(let i=0,lens=playerList.length;i<lens;i++) {
                if(!!playerList[i].home && (!alreadySendMap.has(playerList[i].home)) && playerList[i].home !== self.noneUid) {
                    uidList.push({uid: playerList[i].home, gid: playerList[i].homeGid});
                    alreadySendMap.set(playerList[i].home, 1);
                }
                if(!!playerList[i].away && (!alreadySendMap.has(playerList[i].away)) && playerList[i].away !== self.noneUid) {
                    uidList.push({uid: playerList[i].away, gid: playerList[i].awayGid});
                    alreadySendMap.set(playerList[i].away, 1);
                }
            }
            if(uidList.length === 0)
            {
                callback(null);
            }
            else
            {
                self.getbattleRank(uidList, 9, function (result) {
                    for(let i=0, lens=result.length; i<lens; i++) {
                        let uid = result[i].uid;
                        self.preBattleInfoMap.get(uid).rank = result[i].rank;
                    }
                    callback(null);
                });
            }
        },
        function (callback) {
            //给押中冠军的人发奖励
            let uidList = [];
            if(!self.chairman.uid)
            {
                return callback(null);
            }
            let chairman = self.preBattleInfoMap.get(self.chairman.uid);
            for(let i in chairman.backer)
            {
                uidList.push({uid: chairman.backer[i].uid, gid: chairman.backer[i].gid});
            }
            if(uidList.length === 0)
            {
                callback(null);
            }
            else
            {
                //发中奖邮件
                self.sendInfoEmail(uidList, commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_RANK_GUESS_WIN, function () {
                    callback(null);
                });
            }
        },
        function (callback) {
            //给没中奖的发通知
            let uidList = [];
            for(let [k, v] of self.preBattleInfoMap)
            {
                if(k === self.chairman.uid)
                    continue;
                for(let i in v.backer)
                {
                    uidList.push({uid: v.backer[i].uid, gid: v.backer[i].gid, name: v.name, win: v.win});
                }
            }
            if(uidList.length === 0)
            {
                callback(null);
            }
            else
            {
                //发失败邮件
                self.sendInfoEmail(uidList, commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_RANK_GUESS_LOSE, function () {
                    callback(null);
                });
            }
        },
        function (callback) {
            //记录本届董事长和副董事长
            self.matchChampionRecord.push({time: TimeUtils.now(), chairman: self.chairman, coChairman: self.coChairman});
            callback(null);
        },
        function (callback) {
            //统计数据
            self.statisticalData(function () {
                callback(null);
            });
        },
        function (callback) {
           //公告播报xxx信仰俱乐部的xxx成为总裁、xxx信仰俱乐部的xxx成为副总裁
            let config =  dataApi.allData.data["Belief"];
            let chairmanBelief = "";
            let coChairmanBelief = "";
            let chairmanName = "";
            let coChairmanName = "";
            if(!!self.chairman.uid && self.chairman.uid !== self.noneUid)
            {
                chairmanBelief = config[self.chairman.beliefId].Team;
                chairmanName = self.chairman.name;
            }
            if(!!self.coChairman.uid && self.coChairman.uid !== self.noneUid)
            {
                coChairmanBelief = config[self.coChairman.beliefId].Team;
                coChairmanName = self.coChairman.name;
            }
            let msg = "恭喜 " + chairmanBelief + "信仰俱乐部的 " + "<font color=0x83db43>" + chairmanName + "</font>" + "成为总裁、" + coChairmanBelief + "信仰俱乐部的 " + "<font color=0x83db43>" + coChairmanName + "</font>" + "成为副总裁";
            let sendMsg = {
                senderName: "系统",
                type: commonEnum.CHAT_TYPE.HORN,// -1系统 0普通 1物品超链接 2球员超链接 3喇叭
                msg: msg,
                channel: commonEnum.CHAT_CHANNEL.SYSTEM_CHANNEL,
            };
            let session = {frontendId: self.app.getServerId()};
            self.app.rpc.game.entryRemote.broadcastChatMsg(session, sendMsg, function (code) {
                callback(null);
            });
        },
        function (callback) {
            let playerList = [];
            for(let [k, v] of self.preBattleInfoMap)
            {
                playerList.push({uid: k, gid: v.gid, seasonId: self.honorSeasonId, rank: v.rank});
            }
            if(playerList.length > 0)
            {
                //更新名次到荣誉墙
                self.updataHonorData(playerList, commonEnum.HONOR_DISPOSE_TYPE.DATA, function () {
                    callback(null);
                });
            }
            else
            {
                callback(null);
            }
        },
        function (callback) {
            //记录比赛记录
            self.matchDao.updatePkMatchRecord(oneMatchRecord, callback);
        }
    ], function (err) {
        logger.debug("sendMail Period finished");
        self.finishPeriod = commonEnum.PEAK_MATCH_PERIOD.SEND_REWARD;
    });
};
//获取排名  playerList = [{uid, gid}]
peakMatchService.prototype.getbattleRank = function(playerList, beginRank, cb)
{
    //根据不同gid区分发送到不同game服务器处理
    let list = [];
    let gid2Index = new Map();
    let index = 0;
    for(let i=0,lens=playerList.length;i<lens;i++) {
        if(gid2Index.has(playerList[i].gid)) {
            list[gid2Index.get(playerList[i].gid)].push(playerList[i]);
        }else {
            list[index] = [];
            list[index].push(playerList[i]);
            gid2Index.set(playerList[i].gid, index);
            index++;
        }
    }
    let self = this;
    let infoList = [];
    async.eachSeries(list, function (sameGameList, callback) {
        let session = {frontendId: self.app.getServerId(), toServerId: sameGameList[0].gid};
        let reMsg = {uidList: sameGameList};
        self.app.rpc.game.entryRemote.getActualStrengthList(session, reMsg, function (teamFormationList) {
            for(let i in teamFormationList)
            {
                infoList.push(teamFormationList[i]);
            }
            callback(null);
        });
    },function (err) {
        for(let i in infoList)
        {
            for(let k in playerList)
            {
                if(infoList[i].uid === playerList[k].uid)
                {
                    // let data = {};
                    // data.uid = playerList[k].uid;
                    // data.gid = playerList[k].gid;
                    // data.ActualStrength = infoList[i].ActualStrength;
                    // data.ADSun = infoList[i].ADSun;
                    // playerList[k] = utils.deepCopy(data);
                    playerList[k].ActualStrength = infoList[i].ActualStrength;
                    playerList[k].ADSun = infoList[i].ADSun;
                    break;
                }
            }
        }
        playerList.sort(__ActualStrength_rabk_func);
        for(let i in playerList)
        {
            playerList[i].rank = beginRank++;
        }
        cb(playerList);
    });
};
function __ActualStrength_rabk_func(rankObj1, rankObj2) {
    if(rankObj1.ActualStrength < rankObj2.ActualStrength) {
        return 1;
    }else if(rankObj1.ActualStrength > rankObj2.ActualStrength) {
        return -1;
    }else
    {
        if(rankObj1.ADSun < rankObj2.ADSun) {
            return 1;
        }else if(rankObj1.ADSun > rankObj2.ADSun) {
            return -1;
        }
    }
    return 0;
}
//得到押注成功的奖励数量   下注玩家 uid
peakMatchService.prototype.getNumberForReward = function(playerId){
    //获胜奖励积分 = 玩家下注额 +（所有队伍下注总额 - 赢家下注总额）× 玩家分配比 ×（玩家下注额 / 赢家下注总额）
    let initial = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.PEAK_INITIAL].Param;   //初始值
    let ratio = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.PEAK_RATIO].Param / 10000;   //玩家分配比
    let total = 0;
    let num  = 0;
    //所有队伍下注总额
    for(let [k, v] of this.preBattleInfoMap)
    {
        for(let i in v.backer)
        {
            total += v.backer[i].num;
        }
        num += 1;
    }
    total += num * initial;//总额加上初始值*队伍数量
    //赢家下注总额
    let winTotal = initial;//初始值
    //玩家该队伍的下注额
    let playerBet = 0;
    let chairman = this.preBattleInfoMap.get(this.chairman.uid);
    for(let i in chairman.backer)
    {
        winTotal += chairman.backer[i].num;
        if(chairman.backer[i].uid === playerId)
        {
            playerBet = chairman.backer[i].num;
        }
    }
    return Math.floor(playerBet + (total - winTotal) * ratio * (playerBet / winTotal));
};
//冠军获得的奖池奖励
peakMatchService.prototype.getChairmanNumberForReward = function(){
    //冠军奖池奖励=（奖池总额-赢家下注总额）× 冠军分配比（配置项）
    let initial = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.PEAK_INITIAL].Param;   //初始值
    let ratio = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.PEAK_CHAIRMAN_RATIO].Param / 10000;   //冠军分配比还没配
    let total = 0;
    let num  = 0;
    //所有队伍下注总额
    for(let [k, v] of this.preBattleInfoMap)
    {
        for(let i in v.backer)
        {
            total += v.backer[i].num;
        }
        num += 1;
    }
    total += num * initial;//总额加上初始值*队伍数量
    //赢家下注总额
    let winTotal = initial;//初始值
    //玩家该队伍的下注额
    let playerBet = 0;
    let chairman = this.preBattleInfoMap.get(this.chairman.uid);
    for(let i in chairman.backer)
    {
        winTotal += chairman.backer[i].num;
    }
    return Math.floor((total - winTotal) * ratio);
};
//清空巅峰赛数据
peakMatchService.prototype.emptyPkMatchData = function() {
    // //加入历届冠军
    // this.matchChampionRecord.push({time: TimeUtils.now(), chairman: this.chairman, coChairman: this.coChairman});
    //清空比赛数据
    this.preBattleInfoMap = new Map();		//预选赛个人比赛信息表
    //对战信息表
    this.preBattleList = [];				//预选赛对战列表 [{uid1, uid2}, ...]
    this.dqCup32BattleList = [];			//32强对战信息
    this.dqCup16BattleList = [];			//16强对战信息
    this.dqCup8BattleList = [];				//8强对战信息
    this.dqCup4BattleList = [];				//4强对战信息
    this.dqCup2BattleList = [];				//2强对战信息
    this.coChairman = {};                   //副董事
    this.chairman = {};						//董事长{uid, gid}
    this.playerTips.clear();                //清空通知列表
};
//发奖励邮件         uidList = [{uid, gid. rank}]
peakMatchService.prototype.sendRewardMailByPeriod = function (uidList, cb) {
    //根据不同gid区分发送到不同game服务器处理
    let list = [];
    let gid2Index = new Map();
    let index = 0;
    for(let i=0,lens=uidList.length;i<lens;i++) {
        if(gid2Index.has(uidList[i].gid)) {
            list[gid2Index.get(uidList[i].gid)].push(uidList[i]);
        }else {
            list[index] = [];
            list[index].push(uidList[i]);
            gid2Index.set(uidList[i].gid, index);
            index++;
        }
    }
    logger.debug("sendRewardMailByPeriod process gid list: ", list);
    //邮件配置
    let config = dataApi.allData.data["UltimateMatchAward"];
    let mailTextConfig = dataApi.allData.data["MailText"][commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_RANK_AWARD];
    let flag = false;
    if(uidList[0].rank === 1)
    {
        mailTextConfig = dataApi.allData.data["MailText"][commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_CHAMPION_AWARD];
        flag = true;
    }
    else if(uidList[0].rank === 2)
    {
        mailTextConfig = dataApi.allData.data["MailText"][commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_SECOND_AWARD];
        flag = true;
    }
    let self = this;
    let rewardConfigId = 5;
    for(let i in config)
    {
        // if(config[i].TopMin <= uidList[0].rank && config[i].TopMax >= uidList[uidList.length - 1].rank)
        // {
        //     rewardConfigId = config[i].ID;
        //     break;
        // }
        if(config[i].TopMax === uidList[0].rank)
        {
            rewardConfigId = config[i].ID;
            break;
        }
    }
    let sendAwardList = [];

    async.eachSeries(list, function (sameGameList, callback) {
        let session = {frontendId: self.app.getServerId(), toServerId: sameGameList[0].gid};
        //发送者uid列表
        for(let n=0,lens=sameGameList.length;n<lens;n++) {
            if(!!sameGameList[n].uid && !!sameGameList[n].uid !== self.noneUid) {
                let msg = {};
                msg.uid = sameGameList[n].uid;
                msg.mailInfo = {};
                msg.mailInfo.title = mailTextConfig.Title;        	//标题
                if(!flag)
                {
                    //内容
                    // msg.mailInfo.content = mailTextConfig.Result1.replace("#0#", sameGameList[n].rank);
                    msg.mailInfo.content = mailTextConfig.Result1.replace("#0#", config[rewardConfigId].ShowName);
                }
                else
                {
                    msg.mailInfo.content = mailTextConfig.Result1;
                }

                msg.mailInfo.attachList = [];						//奖励
                for(let n=1;n<=5;n++) {
                    let reward = {};
                    reward.ResId = config[rewardConfigId]["Reward"+n];
                    reward.Num = config[rewardConfigId]["Num"+n];
                    reward.ItemType = commonEnum.MAIL_ITEM_TYPE.ITEM;
                    if(reward.ResId > 0 && reward.Num > 0) {
                        msg.mailInfo.attachList.push(reward);
                    }
                }
                sendAwardList.push(utils.deepCopy(msg));
            }
        }
        //logger.error("DqCup Send Reward Debug, period, msg: ", period, msg);
        self.app.rpc.game.entryRemote.sendPeakMatchMailByList(session, sendAwardList, function (code) {
            //logger.debug("sendEditMail 1 code:", code, msg);
            callback(null);
        })
    }, function (err) {
        logger.debug("send mail finished. uidList:", uidList);
        cb();
    });

};
//发通知邮件         playerList [{uid, gid}]
peakMatchService.prototype.sendInfoEmail = function(playerList, typeId, cb)
{
    let mailTextConfig = dataApi.allData.data["MailText"][typeId];
    //如果是首轮
    if(typeId === commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_ROUND_LOSE && this.matchPeriod === commonEnum.PEAK_MATCH_PERIOD.MATCH_32)
    {
        mailTextConfig = dataApi.allData.data["MailText"][commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_ONE_ROUND_LOSE];
    }
    let beliefConfig = dataApi.allData.data["Belief"];
    let rewardList = [];
    let info = {};
    info.sendPlayerId = "系统";
    info.recvPlayerId = "";
    info.title = mailTextConfig.Title;
    info.attachList = utils.cloneArray(rewardList);

    if(playerList.length < 1) {
        return cb();
    }
    let self = this;
    async.eachSeries(playerList, function (msg, callback){
        let gid = msg.gid;
        info.recvPlayerId = msg.uid;
        let playerInfo = self.preBattleInfoMap.get(msg.uid);
        //开赛阶段4个
        if(typeId === commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_OPEN)
        {
            let beliefName = beliefConfig[playerInfo.beliefId].Team;//信仰名
            let data = new Date();
            // let time = self.getSchduleConfigHourAndMinute(commonEnum.PEAK_MATCH_SCHEDULE.MATCH_32);
            let time = dataApi.allData.data["UltimateMatchTime"][commonEnum.PEAK_MATCH_SCHEDULE.MATCH_32].Time.split(":");
            info.content = mailTextConfig.Result1.replace("#0#", data.getMonth() + 1);//月
            info.content = info.content.replace("#0#", data.getDate());//日
            info.content = info.content.replace("#0#", time[0] + ":" + time[1]);//时分
            info.content = info.content.replace("#0#", beliefName);//俱乐部名
        }
        else if(typeId === commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_ROUND_WIN || typeId === commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_ROUND_LOSE)
        {
            let score;
            let rivalInfo = {};
            let beliefName = "";
            //获取当前阶段对战列表
            let battleList = self.getBattleListByPyPeriod(self.matchPeriod);

            if(typeId === commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_ROUND_WIN)//晋升成功的发奖励
            {
                //奖励配置
                let config = dataApi.allData.data["UltimateMatchAward"];
                let rewardConfig = 5;
                for(let i in config)
                {
                    if(config[i].TopMax === battleList.length)
                    {
                        rewardConfig = config[i].ID;
                    }
                }
                info.attachList = [];
                for(let n=1; n<5; n++)
                {
                    let reward = {};
                    reward.ResId = config[rewardConfig]["Reward"+n];
                    reward.Num = config[rewardConfig]["Num"+n];
                    reward.ItemType = commonEnum.MAIL_ITEM_TYPE.ITEM;
                    if(reward.ResId > 0 && reward.Num > 0){
                        info.attachList.push(reward);
                    }
                }
            }
            else if(typeId === commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_ROUND_LOSE && self.matchPeriod === commonEnum.PEAK_MATCH_PERIOD.MATCH_32)//如果是第一轮失败的也有奖励
            {
                //奖励配置
                let config = dataApi.allData.data["UltimateMatchAward"];
                let rewardConfig = 6;
                for(let i in config)
                {
                    if(config[i].TopMax === battleList.length * 2)
                    {
                        rewardConfig = config[i].ID;
                    }
                }
                info.attachList = [];
                for(let n=1; n<5; n++)
                {
                    let reward = {};
                    reward.ResId = config[rewardConfig]["Reward"+n];
                    reward.Num = config[rewardConfig]["Num"+n];
                    reward.ItemType = commonEnum.MAIL_ITEM_TYPE.ITEM;
                    if(reward.ResId > 0 && reward.Num > 0){
                        info.attachList.push(reward);
                    }
                }
            }
            for(let i in battleList)
            {
                if(battleList[i].home === msg.uid)
                {
                    score = battleList[i].homeScore + ":" + battleList[i].awayScore;
                    if(battleList[i].away === self.noneUid)
                    {
                        beliefName = "轮空";
                        rivalInfo.name = "轮空";
                        break;
                    }
                    else
                    {
                        rivalInfo = self.preBattleInfoMap.get(battleList[i].away);
                        beliefName = beliefConfig[rivalInfo.beliefId].Team;//信仰名
                    }


                    break;
                }
                else if(battleList[i].away === msg.uid)
                {
                    score = battleList[i].awayScore + ":" + battleList[i].homeScore;
                    if(battleList[i].home === self.noneUid)
                    {
                        beliefName = "轮空";
                        rivalInfo.name = "轮空";
                        break;
                    }
                    else
                    {
                        rivalInfo = self.preBattleInfoMap.get(battleList[i].home);
                        beliefName = beliefConfig[rivalInfo.beliefId].Team;//信仰名
                    }
                    break;
                }
            }
            if(typeId === commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_ROUND_LOSE && self.matchPeriod === commonEnum.PEAK_MATCH_PERIOD.MATCH_32)
            {
                info.content = mailTextConfig.Result1.replace("#0#", self.getRoundName(self.matchPeriod));//阶段名
                info.content = info.content.replace("#0#", beliefName);//对手俱乐部名
                info.content = info.content.replace("#0#", rivalInfo.name);//对手名
                info.content = info.content.replace("#0#", score);//比分
            }
            else
            {
                info.content = mailTextConfig.Result1.replace("#0#", self.getRoundName(self.matchPeriod));//阶段名
                info.content = info.content.replace("#0#", beliefName);//对手俱乐部名
                info.content = info.content.replace("#0#", rivalInfo.name);//对手名
                info.content = info.content.replace("#0#", score);//比分
                info.content = info.content.replace("#0#", self.getRoundName(self.matchPeriod + 1));//下个阶段名
            }
        }
        else if(typeId === commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_CHAMPION || typeId === commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_SECOND)
        {
            let score;
            let rivalInfo = {};
            let beliefName = "轮空";
            //获取当前阶段对战列表
            let battleList = self.getBattleListByPyPeriod(self.matchPeriod);
            for(let i in battleList)
            {
                if(battleList[i].home === msg.uid)
                {
                    score = battleList[i].homeScore + ":" + battleList[i].awayScore;
                    if(battleList[i].away !== self.noneUid)
                    {
                        rivalInfo = self.preBattleInfoMap.get(battleList[i].away);
                        beliefName = beliefConfig[rivalInfo.beliefId].Team;//信仰名
                    }
                    else
                    {
                        rivalInfo.name = "轮空";
                    }
                    break;
                }
                else if(battleList[i].away === msg.uid)
                {
                    score = battleList[i].awayScore + ":" + battleList[i].homeScore;
                    if(battleList[i].away !== self.noneUid)
                    {
                        rivalInfo = self.preBattleInfoMap.get(battleList[i].home);
                        beliefName = beliefConfig[rivalInfo.beliefId].Team;//信仰名
                    }
                    else
                    {
                        rivalInfo.name = "轮空";
                    }
                    break;
                }
            }
            if(typeId === commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_CHAMPION)//得到冠军发奖励
            {
                //奖励配置
                let config = dataApi.allData.data["UltimateMatchAward"];
                let rewardConfig = "";
                for(let i in config)
                {
                    if(config[i].TopMax === 1)
                    {
                        rewardConfig = config[i].ID;
                    }
                }
                info.attachList = [];
                for(let n=1; n<5; n++)
                {
                    let reward = {};
                    reward.ResId = config[rewardConfig]["Reward"+n];
                    reward.Num = config[rewardConfig]["Num"+n];
                    reward.ItemType = commonEnum.MAIL_ITEM_TYPE.ITEM;
                    if(reward.ResId > 0 && reward.Num > 0){
                        info.attachList.push(reward);
                    }
                }
            }
            // let beliefName = beliefConfig[rivalInfo.beliefId].Team;//信仰名
            info.content = mailTextConfig.Result1.replace("#0#", self.getRoundName(self.matchPeriod));//阶段名
            info.content = info.content.replace("#0#", beliefName);//对手俱乐部名
            info.content = info.content.replace("#0#", rivalInfo.name);//对手名
            info.content = info.content.replace("#0#", score);//比分
        }
        else if(typeId === commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_RANK_GUESS_WIN)//竞猜获胜
        {
            let reward = {};
            let initial = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.PEAK_INITIAL].Param;   //初始值
            let total = 0;
            let num  = 0;
            //所有队伍下注总额
            for(let [k, v] of self.preBattleInfoMap)
            {
                for(let i in v.backer)
                {
                    total += v.backer[i].num;
                }
                num += 1;
            }
            total += num * initial;//总额加上初始值*队伍数量
            //赢家下注总额
            let winTotal = initial;//初始值
            //玩家该队伍的下注额
            let chairman = self.preBattleInfoMap.get(self.chairman.uid);
            for(let i in chairman.backer)
            {
                winTotal += chairman.backer[i].num;
            }
            info.attachList = [];
            reward.ResId = commonEnum.ITEM_RESID.BELIEF_INTEGRAL;
            reward.Num = self.getNumberForReward(msg.uid);
            reward.ItemType = commonEnum.MAIL_ITEM_TYPE.ITEM;
            info.attachList.push(reward);
            let winName = self.preBattleInfoMap.get(self.chairman.uid).name;
            info.content = mailTextConfig.Result1.replace("#0#", winName);//冠军名
            info.content = info.content.replace("#0#", total);//奖池累计总额
            info.content = info.content.replace("#0#", winTotal);//中奖队伍奖池总额
            info.content = info.content.replace("#0#", reward.Num);//获得奖励总额
        }
        else if(typeId === commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_RANK_GUESS_LOSE)//竞猜失败
        {
            let name = msg.name;
            let periodName = self.getRoundName(msg.win + 3);
            let winName = self.preBattleInfoMap.get(self.chairman.uid).name;
            info.content = mailTextConfig.Result1.replace("#0#", name);//竞猜队伍名
            info.content = info.content.replace("#0#", periodName);//该队伍失败阶段名
            info.content = info.content.replace("#0#", winName);//冠军名
        }
        else if(typeId === commonEnum.MAIL_TRANSLATE_CONTENT.PEAK_MATCH_RANK_GUESS_AWARD)//冠军的竞猜奖励
        {
            let reward = {};
            let initial = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.PEAK_INITIAL].Param;   //初始值
            let total = 0;
            let num  = 0;
            //所有队伍下注总额
            for(let [k, v] of self.preBattleInfoMap)
            {
                for(let i in v.backer)
                {
                    total += v.backer[i].num;
                }
                num += 1;
            }
            total += num * initial;//总额加上初始值*队伍数量
            //赢家下注总额
            let winTotal = initial;//初始值
            //玩家该队伍的下注额
            let chairman = self.preBattleInfoMap.get(self.chairman.uid);
            for(let i in chairman.backer)
            {
                winTotal += chairman.backer[i].num;
            }
            info.attachList = [];
            reward.ResId = commonEnum.ITEM_RESID.BELIEF_INTEGRAL;
            reward.Num = self.getChairmanNumberForReward();
            reward.ItemType = commonEnum.MAIL_ITEM_TYPE.ITEM;
            info.attachList.push(reward);
            info.content = mailTextConfig.Result1.replace("#0#", total);//奖池累计总额
            info.content = info.content.replace("#0#", winTotal);//中奖队伍奖池总额
            info.content = info.content.replace("#0#", reward.Num);//获得奖励总额
        }
        let session = {frontendId: self.app.getServerId(), toServerId: gid};
        //发送邮件
        self.app.rpc.game.entryRemote.sendEditMail(session, info, function (err) {
            if (err != Code.OK) {
                logger.error("sendInfoEmail err: ", err);
                callback(null);
                return;
            }
            callback(null);
        });
    },function (err) {
        cb();
    });
};
peakMatchService.prototype.getRoundName = function (period) {
    logger.error("getRoundName ", period);
    if(period === commonEnum.PEAK_MATCH_PERIOD.MATCH_32) {
        return "1/16决赛";
    }else if(period === commonEnum.PEAK_MATCH_PERIOD.MATCH_16) {
        return "1/8决赛";
    }else if(period === commonEnum.PEAK_MATCH_PERIOD.MATCH_8) {
        return "1/4决赛";
    }else if(period === commonEnum.PEAK_MATCH_PERIOD.MATCH_4) {
        return "半决赛";
    }else if(period === commonEnum.PEAK_MATCH_PERIOD.MATCH_2) {
        return "决赛";
    }
    return 0;
};

//得到当前阶段
peakMatchService.prototype.getNowPeriod = function (playerId, cb) {
    let inform = false;
    if(this.playerTips.has(playerId))
    {
        inform = true;//通知过了
    }
    else
    {
        this.playerTips.set(playerId, 1);
    }
    return cb(this.matchPeriod, inform);
};

//统计数据          下注人数&金额   各俱乐部被下注额   总奖池金额   职位记录
peakMatchService.prototype.statisticalData = function (cb) {
    // let initial = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.PEAK_INITIAL].Param;   //初始值
    let day = TimeUtils.dayInterval("2020/5/4 00:0:00");
    let week = Math.floor(day / 7); //第几周
    let date = new Date();
    let time = date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate();
    //下注人数
    let betNum = 0;
    //金额
    let betList = [];//{uid, num}
    //各俱乐部被下注额
    let clubBetList = [];//{uid, name, beliefId, num}
    //总奖池金额
    let total = 0;
    //职位记录
    let postList = {
        chairman: this.chairman,
        coChairman: this.coChairman
    };
    for(let [k, v] of this.preBattleInfoMap)
    {
        betNum += v.backer.length;
        for(let i in v.backer)
        {
            betList.push({uid: v.backer[i].uid, num: v.backer[i].num});
        }
        clubBetList.push({uid: k, name: v.name, beliefId: v.beliefId, num: this.getBetOnFollowing(k)});
        total += this.getBetOnFollowing(k);
    }
    // total += this.preBattleInfoMap.size * initial;
    let ssMsg = {
        week: week,
        time: time,
        betList: betList,
        betNum: betNum,
        clubBetList: clubBetList,
        total: total,
        postList: postList
    };
    let session = {frontendId: this.app.getServerId()};
    let self = this;
    self.app.rpc.datanode.dataNodeRemote.updatePkMatchData(session, ssMsg, function (err) {
        if(!!err)
        {
            logger.error("chairmanMatchService statisticalData updateChMatchData err", err, ssMsg);
        }
        cb(null);
    });
};
//更新玩家荣誉墙
peakMatchService.prototype.updataHonorData = function (playerList, type, cb)
{
    //按gid分
    let map = new Map();
    let gidPlayerList = [];
    for(let i in playerList)
    {
        if(map.has(playerList[i].gid))
        {
            let list = map.get(playerList[i].gid);
            list.push(playerList[i]);
        }
        else
        {
            let list = [];
            list.push(playerList[i]);
            map.set(playerList[i].gid, list);
        }
    }
    for(let [k, v] of map)
    {
        gidPlayerList.push(v);
    }
    let self = this;
    async.eachSeries(gidPlayerList, function (playerList, callback) {
        if(playerList.length > 0)
        {
            let gid = playerList[0].gid;
            let session = {frontendId: self.app.getServerId(), toServerId: gid};
            if(type === commonEnum.HONOR_DISPOSE_TYPE.JOIN_NUM)//加参赛次数
            {
                let msg = {playerList: playerList, type: commonEnum.HONOR_WALL_TYPE.PEAK};
                self.app.rpc.game.entryRemote.updateHonorWallJoinNum(session, msg, function (err) {
                    if (err !== Code.OK) {
                        logger.error("updateHonorWallJoinNum err: ", err, commonEnum.HONOR_WALL_TYPE.PEAK);
                        callback(null);
                        return;
                    }
                    callback(null);
                });
            }
            else if(type === commonEnum.HONOR_DISPOSE_TYPE.DATA)//更新赛季数据
            {
                //playerList[i] = {uid, gid, seasonId, rank}
                self.app.rpc.game.entryRemote.updateHonorWallData(session, playerList, commonEnum.HONOR_WALL_TYPE.PEAK, function (err) {
                    if (err !== Code.OK) {
                        logger.error("updateHonorWallData peak err: ", err, commonEnum.HONOR_WALL_TYPE.PEAK);
                        callback(null);
                        return;
                    }
                    callback(null);
                });
            }
        }
        else
        {
            callbcak(null);
        }
    }, function (err) {
        cb()
    });
};