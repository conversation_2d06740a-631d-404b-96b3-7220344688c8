/**
 * Idea and Persist
 * Created by sea on 2019/12/16.
 */
let logger = require('pomelo-logger').getLogger('pomelo', __filename);
let EventEmitter = require('events').EventEmitter;
let util = require('util');
let Code = require('../../../../shared/code');
let utils = require('../../util/utils');
let TimeUtils = require('../../util/timeUtils');
let Association = require("../entities/association");
let async = require('async');
let dataApi = require('../../util/dataApi');
let commonEnum = require('../../../../shared/enum');
module.exports.create = function(app, dbclient){
    return new AssociationService(app, dbclient);
};

let AssociationService = function(app, dbclient, cb){
    EventEmitter.call(this);
	this.app = app;
	this.allAssociation = new Map();                  //所有协会 协会id->对象
	this.allAssociationNameToId = new Map();          //key:协会名->value:协会id
	this.associationDao = require("../../dao/associationDao").create(dbclient);
};

util.inherits(AssociationService, EventEmitter);

AssociationService.prototype.init = function() {
	logger.info('----------AssociationService init -------------');
	this.app.event.on("start_all", afterAllServerStartUp.bind(null, this));
};


let afterAllServerStartUp = function(self) {
	// let id = 10086;
	self.loadAssociationData();
};

AssociationService.prototype.loadAssociationData = function() {
	let self = this;
	self.associationDao.readWholeAssociation(function(res){
		if(!res) {
			return;
		}

		for(let i in res) {
			let associationId = res[i].associationId;
			let associationName = res[i].associationData.associationName;
			let association = new Association(associationId);
			association.initByDB(res[i].associationData);
			self.allAssociation.set(associationId, association);
			self.allAssociationNameToId.set(associationName, associationId);
		}
	});
}

/**
 * 获取一个协会id
 */
AssociationService.prototype.randAssociationId = function() {
	let num1 = Math.floor(Math.random()*10) + 1;
	let num2 = Math.floor(Math.random()*100) + 1;
	let num3 = Math.floor(Math.random()*1000) + 1;
	let str = num1.toString() + num2.toString() + num3.toString()
	let id  = Number(str);
	if(!this.allAssociation.has(id)) {
		return id;
	}
	return this.randAssociationId();
}

AssociationService.prototype.createAssociation = function(playerId, msg, cb) {
	if(!playerId) {
		cb(Code.FAIL);
		return;
	}

	let name = msg.name;
	let faceId = msg.faceId;
	let gid = msg.gid;
	let rate = msg.rate;
	let faceUrl = msg.faceUrl;
	let playerName = msg.playerName;
	let frontendId = msg.frontendId;
	let sessionId = msg.sessionId;
	//测试
	faceId = 1;

	//要创建的协会是否已存在
	if(this.allAssociationNameToId.has(name)) {
		cb(Code.ASSOCIATION.ASSOCIATION_EXIST_FAIL);
		return;
	}

	let associationId = this.randAssociationId();
	let association = new Association(associationId);
	association.newAssociation(playerId,playerName, name, faceId,  rate, gid, faceUrl, frontendId, sessionId, associationId);
	this.allAssociation.set(associationId, association);
	this.allAssociationNameToId.set(name, associationId);
	this.saveAssociation(associationId);
	cb(Code.OK);
}

AssociationService.prototype.exitAssociation = function(playerId, msg, cb) {
	if(!playerId) {
		cb(Code.FAIL);
		return;
	}

	let leagueName = msg.leagueName;
	let associationId= this.allAssociationNameToId.get(leagueName);
	let association = this.allAssociation.get(associationId);
	if(!association) {
		return cb(Code.FAIL);
	}

	let ret = association.exitAssociation(playerId);
	if (ret.code !== Code.OK) {
		return cb(ret.code);
	}

	let self = this;
	if(ret.pos === 1) {
		async.eachSeries(association.allPlayer, function (msg, callback) {
			let session = {frontendId: self.app.getServerId(), toServerId: msg.gid};
			self.app.rpc.game.entryRemote.notifyPlayerLeagueDissolve(session, msg.playerId, function (err) {
				if (err != Code.OK) {
					logger.error("notifyPlayerLeagueDissolve: ", err);
					callback(err);
					return;
				}
				callback(null);
			});
		}, function (err) {
			logger.error("删除协会成功--------------")
			//协会解散 删除聊天数据
			let dataNodeService = self.app.get("dataNodeService");
			let msg = {associationId: associationId};
			dataNodeService.delAssociationChatMsgToDb(msg);
			self.removeAssociation(associationId);
			cb(Code.OK);
		})
	}else {
		cb(Code.OK);
	}
}

AssociationService.prototype.getAllAssociation = function(playerId, msg, cb) {
	if(!playerId) {
		cb(Code.FAIL);
		return;
	}
	let allAssociation = [];
	for(let [k, v] of this.allAssociation) {
		let association = this.allAssociation.get(k);
		let associationInfo = association.makeClientAllAssociationInfo();
		if(JSON.stringify(associationInfo) !== "{}") {
			allAssociation.push(associationInfo);
		}
	}
	//排序
	allAssociation.sort(this.comp_func("level"));
	cb(Code.OK, allAssociation);
}


AssociationService.prototype.comp_func = function(param) {
	return (obj1, obj2) => {
		let val1 = obj1[param];
		let val2 = obj2[param];
		if(val1 !== val2) {
			if(val1 > val2) {
				return 1;
			} else if(val1 < val2) {
				return -1;
			}
		}
		return 0;
	}
}

AssociationService.prototype.getAssociationInfo = function(playerId, msg, cb) {
	if(!playerId) {
		cb(Code.FAIL, {});
		return;
	}

	logger.error("ssssssssssssssssssssssssss")
	let leagueName = msg.leagueName;
	let associationId = this.allAssociationNameToId.get(leagueName);
	let association = this.allAssociation.get(associationId);
	if(!association) {
		return cb(Code.FAIL, {});
	}
	logger.error("wwwwwwwwwwwwwwwwwwwww")
	let ret = association.getAssociationInfo();
	if(ret.code !== Code.OK) {
		cb(ret.code, ret.associationInfo);
		return;
	}
	this.saveAssociation(associationId);
	cb(Code.OK, ret.associationInfo);
}

AssociationService.prototype.updatePlayerStatus = function(playerId, msg, cb) {
	let leagueName = msg.leagueName;
	let type = msg.type;
	let associationId= this.allAssociationNameToId.get(leagueName);
	let association = this.allAssociation.get(associationId);
	if(!association) {
		return cb(Code.FAIL);
	}
	association.updatePlayerStatus(playerId, type);
	this.saveAssociation(associationId);
	cb(Code.OK);
}

AssociationService.prototype.updatePlayerSessionId = function(playerId, msg, cb) {
	let leagueName = msg.leagueName;
	let frontendId = msg.frontendId;
	let sessionId = msg.sessionId;
	let associationId= this.allAssociationNameToId.get(leagueName);
	let association = this.allAssociation.get(associationId);
	if(!association) {
		return cb(Code.FAIL);
	}
	association.updatePlayerSessionId(playerId, frontendId, sessionId);
	this.saveAssociation(associationId);
	cb(Code.OK);
}

AssociationService.prototype.updatePlayerExp = function(playerId, msg, cb) {
	let leagueName = msg.leagueName;
	let num = msg.num;
	let associationId= this.allAssociationNameToId.get(leagueName);
	let association = this.allAssociation.get(associationId);
	if(!association) {
		return cb(Code.FAIL);
	}
	association.updatePlayerExp(playerId, num);
	this.saveAssociation(associationId);
	cb(Code.OK);
}

AssociationService.prototype.updateAssociationStrength = function(playerId, msg, cb) {
	let value = msg.value;
	let leagueName = msg.leagueName;
	let associationId= this.allAssociationNameToId.get(leagueName);
	let association = this.allAssociation.get(associationId);
	if(!association) {
		return cb(Code.FAIL);
	}
	association.updateAssociationStrength(playerId, value);
	this.saveAssociation(associationId);
	cb(Code.OK);
}

AssociationService.prototype.getAssociationPlayerList = function(playerId, msg, cb) {
	if(!playerId) {
		cb(Code.FAIL);
		return;
	}

	let associationId = msg.associationId;
	let association = this.allAssociation.get(associationId);
	if(!association) {
		return cb(Code.FAIL);
	}
	let ret = association.getAssociationPlayerList();
	if(ret.code	!== Code.OK) {
		cb(ret.code, ret.playerList);
		return;
	}
	this.saveAssociation(associationId);
	cb(Code.OK, ret.playerList);
}

AssociationService.prototype.getAssociationPlayerListByName = function(playerId, msg, cb) {
	if(!playerId) {
		cb(Code.FAIL, [], 0);
		return;
	}

	let leagueName = msg.associationName;
	let associationId= this.allAssociationNameToId.get(leagueName);
	let association = this.allAssociation.get(associationId);
	if(!association) {
		return cb(Code.FAIL, [], 0);
	}
	let ret = association.getAssociationPlayerListByName();
	if(ret.code	!== Code.OK) {
		cb(ret.code, ret.playerList, associationId);
		return;
	}
	cb(Code.OK, ret.playerList, associationId);
}

AssociationService.prototype.getAssociationIdByLeagueName = function(playerId, msg, cb) {
	if(!playerId) {
		cb(Code.FAIL, 0);
		return;
	}

	let leagueName = msg.associationName;
	let associationId= this.allAssociationNameToId.get(leagueName);
	let association = this.allAssociation.get(associationId);
	if(!association) {
		return cb(Code.FAIL, 0);
	}
	cb(Code.OK, associationId);
}

AssociationService.prototype.joinAssociation = function(playerId, msg, cb) {
	if(!playerId) {
		cb(Code.FAIL);
		return;
	}

	let associationId = msg.associationId;
	let playerName = msg.playerName;
	let gid = msg.gid;
	let faceUrl = msg.faceUrl;
	let strength = msg.strength;
	let frontendId = msg.frontendId;
	let sessionId = msg.sessionId;
	let association = this.allAssociation.get(associationId);
	if(!association) {
		return cb(Code.FAIL);
	}

	let ret = association.joinAssociation(playerId, playerName, gid, faceUrl, strength, frontendId, sessionId);
	if(ret.code !== Code.OK) {
		return cb(ret.code);
	}
	this.saveAssociation(associationId);
	return cb(ret.code);
}

AssociationService.prototype.removeJoinAssociation = function(playerId, msg, cb) {
	if(!playerId) {
		cb(Code.FAIL);
		return;
	}

	let associationId = msg.associationId;
	let association = this.allAssociation.get(associationId);
	if(!association) {
		return cb(Code.FAIL);
	}
	let ret = association.removeJoinAssociation(playerId);
	if(ret.code !== Code.OK) {
		return cb(ret.code);
	}
	this.saveAssociation(associationId);
	return cb(Code.OK);
}

AssociationService.prototype.agreeJoinAssociation = function(playerId, msg, cb) {
	if(!playerId) {
		cb(Code.FAIL);
		return;
	}

	let associationId = msg.associationId;
	let agreeId = msg.agreeId;
	let type = msg.type;
	let association = this.allAssociation.get(associationId);
	if(!association) {
		return cb(Code.FAIL);
	}

	let ret = association.agreeJoinAssociation(playerId, agreeId);
	if(ret.code !== Code.OK) {
		return cb(ret.code);
	}
	let self = this;
	//1同意  2拒绝
	if(type === 1) {
		//检查玩家是否已加入其他协会
		let session = {frontendId: self.app.getServerId(), toServerId: ret.gid};
		let msg = {playerId: agreeId, leagueName: association.associationName};
		self.app.rpc.game.entryRemote.playerIsJoinAssociation(session, msg, function (err) {
			if(err !== Code.OK) {
				//删除
				logger.error("玩家已加入其他协会-------1111----------")
				//删除申请列表里面得人
				association.deleteApprovalListByPlayerId(agreeId);
				return cb(Code.ASSOCIATION.JOIN_OTHER_FAIL);
			}

			//检查玩家是否在线
			self.app.rpc.game.entryRemote.playerIsOnline(session, msg, function (err, strength, faceUrl) {
				logger.error("同意玩家加入协会-----------------", err, strength, faceUrl);
				//加入协会
				let playerInfo = {};
				playerInfo.playerId = agreeId;                    //玩家UID
				playerInfo.playerName = ret.playerName;               //玩家名字
				playerInfo.isOnLine = 1;                          //是否在线
				playerInfo.leaveTime = TimeUtils.now();           //离线时间
				playerInfo.exp = 0;                               //玩家活跃
				playerInfo.contribute = 0;                        //玩家贡献
				playerInfo.pos = 3;                               //玩家职位     1会长 2副会长 3普通成员
				playerInfo.strength = strength;                   //玩家实力
				playerInfo.faceUrl = faceUrl;                     //玩家头像
				playerInfo.gid = ret.gid;                             //玩家gid
				playerInfo.frontendId = 0;
				playerInfo.sessionId = 0;
				if(err !== Code.OK) {
					playerInfo.isOnLine = 0;                      //是否在线
				}
				//删除申请列表里面得人
				association.deleteApprovalListByPlayerId(agreeId);
				association.allPlayer.push(playerInfo);
				self.saveAssociation(true);
				cb(Code.OK);
			});
		});
	} else if(type === 2) {  //拒绝
		//删除申请列表里面得人
		association.deleteApprovalListByPlayerId(agreeId);
		let session = {frontendId: self.app.getServerId(), toServerId: ret.gid};
		let msg = {playerId: agreeId, associationId: associationId};
		//更新玩家申请列表
		self.app.rpc.game.entryRemote.updatePlayerApplyList(session, msg, function (err) {
			//删除
			cb(Code.OK);
		});
	}
}

AssociationService.prototype.getApprovalList = function(playerId, msg, cb) {
	if(!playerId) {
		cb(Code.FAIL, []);
		return;
	}

	let associationId = msg.associationId;
	let association = this.allAssociation.get(associationId);
	if(!association) {
		return cb(Code.FAIL, []);
	}
	let ret = association.getApprovalList( associationId);
	this.saveAssociation(associationId);
	return cb(Code.OK, ret.approvalList);
}

AssociationService.prototype.changeAssociationPosition = function(playerId, msg, cb) {
	if(!playerId) {
		cb(Code.FAIL);
		return;
	}

	let associationId = msg.associationId;
	let enforcedId = msg.enforcedId;
	let changePos = msg.changePos;
	let association = this.allAssociation.get(associationId);
	if(!association) {
		return cb(Code.FAIL);
	}
	let ret = association.changeAssociationPosition(playerId, enforcedId, changePos);
	if(ret !== Code.OK) {
		return cb(ret);
	}
	this.saveAssociation(associationId);
	return cb(ret);
}

AssociationService.prototype.modifyNotice = function(playerId, msg, cb) {
	if(!playerId) {
		cb(Code.FAIL);
		return;
	}

	let associationId = msg.associationId;
	let content = msg.content;
	let association = this.allAssociation.get(associationId);
	if(!association) {
		return cb(Code.FAIL);
	}
	association.modifyNotice(content);
	this.saveAssociation(associationId);
	return cb(Code.OK);
}


AssociationService.prototype.saveAssociation = function(associationId) {
	let association = this.allAssociation.get(associationId);
	if(!association) {
		return;
	}
	utils.funcSave(this.app, true, "associationSync.saveAssociation",associationId, association)
}

AssociationService.prototype.removeAssociation = function(associationId) {
	let association = this.allAssociation.get(associationId);
	let leagueName = association.associationName;
	if(!association) {
		return;
	}
	utils.funcSave(this.app, true, "associationSync.removeAssociation",associationId, association)
	this.allAssociation.delete(associationId);
	this.allAssociationNameToId.delete(leagueName);

}

