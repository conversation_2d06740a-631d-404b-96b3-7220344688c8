/**
 * Created by shine on 2015/3/20.
 */
var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var fs = require('fs');
var filterStr = require('../../util/filterstr');
var debugConfig = require('../../../config/debugConfig');

var dataApi = require('../../util/dataApi');
var utils = require('../../util/utils');
var commonEnum = require('../../../../shared/enum');
var calc = require('../../util/calc');
var Code = require('../../../../shared/code');
var timeUtils = require('../../util/timeUtils');
var Heros = require('./heros');
var Bag = require('./bag');
var Tasks = require('./tasks');
var TeamFormations = require('./teamFormations');
var LeagueCopy =  require('./leagueCopy');
var Item = require("./item");
var Email = require("./email");
var Scout = require("./scout");
var FootballGround = require("./footballGround");
var BusinessMatch = require("./businessMatch");
var TrophyCopy = require("./trophyCopy");
var Trainer = require("./trainer");
var Follow = require("./follow");
var Store = require("./store");
var SevenDaySign = require("./sevenDaySign");
var NewPlayerSign = require("./newPlayerSign");
var EveryDaySign = require("./everyDaySign");
var VipShop = require("./vipShop");
var SeasonStore = require("./seasonStore");
var Act = require("./act");
var OfflineEvent = require("./offlineEvent");
var LimitStore = require("./limitStore");
var NewerGuide = require("./newerGuide");
var WorldCup = require("./worldCup");
var Relay = require("./relay");
var middleEastCup = require("./middleEastCup");
var gulfCup = require("./gulfCup");
var MLS = require("./MLS");
var EveryDayEnergy = require("./everyDayEnergy");
let NewerTask = require("./newerTask");
let ExchangeHall = require("./exchangeHall");
let Sign = require('./sign');
let CommonActivity = require('./commonActivity');
let BeliefSkill = require('./beliefSkill');
let honorWall = require("./honorWall");

var Player = function(playerId) 
{
	this.playerId = playerId;
	this.openId = "";						//平台外部Uid
	this.name = "";							//账号名字
	//this.teamName = "";						//球队名字 (废弃)
	this.avatar = "";						//头像(from 懂球帝)
	this.level = 1;
	// this.exp = 0;                          //当前经验
	// this.allExp = 0;                       //总经验
	this.faceIcon = 10600011;					//头像id
	this.faceUrl = "";							//Dqd用户头像Url
	this.fame = 0;								//声望
	this.allFame = 0;	                        //总声望
	this.worldCoin = 0;                         //世界boss币
	this.chip = 0;                              //合约碎片
	this.integral = 0;							//转播积分

	//外网配置
	this.cash = 10000;								//欧元
	this.gold = 0;									//金币
	this.isFristGetReward = 0;                      //是否第一次领取1亿欧元奖励
	//私有配置 debug
	// this.cash = 1000000;								//欧元
	// this.gold = 1000000;								//金币
	this.energy = dataApi.allData.data["SystemParam"][commonEnum.ENERGY_INFO.energyInit].Param;						//体力
	this.fieldLevel = 0;					  //球场等级
	this.qualified = 0;
	this.trophy = 0;                          //奖杯
	this.league = "";                         //所属联盟
	this.vip = 0;                             //vip
	this.vipExp = 0;
	this.isFirstRecharge = [];				  //首冲状态列表
	this.firstRechargeRefreshTime = 0;		  //首冲重置时间
	this.totalRecharge = 0;					  //累计充值
	this.clubFaceIcon = [2110146, 2110026,2110191, 2110036, 2110103];                  //俱乐部头像
	this.countryFaceIcon = [];                //国家队头像

	this.buyEnergyCount = 1;               //购买体力次数
	this.buyTime = 1;                      //购买体力时间
	this.freeTime = 1;                 	   //免费领取体力时间
	this.firstFree = 0;                    //第一次免费领取体力
	this.secondFree = 0;                   //第二次免费领取体力

	this.trainer = new Trainer(this);    //教练
	this.heros = new Heros(this);		//球员列表
	this.bag = new Bag(this); 				//道具：皮肤碎片、角色碎片、礼包、箱子、皮肤、角色
	this.teamFormations = new TeamFormations(this); //阵型列表
	this.loginTime = 0;
	this.leaveTime = 0;
	this.createTime = 0;                 //创角时间
	this.activeDay = 0;					//活跃天数
	this.recentActiveDay = 0;			//近期活跃天数 (中间一天没有登录就清零)
	this.ip = "";
	this.frontendId = null;
	this.sessionId = null;
	this.beliefId = 0;                  //信仰Id

	this.isRobot = 0;                      //是否为机器人
	this.creatRoleStep = commonEnum.CREATE_ROLE_STEP.FINISHED_STEP;     //创角步骤(不包括在新手引导)
	this.isNewer = 0;                      //是否为新手玩家

	//口香糖
	this.continuedBuffStartTime = 0; //持续buff开始时间
	this.continuedBuffEndTime   = 0; //持续buff结束时间
	this.lastBuffUpdateTime     = 0; //上一次更新buff的时间
	this.alreadyAddEnergyCount  = 0; //已添加体力的次数

	//兑换码 用来限制玩家只能领取同一批次的兑换码
	this.deemCodeList = new Map();   //Group => CodeId

	//for temporary 临时变量,不存入数据库
	this.battleRoomId = 0;

	//to do
	//this.friends = new Friends(playerId);			  //好友系统
	this.email = new Email(this);					  //邮件
	//this.equips = new Equips(playerId);		      //装备
	this.tasks = new Tasks(this);					  //任务
	//this.family = new Family(playerId);		   	  //家族
	this.leagueCopy = new LeagueCopy(this);     	  //关卡 PVE推图
	this.item = new Item(this);                       //物品
	this.scout = new Scout(this);               	  //球探
	this.footballGround = new FootballGround(this);   //球场
	this.businessMatch = new BusinessMatch(this);     //商业赛
	this.trophyCopy    = new TrophyCopy(this);        //杯赛
	this.follow        = new Follow(this);            //关注
	this.store 		   = new Store(this);             //商城
	this.vipShop       = new VipShop(this);           //vip商店
	this.act 	       = new Act(this);               //活动
	this.newPlayerSign = new NewPlayerSign(this);     //新手签到
	this.sevenDaySign = new SevenDaySign(this);       //七天签到
	this.everyDaySign = new EveryDaySign(this);       //每日签到
	this.seasonStore = new SeasonStore(this);         //赛季商城
	this.offlineEvent  = new OfflineEvent(this);      //离线事件
	this.limitStore = new LimitStore(this);           //限时商城
	this.newerGuide = new NewerGuide(this);           //新手引导
	this.worldCup = new WorldCup(this);               //世界杯
	this.middleEastCup = new middleEastCup(this);	  //中东杯
	this.gulfCup = new gulfCup(this);	  			  //海湾杯
	this.MLS = new MLS(this);	  			  		  //美职联
	this.everyDayEnergy = new EveryDayEnergy(this);   //每日体力
	this.newerTask = new NewerTask(this);             //新手任务
	this.relay = new Relay(this);      				  //赛事转播
	this.exchangeHall = new ExchangeHall(this);       //兑换大厅
	this.sign = new Sign(this);                       //签到送精力
	this.commonActivity = new CommonActivity(this);   //精力回馈  通用活动
	this.beliefSkill = new BeliefSkill(this);         //信仰技能
	this.reExchangeHallTime = 0;                      //兑换大厅刷新时间
	this.exchangeList = [];                           //兑换列表
	this.honorWall = new honorWall(this);			  //荣耀墙
	//联赛数据
	this.leagueEnrollTipsTime = 0;				//联赛发送报名提示框时间记录
	//每天聊天次数
	this.chatNum = 0;				//每天世界聊天次数
	this.lastChatTime = 0;			//最后一次聊天时间
	this.isBuyedCoach = 0;          //朴教练活动  0是  1不是
	this.isEnergyFull = 0;          //体力是否满  0:满   1:不满
	this.energyRecoverTime = timeUtils.now() / 1000;     //恢复时间
	this.leagueList = [];               //玩家申请协会列表
	this.exchangeNumList = [];          //兑换大厅刷新次数
	this.associationSponsorReward = []; //协会赞助
	this.beliefChangeRecord = [];         //信仰更改记录
	this.honor = 0;    //教练荣誉
	this.beliefNum = 0;    //个人信仰值
	this.isShowTactics = 0;             //战术 0显示  1不显示
	this.lockBelief = false;	//是否可以更改信仰
	this.buyBestFootballerNum = 0;     //最佳十一人购买次数
	this.bestFootballerTime = 0; //最佳11人开始时间
	this.beliefLiveness = 0;       //信仰活跃度   通过信仰任务获得
	this.reChangeBeliefTime = 0;         //上次修改信仰时间
	this.regTime = 0;                	//注册时间
};

module.exports = Player;
util.inherits(Player, EventEmitter);

//初始化模块数据 (新建出生时调用)
Player.prototype.bornInit = function(msg) 
{
	//账号信息初始化
	this.creatRoleStep = commonEnum.CREATE_ROLE_STEP.WATCH_RECORD;
	this.openId = msg.openId || "";						//平台外部Uid
	this.name = msg.name || "";
	this.avatar = msg.avatar || 0;
	this.initByConfig();
	this.bag.checkBookMarkInitDB();	//初始化背包
	this.trophyCopy.initTrophyCopyData();	//初始化杯赛数据
	this.vipShop.checkGoodsData();          
	this.act.loadGlobalAct();               //活动存储数据

	//出生需要添加的物品
	// this.initAddItems();

	this.saveItem();
	this.saveBag();
	this.saveLeagueCopy();
};

Player.prototype.initByConfig = function() 
{
	//角色出生时，策划表格配置的初始化属性(策划还未配置)
	this.initFirstRechargeState();
};

Player.prototype.initAddItems = function()
{
	//角色出生时，添加战斗跳过卡
	this.bag.addItem(dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.BATTLE_SKIP_ITEM].Param, 1);
};

Player.prototype.initFirstRechargeState = function() {
	if(!this.isFirstRecharge) {
		this.isFirstRecharge = [];
	}
	for(let id in dataApi.allData.data["Recharge"]) {
		this.isFirstRecharge.push(1);
	}
};

Player.prototype.initAfterLogin = function()
{
	this.leagueCopy.initAfterLogin();
	this.tasks.resetSignTask();
	this.tasks.resetTask();
	this.tasks.resetGiftTask();
	this.tasks.resetBeliefTask();
	this.tasks.checkEveryDayRechargeNum();
	// this.heros.checkHeroLeaveTime();
	this.heros.checkHeroTreatyDay();
	this.leagueCopy.checkTaskIsFinish();
	this.heros.fixAdvanceStage();
	this.heros.checkHeroSkill();
	this.tasks.fixTaskData();
	this.commonActivity.checkActivityIsTimeout();
	this.trainer.fixTrainer();
	this.footballGround.fixBallHandbook();
};

//DB-Cache数据初始化
Player.prototype.initByDB = function(doc) {
	this.name = doc.name || "";
	this.openId = doc.openId || "";
	this.avatar = doc.avatar || "";
	//this.teamName = doc.teamName|| "";
	this.level = doc.level || 1;
	// this.exp = doc.exp || 0;
	this.faceIcon = doc.faceIcon || 0;					//头像
	this.faceUrl = doc.faceUrl || "";
	this.fame = doc.fame || 0;							//当前声望
	this.allFame = doc.allFame || 0;                   //总声望
	this.cash = doc.cash || 0;						//欧元
	this.gold = doc.gold || 0;					//充值货币
	this.energy = doc.energy || 0;						//体力
	this.trophy = doc.trophy || 0;                      //奖杯
	this.fieldLevel = doc.fieldLevel || 0;				//球场等级
	this.qualified = doc.qualified || 0;
	this.loginTime = doc.loginTime || 0;
	this.leaveTime = doc.leaveTime || 0;
	this.createTime = doc.createTime || 0;
	this.activeDay = doc.activeDay || 0;
	this.recentActiveDay = doc.recentActiveDay || 0;
	this.creatRoleStep = doc.creatRoleStep || 0;        //创角步骤
	this.isNewer = doc.isNewer || 0;                    //是否为新手玩家
	this.league = doc.league || "";                  //所属联盟
	this.vip = doc.vip || 0;
	this.vipExp = doc.vipExp || 0;
	this.isFirstRecharge = doc.isFirstRecharge || [];
	this.firstRechargeRefreshTime = doc.firstRechargeRefreshTime || 0;
	this.totalRecharge = doc.totalRecharge || 0;
	this.clubFaceIcon = doc.clubFaceIcon || [];                    //俱乐部头像
	this.countryFaceIcon = doc.countryFaceIcon || [];                 //国家队头像
	this.buyEnergyCount = doc.buyEnergyCount || 1;
	this.buyTime = doc.buyTime || 1;
	this.freeTime = doc.freeTime || 1;                 //免费领取体力时间
	this.firstFree = doc.firstFree || 0;                //第一次免费领取体力
	this.secondFree = doc.secondFree ||0;               //第二次免费领取体力
	this.continuedBuffStartTime = doc.continuedBuffStartTime || 0; //持续buff开始时间
	this.continuedBuffEndTime = doc.continuedBuffEndTime || 0; 	   //持续buff结束时间
	this.lastBuffUpdateTime = doc.lastBuffUpdateTime || 0;     	   //上一次更新buff的时间
	this.alreadyAddEnergyCount = doc.alreadyAddEnergyCount || 0;    //
	this.deemCodeList = this.deemCodeListToMap(doc.deemCodeList) || new Map();
	/*
	this.ip = "";
	this.frontendId = null;
	this.sessionId = null;
	*/
	// this.allExp = doc.allExp || 0;
	this.isRobot = doc.isRobot || 0;
	this.isFristGetReward = doc.isFristGetReward || 0;

	this.leagueEnrollTipsTime = doc.leagueEnrollTipsTime || 0;

	this.chatNum = doc.chatNum || 0;
	this.lastChatTime = doc.lastChatTime || 0;
	this.isBuyedCoach = doc.isBuyedCoach || 0;
	this.isEnergyFull = doc.isEnergyFull || 0;          //体力是否满  0:满   1:不满
	this.energyRecoverTime = doc.energyRecoverTime || timeUtils.now() / 1000;     //恢复时间
	this.worldCoin = doc.worldCoin || 0;
	this.integral = doc.integral || 0;
	this.chip = doc.chip || 0;
	this.reExchangeHallTime = doc.reExchangeHallTime || 0;
	this.exchangeList = doc.exchangeList || [];
	this.leagueList = doc.leagueList || [];
	this.exchangeNumList = doc.exchangeNumList || [];
	this.associationSponsorReward = doc.associationSponsorReward || [];
	this.beliefId = doc.beliefId || 0;
	this.beliefChangeRecord = doc.beliefChangeRecord || [];
	this.honor = doc.honor || 0;
	this.beliefNum = doc.beliefNum || 0;
	this.isShowTactics = doc.isShowTactics || 0;
	this.lockBelief = doc.lockBelief || false;
	this.buyBestFootballerNum = doc.buyBestFootballerNum || 0;
	this.bestFootballerTime = doc.bestFootballerTime || 0;
	this.beliefLiveness = doc.beliefLiveness || 0;
	this.reChangeBeliefTime = doc.reChangeBeliefTime || 0;
	this.regTime = doc.regTime || 0;

	return this;
};

//Cache-DB数据初始化
Player.prototype.toJSONforDB = function(){
	var doc = {
		openId: this.openId,
		name: this.name,
		avatar: this.avatar,
		level: this.level,
		faceIcon: this.faceIcon,
		faceUrl: this.faceUrl,
		fame: this.fame,
		cash: this.cash,
		gold: this.gold,
		energy: this.energy,
		trophy: this.trophy,
		fieldLevel: this.fieldLevel,
		loginTime: this.loginTime,
		leaveTime: this.leaveTime,
		createTime: this.createTime,
		activeDay: this.activeDay,
		recentActiveDay: this.recentActiveDay,
		qualified: this.qualified,
		creatRoleStep: this.creatRoleStep,
		isNewer: this.isNewer,
		isRobot: this.isRobot,                
		league: this.league,
		vip: this.vip,
		vipExp: this.vipExp,
		isFirstRecharge: this.isFirstRecharge,
		firstRechargeRefreshTime: this.firstRechargeRefreshTime,
		totalRecharge: this.totalRecharge,
		clubFaceIcon: this.clubFaceIcon,
		countryFaceIcon: this.countryFaceIcon,
		buyEnergyCount: this.buyEnergyCount,
		buyTime: this.buyTime,
		continuedBuffStartTime: this.continuedBuffStartTime,
		continuedBuffEndTime: this.continuedBuffEndTime,
		lastBuffUpdateTime: this.lastBuffUpdateTime,
		alreadyAddEnergyCount: this.alreadyAddEnergyCount,
		deemCodeList: this.deemCodeListToArr(this.deemCodeList),
		isFristGetReward: this.isFristGetReward,
		leagueEnrollTipsTime: this.leagueEnrollTipsTime,
		chatNum: this.chatNum,
		lastChatTime: this.lastChatTime,
		isBuyedCoach: this.isBuyedCoach,
		isEnergyFull: this.isEnergyFull,
		energyRecoverTime: this.energyRecoverTime,
		worldCoin: this.worldCoin,
		integral: this.integral,
		chip: this.chip,
		reExchangeHallTime: this.reExchangeHallTime,
		exchangeList: this.exchangeList,
		leagueList: this.leagueList,
		exchangeNumList: this.exchangeNumList,
		associationSponsorReward: this.associationSponsorReward,
		beliefId: this.beliefId,
		beliefChangeRecord: this.beliefChangeRecord,
		honor: this.honor,
		beliefNum: this.beliefNum,
		isShowTactics: this.isShowTactics,
		lockBelief: this.lockBelief,
		buyBestFootballerNum: this.buyBestFootballerNum,
		bestFootballerTime: this.bestFootballerTime,
		beliefLiveness: this.beliefLiveness,
		reChangeBeliefTime: this.reChangeBeliefTime,
		regTime: this.regTime
	};
	return doc;
};

//Cache-Client数据初始化
Player.prototype.toJSONforClient = function(){
	let freeChatNum = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.FreeChatTimes].Param - this.chatNum;
	if(freeChatNum < 0) freeChatNum = 0;
	let loginFlag = 0;
	//是否同一天
	if(!timeUtils.isToday(this.leaveTime))	{
		loginFlag = 1;
	}

	var doc = {
		name: this.name,
		level: this.level,
		fame: this.fame,
		faceIcon: this.faceIcon,
		faceUrl: this.faceUrl,
		cash: this.cash,
		gold: this.gold,
		energy: this.energy,
		trophy: this.trophy,         
		league: this.league,                          
		vip: this.vip,
		vipExp: this.vipExp,
		isFirstRecharge: this.isFirstRecharge,
		qualified: this.qualified,
		createTime: this.createTime,
		freeChatNum: freeChatNum,
		isBuyedCoach: this.isBuyedCoach,
		loginFlag: loginFlag,
		formationAct: this.getFormationAct(),
		worldCoin: this.worldCoin,
		integral: this.integral,
		beliefId: this.beliefId,
		honor: this.honor,
		beliefNum: this.beliefNum,
		isShowTactics: this.isShowTactics,
		beliefLiveness: this.beliefLiveness,
		playerId: this.playerId,
		buyTeamFormationGiftNum: this.commonActivity.getBuyTeamFormationGiftNum() || 0,
	};
	//logger.debug('------------ to Client Player info -------------',doc);
	return [doc];
};

Player.prototype.testGetConfig = function() {
	logger.error("player::::::::::::::", dataApi.allData.data["BestFootball"]);
}

Player.prototype.deemCodeListToMap = function(arr) {
    var map =  new Map();
    if (!arr)
    {
        return map;
    }
  
    for (var i in arr)
    {
       const object = arr[i];
       var groupId = object["groupId"];
       var codeId = object["codeId"];
       map.set(groupId, codeId);
    }
    return map;
};

Player.prototype.deemCodeListToArr = function(map) {
    var arr =  [];
    if (!map)
    {
        return arr;
    }
  
    for (var [k, v] of map)
    {
       let obj = {
		   groupId: k,
           codeId: v,
       };
       arr.push(obj);
    }
    return arr;
};

Player.prototype.addDeemCodeID = function(groupId, codeId) 
{
	if (!this.deemCodeList.has(groupId))
	{
		this.deemCodeList.set(groupId, codeId);
	}
};

Player.prototype.checkDeemCodeID = function(groupId) 
{
 	return this.deemCodeList.has(groupId);
};

Player.prototype.getDeemCodeID = function(groupId) 
{
	return this.deemCodeList.get(groupId);
};

Player.prototype.login = function(frontendId, sessionId, ip, avatar, name){
	this.frontendId = frontendId;
	this.sessionId = sessionId;
	this.ip = ip;
	//活跃统计
	let now = timeUtils.now();
	let passDay = timeUtils.dayInterval(this.loginTime);
	if(passDay > 0) {
		this.activeDay++;
		if(passDay > 1) {
			this.recentActiveDay = 1;
		}else {
			this.recentActiveDay++;
		}
	}
	this.loginTime = now;
	//记录注册时间
	if(!this.regTime) {
		this.regTime = now;
	}
	
	this.faceUrl = avatar;
	if(!!name) {
		this.name = name;
	}
	//检查首冲重置时间
	let todayDate = new Date();
	let refreshDateStr = todayDate.getFullYear()+'-'+(todayDate.getMonth()+1)+'-'
		+dataApi.allData.getSystemTableParam(commonEnum.TABLE_SYSTEM_PARAM.ResetDoubleRechargeDate)+' 00:00:00';
	let refreshTime = new Date(refreshDateStr).getTime();
	let fixTime = new Date("2020-06-02 00:00:00").getTime();
	logger.debug("reFreshDateStr: ",refreshDateStr, refreshTime, fixTime, now, this.firstRechargeRefreshTime);
	logger.debug("xxxx1111: ", this.firstRechargeRefreshTime < refreshTime, this.firstRechargeRefreshTime > fixTime, refreshTime > fixTime);
	if((this.firstRechargeRefreshTime === 0 && now >= refreshTime)
		|| (this.firstRechargeRefreshTime > 0 && this.firstRechargeRefreshTime < refreshTime && now > refreshTime &&
			((this.firstRechargeRefreshTime > fixTime) || refreshTime > fixTime))) {
		if(this.isFirstRecharge.length > 0) {
			let index = 0;
			for(let id in dataApi.allData.data["Recharge"]) {
				this.isFirstRecharge[index] = 1;
				index++;
			}
			this.firstRechargeRefreshTime = now;
			this.save();
		}
	}
};

Player.prototype.leave = function(){
	this.frontendId = null;
	this.sessionId = null;
	this.leaveTime = timeUtils.now();
};

Player.prototype.checkIsBuyCoachReward = function() {
	let itemCount = this.item.getItemCountByResId(12017);
	if(itemCount >= 1) {
		if(!this.isBuyedCoach) {
			this.isBuyedCoach = 1;
		}
		return true;
	}

	let trainer = this.trainer.getOneTrainerByResId(1018);
	if(JSON.stringify(trainer) !== "{}") {
		if(!this.isBuyedCoach) {
			this.isBuyedCoach = 1;
		}
		return true;
	}
	return false;
}

//购买朴教练
Player.prototype.buyCoachReward = function(){
	let ret = {};

	//检查是否已购买
	if(this.checkIsBuyCoachReward()) {
		ret.code = Code.BUY_FAIL;
		ret.isBuyedCoach = this.isBuyedCoach;
		return ret;
	}

	//购买所需货币
	let costMoney = dataApi.allData.data["SystemParam"][commonEnum.PLAYER.COACH].Param;
	if(!costMoney) {
		ret.code = Code.CONFIG_FAIL;
		ret.isBuyedCoach = this.isBuyedCoach;
		return ret;
	}

	//检查货币是否足够
	let isEnough = this.checkMoneyIsEnough(commonEnum.PLAY_INFO.gold, costMoney);
	if (!isEnough) {
		ret.code = Code.GOLD_FALL;
		ret.isBuyedCoach = this.isBuyedCoach;
		return ret;
	}
	//扣钱
	this.deductMoney(commonEnum.PLAY_INFO.gold, costMoney);
	//朴教练道具id 12017 数量 5
	this.bag.addItem(12016, 5);
	this.isBuyedCoach = 1;
	ret.code = Code.OK;
	ret.isBuyedCoach = this.isBuyedCoach;
	return ret;
};

//领取1亿欧元
Player.prototype.getFirstGiftReward = function(){
	//已经领取过了
	if(this.isFristGetReward) {
		return Code.GET_FAIL;
	}

	let cashNum = dataApi.allData.data["SystemParam"][commonEnum.PLAYER.CASH_REWRAD].Param;
	this.addResource(commonEnum.PLAY_INFO.cash, cashNum);
	this.isFristGetReward = 1;
	return Code.OK;
};

//获取信仰更改记录
Player.prototype.getBeliefChangeRecord = function(){
	// for(let i = this.beliefChangeRecord.length - 1; i >= 0; i--) {
		// if(timeUtils.dayInterval(this.beliefChangeRecord[i].mvTime) >= 30) {
			// this.beliefChangeRecord.splice(i, 1);
		// }
	// }
	return this.beliefChangeRecord;
};

//修改信仰  id 信仰id
Player.prototype.modifyBeliefChangeRecord = function(id){
	let ret = {code: Code.FAIL, beliefId: 0, mvTime: this.reChangeBeliefTime};
	let config = dataApi.allData.data["Belief"][id];
	if(!config) {
		ret.code = Code.CONFIG_FAIL;
		return ret;
	}

	//30天修改一次
	let needDay = dataApi.allData.data["SystemParam"][10101].Param;
	if(this.reChangeBeliefTime !== 0 && timeUtils.dayInterval(this.reChangeBeliefTime) < needDay) {
		ret.code = Code.WAR_OF_FAITH_CODE.TIME_FAIL;
		return ret;
	}

	let mvTime = timeUtils.now();
	let oldBeliefId = this.beliefId;
	//todo  道具使用
	let itemId = 21000;
	let itemNum = this.item.getItemCountByResId(itemId);
	let itemUid = this.item.getItemUidByResId(itemId);
	//检查物品是否足够
	if(itemNum < 1){
		ret.code = Code.ITEM_FAIL
		return ret;
	}

	let itemConfig = dataApi.allData.data['Item'][itemId];
	if(!itemConfig) {
		ret.code = Code.CONFIG_FAIL;
		return ret;
	}

	if(this.level < itemConfig.ItemParameters){
		ret.code = Code.LEVEL_FAIL;
		return ret;
	}

	this.item.delItem(itemUid, 1);
	let info = {
		oldBeliefId: oldBeliefId,
		newBeliefId: id,
		mvTime: mvTime
	}
	this.beliefChangeRecord.push(info);
	this.reChangeBeliefTime = mvTime;
	this.beliefId = id;
	ret.beliefId = id;
	ret.code= Code.OK;
	ret.mvTime = mvTime;
	return ret;
};

/*
------------------------------------------------------
//player
*/

/**
 * 获取个人信息
 */
Player.prototype.getPersonInfo = function() {
	let Info = {
		name: this.name,
		level: this.level,
		fame: this.fame,
		energy: this.energy,
		formationAct: this.getFormationAct(),
		league: this.league,
		faceIcon: this.faceIcon,
		faceUrl: this.faceUrl,
		vip: this.vip,
		vipExp: this.vipExp,
		beliefId: this.beliefId
	};
	return Info;
}

/**
 * 修改玩家姓名
 * @param name   玩家名字
 * @param resId  改名道具id
 */
Player.prototype.modifyPlayerName = function(name) {
	if(name === "" || !name) {
		return Code.FAIL;
	} else if(name === this.name) {
		return Code.FAIL
	}else if(name.length > 8) {   //汉字和字母长度都为1
		return Code.FAIL;
	}
	let content = dataApi.allData.data["keyword"];
	let scanner = new filterStr(content);
	// options = {quick:true};
	//检查名字是否包含敏感字
	let isFilter = scanner.isFilter(name)
	if (isFilter){
		return Code.PLAYER.NAME_FALL;
	} 
	// let itemCount = this.item.getItemCountByResId(resId);
	// let itemUid = this.player.item.getItemUidByResId(resId);   
	// if( itemCount < 1) {
	// 	return Code.FAIL;
	// }
	// //消耗改名卡
	//this.bag.removeItem(itemUid, itemCount);  
	this.name = name;
	return Code.OK;
}

/**
 * 获取当前使用阵容实力
 */
Player.prototype.getFormationAct = function() {
	//获取当前使用的阵容
	let curId = this.teamFormations.currTeamFormationId;
	if(curId === "" || !curId) {
		logger.error("-------getFormationAct----curId-----",curId);
		return 0;
	}

	let formation = this.teamFormations.getOneTeamFormation(curId);
	if(!formation){
		logger.error("-------getFormationAct--formation-------");
		return 0;
	}
	return Math.floor(formation.ActualStrength);
}

Player.prototype.getLeagueTFActualStrength = function()
{
	return this.teamFormations.getLeagueActualStrength()
};

Player.prototype.getLeagueTotalValue = function()
{
	return this.teamFormations.calcTeamValue(this.teamFormations.leagueTeamFormationId);
};

/**
 * 获取所属联盟
 */
Player.prototype.getLeague = function() {

	return this.league;
}

/**
 * 获取队微图标
 */
Player.prototype.getAllFaceIcon = function() {
	let allFaceIcon = 
	{
		clubFaceIcon: this.clubFaceIcon,                    //俱乐部头像
		countryFaceIcon: this.countryFaceIcon, 
	};
	return allFaceIcon;
}

/**
 * 添加队微图标
 * @param  resId  队微id
 */
Player.prototype.addFaceIcon = function(resId) {

	//todo 判断物品类型  查表
	if(type == 1){
		this.clubFaceIcon.push(unlockFaceIcon);
	} else {
		this.countryFaceIcon.push(unlockFaceIcon);
	}

}

/**
 * 修改头像
 * @param resId   头像ID
 */
Player.prototype.modifyFaceIcon = function(resId) {
	if(!resId) {
		return Code.FAIL;
	} 
	if(resId === this.faceIcon) {
		return Code.OK;
	}
	let config = dataApi.allData.data["TeamLogo"][resId];
	if (!config) {
		logger.error("faceIcon is not",config.ID);
		return;
	}
	this.faceIcon = resId;
	return Code.OK;
};

Player.prototype.getgold = function() 
{
	return this.gold;
};


/**
 * 战斗消耗体力
 */
// this.energyReTime = 0;                 //恢复体力时间点
// this.isFull = 0;                       //体力是否满    0:满   1:不满
Player.prototype.consumeEnergy = function(num)
{
	if (!num || num <= 0) {
		logger.error("consumeEnergy: the num is 0", num);
		return Code.PARAM_FAIL;

	}

	let energyInit = dataApi.allData.data["SystemParam"][commonEnum.ENERGY_INFO.energyInit].Param;
	if(!energyInit){
		return Code.CONFIG_FAIL;
	}
	// this.isEnergyFull = 0;          //体力是否满  0:满   1:不满
	// this.energyRecoverTime = timeUtils.now() / 1000;     //恢复时间
	this.subtractResource(commonEnum.PLAY_INFO.energy,num);
	if(this.energy < energyInit && this.isEnergyFull === 0){
		this.energyRecoverTime = Date.now() / 1000;
		this.isEnergyFull = 1;
	}
	this.save(true);
	this.upPlayerInfo([{type: commonEnum.PLAY_INFO.energy, value: this.energy}]);
	return Code.OK;
};

/**
 * 获取体力信息
 */
Player.prototype.getEnergyInfo = function() {
	let energyInit = dataApi.allData.data["SystemParam"][commonEnum.ENERGY_INFO.energyInit].Param;
	let recoverTime = dataApi.allData.data["SystemParam"][commonEnum.ENERGY_INFO.recoverTime].Param;
	let recoverNum = dataApi.allData.data["SystemParam"][commonEnum.ENERGY_INFO.recoverNum].Param;
	let ret = {};
	let sysTime = Date.now() / 1000;
	//已经满了就不用走下面的流程了
	if (this.energy >= energyInit){
		ret.code = Code.OK;
		ret.energyReTime = this.energyRecoverTime;
		ret.sysTime = sysTime;
		ret.energy = this.energy;
		return ret;
	}

	if(this.energyReTime !== 1){
		if (timeUtils.secInterval(this.energyRecoverTime) > recoverTime){
			let n = timeUtils.secInterval(this.energyRecoverTime) / recoverTime;
			let addNum = Math.floor(recoverNum * n);
			this.energy += addNum;
			this.energyRecoverTime += n * recoverTime;
		}
	}

	//最后一次超出后
	if (this.energy >= energyInit){
		this.energy = energyInit;
		this.isEnergyFull = 0;
	}

	ret.code = Code.OK;
	ret.energy = this.energy;
	ret.energyReTime = this.energyRecoverTime;
	ret.sysTime = sysTime;

	return ret;
}






// Player.prototype.getEnergyInfo = function()
// {
// 	let intervalTime = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.ContinuedRecoverTime].Param;
// 	let energyCount = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.ContinuedRecoverEnergyCount].Param;
// 	let continuedBuff = 0; //是有口香糖buffer
// 	let canAddCount = 0;   //在buff生效的时间段，总共可增加多少点体力
// 	let energyReTime = 0;  //客户端下一次拉取体力信息的时间 单位 s(秒)
// 	//intervalTime = 10;
//
// 	//检查附加的buff
// 	this.checkContinuedBuff();
//
// 	if (this.isExistBuff())
// 	{
// 		continuedBuff = 1;
// 		let currTime = timeUtils.now();
// 		let totalTimes = Math.floor( Math.floor((this.continuedBuffEndTime - this.continuedBuffStartTime)) / (intervalTime * 1000));
// 		let currTimes = Math.floor( Math.floor((currTime - this.continuedBuffStartTime)) / (intervalTime * 1000));
// 		let nextTimes = currTimes + 1;
// 		let diffTime = nextTimes * intervalTime * 1000
// 		energyReTime = Math.floor((this.continuedBuffStartTime + diffTime - currTime)/ 1000);
//
// 		// logger.info("getEnergyInfo: totalTimes, currTimes, nextTimes, diffTime, energyReTime", this.continuedBuffStartTime/1000,
// 		// totalTimes, currTimes, nextTimes, diffTime/ 1000, energyReTime);
//
// 		canAddCount = totalTimes * energyCount;
// 	}
//
// 	let ret = {};
// 	ret.code = Code.OK;
// 	ret.energy = this.energy;
// 	ret.energyReTime = energyReTime;
// 	ret.sysTime = Math.floor(timeUtils.now() / 1000);
// 	ret.continuedBuff = continuedBuff;
// 	ret.continuedBuffEndTime = this.continuedBuffEndTime ? Math.floor(this.continuedBuffEndTime / 1000) : 0;
// 	ret.alreadyAddCount = this.alreadyAddEnergyCount;
// 	ret.canAddCount = canAddCount;
// 	return ret;
// };

/**
 * 获取购买体力次数
 */
Player.prototype.getBuyEnergyCount = function() {
	//是否同一天
	if(!timeUtils.isToday(this.buyTime)){
		this.buyEnergyCount = 1;
		this.buyTime = Date.now();
	}
	return this.buyEnergyCount;
};

Player.prototype.checkEnergyIsFull = function()
{
	let energyInit = dataApi.allData.data["SystemParam"][commonEnum.ENERGY_INFO.energyInit].Param;
	if (this.energy >= energyInit){
		return true;
	}
	return false;
};

Player.prototype.addContinuedBuff = function()
{
	let duration = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.ContinuedRecoverDuration].Param;
	let currTime = timeUtils.now();
	//duration = 2 * 60;
	//logger.info("addContinuedBuff", duration, currTime);
	if (this.continuedBuffEndTime <= currTime) //过期
	{
		this.lastBuffUpdateTime = currTime;
		this.continuedBuffStartTime = currTime;
		this.continuedBuffEndTime = currTime + duration * 1000;
		logger.info("addContinuedBuff 1", this.lastBuffUpdateTime, this.continuedBuffStartTime, this.continuedBuffEndTime);
	}else //持续加成
	{
		//开始时间保持不变
		//结束时间加成
		this.continuedBuffEndTime = this.continuedBuffEndTime + duration * 1000;
		logger.info("addContinuedBuff 2", this.lastBuffUpdateTime, this.continuedBuffStartTime, this.continuedBuffEndTime);
	}
};

// Player.prototype.resetBuffTime = function()
// {
// 	if (this.continuedBuffStartTime !== 0)
// 	{
// 		this.continuedBuffStartTime = 0;
// 	}
//
// 	if (this.continuedBuffEndTime !== 0)
// 	{
// 		this.continuedBuffEndTime = 0;
// 	}
//
// 	if (this.lastBuffUpdateTime !== 0)
// 	{
// 		this.lastBuffUpdateTime = 0;
// 	}
//
// 	if (this.alreadyAddEnergyCount !== 0)
// 	{
// 		this.alreadyAddEnergyCount = 0;
// 	}
// };

// Player.prototype.isExistBuff = function()
// {
// 	if (this.continuedBuffEndTime === 0 && this.continuedBuffStartTime === 0 && this.lastBuffUpdateTime === 0)
// 	{
// 		return false;
// 	}
//
// 	return true;
// };

// Player.prototype.checkContinuedBuff = function()
// {
// 	let addValue = 0;
// 	let currEnergy = this.energy;
// 	let energyInit = dataApi.allData.data["SystemParam"][commonEnum.ENERGY_INFO.energyInit].Param;
// 	let intervalTime = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.ContinuedRecoverTime].Param;
// 	let energyCount = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.ContinuedRecoverEnergyCount].Param;
// 	//intervalTime = 10;
// 	let currTime = timeUtils.now();
// 	//logger.info("checkContinuedBuff", intervalTime, currTime, this.continuedBuffStartTime, this.continuedBuffEndTime);
//
// 	if (!this.isExistBuff())
// 	{
// 		// logger.info("checkContinuedBuff: not buffer");
// 		return;
// 	}
//
// 	if (this.continuedBuffEndTime <= currTime) //过期
// 	{
// 		//再看看还有几次体力
// 		if (!this.checkEnergyIsFull()) //未满
// 		{
// 			let times = Math.floor( Math.floor((this.continuedBuffEndTime - this.lastBuffUpdateTime)) / (intervalTime * 1000));
// 			if (times > 0) //还没有经过一个周期更新
// 			{
// 				 addValue = energyCount * times;
// 				if (addValue + currEnergy >= energyInit) //超过阈值
// 				{
// 					addValue = energyInit - currEnergy;
// 				}
//
// 				this.alreadyAddEnergyCount += addValue;
// 				this.addResource(commonEnum.PLAY_INFO.energy, addValue);
// 				this.upPlayerInfo([{type: commonEnum.PLAY_INFO.energy, value: this.energy}]); //体力变更通知
// 				//logger.info("checkContinuedBuff 1 : addResource_____________________", addValue) ;
// 			}
// 		}
//
// 		this.resetBuffTime();
// 		return;
// 	}
//
// 	//还在时间内
// 	let times = Math.floor( Math.floor((currTime - this.lastBuffUpdateTime)) / (intervalTime * 1000));
// 	//logger.info("checkContinuedBuff 2 : times", times);
// 	if (times > 0) //还没有经过一个周期更新
// 	{
// 		this.lastBuffUpdateTime = this.lastBuffUpdateTime + times * intervalTime * 1000;
// 		if (!this.checkEnergyIsFull()) //精力未满
// 		{
// 			addValue = energyCount * times;
// 			if (addValue + currEnergy >= energyInit) //超过阈值
// 			{
// 				addValue = energyInit - currEnergy;
// 			}
//
// 			this.alreadyAddEnergyCount += addValue;
// 			this.addResource(commonEnum.PLAY_INFO.energy, addValue);
// 			this.upPlayerInfo([{type: commonEnum.PLAY_INFO.energy, value: this.energy}]); //体力变更通知
// 			//logger.info("checkContinuedBuff 2: addResource_____________________", addValue);
// 		}
// 	}
//
// 	if (currTime >= this.continuedBuffEndTime)
// 	{
// 		this.resetBuffTime();
// 	}
// };

/**
 * 购买体力
 */
Player.prototype.buyEnergy = function() {
	//todo 读配置表	
	let ret = {code:Code.FAIL, buyEnergyCount: 0, energy: 0, gold: 0}
	let vipConfig = dataApi.allData.data["BuyEnergy"];
	let count = this.buyEnergyCount;
	let energyNum = dataApi.allData.data["EnergyTimes"][count].EnergyNum;
	let gold = dataApi.allData.data["EnergyTimes"][count].Gold;
	let energyInit = dataApi.allData.data["SystemParam"][commonEnum.ENERGY_INFO.energyInit].Param;
	if(!vipConfig || !energyNum || !gold || !energyInit){
		logger.error("config is fail~~~~~~~~~~");
		return ret;
	}

	let vipCount = 0;
	let vip = this.vip;	
	for( let i in vipConfig){
		if(vipConfig[i].VipLevel === vip){
			vipCount = vipConfig[i].Num;
			break;
		}
	}
	//是否同一天
	if(!timeUtils.isToday(this.buyTime)){
		this.buyEnergyCount = 1;
		this.buyTime = Date.now();
	}
	//是否有次数
	if (count > vipCount){
		logger.error("vipcount is fail~~~~~~~buyEnergy~~~~~~~~", count, vipCount)
		return ret;
	}
	//钱是否够
	if(!this.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, gold)){
		logger.error("gold is fail~~~~~~~buyEnergy~~~~~~~~", this.gold, gold)
		ret.code = Code.GOLD_FALL;
		return ret;
	}

	this.energy +=  energyNum;
	this.deductMoney(commonEnum.PLAY_INFO.gold, gold);
	this.buyEnergyCount += 1;
	if(this.energy >= energyInit && this.isEnergyFull === 1){
		this.isEnergyFull= 0;
	}

	ret.code = Code.OK;
	ret.buyEnergyCount = count;
	ret.energy = this.energy;
	ret.gold = this.gold;
	return ret;
}

/**
 * 领取体力奖励
 */
Player.prototype.getEnergyReward = function() {
	//读配置
	let ret = {code: Code.FAIL, isGet: 0, sysTime: 0};
	let bFirstReTime = dataApi.allData.data["SystemParam"][commonEnum.ENERGY_INFO.bFirstReTime].Param;
	let eFirstReTime = dataApi.allData.data["SystemParam"][commonEnum.ENERGY_INFO.eFirstReTime].Param;
	let bSecondReTime = dataApi.allData.data["SystemParam"][commonEnum.ENERGY_INFO.bSecondReTime].Param;
	let eSecondReTime = dataApi.allData.data["SystemParam"][commonEnum.ENERGY_INFO.eSecondReTime].Param;
	let freeEnergy = dataApi.allData.data["SystemParam"][commonEnum.ENERGY_INFO.freeEnergy].Param;

	if(!bFirstReTime || !eFirstReTime || !bSecondReTime || !eSecondReTime || !freeEnergy)
	{
		logger.error("getEnergyReward sysParam is fail!~~~~~~~~~~~", bFirstReTime, eFirstReTime, bSecondReTime, eSecondReTime);
		return ret;
	}
	//系统时间
	let sysTime = new Date().getHours();
	//不是同一天重置领取次数
	if (!timeUtils.isToday(this.freeTime)){
		this.freeTime = Date.now();
		this.firstFree = 0;
		this.secondFree = 0;
	}
	//第一次免费判断
	if(sysTime > bFirstReTime && sysTime < eFirstReTime && this.firstFree === 0){
		this.firstFree = 1;
		this.energy += freeEnergy;

		ret.code = Code.OK;
		ret.isGet = 1;
		ret.sysTime = sysTime;
		return ret;
	}
	//第二次免费判断
	if(sysTime > bSecondReTime && sysTime < eSecondReTime && this.secondFree === 0){
		this.secondFree = 1;
		this.energy += freeEnergy;

		ret.code = Code.OK;
		ret.isGet = 1;
		ret.sysTime = sysTime;
		return ret;
	}
	return ret;
}

//检查钱是否足够
Player.prototype.checkMoneyIsEnough= function(type, num) {
	if(type == commonEnum.PLAY_INFO.cash) {
		if (this.cash < num){
			return false;
		}
		return true;
	} else {
		if(this.gold < num) {
			return false;
		}
		return true;
	}
};

//扣钱
Player.prototype.deductMoney = function(type, num) {
	this.subtractResource(type, num);
	if(type === commonEnum.PLAY_INFO.cash) {
		this.upPlayerInfo([{type: commonEnum.PLAY_INFO.cash, value: this.cash}]);
	} else if(type === commonEnum.PLAY_INFO.gold) {
		this.upPlayerInfo([{type: commonEnum.PLAY_INFO.gold, value: this.gold}]);
	} else {
		logger.error("deductMoney type error:", type);
	}
};

//记录充值数量
Player.prototype.addRechargeNum = function (num) {
	if(!!num) {
	 	this.totalRecharge += num;
	}
}

/**
 * 添加各种资源
 */
Player.prototype.addResource = function (type, num, rechargeId) {
	if(!type){
		return Code.FAIL;
	}
	if (!rechargeId)
	{
		rechargeId = 0;
	}
	switch(type){
		case commonEnum.PLAY_INFO.level:
			this.level += num;
		break;
		case commonEnum.PLAY_INFO.exp:
			this.exp += num;
		break;
		case commonEnum.PLAY_INFO.energy:
			this.energy += num;
			this.recordSlog(this.playerId, commonEnum.STATIS_LOG_TYPE.ADD_ENERGY, [num, this.energy], {});
		break;
		case commonEnum.PLAY_INFO.cash:
			//let srcGold = this.cash;
			this.cash += num;
			//触发任务
			this.tasks.triggerTask(commonEnum.TARGET_TYPE.TWENTY_ONE, 0, 0, num);
			this.recordSlog(this.playerId, commonEnum.STATIS_LOG_TYPE.ADD_CASH, [num, this.cash], {});
			//logger.info("addResource", srcGold, this.cash, num);
		break;
		case commonEnum.PLAY_INFO.gold:
			// if (commonEnum.ACT_RECHARGE_ID.MONTH_CARD !== rechargeId) //月卡不添加球币
			// {
			this.gold += num;
			//触发任务
			this.tasks.triggerTask(commonEnum.TARGET_TYPE.TWENTY_TWO, 0, 0, num);
			this.recordSlog(this.playerId, commonEnum.STATIS_LOG_TYPE.ADD_GOLD, [num, this.gold], {});
			// }
			logger.info("addResource [gold]: ", this.gold, num);
			if (rechargeId > 0)
			{
				this.act.onCharge(num, rechargeId);
				this.tasks.addEveryDayRechargeNum(num);
			}
		break;
		case commonEnum.PLAY_INFO.vip:
			this.vip += num;
		break;
		case commonEnum.PLAY_INFO.trophy:
			this.trophy += num;
		break;
		case commonEnum.PLAY_INFO.fame:
			this.addExp(num);
			break;
		case commonEnum.PLAY_INFO.trainCount:
			this.footballGround.addTrainCount(num);
			break;
		case commonEnum.PLAY_INFO.scoutEnergy:
			this.scout.addScoutEnergyByPlayer(num);
			break;
		case commonEnum.PLAY_INFO.vipExp: 
			this.vipExp += num;
			break;
		case commonEnum.PLAY_INFO.worldCoin:
			this.worldCoin += num;
			break;
		case commonEnum.PLAY_INFO.chip:
			this.chip += num;
			break;
		case commonEnum.PLAY_INFO.integral:
			this.integral += num;
			break;
		case commonEnum.PLAY_INFO.honor:
			this.honor += num;
			break;
		case commonEnum.PLAY_INFO.beliefNum:
			this.beliefNum += num;
			this.recordSlog(this.playerId, commonEnum.STATIS_LOG_TYPE.ADD_BELIEF_NUM, [num, this.beliefNum], {});
			break;
		case commonEnum.PLAY_INFO.beliefLiveness:
			this.beliefLiveness += num;
			this.upPlayerInfo([{type: commonEnum.PLAY_INFO.beliefLiveness, value: this.beliefLiveness}]);
			this.recordSlog(this.playerId, commonEnum.STATIS_LOG_TYPE.ADD_BELIEFLIVENESS, [num, this.beliefLiveness], {});
			break;
		case commonEnum.PLAY_INFO.honorWallIntegral:
			this.honorWall.integral += num;
			//更新称号
			this.honorWall.checkTitleUpdata();
			break;
		default:
			logger.error("addResource error type: ", type);
			break;
	}
	this.save();
	return Code.OK;
};




//检查资源
//note: 此函数使用前请先使用checkResourceIsEnough检查是否足够
Player.prototype.subtractResource = function (type, num) {
	if(!type){
		return Code.FAIL;
	}

	switch(type){
		case commonEnum.PLAY_INFO.level:
			if (this.level < num)
			{
				this.leave = 0
			}else
			{
				this.level -= num;
			}
		break;
		case commonEnum.PLAY_INFO.exp:
			if (this.exp < num)
			{
				this.exp = 0;
			}else
			{
				this.exp -= num;
			}
		break;
		case commonEnum.PLAY_INFO.energy:
			if (this.energy < num)
			{
				this.energy = 0;
			}else
			{
				this.energy -= num;
				this.commonActivity.addEnergyVal(num);
				this.recordSlog(this.playerId, commonEnum.STATIS_LOG_TYPE.ENERGY_COST,[num, this.energy], {});
			}
		break;
		case commonEnum.PLAY_INFO.cash:
			if (this.cash < num)
			{
				this.cash = 0;
			}else
			{
				this.cash -= num;
			}
			this.tasks.triggerTask(commonEnum.TARGET_TYPE.TWENTY_SIX, 0, 0, num);
			this.recordSlog(this.playerId, commonEnum.STATIS_LOG_TYPE.USE_CASH,[num, this.cash], {});
		break;
		case commonEnum.PLAY_INFO.gold:
			if (this.gold < num)
			{
				this.gold = 0;
			}
			else
			{
				this.gold -= num;
			}
			this.act.onConsume(num);
			this.tasks.triggerTask(commonEnum.TARGET_TYPE.TWENTY_FIVE, 0, 0, num);
			this.recordSlog(this.playerId, commonEnum.STATIS_LOG_TYPE.USE_GOLD,[num, this.gold], {});
		break;
		case commonEnum.PLAY_INFO.vip:
			if (this.vip < num)
			{
				this.vim = 0;
			}else
			{
				this.vip -= num;
			}
		break;
		case commonEnum.PLAY_INFO.trophy:
			if (this.trophy < num)
			{
				this.trophy = 0;
			}else
			{
				this.trophy -= num;
			}
		break;
		case commonEnum.PLAY_INFO.fame:
				if (this.fame < num)
				{
					this.fame = 0;
				}else
				{
					this.fame -= num;
				}
			break;
		case commonEnum.PLAY_INFO.vipExp: 
			if (this.vipExp < num)
			{
				this.vipExp = 0;
			}
			else
			{
				this.vipExp-= num;
			}
			break;
		case commonEnum.PLAY_INFO.worldCoin:
			if (this.worldCoin < num)
			{
				this.worldCoin = 0;
			}
			else
			{
				this.worldCoin -= num;
			}
			break
		case commonEnum.PLAY_INFO.chip:
			if (this.chip < num)
			{
				this.chip = 0;
			}
			else
			{
				this.chip -= num;
			}
			break
		case commonEnum.PLAY_INFO.integral:
			if (this.integral < num)
			{
				this.integral = 0;
			}
			else
			{
				this.integral -= num;
			}
			break
		case commonEnum.PLAY_INFO.honor:
			if (this.honor < num)
			{
				this.honor = 0;
			}
			else
			{
				this.honor -= num;
			}
			break
		case commonEnum.PLAY_INFO.beliefNum:
			if (this.beliefNum < num)
			{
				this.beliefNum = 0;
			}
			else
			{
				this.beliefNum -= num;
				this.recordSlog(this.playerId, commonEnum.STATIS_LOG_TYPE.USE_BELIEF_NUM,[num, this.beliefNum], {});
			}
			break
		case commonEnum.PLAY_INFO.beliefLiveness:
			if (this.beliefLiveness < num)
			{
				this.beliefLiveness = 0;
			}
			else
			{
				this.beliefLiveness -= num;
				this.upPlayerInfo([{type: commonEnum.PLAY_INFO.beliefLiveness, value: this.beliefLiveness}]);
				this.recordSlog(this.playerId, commonEnum.STATIS_LOG_TYPE.USE_BELIEFLIVENESS,[num, this.beliefLiveness], {});
			}
			break
		default:

			break;
	}

	this.onConsume(type, num);
	return Code.OK;
};

//检查资源是否充足
Player.prototype.checkResourceIsEnough = function (type, num) 
{
	let retCode = false;
	if(!type){
		return false;
	}

	switch(type)
	{
		case commonEnum.PLAY_INFO.level:
			retCode =  this.level - num >= 0 ? true:false;
			break;
		case commonEnum.PLAY_INFO.fame:
			retCode =  this.fame - num >= 0? true:false;
			break;
		case commonEnum.PLAY_INFO.energy:
			retCode = this.energy - num >= 0? true:false;
			break;
		case commonEnum.PLAY_INFO.cash:
			retCode = this.cash - num >= 0 ? true:false;
			break;
		case commonEnum.PLAY_INFO.gold:
			retCode =  this.gold - num >= 0 ?true:false;
			break;
		case commonEnum.PLAY_INFO.vip:
			retCode = this.vip - num >= 0 ? true:false;
			break;
		case commonEnum.PLAY_INFO.trophy:
			retCode = this.trophy - num >= 0 ?true:false;
			break;
		case commonEnum.PLAY_INFO.worldCoin:
			retCode = this.worldCoin - num >= 0 ?true:false;
			break;
		case commonEnum.PLAY_INFO.chip:
			retCode = this.chip - num >= 0 ?true:false;
			break;
		case commonEnum.PLAY_INFO.integral:
			retCode = this.integral - num >= 0 ?true:false;
			break;
		case commonEnum.PLAY_INFO.honor:
			retCode = this.honor - num >= 0 ? true:false;
			break;
		case commonEnum.PLAY_INFO.beliefNum:
			retCode = this.beliefNum - num >= 0 ? true:false;
			break;
		case commonEnum.PLAY_INFO.beliefLiveness:
			retCode = this.beliefLiveness - num >= 0 ? true:false;
			break;
		default:
			break;
	}

	return retCode;
};

//消耗
Player.prototype.onConsume = function(type, num)
{
	switch(type){
		case commonEnum.PLAY_INFO.level:
		break;
		case commonEnum.PLAY_INFO.exp:
		break;
		case commonEnum.PLAY_INFO.energy:
		break;
		case commonEnum.PLAY_INFO.cash:
		break;
		case commonEnum.PLAY_INFO.gold: //消耗懂币vip经验增加
			this.vipExp += num;
		break;
		case commonEnum.PLAY_INFO.vip:
		break;
		case commonEnum.PLAY_INFO.trophy:
		break;
		case commonEnum.PLAY_INFO.fame:
			break;
		
		case commonEnum.PLAY_INFO.vipExp:
		default:
			break;
	}

	this.checkVipLevelUp(); //检查vip等级提升
};

Player.prototype.getVipLevelByTotalConsumption = function()
{
	let vip = 0;
	let config = dataApi.allData.data["Vip"];
	if (!config)
	{
		logger.error("getVipLevelByTotalConsumption: Vip config not found!");
		return vip;
	}

	for(let idx in config)
	{
		let data = config[idx];
		let totalConsumption = data.TotalConsumption;
		if (this.vipExp >= totalConsumption)
		{
			vip = data.Lv;
		}
	}

	return vip;
};

Player.prototype.checkVipLevelUp = function()
{
	this.upPlayerInfo([{type: commonEnum.PLAY_INFO.vipExp, value: this.vipExp}]);
	let vip = this.getVipLevelByTotalConsumption();
	if (vip !== this.vip)
	{
		this.vip = vip;
		this.upPlayerInfo([{type: commonEnum.PLAY_INFO.vip, value: this.vip}]);
		this.updateRedDotHintState(commonEnum.REDDOT_HINT.VIP);//vip升级的时候红点提示
	}
};

/**
 * 兑换礼包
 */
Player.prototype.exchangeGiftBag = function (gift) {
	if(!gift || gift === ""){
		return Code.FAIL;
	}
	var config = dataApi.allData.data["GiftCode"];
    if (!config) {
        logger.error("exchangeGiftBag: config not found!", gift);
        return Code.FAIL;
    }

	let rewardList = [];
	let index = 0;
	for( let i in config){
		if(gift === config[i].Gift){
			//礼包奖励
			for (let k = 1; k <= 3; k++) {
				let rewardId = config[i]["ResId"+ k];
				let rewardNum = config[i]["Num"+ k];
				if (rewardId <= 0 || rewardNum <= 0) {
					continue;
				}
				logger.info("sendMailReward", rewardId, rewardNum);
				rewardList[index] = {};
				rewardList[index].ItemType = commonEnum.MAIL_ITEM_TYPE.HERO;
				rewardList[index].ResId = rewardId;
				rewardList[index].Num = rewardNum;
				index++;
			}
			break;
		}
	}	
	
	let specialAttachInfo = {
		roomUid: "",
	};
	this.email.sendMailReward("Sys", commonEnum.MAIL_TRANSLATE_CONTENT.EXCHANGE_REWARD, commonEnum.MailType.SYSMAIL, rewardList, specialAttachInfo, "", "", "", "");
	return Code.OK;
}

Player.prototype.initTaskList = function () {
	let config = dataApi.allData.data["Task"];
	if(!config){
		return;
	}
	for(let i in config){
		if(config[i].IsInitialTask === 1 || config[i].IsInitialTask === 2){
			//金牌任务不需要自动加载
			if (config[i].Type === commonEnum.TASK_INFO.goldCoachType)
			{
				continue;
			}
			this.tasks.addTask(config[i].Id);
		}
	}
};

/*
------------------------------------------------------
 */
//hero 
Player.prototype.addHero = function (resId) {
	if (resId <= 0) {
		logger.debug("the resId is error!");
		return {code: Code.FAIL, hero: {}};
	}

	logger.info("******player addHero msg", resId);
	var newHero = this.heros.addHero(resId);
	return {code: Code.OK, hero: newHero};
};

Player.prototype.delHero = function (uid) {
	if (uid <= 0) {
		logger.debug("the hero uid is error!");
		return Code.FAIL;
	}

	var hero = this.heros.getHero(uid);
	if(!hero) {
		logger.warn('delHero err heroId',uid);
		return Code.INVALID;
	}
	
	logger.info("******player delHero msg", uid);
	this.heros.delHeroData(uid);
	return Code.OK;
};

Player.prototype.getHeroList = function () {
	//logger.info("******player getHeroList msg");
	//在这里添加英雄
 	var heroList = this.heros.getHeroList();
	return {code: Code.OK, heroList: heroList};
};

//teamFormation 阵容
Player.prototype.getTeamFormationList =function() {
	let list = this.teamFormations.getTeamFormationList();
	return {code: Code.OK, list: list};	
};

Player.prototype.addTeamFormation = function (resId) {
	if (resId <= 0) {
		logger.debug("the resId is error!");
		return Code.FAIL;
	}

	// logger.info("******player addTeamFormation msg", destId);
	 var oneTeamFormation = this.teamFormations.addTeamFormation(resId);
	 var clientOneTeam = this.teamFormations.makeClientTeamFormation(oneTeamFormation.Uid);
	return {code: Code.OK, oneTeam: clientOneTeam};
};

Player.prototype.checkHeroResId = function(resId)
{
	return this.heros.checkHeroResIdIsExist(resId);
};

Player.prototype.setFormation = function (uid, destId) {
	var clientOneTeam = [];
	if (destId <= 0) {
		logger.debug("the setFormation is error!");
		return {code: Code.FAIL, oneTeam: clientOneTeam};
	}

	logger.info("******player setFormation msg", destId);
	var ret = this.teamFormations.setFormation(uid, destId);;
	if (ret !== Code.OK) {
		return {code: Code.FAIL, oneTeam: clientOneTeam};
	}

	clientOneTeam = this.teamFormations.makeClientTeamFormation(uid);
	return {code: Code.OK, oneTeam: clientOneTeam};
};

Player.prototype.delTeamFormation = function (uid) {
	let ret = {code :Code.FAIL};
	if (uid <= 0) {
		logger.debug("the hero uid is error!");
		return ret;
	}
	var oneTeamFormation = this.teamFormations.getOneTeamFormation(uid);
	if(!oneTeamFormation) {
		logger.warn('delTeamFormation err uid',uid);
		return ret;
	}
	logger.debug("111delTeamFormation isInitFormation: ", oneTeamFormation.isInitFormation);
	//不能删除初始阵容
	if (oneTeamFormation.isInitFormation === 1) {
		logger.warn("delTeamFormation isInitFormation: ", oneTeamFormation.isInitFormation);
		return ret;
	}
	//不能删除比赛阵容
	let leagueTeamId = this.teamFormations.getLeagueTeamFormationId();
	if (uid === leagueTeamId) {
		logger.error("delTeamFormation: can not delete league team formation Id", leagueTeamId);
		ret.code = Code.NOT_DEL_TEAM;
		return ret;
	}
	//删除阵容
	let ok = this.teamFormations.delTeamFormation(uid);
	return ok;
};

Player.prototype.addTeamFormationHero = function (uid, position, heroUid) {
	if (uid <= 0) {
		logger.debug("the uid is error!", uid);
		return Code.FAIL;
	}

	if (heroUid <= 0) {
		logger.debug("the heroUid is error!", heroUid);
		return Code.FAIL;
	}

 	var code = this.teamFormations.addHero(uid, position, heroUid);
	return {code: code, list: this.teamFormations.makeClientTeamFormation(uid)};
};

Player.prototype.delTeamFormationHero = function (uid, heroUid) {
	if (uid <= 0) {
		logger.debug("the uid is error!");
		return Code.FAIL;
	}

	if (heroUid <= 0) {
		logger.debug("the heroUid is error!");
		return Code.FAIL;
	}

	logger.info("******player delTeamFormation msg", uid, heroUid);
	return this.teamFormations.delHero(uid, heroUid);
};

Player.prototype.teamFormationExchangeHero = function (teamFormationUid, heroUid1, heroUid2) {
	if (teamFormationUid <= 0) {
		logger.error("the heroUid is error!", teamFormationUid);
		return Code.FAIL;
	}

	if (heroUid1 <= 0) {
		logger.error("the uid is error!", heroUid1);
		return Code.FAIL;
	}

	if (heroUid2 <= 0) {
		logger.error("the uid is error!", heroUid2);
		return Code.FAIL;
	}

	var ret = this.teamFormations.teamFormationExchangeHero(teamFormationUid, heroUid1, heroUid2);
	if (ret !== Code.OK) {
		//logger.error("teamFormationExchangeHero is error!", ret, teamFormationUid, heroUid1, heroUid2);
		return ret;
	}

	return Code.OK;
};

/**
 * @return {number}
 */
Player.prototype.DelHeroAttackAndDefend = function (heroId, teamFormationId) {
	if (heroId <= 0) {
		logger.debug("the uid is error!", heroId);
		return Code.FAIL;
	}

	if (teamFormationId <= 0) {
		logger.debug("the heroUid is error!", teamFormationId);
		return Code.FAIL;
	}

	logger.info("******player DelHeroAttackAndDefend msg: ", heroId, teamFormationId);
	 this.heros.DelHeroAttackAndDefend(heroId, teamFormationId);
	return Code.OK;
};

//生成思路:
//1.生成球员
//2.生成阵容
//3.根据阵容中的阵型,把球员添加到阵型中去
//4.判断是否为新手,赠送球员
Player.prototype.createInitBallerAndTeam = function () {
	var createBallerCfg = dataApi.allData.data["CreateBaller"];
	var teamFormationCfg = dataApi.allData.data["CreateTeamFormation"];

	if (!createBallerCfg) 
	{
		logger.error("createInitBallerAndTeam: createBallerCfg not found!");
		return Code.FAIL;
	}

	if (!teamFormationCfg) 
	{
		logger.error("createInitBallerAndTeam: teamFormationCfg not found!");
		return Code.FAIL;
	}

	//1.生成创建角色必须生成的球员
	var count = 0;
	var heroIdArr = {};
	for (let id in createBallerCfg) 
	{
		const cfg = createBallerCfg[id];
		var mode = cfg.Mode;
		var ballerID = cfg.BallerID;
		if (commonEnum.CREATE_INIT_BALLER_MODE.INIT_BALLER === mode) {
			var newHero = this.heros.addHero(ballerID, 1);
			if (!newHero) {
				logger.error("createInitBallerAndTeam: addHero failed! uid, resID", this.playerId, ballerID);
				return Code.FAIL;
			}else
			{
				heroIdArr[ballerID] =  newHero.Uid;
				count++;
			}
		}
	}

	//2.检查的生成的球员的数量，不满足11个说明配置表错误了
	if (count < 11)
	{
		logger.error("createInitBallerAndTeam: addHero failed! uid, count", this.playerId, count);
		return Code.FAIL;
	}
	
	//3.生成默认阵容,添加球员到阵容中
	var oneTeamFormationUid = 0;
	for (let id in teamFormationCfg) 
	{
		const cfg = teamFormationCfg[id];
		var formationId = cfg.FormationId;
		var positionArr = cfg.Position;
		var footballerIdArr = cfg.FootballerId;

		//创建阵容
		var oneTeamFormation = this.teamFormations.addTeamFormation(formationId);
		if (!oneTeamFormation) {
			logger.error("createInitBallerAndTeam: oneTeamFormation failed! uid, resID", this.playerId, formationId);
			return Code.FAIL;
		}
		
		//添加球员到阵容当中去
		//初始阵容一
		oneTeamFormation.isInitFormation = 1;
		oneTeamFormationUid = oneTeamFormation.Uid;
		for (let i = 0; i < positionArr.length; i++) {
			const position = positionArr[i];
			const ballerID = footballerIdArr[i];
			const heroUid = heroIdArr[ballerID];
			var ret = this.addTeamFormationHero(oneTeamFormationUid, position, heroUid);
			if (ret.code !== Code.OK) {
				logger.error("createInitBallerAndTeam: addTeamFormationHero failed! uid, oneTeamFormationUid, heroUid",
				this.playerId, oneTeamFormationUid, heroUid);
				return Code.FAIL;
			}
		}

		this.teamFormations.setCurrTeamFormationId(oneTeamFormationUid);
		this.setLeagueTeamFormationId(oneTeamFormationUid);
		this.teamFormations.setWarOfFaithTeamFormationId(oneTeamFormationUid);
		break;
	}
	
	//必须克隆一份数据（引用坑）
	let obj = utils.clone(commonEnum.TEAM_TACTICS_LIST)
	this.teamFormations.allTactics.set(this.playerId,obj); //各种战术
	
	this.initTaskList();
	this.footballGround.createFootballGround(this.playerId);
	logger.info("createInitBallerAndTeam", this.playerId);
	return {code: Code.OK, teamFormationUid: oneTeamFormationUid};
};

/**
 * 创建初始阵容
 */
Player.prototype.createInitTeamFormation = function (teamId, teamType) {
	// let ret = {};
	// if(type === commonEnum.FORMATION_TYPE.COMMON || !type) {
	// 	ret = this.teamFormations.createTeamFromInit();
	// }else {
	// 	ret = this.teamFormations.createTeamByNoRepeat(type);
	// }
	let ret = this.teamFormations.addNewTeam(teamId, teamType);
	if (ret.code !== Code.OK) {
		logger.error("createInitTeamFormation srcUid failed! srcUid");
	 	return { code: ret.code, initTeam: {}};
	}

	return {code: Code.OK, initTeam: this.teamFormations.makeClientTeamFormation(ret.uid)};
};

/**
 * 设置初始阵容
 * @param uid 阵容uid
 */
Player.prototype.setInitTeamFormationId = function(uid) {
	let ret = {code: Code.FAIL, name:""};
	let isExist = this.teamFormations.getOneTeamFormation(uid);
	if(!isExist){
		return ret;
	}
	let srcUid = this.teamFormations.getCurrTeamFormationId();
	//未改变阵容不重复赋值
	if (srcUid === uid) {
		ret.code = Code.OK;
		ret.name = srcUid.Name
		return ret;
	}
	this.teamFormations.setCurrTeamFormationId(uid);
	ret.code = Code.OK;
	ret.name = isExist.Name
	return ret;
};

//设置联赛阵容
Player.prototype.setLeagueTeamFormationId = function(uid)
{
	let isExist = this.teamFormations.getOneTeamFormation(uid);
	if(!isExist){
		return Code.FAIL;
	}

	let srcUid = this.teamFormations.getLeagueTeamFormationId();
	if (srcUid !== uid)
	{
		this.teamFormations.setLeagueTeamFormationId(uid);
	}

	return Code.OK;
};

//获取个人信仰id
Player.prototype.getBeliefId = function()
{
	return this.beliefId;
};

//设置个人信仰id
Player.prototype.setBeliefId = function(beliefId)
{
	if(typeof beliefId === "number" && beliefId !== 0) {
		this.beliefId = beliefId;
	}
};

//新手赠送新球员
Player.prototype.createNewerHero = function(resId) {
	let config = dataApi.allData.data["Belief"][resId];
	if(!config) {
		return Code.CONFIG_FAIL;
	}
	if (this.beliefId !== 0) {
		return Code.OK;
	}

	this.bag.addItem(config.Gift, 1);
	let itemUid = this.item.getItemUidByResId(config.Gift);
	this.bag.useItem(itemUid, 1);
	this.beliefId = config.ID;
	return Code.OK;
};

//leagueCopy
Player.prototype.getLeagueCopyData = function () 
{
	var result = this.leagueCopy.getLeagueCopyData();
	if (result.code !== Code.OK) {
		logger.error("getLeagueCopyData error!");
		return {code: Code.FAIL, result: result};
	}
	
	return {code : Code.OK, result: result};
};

Player.prototype.PVELeagueCopyBattle = function (leagueId, teamCopyId, selfScore, enemyScore) {

	var result = this.leagueCopy.PVELeagueCopyBattle(leagueId, teamCopyId, selfScore, enemyScore);
	if (result.code !== Code.OK) {
		logger.error("_PVELeagueCopyBattle error!");
		return {code: Code.FAIL, result: result};
	}

	if (result.isNeedUpdateMail) {
		this.updateEmail();
		//logger.info("_PVELeagueCopyBattle update mail list to client", leagueId, teamCopyId);
	}

	//保存数据
	this.saveLeagueCopy();

	return {code: Code.OK, result: result};
};

//注意!注意！注意 此函数为测试函数
Player.prototype.commonFunction = function (cmdId) {
	logger.info("commonFunction ________________________cmdId", cmdId);
	switch (cmdId) {
		case commonEnum.CommonFunctionCmdId.INIT_MAIL_LIST: //初始化邮件列表
			break;
		default:
			break;
	}
};

/**
 * 添加经验
 * @param num 经验值
 * */ 
Player.prototype.addExp = function (num) {
	if(this.level >= 100) {
		logger.error("the level max is 101");
		return;
	}

	// logger.info("addExp", this.playerId, num);
	let levelData = dataApi.allData.data["FootballLevel"];
	let upLevel = false;
	let srcLevel = this.level;
	let remainExp = num + this.fame;
	for(let i in levelData) {
		//var allExp = levelData[i]["Expall"]
		if(Number(i) > 100) {
			continue;
		}
		let level = levelData[i].Level;
		if(this.level === level && remainExp >= levelData[i]["Exp"]) {
			remainExp = remainExp - levelData[i]["Exp"];
			this.level += 1;
			upLevel = true;
			if(this.level >= 100) {
				remainExp = levelData[i]["Exp"];
				break;
			}
		}
	}

	this.fame = remainExp;
	this.allFame += num;
	if(this.level > 100) {
		this.level = 100;
	}
	if(upLevel){
		this.upPlayerInfo([{type: commonEnum.PLAY_INFO.level, value: this.level}, {type: commonEnum.PLAY_INFO.fame, value: this.fame}]);
		this.tasks.triggerTask(commonEnum.TARGET_TYPE.FIVE, 0, 0, this.level);
		this.newerTask.triggerTask(commonEnum.NEWER_TASK.PLAYER_LEVEL, 0,0, this.level);
		for(let i = srcLevel + 1; i <= this.level; i++)
		{
			//logger.info("addExp: playerId, num, i, level", this.playerId, num, i, this.level);
			this.checkGuideTrigger(commonEnum.NEWER_GUIDE_TRIGGER_CONDITION.ROLE_LEVEL, i);
			this.act.checkLevelIsStandard(i);
		}
		this.recordSlog(this.playerId, commonEnum.STATIS_LOG_TYPE.LEVEL_UP, [this.level], {});
	}else 
	{
		this.upPlayerInfo([{type: commonEnum.PLAY_INFO.fame, value: this.fame}]);
		this.checkGuideTrigger(commonEnum.NEWER_GUIDE_TRIGGER_CONDITION.ROLE_LEVEL, this.level);
	}
};

Player.prototype.lotteryHero = function(type, times) {
	//参数校验
	if(type !== commonEnum.Lottery.Type.Gold && type !== commonEnum.Lottery.Type.Token) {
		return {code: Code.LOGIC.FA_HANDLER_PARAM_ERR};
	}
	if(times !== commonEnum.Lottery.Times.Ten && times !== commonEnum.Lottery.Times.Once) {
		return {code: Code.LOGIC.FA_HANDLER_PARAM_ERR};
	}
	var result = [];
	var self = this;
	var cost = 0;
	var costType = 0;
	if(type == commonEnum.Lottery.Type.Gold) {
		cost = dataApi.allData.data["Setting"][38]["Value"];
		costType = commonEnum.System.Hero.goldOnceLottery;
		if(times == commonEnum.Lottery.Times.Ten) {
			cost = dataApi.allData.data["Setting"][39]["Value"];
			costType = commonEnum.System.Hero.goldTenLottery;
		}
		if(this.reduceGold(cost, costType) != Code.OK) {
			return {code: Code.LOGIC.FA_NOT_ENOUGH_GOLD};
		}
	}else if(type == commonEnum.Lottery.Type.Token) {
		cost = dataApi.allData.data["Setting"][36]["Value"];
		costType = commonEnum.System.Hero.tokenOnceLottery;
		if(times == commonEnum.Lottery.Times.Ten) {
			cost = dataApi.allData.data["Setting"][37]["Value"];
			costType = commonEnum.System.Hero.tokenTenLottery;
		}
		if(this.reduceToken(cost, costType) != Code.OK) {
			return {code: Code.LOGIC.FA_NOT_ENOUGH_TOKEN};
		}
	}
	if(times == commonEnum.Lottery.Times.Once) {
		result.push(self.lotteryOnce(type));
	}else if(times == commonEnum.Lottery.Times.Ten) {
		for(var i= 0;i<10;i++) {
			result.push(self.lotteryOnce(type));
		}
	}
	var resultType = [];
	var resultId = [];
	var resultNum = [];
	for(var n=0;n<result.length;n++) {
		var resId = result[n][0];
		var num = result[n][1];
		var goodsType = dataApi.allData.data["LuckyHero"][resId]["GoodsType"];
		logger.debug('--------- resId,num -----',resId,num,goodsType,commonEnum.Lottery.ItemType);
		if(goodsType == commonEnum.Lottery.ItemType.HERO) {
			var heroResId = dataApi.allData.data["LuckyHero"][resId]["HeroId"];
			if(self.heros.findSameModel(heroResId)) {
				var pieceId = dataApi.allData.data["NpcHero"][heroResId]["QualityCost"];
				var pieceNum = dataApi.allData.data["NpcHero"][heroResId]["DecompositionFragments"];
				self.bag.addItemToBag(pieceId, pieceNum);
				resultType.push(commonEnum.Lottery.ItemType.PIECE);
				resultId.push(Number(pieceId));
				resultNum.push(pieceNum);
			}
			else{
				self.heros.addHero(heroResId, commonEnum.Lottery.Currency.TOKEN);
				resultType.push(commonEnum.Lottery.ItemType.HERO);
				resultId.push(Number(heroResId));
				resultNum.push(1);
			}
		}else if(goodsType == commonEnum.Lottery.ItemType.EQUIPMENT) {
			var entityId = self.equips.addEquip(dataApi.allData.data["LuckyHero"][resId]["HeroId"], dataApi.allData.data["LuckyHero"][resId]["star"]);
			self.bag.addEquipToBag(entityId);
			resultType.push(commonEnum.Lottery.ItemType.EQUIPMENT);
			resultId.push(entityId);
			resultNum.push(1);
		}else if(goodsType == commonEnum.Lottery.ItemType.PIECE) {
			self.bag.addItemToBag(dataApi.allData.data["LuckyHero"][resId]["HeroId"], num);
			resultType.push(commonEnum.Lottery.ItemType.PIECE);
			resultId.push(Number(dataApi.allData.data["LuckyHero"][resId]["HeroId"]));
			resultNum.push(num);
		}
	}
	logger.debug('lotteryHero',result,result.length);
	//logger.debug('---------- heros, equips, bag',this.heros, this.equips, this.bag);
	return {code: Code.OK, resultType: resultType, resultId: resultId, resultNum: resultNum};
};

Player.prototype.lotteryOnce = function (type) {
	var amount = 0;
	var below = 0;
	if(type == commonEnum.Lottery.Type.Gold) {
		amount = dataApi.allData.data["LuckyHeroEx"]["goldAmount"];
	}else if(type == commonEnum.Lottery.Type.Token) {
		amount = dataApi.allData.data["LuckyHeroEx"]["tokenAmount"];
	}
	var randNum = calc.randRange(1,amount);
	for(var key in dataApi.allData.data["LuckyHero"]) {
		if(type == commonEnum.Lottery.Type.Gold) {
			below += dataApi.allData.data["LuckyHero"][key]["OddsGold"];
		}else if(type == commonEnum.Lottery.Type.Token) {
			below += dataApi.allData.data["LuckyHero"][key]["OddsRMB"];
		}
		//logger.debug('----------- lottery  --------',below, randNum);
		if(below >= randNum) {
			logger.debug('----------- lottery result --------',key);
			if(type == commonEnum.Lottery.Type.Gold) {
				return [key, dataApi.allData.data["LuckyHero"][key]["FameNum"]];
			}else if(type == commonEnum.Lottery.Type.Token) {
				return [key, dataApi.allData.data["LuckyHero"][key]["RMBNum"]];
			}
		}
	}
};

/*
 -----------------------------------------------------
 */
Player.prototype.save = function(isflush){
	this.emit("save", isflush);
};

Player.prototype.saveHeros = function(isflush){
	this.emit("saveHeros", isflush);
};

Player.prototype.saveItem = function(isflush){
	this.emit("saveItem", isflush);
};

Player.prototype.saveBag = function(isflush){
	this.emit("saveBag", isflush);
};

Player.prototype.saveTeamFormation = function(isflush){
	this.emit("saveTeamFormation", isflush);
}

Player.prototype.saveEMail = function (isflush) {
	this.emit("saveEMail", isflush);
};

Player.prototype.saveBuildings = function(isflush){
	this.emit("saveBuildings", isflush);
};

Player.prototype.saveEquips = function(isflush){
	this.emit("saveEquips", isflush);
};

Player.prototype.saveChapter = function(isflush){
	this.emit("saveChapter", isflush);
};

Player.prototype.saveRecord = function (isflush) {
	this.emit("saveRecord", isflush);
};

Player.prototype.saveWhole = function(isflush){
	this.emit("saveWhole", isflush);
};

Player.prototype.saveLeagueCopy = function(isflush){
	this.emit("saveLeagueCopy", isflush);
};

Player.prototype.saveScout = function(isflush){
	this.emit("saveScout", isflush);
};

Player.prototype.saveTasks = function(isflush){
	this.emit("saveTasks", isflush);
};

Player.prototype.saveBallGround = function(isflush){
	this.emit("saveBallGround", isflush);
};

Player.prototype.saveTrainer = function(isflush){
	this.emit("saveTrainer", isflush);
}

Player.prototype.saveStore = function(isflush){
	this.emit("saveStore", isflush);
}

Player.prototype.saveBusinessMatch = function(isflush){
	this.emit("saveBusinessMatch", isflush);
};

Player.prototype.saveTrophyCopy = function(isflush){
	this.emit("saveTrophyCopy", isflush);
};

Player.prototype.saveFollow = function(isflush){
	this.emit("saveFollow", isflush);
};

Player.prototype.saveNewPlayerSign = function(isflush){
	this.emit("saveNewPlayerSign", isflush);
};

Player.prototype.saveSevenDaySign = function(isflush){
	this.emit("saveSevenDaySign", isflush);
};

Player.prototype.saveSign = function(isflush){
	this.emit("saveSign", isflush);
};

Player.prototype.saveEveryDaySign = function(isflush){
	this.emit("saveEveryDaySign", isflush);
};

Player.prototype.saveVipShop = function(isflush){
	this.emit("saveVipShop", isflush);
};

Player.prototype.saveAct = function(isflush){
	this.emit("saveAct", isflush);
};

Player.prototype.saveOfflineEvent = function(isflush){
	this.emit("saveOfflineEvent", isflush);
};

Player.prototype.saveSeasonStore = function(isflush){
	this.emit("saveSeasonStore", isflush);
};

Player.prototype.saveLimitStore = function(isflush){
	this.emit("saveLimitStore", isflush);
};

Player.prototype.saveNewerGuide = function(isflush){
	this.emit("saveNewerGuide", isflush);
};

Player.prototype.saveWorldCup = function(isflush){
	this.emit("saveWorldCup", isflush);
};

Player.prototype.saveMiddleEastCup = function(isflush){
	this.emit("saveMiddleEastCup", isflush);
};

Player.prototype.saveGulfCup = function(isflush){
	this.emit("saveGulfCup", isflush);
};

Player.prototype.saveMLS = function(isflush){
	this.emit("saveMLS", isflush);
};

Player.prototype.saveRelay = function(isflush){
	this.emit("relay", isflush);
};

Player.prototype.saveEveryDayEnergy = function(isflush){
	this.emit("saveEveryDayEnergy", isflush);
};

Player.prototype.saveNewerTask = function(isflush){
	this.emit("saveNewerTask", isflush);
};

Player.prototype.saveCommonActivity = function(isflush){
	this.emit("saveCommonActivity", isflush);
};

Player.prototype.saveBeliefSkill = function(isflush){
	this.emit("saveBeliefSkill", isflush);
};

Player.prototype.saveHonorWall = function(isflush){
	this.emit("saveHonorWall", isflush);
};

Player.prototype.updatePlayer = function () {
	this.emit("onUpdatePlayer");
};

Player.prototype.updateBag = function () {
	this.emit("onUpdateBag");
};

Player.prototype.updateHeros = function () {
	this.emit("onUpdateHeros");
};

Player.prototype.updateEmail = function () {
	this.emit("updateEmail");
};

Player.prototype.updateTeamFormations = function (serverType) {
	this.emit("onUpdateTeamFormations", serverType || commonEnum.FORMATION_TYPE.COMMON);
};

Player.prototype.updateScoutInfo = function () {
	this.emit("updateScoutInfo");
};

Player.prototype.updateTaskInfo = function (type) {
	this.emit("updateTaskInfo", type);
};

Player.prototype.upPlayerInfo = function (playerInfo) {
	this.emit("upPlayerInfo", playerInfo);
};

Player.prototype.upAddHero = function (uid) {
	this.emit("upAddHero", uid);
};

Player.prototype.upDelHero = function (uid) {
	this.emit("upDelHero", uid);
};

Player.prototype.updateRetirementHero = function () {
	this.emit("updateRetirementHero");
};

Player.prototype.updateEnergyInfo = function()
{
	this.emit("updateEnergyInfo");
};

Player.prototype.updateAllActivity = function (allActivity) {
	this.emit("updateAllActivity", allActivity);
};

Player.prototype.updateHospitalPosInfo = function () {
	this.emit("updateHospitalPosInfo");
};

Player.prototype.updateHonorWall = function () {
	this.emit("updateHonorWall");
};

Player.prototype.recordSlog = function (playerId, behaviorType, valueArray, moreInfo) {
	this.emit("recordSlog", playerId, behaviorType, valueArray, moreInfo);
};

Player.prototype.addBeliefLiveness = function (playerId, value) {
	this.emit("addBeliefLiveness", playerId, value);
};

Player.prototype.mailAddBeliefLiveness = function (playerId, value) {
	this.emit("mailAddBeliefLiveness", playerId, value);
};

Player.prototype.updateRedDotHintState = function (hintType) {
    this.emit("updateRedDotHintState", hintType);
};

Player.prototype.updateFirstGetHeroList = function () {
	this.emit("updateFirstGetHeroList");
};

Player.prototype.updateNewerTask = function () {
	this.emit("updateNewerTask");
};

Player.prototype.updatePushGift = function (gift) {
	this.emit("updatePushGift", gift);
};

Player.prototype.updateHeroHealth = function (uid, value) {
    this.emit("updateHeroHealth", uid, value);
};

Player.prototype.updateStrength = function (playerId, leagueName, value) {
	this.emit("updateStrength", playerId, leagueName, value);
};

Player.prototype.updateOneTrainer = function (uid) {
	this.emit("updateOneTrainer", uid);
};

Player.prototype.updateAllTrainer = function () {
	this.emit("updateAllTrainer");
};
/*
 ------------------------------------------------------------------
 */

Player.prototype.updateFuncSwitch = function(funcSwitchInfo) 
{
	this.emit("updateFuncSwitch", funcSwitchInfo);
};

//通知报名提示框
Player.prototype.updateLeagueEnrollTips = function(updateInfo)
{
	this.emit("updateLeagueEnrollTips", updateInfo);
};

//当前懂联赛赛季
Player.prototype.updateLeagueSeasonId = function(seasonId)
{
	this.emit("updateLeagueSeasonId", seasonId);
};

//赛事转播过期提示
Player.prototype.updateRelayEndTime = function(endTime)
{
	this.emit("updateRelayEndTime", endTime);
};

//通知巅峰赛竞猜提示框
Player.prototype.updatePeakMatchTips = function(period)
{
	this.emit("updatePeakMatchTips", period);
};

//滚屏公告	msg = {type: 3, channel: commonEnum.CHAT_CHANNEL.HORN_CHANNEL}
Player.prototype.updateScrolling = function(sendMsg)
{
	this.emit("updateScrolling", sendMsg);
};
/*
------------------------------------------------------------------
 */
Player.prototype.getPlayerInfo = function() {
	var playerInfo = {
		name: this.name,
		level: this.level,
		fame: this.fame,
		cash: this.cash,
		gold: this.gold,
		energy: this.energy,
		trophy:this.trophy,
		faceId: this.faceIcon,
	};
	//名字
	//等级
	//经验
	//金钱
	//充值货币
	//体力
	//奖杯
	//头像id
	return playerInfo;
};

Player.prototype.getId = function() {
	return this.playerId;
};

Player.prototype.isOnline = function(){
	return this.frontendId && this.sessionId;
};

Player.prototype.getSessionId = function() {
	return this.sessionId;
};

Player.prototype.getFrontendId = function() {
	return this.frontendId;
};

Player.prototype.getName = function() {
	return this.name;
};

Player.prototype.getFaceIcon= function() {
	return this.faceIcon;
};
 
Player.prototype.getIp = function() {
	return this.ip;
};

Player.prototype.getBag = function() {
	return this.bag;
};

Player.prototype.getOneHero = function (heroId) {
	return this.heros.getHero(heroId);
};

Player.prototype.reCalcAttr = function (heroId){
	this.heros.reCalcAttrRevision(heroId);
	// this.heros.reCalcAttr(heroId);
};

Player.prototype.reCalcAttrToBattle = function (heroId, teamUid){
	// this.heros.reCalcAttrRevision(heroId);
	this.heros.reCalcAttr(heroId, teamUid);
};

Player.prototype.calcBallerAttackAndDefend = function (heroUid, teamFormationId, position){
	this.heros.calcBallerAttackAndDefend(heroUid, teamFormationId, position);
};

Player.prototype.getRating = function(heroUid) {
	return this.heros.getRating(heroUid);
};

Player.prototype.getVip = function()
{
	return this.vip;
};

//检查球员是否在任意阵容中
Player.prototype.checkHeroInArbitrarilyFormation = function(heroUid) 
{
	let teamList = this.teamFormations.checkHeroInArbitrarilyFormation(heroUid);
    for (let i = 0; i < teamList.length; ++i) {
        //重新计算球队属性
        this.teamFormations.reCalcTeamFormationAttrByUid(teamList[i]);
    }
};

Player.prototype.getNoPositionHerosArrayByUid = function(teamFormationUid)  {
	return this.teamFormations.getNoPositionHerosByUid(teamFormationUid);
};

Player.prototype.getBattleData = function() {
	let battleData = {};
	battleData.playerId = this.playerId;
	battleData.name = this.name;
	battleData.level = this.level;
	battleData.fieldLevel = this.fieldLevel;
	battleData.faceUrl = this.faceUrl;
	battleData.heros = this.heros.toJSONforDB();
	battleData.bag = this.bag.toJSONforDB();
	battleData.teamFormations = this.teamFormations.toJSONforDB();
	battleData.item = this.item.toJSONforDB();
	battleData.trainer = this.trainer.toJSONforDB();
	battleData.beliefSkill = this.beliefSkill.toJSONforDB();
	battleData.footballGround = this.footballGround.toJSONforDB();
	battleData.totalValue = this.teamFormations.calcTeamValue(this.teamFormations.currTeamFormationId);
	return battleData;
};

Player.prototype.getLeagueBattleData = function() {
	let battleData = {};
	battleData.playerId = this.playerId;
	battleData.name = this.name;
	battleData.level = this.level;
	battleData.fieldLevel = this.fieldLevel;
	battleData.faceIcon = this.faceIcon;
	battleData.faceUrl = this.faceUrl;
	battleData.heros = this.heros.toJSONforDB();
	battleData.bag = this.bag.toJSONforDB();
	battleData.teamFormations = this.teamFormations.toJSONforLeague();
	battleData.item = this.item.toJSONforDB();
	battleData.trainer = this.trainer.toJSONforDB();
	battleData.beliefSkill = this.beliefSkill.toJSONforDB();
	battleData.footballGround = this.footballGround.toJSONforDB();
	battleData.totalValue = this.teamFormations.calcTeamValue(this.teamFormations.leagueTeamFormationId);
	return battleData;
};

Player.prototype.getWarOfFaithBattleData = function() {
	let battleData = {};
	battleData.playerId = this.playerId;
	battleData.name = this.name;
	battleData.level = this.level;
	battleData.fieldLevel = this.fieldLevel;
	battleData.faceIcon = this.faceIcon;
	battleData.faceUrl = this.faceUrl;
	battleData.heros = this.heros.toJSONforDB();
	battleData.bag = this.bag.toJSONforDB();
	battleData.teamFormations = this.teamFormations.toJSONforWarOfFaith();
	battleData.item = this.item.toJSONforDB();
	battleData.trainer = this.trainer.toJSONforDB();
	battleData.beliefSkill = this.beliefSkill.toJSONforDB();
	battleData.footballGround = this.footballGround.toJSONforDB();
	let currTeamUid = this.teamFormations.warOfFaithTeamFormationId;
	if(this.teamFormations.warOfFaithTeamFormationId === "") {
		currTeamUid = this.teamFormations.currTeamFormationId;
	}
	battleData.totalValue = this.teamFormations.calcTeamValue(currTeamUid);
	return battleData;
};
/*
---------------------------------------------------------------
 */
Player.prototype.addToken = function (token, systemNo) {
	this.token += token;
	logger.info('addToken:',token,systemNo);
};

Player.prototype.reduceToken = function (token, systemNo) {
	if(token>this.token) {
		logger.warn('reduce token err',token, this.token, systemNo, this.playerId);
		return Code.FAIL;
	}
	this.token -= token;
	logger.info('reduceToken:',token,systemNo);
	return Code.OK;
};

Player.prototype.addGold = function (gold, systemNo) {
	this.gold += gold;
	logger.info('addGold:',gold,systemNo);
};

Player.prototype.reduceGold = function (gold, systemNo) {
	if(gold>this.gold) {
		logger.warn('reduce gold err',gold, this.gold, systemNo, this.playerId);
		return Code.FAIL;
	}
	logger.info('reduceGold:',gold,systemNo);
	this.gold -= gold;
	return Code.OK;
};

Player.prototype.reduceItem = function (resId, num, systemNo) {
	var type = dataApi.allData.data["Item"][resId]["ItemType"];
	if(type == 4 && num > 1) {
		logger.warn('reduceItem fail, because item is equip and num > 1');
		return Code.FAIL;
	}
	if(type == 4) {
		//to do 从equipBag里面删除，并从allEquips里面删除
	}else {
		//从背包里面删除
		return this.bag.removeInItemBag(resId, num);
	}
};

Player.prototype.addSoul = function (soul, systemNo) {
	this.heroExp += soul;
	logger.info('addSoul:',soul,systemNo);
};

Player.prototype.reduceSoul = function (soul, systemNo) {
	if(soul>this.heroExp) {
		logger.warn('reduce soul err',soul, this.heroExp, systemNo, this.playerId);
		return Code.FAIL;
	}
	this.heroExp -= soul;
	logger.info('reduceSoul:',soul,systemNo,this.heroExp);
	return Code.OK;
};

Player.prototype.dropItem = function (dropId, systemNo) {
	var rewardNums = dataApi.allData.data["DropPve"][dropId]["RewardNumb"];
	var rewardOdds = dataApi.allData.data["DropPve"][dropId]["RewardOdds"];
 	logger.debug('----------- dropItem --------------',rewardNums, rewardOdds);
	var rand = calc.randRange(1, 1000);
	var num = 0;
	var sum = 0;
	for(var i=0;i<rewardOdds.length;i++) {
		sum += rewardOdds[i];
		if(rewardOdds[i] != 0 && rand <= sum) {
			num = rewardNums[i];
			break;
		}
	}
	logger.debug('----------xxxxxx1-------',rand,sum,num);
	var dropType = [];
	var dropIds = [];
	var dropNum = [];
	for(var n=0;n<num;n++) {
		var result = this.dropOnce(dropId);
		if(result.type == commonEnum.DropType.Equipment) {
			var entityId = this.equips.addEquip(result.key, result.star);
			this.bag.addEquipToBag(entityId);
			dropType.push(result.type);
			dropIds.push(entityId);
			dropNum.push(1);
		}
		else if(result.type == commonEnum.DropType.StarEquip) {
			var r = calc.randRange(1, dataApi.allData.data["Equipment"].length);
			var x = 0;
			for(var resId in dataApi.allData.data["Equipment"]) {
				x += 1;
				if(x == r) {
					var entityId = this.equips.addEquip(resId, result.star);
					this.bag.addEquipToBag(entityId);
					dropType.push(result.type);
					dropIds.push(entityId);
					dropNum.push(1);
				}
			}
		}
	}
	return {dropType: dropType, dropId: dropIds, dropNum: dropNum};
};

Player.prototype.dropOnce = function (dropId) {
	var data = dataApi.allData.data["DropPve"][dropId];
	var types = data["RewardType"];
	var odds = data["odds"];
	var rand = calc.randRange(1,100);
	var below = 0;
	for(var n=0;n<types.length;n++) {
		below += odds[n];
		if(rand <= below) {
			return {type: types[n], key: data["Key"][n], star: data["Value"][n], num: data["RewardNum"][n]};
		}
	}
};

Player.prototype.isSearchListEmpty = function () {
	if(this.searchList.length <= 0) {
		return true;
	}
	return false;
};

Player.prototype.syncSearchList = function (list) {
	for(var i in list) {
		this.searchList.push(list[i]);
	}
};

Player.prototype.getNextOpponent = function (playerDao, cb) {
	var id = this.searchList[0];
	if(!id) {
		logger.debug('cannot get the op, need robot !');
		return cb(Code.FAIL);
	}
	var self = this;
	//删除移到unlockList
	self.searchList.shift();
	self.lockList.push(id);
	//获取玩家信息
	playerDao.getPvpPlayerInfo(id, function (doc) {
		if(!!doc) {
			var info = {};
			info.player = self.transToClientPlayer(doc.player, doc.buildings.buildings);
			info.buildings = JSON.stringify(doc.buildings.buildings);
			//info.heros = JSON.stringify(doc.heros.heros);
			var heros = [];
			if(doc.heros) {
				for(var i in doc.heros.heros) {
					if(doc.heros.heros[i]["onBattle"] >= 0) {
						heros.push(doc.heros.heros[i]);
					}
				}
			}
			info.heros = JSON.stringify(heros);
			var equips = [];
			if(doc.equips) {
				logger.debug('allEquips ---- 111',doc.equips.allEquips);
				for(var n in doc.equips.allEquips) {
					if(doc.equips.allEquips[n]["heroEntityId"] >= 0) {
						equips.push(doc.equips.allEquips[n]);
					}
				}
			}
			info.equips = JSON.stringify(equips);
			self.opponentId = doc.player.playerId;
			self.opponentHonor = Number(doc.player.honor);
			self.opponentName = doc.player.name;
			self.opponentLevel = doc.player.level
			//通过获得的数据计算可掠夺的资源
			self.getRobResource(doc.player, doc.buildings.buildings, equips);
			logger.debug('rob resource ==========',self.fighterStoreGold, self.fighterStoreSoul, self.fighterEquips);
			info.gold = Math.floor(self.fighterStoreGold*commonEnum.Fight.RobRate.storeRate+self.fighterResGold*commonEnum.Fight.RobRate.resRate);
			info.soul = Math.floor(self.fighterStoreSoul*commonEnum.Fight.RobRate.storeRate+self.fighterResSoul*commonEnum.Fight.RobRate.resRate);
			var result = self.fightFactorCalc(self.honor, self.opponentHonor);
			info.honor = Math.floor(result.baseHonor * result.factor1);
			logger.debug('getPvpPlayerInfo ----- equips',self.fighterEquips);
			cb(Code.OK, info);
		}else {
			cb(Code.FAIL);
		}
	})
};

Player.prototype.getRobResource = function (player, buildings, equips) {
	logger.debug('getRobResource -------- player, buildings', player, buildings);
	var storeGold = 0;
	var storeSoul = 0;
	var resGold = 0;
	var resSoul = 0;
	for(var id in buildings) {
		var resId = buildings[id]["resId"];
		if(this.buildings.isGoldStore(resId)) {
			storeGold += dataApi.allData.data["Building"][resId]["TroopCapacity"];
		}else if(this.buildings.isSoulStore(resId)) {
			storeSoul += dataApi.allData.data["Building"][resId]["TroopCapacity"];
		}else if(this.buildings.isGoldProducer(resId)) {
			// need to do --> 并且被抢夺后需要重新计算该玩家的资源采集量，并重新入库
			resGold += 0;
		}else if(this.buildings.isSoulProducer(resId)) {
			// need to do
			resSoul += 0;
		}
	}
	//1.仓库的容量
	if(player.gold > storeGold) {
		this.fighterStoreGold = storeGold;
	}else {
		this.fighterStoreGold = player.gold;
	}
	if(player.heroExp > storeGold) {
		this.fighterStoreSoul = storeSoul;
	}else {
		this.fighterStoreSoul = player.heroExp;
	}
	//2.资源采集器的可抢夺 need to do
	this.fighterResGold = 0;
	this.fighterResSoul = 0;
	//3.可掠夺装备
	this.fighterEquips = utils.cloneArray(equips);
};

Player.prototype.fightFactorCalc = function (honor, opponentHonor) {
	//计算基础系数
	var baseHonor = 0;
	if(honor <= 500) {
		baseHonor = 28;
	}else if(honor <= 1200) {
		baseHonor = 26;
	}else if(honor <= 2000) {
		baseHonor = 24;
	}else if(honor <= 2800) {
		baseHonor = 20;
	}else if(honor <= 3600) {
		baseHonor = 17;
	}else if(honor <= 4400) {
		baseHonor = 14;
	}else {
		baseHonor = 10;
	}
	var factor1 = 1 - (honor - opponentHonor)/500;
	if(factor1 < 0) {
		factor1 = 0;
	}else if(factor1 > 3) {
		factor1 = 3;
	}
	var factor2 = 1 + (honor - opponentHonor)/500;
	if(factor2 < 0) {
		factor2 = 0;
	}else if(factor1 > 3) {
		factor2 = 3;
	}
	return {baseHonor: baseHonor, factor1: factor1, factor2: factor2}
};

Player.prototype.getUnlockListExcept = function (exceptId) {
	var unlockList = [];
	for(var i in this.searchList) {
		if(exceptId && this.searchList[i] == exceptId) {
		}else {
			unlockList.push(this.searchList[i]);
		}
	}
	for(var n in this.lockList) {
		if(exceptId && this.lockList[n] == exceptId) {
		}else {
			unlockList.push(this.lockList[n]);
		}
	}
	logger.debug('getUnlockListExcept', unlockList, this.searchList, this.lockList, exceptId);
	return unlockList;
};

Player.prototype.clearLockList = function () {
	this.searchList.length = 0;
	this.lockList.length = 0;
};

Player.prototype.processOfflineEvent = function()
{
	this.offlineEvent.process();
};

Player.prototype.newActMail = function(actId, actType, actName, tagIndex, realAwardList, endTime)
{
	this.offlineEvent.newActMail(actId, actType, actName, tagIndex, realAwardList, endTime);
};

Player.prototype.delActMail = function(actId, actType, tagIndex)
{
	this.offlineEvent.delActMail(actId, actType, tagIndex);
};

Player.prototype.checkGuideTrigger = function(triggerType, param)
{
	this.newerGuide.checkGuideTrigger(triggerType, param);
};

Player.prototype.setFinishGuide = function(id)
{
	return this.newerGuide.updateGuide(id);
};

Player.prototype.setNextId = function(id)
{
	return this.newerGuide.setNextId(id);
};

Player.prototype.checkFix = function()
{
	//修复异常数据
	if(this.createTime === 0 && this.teamFormations.allTeamFormations.size === 0) {
		this.createInitBallerAndTeam();
		this.energy = dataApi.allData.data["SystemParam"][commonEnum.ENERGY_INFO.energyInit].Param; //体力
	}
	if(!this.isFirstRecharge || this.isFirstRecharge.length === 0) {
		this.initFirstRechargeState();
	}
	//阵容改版
	this.teamFormations.fixRevisionTeam();
	this.heros.fixAttr();
	this.teamFormations.fixAttr(); //修复阵容数据
	this.footballGround.fixGroundData();
	this.saveHeros();
	this.saveTeamFormation();
	this.saveBallGround();
};

Player.prototype.getRedDotHintState = function(hintType)//红点事件
{
	var state = 0;
	var type = hintType;
	var redHintList = [];
	switch (hintType)
	{
		case commonEnum.REDDOT_HINT.EMAIL://邮件红点
			//遍历邮件列表
			var mailList = this.email.makeClientList();//获取玩家邮件列表
			for(let idx in mailList)
			{
				var email = mailList[idx];
				if(commonEnum.MAIL_TAKE_STATE.NOT_OPEN == email.IsOpen)//没打开
				{
					state = 1;
					redHintList.push({type: type, state: state});
					//logger.error("红点",type, redHintList);
					return redHintList;
				}
			}
			break;
		case commonEnum.REDDOT_HINT.TASKS://任务红点
			//遍历任务列表
			let allTaskList = this.tasks.allTaskList;//获取玩家任务列表
			for (let i = 0; i < 4; i++)//allTaskList[0]
			{
				let Tasks = allTaskList[i];
				for(let k in Tasks)//allTaskList[task1]
				{
					let task = Tasks[k];
					for(let n in task)//allTaskList[task1][0]
					{
						if(!this.tasks.PrepositionTask(task[n].resId))
							continue;
						if((task[n].status === 0) || (task[n].type === 3 && task[n].status === 2))
							continue;
						if(task[n].status === 1)//已完成未领取的任务
						{
							state = 1;
							redHintList.push({type: type, state: state});
							//logger.error("红点",type, redHintList);
							return redHintList;
						}
					}

				}
			}
			break;
		case commonEnum.REDDOT_HINT.SCOUT://球探红点
			let primaryRe = dataApi.allData.data["SystemParam"][commonEnum.SCOUT_PARAM.primaryRe].Param;
			let seniorRe = dataApi.allData.data["SystemParam"][commonEnum.SCOUT_PARAM.seniorRe].Param;
			let topRe = dataApi.allData.data["SystemParam"][commonEnum.SCOUT_PARAM.topRe].Param;
			let time;
			let now = Date.now();
			for (let i in this.scout.scoutGroup) {
				switch(i)
				{
					case '0':
						time = primaryRe * 1000;
						break;
					case '1':
						time = seniorRe * 1000;
						break;
					case '2':
						time = topRe * 1000;
						break;
				}
				if (this.scout.scoutGroup[i].count > 0 && ((this.scout.scoutGroup[i].getTime * 1000 + time) < now))
				{
					redHintList.push({type: type, state: 1});
					//logger.error("红点",type, redHintList);
					return redHintList;
				}
			}
			return redHintList;
			break;
		case commonEnum.REDDOT_HINT.VIP://VIP红点
			let viplv = this.getVip();
			let goodsBuyList = this.vipShop.goodsBuyList;
			for(let i = 0; i < viplv; i++)
			{
				let key = i + 1;
				let arr = goodsBuyList.get(key);
				for(let k in arr)
				{
					if(arr[k].takeStatus === 1)
					{
						state = 1;
						redHintList.push({type: type, state: state});
						//logger.error("红点",type, redHintList);
						return redHintList;
					}
				}
			}
			break;
		case commonEnum.REDDOT_HINT.HOSPITAL://治疗红点
			if(this.footballGround.isOpen === commonEnum.FOOTBALL_GROUND_STATUS.CLOSE)
				break;
			let hospital = this.footballGround.hospitalGround.get(this.playerId);
			for(let i in hospital.HospitalPos)
			{
				if(hospital.HospitalPos[i].state === 4)//状态  1锁住  2可添加  3已有人 4已完成
				{
					state = 1;
					redHintList.push({type: type, state: state});
					//logger.error("红点",type, redHintList);
					return redHintList;
				}
			}
			break;
		case commonEnum.REDDOT_HINT.TRAIN://训练红点
			if(this.footballGround.isOpen === commonEnum.FOOTBALL_GROUND_STATUS.CLOSE)
				break;
			let train = this.footballGround.trainGround.get(this.playerId);
			for(let i in train.TrainPos)
			{
				if(train.TrainPos[i].state === 4)//状态  1锁住 2可添加 3训练中 4已完成
				{
					state = 1;
					redHintList.push({type: type, state: state});
					//logger.error("红点",type, redHintList);
					return redHintList;
				}
			}
			break;
		case commonEnum.REDDOT_HINT.RELAY://赛事转播红点
			let ScheduleList = this.relay.ScheduleList;
			//logger.error("!!!!!!!!!!!!!!!1", ScheduleList);
			for(let i in ScheduleList)
			{
				if(ScheduleList[i].status === 1)
				{
					state = 1;
					redHintList.push({type: type, state: state});
					return redHintList;
				}
			}
			let taskList = this.tasks.allTaskList;
			//logger.error("!!!!!!!!!!!!!!!2", taskList[6].task6);
			if(taskList[6].task6)
			{
				for(let i in taskList[6].task6)
				{
					if(taskList[6].task6[i].status === 1)
					{
						state = 1;
						redHintList.push({type: type, state: state});
						return redHintList;
					}
				}
			}
			break;
		case commonEnum.REDDOT_HINT.FOLLOW://好友赠送体力可领取红点
			let fansCache = this.follow.fansCache;
			for(let [k, v] of fansCache)
			{
				if(v.giveEnergy > 0 && timeUtils.isToday(v.giveTime))
				{
					state = 1;
					redHintList.push({type: type, state: state});
					return redHintList;
				}
			}
			break;
		default ://不需要特殊处理的直接走这里就好了  物品：3 领取精力：6
			redHintList.push({type: type, state: 1});
			//logger.error("红点",type, redHintList);
			return redHintList;
	}
	redHintList.push({type: type, state: 0});
	return redHintList;
};

Player.prototype.getCurrFame = function()
{
	return this.fame;
};

Player.prototype.setIsShowTactics = function(type)
{
	this.isShowTactics = type;
};

/**
 * 根据表名获取时间
 * @param configName  表名
 * @param configId  id
 * @returns {{min: number, hour: number}}
 */
Player.prototype.getSchduleConfigHourAndMinute = function (configName, configId) {
	let timeArr = dataApi.allData.data[configName][configId].Time.split(":");
	return {hour: parseInt(timeArr[0], 10), min: parseInt(timeArr[1], 10)};
};
