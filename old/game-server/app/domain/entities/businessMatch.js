var logger = require("pomelo-logger").getLogger('pomelo', __filename);
var EventEmitter = require("events").EventEmitter;
var util = require("util");
var dataApi = require("../../util/dataApi");
var utils = require("../../util/utils");
var commonEnum = require("../../../../shared/enum");
var Calc = require("../../util/calc");
var Code = require("../../../../shared/code");
var Constant = require("../../../../shared/constant");
var TimeUtils = require("../../util/timeUtils");

var BusinessMatch = function(player) 
{      
    this.player = player;
    this.uid = player.playerId; //玩家UID
    this.searchCost = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.matchSearchCost].Param;
    this.matchRecordList = [];  //匹配记录
    this.GetRivalList = [];
};

util.inherits(BusinessMatch, EventEmitter);
module.exports = BusinessMatch;

BusinessMatch.prototype.initByDB = function(doc) {
	this.uid = doc.uid;
    this.matchRecordList = doc.matchRecordList || [];
    this.maxRecordNum = 30;
};

BusinessMatch.prototype.toJSONforDB = function() {
	var item = {
        uid: this.uid,
        matchRecordList: this.matchRecordList,
	};
	return item;
};

BusinessMatch.prototype.getSearchCost = function()
{
    return this.searchCost;
};

function __match_record_compare_func(rankObj1, rankObj2)
{
    let fightTime1 = rankObj1.beginTime;
    let fightTime2 = rankObj2.beginTime;
    if (fightTime1 !== fightTime2)
    {
        if (fightTime1 < fightTime2)
        {
            return 1;
        }else if(fightTime1 > fightTime2)
        {
            return -1;
        }
    }

    return 0;
}

BusinessMatch.prototype.getLastMatchRecord = function()
{
    let lastLimit = 5; //最多取5条记录
    let lastMatchRecord = [];
    let count = 1;
    for(let idx in this.matchRecordList)
    {
        if (count > lastLimit)
        {
            break;
        }
        lastMatchRecord.push(this.matchRecordList[idx]);
        count++;
    }
    return lastMatchRecord;
};

BusinessMatch.prototype.getAllMatchRecord = function() {
    return this.matchRecordList;
};

//添加匹配记录
BusinessMatch.prototype.addMatchRecord = function(matchRecord)
{
    //最大条数为30条
    if(this.matchRecordList.length >= this.maxRecordNum) {
        for(let i=0, n=(this.matchRecordList.length-this.maxRecordNum+1); i<n; i++) {
            this.matchRecordList.shift();
        }
    }
    this.matchRecordList.push(matchRecord);
    this.matchRecordList.sort(__match_record_compare_func);
    //logger.info("addMatchRecord", this.uid, matchRecord, this.matchRecordList);
};

BusinessMatch.prototype.getMatchRecordByRoomUid = function(roomUid)
{
    for (let idx in this.matchRecordList) {
        const data = this.matchRecordList[idx];
        if (data.roomUid === roomUid)
        {
            return data;
        }
    }
    return null;
};