/**
 * Created by sea on 2019/7/12.
 */
var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var Code = require('../../../../shared/code');
var utils = require('../../util/utils');
var timeUtils = require("../../util/timeUtils");
var dataApi = require('../../util/dataApi');
var commonEnum = require('../../../../shared/enum');

var Trainer = function (player) {
    this.player = player;
    this.uid = player.playerId; //玩家UID
    this.allTrainer = new Map(); //所有教练
    this.fixId = 0;
};

util.inherits(Trainer, EventEmitter);

module.exports = Trainer;

//读数据库
Trainer.prototype.initByDB = function (doc) {
    this.uid = doc.uid;
    this.allTrainer = utils.toMap(doc.allTrainer) || new Map();
    this.fixId = doc.fixId || 0;
};

Trainer.prototype.toJSONforClient = function () {};

//存数据库
Trainer.prototype.toJSONforDB = function () {
    let allTrainer = {
        uid: this.uid,
        allTrainer: utils.toArray(this.allTrainer),
        fixId: this.fixId
    }
    return allTrainer;
};


//教练属性修复接口
Trainer.prototype.fixTrainer = function()
{
    this.fixTrainerStar();
    this.fixTrainerData();
};

//修复星级
Trainer.prototype.fixTrainerStar = function()
{
    if(this.fixId === 0) {
        for (let [k, v] of this.allTrainer) {
            let trainer = this.getOneTrainer(k);
            if(trainer.Star === 1) {
                trainer.Star = 0;
            }
            if(trainer.Level === 1) {
                trainer.Level = 0;
            }
        }
    }
    this.fixId = 1;
};

//修复属性字段
Trainer.prototype.fixTrainerData = function()
{
    for (let [k, v] of this.allTrainer) {
        let trainer = this.getOneTrainer(k);
        if(typeof (trainer.SkillId) === "undefined") {
            trainer.SkillId = 0;
        }
        if(typeof (trainer.Tactics) === "undefined") {
            trainer.Tactics = [];
        }
    }
};


//添加一个教练
Trainer.prototype.addTrainer = function (resId) {
    let trainer = this.createTrainer(resId);
    let uid = trainer.Uid;
    this.allTrainer.set(uid, trainer);
    //计算教练属性
    this.calcTrainerAttr(uid);
    return uid;
};

Trainer.prototype.createTrainer = function (resId) {
    let trainer = {};
    trainer.Uid = utils.syncCreateUid();
    trainer.ResId = resId;
    
    let oneLevelAttr = {};
    for (let i in commonEnum.ONE_LEVEL_ATTR_NAMES) {
        if (i === "ResistanceDamage") {
            continue;
        }
        oneLevelAttr[i] = 0;
    }
    trainer.OneLevelAttr = oneLevelAttr;
    trainer.Star = 0;        //教练星级
    trainer.Level = 0;       //教练等级
    trainer.Actual = 0;      //教练实力
    trainer.SkillId = 0;     //技能Id
    trainer.Tactics = [];    //战术加成
    return trainer;
};

Trainer.prototype.calcTrainerAttr = function (uid) {
    let trainer = this.allTrainer.get(uid);
    let oneLevelAttr = {};
    let resId = trainer.ResId;
    let level = trainer.Level;
    let config = dataApi.allData.data["TrainningCoach"][resId];
    let levelConfig = dataApi.allData.data["CoachUpgrade"][resId];
    if(!config || !levelConfig) {
        return;
    }
    for (let i in commonEnum.ONE_LEVEL_ATTR_NAMES) {
        if (i === "ResistanceDamage") {
            continue;
        }
        let levelNum = level * (config[i] * levelConfig[i] / 10000);  //每级加成值
        oneLevelAttr[i] = config[i] + levelNum;
    }
    // logger.error("yyyyyyyyyyyyyyyyyyyyyyyyyyyy", oneLevelAttr);
    trainer.OneLevelAttr = oneLevelAttr;
    let sum = 0;
    let index = 0;
    for(let j in trainer.OneLevelAttr) {
        sum += trainer.OneLevelAttr[j];
        index++;
    }
    trainer.Actual = Math.floor(sum / index * 11);  //教练实力
}

Trainer.prototype.calcAllTrainerAttr = function () { 
    for(let [uid, obj] of this.allTrainer){
        if(!uid) {
            continue;
        }
        this.calcTrainerAttr(uid);
    }
}


Trainer.prototype.getOneTrainer = function (uid) {
    return this.allTrainer.get(uid);
}

/**
 * 获取教练列表
 */
Trainer.prototype.getAllTrainer = function () {
    let clientTrainerList = [];
    for(let [uid, obj] of this.allTrainer){
        if(!obj) {
            continue;
        }
        let clientTrainer = this.makeClientTrain(uid);
        clientTrainerList.push(clientTrainer);
    }
    return clientTrainerList;
}

//根据教练resId获取教练信息
Trainer.prototype.getOneTrainerByResId = function (resId) {
    let trainerObj = {};
    for(let [uid, obj] of this.allTrainer){
        if(!uid) {
            continue;
        }
        let trainer = this.getOneTrainer(uid);
        if(trainer.ResId === resId){
            trainerObj = trainer;
            break;
        }
    }
    return trainerObj;
}

//教练升星  技能-战术
Trainer.prototype.upgradeTrainerStar = function (uid) {
    let ret = {code: Code.FAIL}
    let trainer = this.getOneTrainer(uid);
    if(!trainer){
        ret.code = Code.FAIL;
        return ret;
    }
    let resId = trainer.ResId;
    let star = trainer.Star + 1;
    let isOk = Math.floor(trainer.Level / star / 10);
    if(isOk < 1) {
        ret.code = Code.TRAINER_CODE.STAR_FAIL;
        return ret;
    }

    let config = dataApi.allData.data["CoachUpstar"];
    if(!config) {
        ret.code = Code.CONFIG_FAIL;
        return ret;
    }
    let skillId = trainer.SkillId;
    let chipId = 0;
    let chipNum = 0;
    for(let i in config) {
        if(resId === config[i].CoachID && star === config[i].Star) {
            skillId = config[i].Skill;
            chipId = config[i].ChipId;
            chipNum = config[i].Chip;
            break;
        }
    }

    let itemNum = this.player.item.getItemCountByResId(chipId);
    let itemUid = this.player.item.getItemUidByResId(chipId);
    if(itemNum < chipNum) {
        ret.code = Code.ITEM_FAIL;
        return ret;
    }

    //删除物品
    this.player.item.delItem(itemUid, chipNum);
    if(skillId !== 0) {
        trainer.SkillId = skillId;
        trainer.Star++;
        for(let n in config) {
            if(resId === config[n].CoachID && star === config[n].Star) {
                for(let j = 1; j < 3; ++j) {
                    if(trainer.Tactics.length < 2) {
                        let info = {
                            type: config[n]["Type"+ j],
                            level: config[n]["Level" + j]
                        }
                        trainer.Tactics.push(info);
                    }else {
                        trainer.Tactics[j-1].type += config[n]["Type" + j];
                        trainer.Tactics[j-1].level += config[n]["Level" + j];
                    }
                }
            }
        }
    }
    //重新计算所有球员加成
    this.player.heros.reCalcAllHeroAttr();
    ret.code = Code.OK;
    return ret;
}


//教练升级
Trainer.prototype.upgradeTrainerLevel = function (uid) {
    let ret = {code: Code.FAIL, level: 0};
    let trainer = this.getOneTrainer(uid);
    if(!trainer){
        return ret;
    }

    let resId = trainer.ResId;
    let config = dataApi.allData.data["TrainningCoach"][resId];
    if(!config){
        ret.code = Code.CONFIG_FAIL;
        return ret;
    }
    let level = trainer.Level + 1;
    if(level > 100) {
        ret.code = Code.MAXLEVEL_FAIL;
        return ret;
    }

    //每10级后必须升星
    let nowStar = trainer.Star;
    let num = Math.floor((level - 1) / 10);
    if(num > nowStar) {
        ret.code = Code.MAXLEVEL_FAIL;
        return ret;
    }

    let levelConfig = dataApi.allData.data["CoachUpgradeExpend"];
    let costMoney = 0;  //欧元
    let costHonor = 0;  //荣誉
    for(let i in levelConfig) {
        if(config.Quality === levelConfig[i].Quality) {
            if(level >= levelConfig[i].LevelMin && level <= levelConfig[i].LevelMax) {
                costMoney = levelConfig[i].Money;
                costHonor = levelConfig[i].Honor;
                break;
            }
        }
    }

    //检查钱是否足够
    if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.cash, costMoney)){
        ret.code = Code.CASH_FALL;
        return ret;
    }

    //检查荣誉是否足够
    if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.honor, costHonor)){
        ret.code = Code.HONOR_FAIL;
        return ret;
    }

    //扣钱
    this.player.deductMoney(commonEnum.PLAY_INFO.cash, costMoney);
    //扣荣誉
    this.player.subtractResource(commonEnum.PLAY_INFO.honor, costHonor);
    trainer.Level++;

    //重新计算教练属性加成
    this.calcTrainerAttr(uid);
    //重新计算所有球员加成
    this.player.heros.reCalcAllHeroAttr();
    ret.code = Code.OK;
    ret.level = trainer.Level;
    return ret;
}





/*----------------------------------------------------------消息结构体-----------------------------------------------------------------------------*/
Trainer.prototype.makeClientTrain = function(uid){
    let clientTrainer = {};
    let trainerObj = this.getOneTrainer(uid);
    if(!trainerObj){
        return clientTrainer;
    }
    clientTrainer.uid = trainerObj.Uid;
    clientTrainer.resId = trainerObj.ResId;
    clientTrainer.level = trainerObj.Level;
    clientTrainer.star = trainerObj.Star;
    clientTrainer.actual = trainerObj.Actual;
    clientTrainer.skillId = trainerObj.SkillId;
    return clientTrainer;
} 

