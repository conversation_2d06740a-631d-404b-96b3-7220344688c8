let mongoClient = require("mongodb").MongoClient;
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let commonEnum = require('../../../../shared/enum');
let fs = require("fs");

let gameConfig = require('../../../config/game.json');
let serversConfig = require('../../../config/servers.json');
let dataApi = require('../dataApi');
let timeUtils = require('../timeUtils');

let isFixed = false;

let dbMap = new Map();
let resultMap = new Map();

let transConfig = {};

let searchDate = "2020-04-01 00:00:00";
let searchTime = (new Date(searchDate)).getTime();

async.waterfall([
    function (callback) {
        async.eachSeries(serversConfig.development.game, function (gameInfo, cb1) {
            let serverId = gameInfo.id;
            let dbUser = serverId + '-admin';
            let gameDBUrl = "mongodb://" + dbUser + ':' + gameConfig.dbPasswd + '@' + gameConfig.gameDBUrl + '/' + serverId;
            mongoClient.connect(gameDBUrl, {useNewUrlParser: true}, function (error, dbclient) {
                if(!!error) {
                    logger.error("connect game db fail.", error, serverId);
                    return cb1(error);
                }
                dbMap.set(serverId, dbclient);
                let db = dbclient.db(serverId);
                let num = 0;
                db.collection("player", function (err, col) {
                    col.find({leaveTime: {$gt: searchTime}}).toArray(function (err, playerList) {
                        db.collection("footballGround", function (err, col1) {
                            async.eachSeries(playerList, function (player, cb2) {
                                col1.findOne({uid: player.uid}, function (err, groundDoc) {
                                    if (err) {
                                        logger.debug("find footballGround err: ", player.uid);
                                        return cb2();
                                    }
                                    //logger.debug("groundDoc: ", groundDoc);
                                    if (!groundDoc || !groundDoc.mainGround) {
                                        return cb2();
                                    }
                                    if(!groundDoc.mainGround[0]) {
                                        logger.debug("groundDoc.mainGround[0] is null: ", groundDoc.mainGround, player.level);
                                        return cb2();
                                    }
                                    if(!groundDoc.groundMatch || !groundDoc.groundMatch.fieldList) {
                                        logger.error("groundMatch is null", player.uid);
                                        return cb2();
                                    }
                                    //清理数据
                                    for(let i=0;i<3;i++) {
                                        let info1 = groundDoc.groundMatch.fieldList[i];
                                        let info2 = groundDoc.groundMatch.occupyFieldList[i];
                                        info1.startTime = timeUtils.now();   //开始自产时间
                                        //占领用户信息
                                        info1.beOccupiedUid = "";            //占领的玩家uid
                                        info1.beOccupiedGid = "";            //占领的玩家gid
                                        info1.beOccupiedTeamUid = "";        //占领的阵容uid
                                        info1.beOccupiedTeamName = "";       //被占领
                                        info1.name = "";                     //占领玩家的名字
                                        info1.faceUrl = "";                  //占领玩家的头像
                                        info1.formationResId = 0;            //阵型id
                                        info1.attack = 0;                    //进攻值
                                        info1.defend = 0;                    //防守值
                                        info1.atkTactic = 0;                 //占领玩家的进攻战术
                                        info1.defTactic = 0;                 //占领玩家的防守战术
                                        info1.str = 0;                       //占领玩家的实力
                                        info1.occupyStartTime = 0;           //占领开始时间
                                        //保护信息
                                        info1.protectType = 0;               //保护类型: 0.无保护 1. 系统保护, 2.道具保护
                                        info1.protectEndTime = 0;            //保护结束时间

                                        info2.resId = 0;               //占领的训练场resId
                                        info2.occupyUid = "";          //占领的玩家Uid
                                        info2.occupyGid = "";          //占领的玩家的Gid
                                        info2.occupyTeamUid = "";      //占领队伍的teamUid (防守队伍)
                                        info2.occupyFaceUrl = "";
                                        info2.occupyTeamIndex = 3;           //占领坑位的index (index等于3, 等于3即是没有占领)
                                        info2.name = "";
                                        info2.protectType = 0;         //保护类型: 0.无保护 1. 系统保护, 2.道具保护
                                        info2.protectEndTime = 0;      //保护结束时间
                                        info2.occupyTime = 0;          //占领开始时间

                                        info2.ballFan = 0;             //占领时的球迷数
                                        info2.mainGroundLevel = 0;     //占领时的球场等级

                                        info2.lastBeReportTime = 0;    //上次被举报时间 (计算CD)
                                        info2.recordList = [];             //占领记录
                                        info2.beReportedList = [];     //被举报列表
                                    }
                                    resultMap.set(player.uid, {sid: serverId, name: player.name});
                                    if(isFixed) {
                                        col1.updateOne({uid: player.uid}, {$set: {
                                                groundMatch: groundDoc.groundMatch
                                            }}, function (err) {
                                            logger.debug("update finish, err: ", player.openId, player.name, err);
                                            cb2();
                                        })
                                    }else {
                                        cb2()
                                    }
                                });
                            }, function () {
                                cb1();
                            });
                        });
                    })
                });
            });
        }, function (err) {
            logger.debug('connect game servers mongodb finish.');
            callback(null);
        })

    }], function (err) {
        //关闭数据库连接
        for(let [k,v] of dbMap) {
            v.close();
        }
        logger.debug("resultMap size: ", resultMap.size);
        let fixData = [];
        for(let [k,v] of resultMap) {
            v.uid = k;
            fixData.push(v);
        }
        let filePath = __dirname + "/clear_ground_field_data.json";
        fs.writeFileSync(filePath, JSON.stringify(fixData));
        process.exit();
});

