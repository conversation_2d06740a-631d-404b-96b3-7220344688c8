let mongoClient = require("mongodb").MongoClient;
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');

let clusterConfig = require("../../../config/cluster.json");
let gameConfig = require("../../../config/game.json");

let playerUid = "w9TH9gToxIvPcjrffl97dL0F";     //無情魒客白教練
let playerGid = "game-outerTest-1";

async.waterfall([
    function (callback) {
        let dbUser = playerGid + '-admin';
        let gameDbUrl = "mongodb://" + dbUser + ':' + gameConfig.dbPasswd + '@' + gameConfig.gameDBUrl + '/' + playerGid;
        mongoClient.connect(gameDbUrl, { useNewUrlParser: true },function(error, dbclient){
            if(error){
                logger.error("connect gameDbUrl failed! err: " + error);
                return callback(error);
            }

            let db = dbclient.db(playerGid);
            db.collection("vipShop", function (err1, col1) {
                col1.findOne({uid: playerUid}, function (err2, doc) {
                    col1.updateOne({uid: playerUid},
                        {$set: {goodsBuyList: [], historyGoodBuyList: []}},
                        function (err3) {
                            logger.debug("update err: ", err3);
                            dbclient.close();
                            callback(null);
                        });
                })
            });
        });
    },
    function (callback) {
        let dbUser = playerGid + '-admin';
        let gameDbUrl = "mongodb://" + dbUser + ':' + gameConfig.dbPasswd + '@' + gameConfig.gameDBUrl + '/' + playerGid;
        mongoClient.connect(gameDbUrl, { useNewUrlParser: true },function(error, dbclient){
            if(error){
                logger.error("connect gameDbUrl failed! err: " + error);
                return callback(error);
            }

            let db = dbclient.db(playerGid);
            db.collection("player", function (err1, col1) {
                col1.findOne({uid: playerUid}, function (err2, doc) {
                    col1.updateOne({uid: playerUid},
                        {$set: {vip: 0, vipExp: 0}},
                        function (err3) {
                            logger.debug("update err: ", err3);
                            dbclient.close();
                            callback(null);
                        });
                })
            });
        });
    },
    function (callback) {
        let dbUser = playerGid + '-admin';
        let gameDbUrl = "mongodb://" + dbUser + ':' + gameConfig.dbPasswd + '@' + gameConfig.gameDBUrl + '/' + playerGid;
        mongoClient.connect(gameDbUrl, { useNewUrlParser: true },function(error, dbclient){
            if(error){
                logger.error("connect gameDbUrl failed! err: " + error);
                return callback(error);
            }

            let db = dbclient.db(playerGid);
            db.collection("bag", function (err1, col1) {
                col1.findOne({uid: playerUid}, function (err2, doc) {
                    doc.itemUidToBookMarkId = [];
                    for(let i in doc.bag) {
                        for(let j in doc.bag[i].Entry.ItemList) {
                            doc.bag[i].Entry.ItemList[j].ItemData.Uid = "";
                        }
                    }

                    col1.updateOne({uid: playerUid}, 
                        {$set: {itemUidToBookMarkId: doc.itemUidToBookMarkId, bag: doc.bag}},
                        function (err3) {
                            logger.debug("update err: ", err3);
                            dbclient.close();
                            callback(null);
                        });
                })
            });
        });
    },function (callback) {
        let dbUser = playerGid + '-admin';
        let gameDbUrl = "mongodb://" + dbUser + ':' + gameConfig.dbPasswd + '@' + gameConfig.gameDBUrl + '/' + playerGid;
        mongoClient.connect(gameDbUrl, { useNewUrlParser: true },function(error, dbclient){
            if(error){
                logger.error("connect gameDbUrl failed! err: " + error);
                return callback(error);
            }
            let db = dbclient.db(playerGid);
            db.collection("item", function (err1, col1) {
                col1.findOne({uid: playerUid}, function (err2, doc) {
                    col1.updateOne({uid: playerUid},
                        {$set: {resId2Uid: [], item: []}},
                        function (err3) {
                            logger.debug("update err: ", err3);
                            dbclient.close();
                            callback(null);
                        });
                })
            });
        });
    }], function (err) {
        logger.debug("fix bag and item data finished .");
        process.exit();
});
