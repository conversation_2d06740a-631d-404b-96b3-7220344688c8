/**
 * Created by aaa on 2015/5/20.
 */

var fs = require('fs');
var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var utils = require('./utils');
var readline = require('readline');
let clusterConfig = require('./../../config/cluster');
let async = require('async');
let mongoClient = require('mongodb').MongoClient;
//var initFile = '../shared/config/data/InitalData.txt';
//var buildInit = '../shared/config/data/BirthData.txt';
//var building = '../shared/config/data/Building.txt';
//var buildingNumLimit = '../shared/config/data/BuildingNumLimit.txt';

var fileDir = __dirname + '/../../../shared/config/data/';
//var fileDir = '../../../shared/config/data/';
var filePath = __dirname + '/../../../shared/config/keyword.txt';
let clusterDBUrl = "mongodb://" + clusterConfig.clusterDBName + '-admin' + ':' + clusterConfig.dbPasswd + '@' + clusterConfig.clusterDBUrl + '/' + clusterConfig.clusterDBName

//存db的文件
let dbConfigList = [
    "ActiveControl",
    "ActiveParam",
    "ActivityConfig",
    "BestFootball",
    "Faith",
    "TurntableReward"
];


var ProxySingletonData = (function () {
    var instance;
    return function (fileDir) {
        if (!instance) {
            instance = new Data(fileDir);
        }
        return instance;
    }
})();

var Data = function (fileDir) {
    let self = this;
    self.data = {};
    fs.readdir(fileDir, function(err, files) {
        if(!files.length) {
            logger.error('read init data config fail! length is null');
            return callback(null);
        }

        // for(let i in files) {
        //     let key = files[i].substring(0,files[i].length-5);
        //     if(dbConfigList.indexOf(key) !== -1) {
        //         // logger.error('init data ----- filename----------: ', key);
        //         continue;
        //     }
        //     self.data[key] = readFile(fileDir+files[i]);
        //     //special deal
        //     specialDeal(key, self);
        // }
        files.forEach(function(file) {
            let key = file.substring(0,file.length-5);
                // if(dbConfigList.indexOf(key) !== -1) {
                //     // logger.error('init data ----- filename----------: ', key);
                //     return false;
                // }
            //logger.debug('init data ----- filename: ', file, key);
            self.data[key] = readFile(fileDir+file);
            //special deal
            specialDeal(key, self);
        });
        //logger.debug('read data result', self.data["Footballer"]["6087"]);
    });
    self.data["keyword"] = [];
    readFileToArr(filePath, function(arr){
        self.data["keyword"] = arr;
    });
};

/*
* 按行读取文件内容
* @return：字符串数组
* @param：fReadName:文件名路径
* */
function readFileToArr(fReadName,callback){
    var fRead = fs.createReadStream(fReadName);
    var objReadline = readline.createInterface({
        input:fRead
    });
    var arr = [];
    objReadline.on('line',function (line) {
        arr.push(line);
        //logger.error('line:'+ line);
    });
    objReadline.on('close',function () {
        //logger.error(arr);
        callback(arr);
    });
}

var specialDeal = function(key, self) {
    //1.Hero Lottery
    if(key == "LuckyHero" || key == "PassiveSkill") {
        amount(key, self, "OddsRMB", "tokenAmount");
        amount(key, self, "OddsGold", "goldAmount");
    }
    //2.PassiveSkill
    if(key == "PassiveSkill") {
        amount(key, self, "OddsRMB", "tokenAmount");
    } 
    //3.Field--球场
    if(key == "Field"){
        category(key, self, "Store", "Storage");
        category(key, self, "Condition", "Grade");
        category(key, self, "Produce", "Output");
    }

    if(key == "Task") {
        newTaskTable(key, self, "NewTask");
    }
};

let newTaskTable = function (key, self, newTable){
    if(!self.data[newTable]){
        self.data[newTable] = {};
    }
    for(let i in self.data[key]) {
        let idx = self.data[key][i].TargetType;
        let info = {
            Id: self.data[key][i].Id,
            Type: self.data[key][i].Type,
            Arg1: self.data[key][i].Arg1,
            Arg2: self.data[key][i].Arg2,
            Arg3: self.data[key][i].Arg3,
        };
        if(!self.data[newTable][idx]) {
            self.data[newTable][idx] = [];
        }
        self.data[newTable][idx].push(info);
    }
};

var category = function (key, self, paramKey, addKey){
    for(let i in self.data[key]) {
        if(!self.data[key][i][addKey]){
            self.data[key][i][addKey] = [];
        }
        if(!self.data[key][i][paramKey] || self.data[key][i][paramKey] === "0"){
            continue;
        }
        let temp = [];
        let strArr = self.data[key][i][paramKey].split('|');
        let index = 0;
        for(let j in strArr){
            temp[index] = {};
            let str = strArr[j].split('*'); 
            temp[index].type = Number(str[0]);
            temp[index].count = Number(str[1]);
            index++;
        }
        self.data[key][i][addKey] = temp; 
    }
};

var amount = function (key, self, paramKey, addKey) {
    if(!self.data[key+"Ex"]) {
        self.data[key+"Ex"] = {};
    }
    var amount = 0;
    for(var tmp in self.data[key]) {
        amount += Number(self.data[key][tmp][paramKey]);
    }
    self.data[key+"Ex"][addKey] = amount;
};

var readFile = function(filePath) {
    let dataInfo = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
    //logger.debug("readFile: ", dataInfo);
    let result = {};
    for(let index in dataInfo) {
        let oneData = dataInfo[index];
        if(oneData["Id"] || oneData["entityId"] || oneData["id"] || oneData["ID"] || oneData["ModeID"]) {
            let key = oneData["Id"] || oneData["entityId"] || oneData["id"] || oneData["ID"] || oneData["ModeID"];
            result[key] = oneData;
            // if(filePath == fileDir+"Account.json") {
            //     logger.debug('Account result oneData: ', key, result[key]);
            // }
        }
    }
    
    // if(filePath == fileDir+"Account.json") {
    //     logger.debug('Account result: ', result);
    // }
    return result;
};

//new Data(initFile);

Data.prototype.getByTableName = function(tableName) {
    if(!this.data[tableName]) {
        logger.error('cannot find the table name in data',tableName);
        return;
    }
    return this.data[tableName];
};

Data.prototype.getSystemTableParam = function(id) {
    return this.data["SystemParam"][id].Param;
};

Data.prototype.readWholeConfig = function(cb) {
    let self = this;
    async.waterfall([
        function (callback) {
            mongoClient.connect(clusterDBUrl, {useNewUrlParser: true}, function (error, dbclient) {
                if (!!error) {
                    return cb(null);
                }
                callback(null, dbclient);
            })
        },
        function (dbclient, callback) {
            let db = dbclient.db(clusterConfig.clusterDBName);
            let collection = db.collection("configData");
            collection.find().toArray().then(function (allConfig) {
                for(let i in allConfig) {
                    let data = allConfig[i].data.toDbData;
                    let key = allConfig[i].tableName;
                    if(!data) {
                        continue;
                    }
                    self.data[key] = makeFileInfo(data);
                    //特殊转换
                    specialDeal(key, self);
                }
                callback(null, dbclient);
            });
        },
        function (dbclient, callback) {
            dbclient.close();
            callback(null);
        },
    ],function (err) {
        cb();
    })
};

let makeFileInfo = function(dataInfo) {
    let result = {};
    for(let index in dataInfo) {
        let oneData = dataInfo[index];
        if(oneData["Id"] || oneData["entityId"] || oneData["id"] || oneData["ID"] || oneData["ModeID"]) {
            let key = oneData["Id"] || oneData["entityId"] || oneData["id"] || oneData["ID"] || oneData["ModeID"];
            result[key] = oneData;
        }
    }
    return result;
};

module.exports = {
    allData: new ProxySingletonData(fileDir)
    //allData: new Data(fileDir)
};

