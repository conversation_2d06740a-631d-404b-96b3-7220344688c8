{"appenders": [{"type": "dateFile", "filename": "./logs/node-log-${opts:serverId}.log", "pattern": ".yyyy-MM-dd-hh", "layout": {"type": "basic"}, "alwaysIncludePattern": true, "daysToKeep": 7, "category": "node-log"}, {"type": "console"}, {"type": "dateFile", "filename": "${opts:base}/logs/con-log-${opts:serverId}.log", "pattern": ".yyyy-MM-dd-hh", "layout": {"type": "basic"}, "alwaysIncludePattern": true, "daysToKeep": 7, "category": "con-log"}, {"type": "dateFile", "filename": "${opts:base}/logs/crash.log", "pattern": ".yyyy-MM-dd-hh", "layout": {"type": "basic"}, "alwaysIncludePattern": true, "daysToKeep": 7, "category": "crash-log"}, {"type": "dateFile", "filename": "${opts:base}/logs/admin.log", "pattern": ".yyyy-MM-dd-hh", "layout": {"type": "basic"}, "alwaysIncludePattern": true, "daysToKeep": 7, "category": "admin-log"}, {"type": "dateFile", "filename": "${opts:base}/logs/pomelo-${opts:serverId}.log", "pattern": ".yyyy-MM-dd-hh", "layout": {"type": "basic"}, "alwaysIncludePattern": true, "daysToKeep": 7, "category": "pomelo"}, {"type": "dateFile", "filename": "${opts:base}/logs/pomelo-admin.log", "pattern": ".yyyy-MM-dd-hh", "layout": {"type": "basic"}, "alwaysIncludePattern": true, "daysToKeep": 7, "category": "pomelo-admin"}, {"type": "dateFile", "filename": "${opts:base}/logs/pomelo-rpc-${opts:serverId}.log", "pattern": ".yyyy-MM-dd-hh", "layout": {"type": "basic"}, "alwaysIncludePattern": true, "daysToKeep": 7, "category": "pomelo-rpc"}], "levels": {"node-log": "DEBUG", "con-log": "DEBUG", "crash-log": "DEBUG", "pomelo": "DEBUG", "pomelo-admin": "DEBUG", "pomelo-rpc": "DEBUG"}, "replaceConsole": true, "lineDebug": true}