[{"type": "gate", "token": "agarxhqb38fpajloaxn14ga8xrunpagkjwlaw5ruxnpaagl23w4rxn"}, {"type": "auth", "token": "agarxhqb75bpajloaxn74ga8xrunpagkjwlaw6ruxnpaagl24w4rxn"}, {"type": "datanode", "token": "agarxhqb95wpajloaxn94ga8xrunpagkjwlaw8ruxnpaagl25w4rxn"}, {"type": "league", "token": "agarxhqb95wpajloaxn94ga8xrunpagkjwlaw8ruxnpaagl25w4rxn"}, {"type": "brecord", "token": "agarxhqb95wpajloaxn94ga8xrunpagkjwlaw8ruxnpaagl25w4rxn"}, {"type": "gconnector", "token": "agarxhqb95wpajloaxn94ga8xrunpagkjwlaw8ruxnpaagl25w4rxn"}, {"type": "bconnector", "token": "agarxhqb95wpajloaxn94ga8xrunpagkjwlaw8ruxnpaagl25w4rxn"}, {"type": "game", "token": "agarxhqb95wpajloaxn94ga8xrunpagkjwlaw8ruxnpaagl25w4rxn"}, {"type": "battle", "token": "agarxhqb95wpajloaxn94ga8xrunpagkjwlaw8ruxnpaagl25w4rxn"}, {"type": "match", "token": "agarxhqb95wpajloaxn94ga8xrunpagkjwlaw8ruxnpaagl25w4rxn"}, {"type": "league", "token": "agarxhqb95wpajloaxn94ga8xrunpagkjwlaw8ruxnpaagl25w4rxn"}, {"type": "pay", "token": "agarxhqb95wpajloaxn94ga8xrunpagkjwlaw8ruxnpaagl25w4rxn"}, {"type": "slog", "token": "agarxhqb95wpajloaxn94ga8xrunpagkjwlaw8ruxnpaagl25w4rxn"}]