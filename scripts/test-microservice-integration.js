/**
 * 微服务集成测试脚本 (microservice-kit 版本)
 *
 * 测试目标：
 * 1. 新的 microservice-kit 库集成验证
 * 2. ConfigModule 配置管理验证
 * 3. 网关HTTP代理功能 (认证API)
 * 4. 路径重写功能验证
 * 5. 网关WebSocket代理功能 (实时通信)
 * 6. 微服务透明调用 (通过WebSocket)
 * 7. Redis通信层验证 (内网服务器)
 * 8. 端到端业务流程
 */

const { io } = require('socket.io-client');
const axios = require('axios');
const chalk = require('chalk');

// 配置
const CONFIG = {
  GATEWAY_URL: 'http://127.0.0.1:3000',
  GATEWAY_WS_URL: 'http://127.0.0.1:3000',
  AUTH_URL: 'http://127.0.0.1:3001',
  REDIS_CONFIG: {
    host: '***************',
    port: 6379,
    password: '123456',
    db: 6  // 微服务通信专用数据库
  },
  TEST_USER: {
    username: 'testuser_' + Date.now(),
    email: 'test_' + Date.now() + '@example.com',
    password: 'SecureP@ssw0rd!',
    confirmPassword: 'SecureP@ssw0rd!',
    acceptTerms: true,
    profile: {
      firstName: 'Test',
      lastName: 'User'
    }
  }
};

class MicroserviceIntegrationTester {
  constructor() {
    this.socket = null;
    this.authToken = null;
    this.testResults = [];
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log(chalk.blue('🚀 开始微服务集成测试 (新路由系统)\n'));

    try {
      // 1. microservice-kit 配置验证
      await this.testMicroserviceKitConfig();

      // 2. 新路由系统测试
      await this.testNewRoutingSystem();

      // 3. 基础连接测试
      await this.testBasicConnectivity();

      // 4. 路径重写测试
      await this.testPathRewriting();

      // 5. 用户注册测试
      await this.testUserRegistration();

      // 6. 用户登录测试
      await this.testUserLogin();

      // 7. WebSocket连接测试
      await this.testWebSocketConnection();

      // 8. 透明微服务调用测试
      await this.testTransparentMicroserviceCalls();

      // 9. 权限检查测试
      await this.testPermissionCheck();

      // 10. 批量调用测试
      await this.testBatchCalls();

      // 11. 错误处理测试
      await this.testErrorHandling();

      // 输出测试结果
      this.printTestResults();

    } catch (error) {
      console.error(chalk.red('❌ 测试执行失败:'), error.message);
    } finally {
      if (this.socket) {
        this.socket.disconnect();
      }
    }
  }

  /**
   * 测试 microservice-kit 配置
   */
  async testMicroserviceKitConfig() {
    console.log(chalk.yellow('⚙️ 测试 microservice-kit 配置...'));

    try {
      // 测试网关服务是否正确加载了 microservice-kit
      const gatewayHealth = await axios.get(`${CONFIG.GATEWAY_URL}/health`);
      this.addTestResult('网关服务启动', gatewayHealth.status === 200, '网关服务正常运行');

      // 测试认证服务是否正确加载了 microservice-kit
      const authHealth = await axios.get(`${CONFIG.AUTH_URL}/health`);
      this.addTestResult('认证服务启动', authHealth.status === 200, '认证服务正常运行');

      // 测试 Redis 连接配置
      const gatewayDetailedHealth = await axios.get(`${CONFIG.GATEWAY_URL}/health/detailed`);
      const redisStatus = gatewayDetailedHealth.data?.details?.redis?.status;
      this.addTestResult('Redis 连接配置', redisStatus === 'up', `Redis 状态: ${redisStatus}`);

      // 测试微服务配置是否正确加载
      const authDetailedHealth = await axios.get(`${CONFIG.AUTH_URL}/health/detailed`);
      const authRedisStatus = authDetailedHealth.data?.details?.redis?.status;
      this.addTestResult('认证服务 Redis 配置', authRedisStatus === 'up', `认证服务 Redis 状态: ${authRedisStatus}`);

      console.log(chalk.green('✅ microservice-kit 配置验证完成'));

    } catch (error) {
      this.addTestResult('microservice-kit 配置测试', false, error.message);
      console.log(chalk.red('❌ microservice-kit 配置验证失败:', error.message));
    }
  }

  /**
   * 测试新路由系统
   */
  async testNewRoutingSystem() {
    console.log(chalk.yellow('🛣️ 测试新路由系统...'));

    try {
      // 测试系统路由
      const healthResponse = await axios.get(`${CONFIG.GATEWAY_URL}/health`);
      this.addTestResult('系统路由 - /health', healthResponse.status === 200, '网关健康检查');

      const docsResponse = await axios.get(`${CONFIG.GATEWAY_URL}/docs`);
      this.addTestResult('系统路由 - /docs', docsResponse.status === 200, 'Swagger文档');

      // 测试微服务路由
      const authHealthResponse = await axios.get(`${CONFIG.GATEWAY_URL}/api/auth/health`);
      this.addTestResult('微服务路由 - /api/auth/health', authHealthResponse.status === 200, '认证服务健康检查');

      // 测试错误处理
      try {
        await axios.get(`${CONFIG.GATEWAY_URL}/api/unknown/test`);
        this.addTestResult('错误处理 - 未知服务', false, '应该返回404');
      } catch (error) {
        this.addTestResult('错误处理 - 未知服务', error.response?.status === 404, '正确返回404');
      }

    } catch (error) {
      this.addTestResult('新路由系统测试', false, error.message);
    }
  }

  /**
   * 测试基础连接
   */
  async testBasicConnectivity() {
    console.log(chalk.yellow('📡 测试基础连接...'));

    try {
      // 测试网关系统路由
      const gatewayHealth = await axios.get(`${CONFIG.GATEWAY_URL}/health`);
      this.addTestResult('网关健康检查 (系统路由)', gatewayHealth.status === 200, gatewayHealth.data);

      // 测试认证服务直接访问
      const authHealth = await axios.get(`${CONFIG.AUTH_URL}/health`);
      this.addTestResult('认证服务健康检查 (直接访问)', authHealth.status === 200, authHealth.data);

      // 测试通过网关代理访问认证服务
      const authProxyHealth = await axios.get(`${CONFIG.GATEWAY_URL}/api/auth/health`);
      this.addTestResult('认证服务健康检查 (网关代理)', authProxyHealth.status === 200, authProxyHealth.data);

    } catch (error) {
      this.addTestResult('基础连接测试', false, error.message);
    }
  }

  /**
   * 测试路径重写
   */
  async testPathRewriting() {
    console.log(chalk.yellow('🔄 测试路径重写...'));

    try {
      // 测试 /api/auth/health -> /health 的路径重写
      const response = await axios.get(`${CONFIG.GATEWAY_URL}/api/auth/health`);

      // 验证响应来自认证服务
      const isFromAuthService = response.data &&
        (response.data.service === 'auth' || response.data.status === 'ok');

      this.addTestResult('路径重写 - /api/auth/health -> /health',
        response.status === 200 && isFromAuthService,
        {
          status: response.status,
          isFromAuthService,
          hasCorrectData: !!response.data
        });

    } catch (error) {
      this.addTestResult('路径重写测试', false, error.message);
    }
  }

  /**
   * 测试用户注册
   */
  async testUserRegistration() {
    console.log(chalk.yellow('👤 测试用户注册...'));

    try {
      const response = await axios.post(`${CONFIG.GATEWAY_URL}/api/auth/auth/register`, CONFIG.TEST_USER,{
        timeout: 10000
      });
      this.addTestResult('用户注册', response.status === 201, response.data);
    } catch (error) {
      // 如果用户已存在，这是正常的
      if (error.response?.status === 409) {
        this.addTestResult('用户注册', true, '用户已存在（正常）');
      } else {
        this.addTestResult('用户注册', false, error.message);
      }
    }
  }

  /**
   * 测试用户登录
   */
  async testUserLogin() {
    console.log(chalk.yellow('🔐 测试用户登录...'));

    try {
      const response = await axios.post(`${CONFIG.GATEWAY_URL}/api/auth/auth/login`, {
        username: CONFIG.TEST_USER.username,
        password: CONFIG.TEST_USER.password
      });

      const success = response.status === 200 && response.data.success && response.data.data.tokens.accessToken;
      if (success) {
        this.authToken = response.data.data.tokens.accessToken;
      }

      this.addTestResult('用户登录', success, response.data);
    } catch (error) {
      this.addTestResult('用户登录', false, error.message);
    }
  }

  /**
   * 测试WebSocket连接
   */
  async testWebSocketConnection() {
    console.log(chalk.yellow('🔌 测试WebSocket连接...'));

    // 检查是否有认证令牌
    if (!this.authToken) {
      this.addTestResult('WebSocket连接', false, '缺少认证令牌，请先登录');
      return;
    }

    console.log(chalk.gray(`  使用令牌: ${this.authToken.substring(0, 20)}...`));

    return new Promise((resolve) => {
      try {
        this.socket = io(CONFIG.GATEWAY_WS_URL, {
          transports: ['websocket', 'polling'],
          timeout: 5000,
          forceNew: true,
          auth: {
            token: this.authToken
          }
        });

        this.socket.on('connect', () => {
          this.addTestResult('WebSocket连接', true, '连接成功');
          resolve();
        });

        this.socket.on('connect_error', (error) => {
          this.addTestResult('WebSocket连接', false, error.message);
          resolve();
        });

        // 超时处理
        setTimeout(() => {
          if (!this.socket.connected) {
            this.addTestResult('WebSocket连接', false, '连接超时');
            resolve();
          }
        }, 5000);

      } catch (error) {
        this.addTestResult('WebSocket连接', false, error.message);
        resolve();
      }
    });
  }

  /**
   * 测试透明微服务调用
   */
  async testTransparentMicroserviceCalls() {
    console.log(chalk.yellow('🔄 测试透明微服务调用...'));

    if (!this.socket || !this.socket.connected) {
      this.addTestResult('透明微服务调用', false, 'WebSocket未连接');
      return;
    }

    // 测试多个微服务调用
    const tests = [
      {
        name: 'verifyToken',
        service: 'auth',
        action: 'verifyToken',
        payload: { token: this.authToken }
      }
    ];

    for (const test of tests) {
      await this.testSingleMicroserviceCall(test);
    }
  }

  /**
   * 测试单个微服务调用
   */
  async testSingleMicroserviceCall(testConfig) {
    return new Promise((resolve) => {
      const requestId = `test_${testConfig.name}_${Date.now()}`;
      const message = {
        id: requestId,
        command: `${testConfig.service}.${testConfig.action}`,  // 使用新的 command 格式
        payload: testConfig.payload
      };

      console.log(chalk.gray(`  📡 测试微服务调用: ${testConfig.service}.${testConfig.action}`));

      // 监听响应
      const responseHandler = (response) => {
        if (response.id === requestId) {
          const success = response.type === 'response' && response.payload && response.payload.success !== false;

          this.addTestResult(
            `透明微服务调用 - ${testConfig.name}`,
            success,
            success ?
              `调用成功: ${JSON.stringify(response.payload).substring(0, 100)}...` :
              `调用失败: ${response.payload?.error || '未知错误'}`
          );

          this.socket.off('message', responseHandler);
          resolve();
        }
      };

      this.socket.on('message', responseHandler);

      // 发送微服务调用
      this.socket.emit('message', message);

      // 超时处理 - 减少到5秒
      setTimeout(() => {
        this.socket.off('message', responseHandler);
        this.addTestResult(`透明微服务调用 - ${testConfig.name}`, false, '调用超时 (5秒)');
        resolve();
      }, 5000);
    });
  }

  /**
   * 测试权限检查
   */
  async testPermissionCheck() {
    console.log(chalk.yellow('🛡️ 测试权限检查...'));

    if (!this.socket || !this.socket.connected) {
      this.addTestResult('权限检查', false, 'WebSocket未连接');
      return;
    }

    return new Promise((resolve) => {
      const requestId = `test_check_permission_${Date.now()}`;
      const message = {
        id: requestId,
        command: 'auth.checkPermission',  // 使用新的 command 格式
        payload: {
          userId: this.userId || 'test-user-id',
          resource: 'player',
          action: 'transfer'
        }
      };

      console.log(chalk.gray(`  🔐 测试权限检查: ${message.payload.resource}.${message.payload.action}`));

      const responseHandler = (response) => {
        if (response.id === requestId) {
          const success = response.type === 'response' && response.payload;

          this.addTestResult(
            '权限检查',
            success,
            success ?
              `权限检查完成: ${JSON.stringify(response.payload).substring(0, 100)}...` :
              `权限检查失败: ${response.payload?.error || '未知错误'}`
          );

          this.socket.off('message', responseHandler);
          resolve();
        }
      };

      this.socket.on('message', responseHandler);
      this.socket.emit('message', message);

      setTimeout(() => {
        this.socket.off('message', responseHandler);
        this.addTestResult('权限检查', false, '调用超时 (5秒)');
        resolve();
      }, 5000);
    });
  }

  /**
   * 测试批量调用
   */
  async testBatchCalls() {
    console.log(chalk.yellow('📦 测试批量调用...'));

    if (!this.socket || !this.socket.connected) {
      this.addTestResult('批量调用', false, 'WebSocket未连接');
      return;
    }

    // 测试多个并发调用
    const calls = [
      { command: 'auth.verifyToken', payload: { token: this.authToken } },
      { command: 'auth.checkPermission', payload: { userId: this.userId || 'test-user', resource: 'test', action: 'read' } },
      { command: 'auth.verifyToken', payload: { token: this.authToken } }
    ];

    console.log(chalk.gray(`  📦 发送 ${calls.length} 个并发调用...`));

    const promises = calls.map((call, index) =>
      this.testSingleCall(`batch_${index}`, call)
    );

    try {
      const results = await Promise.allSettled(promises);
      const successCount = results.filter(r => r.status === 'fulfilled' && r.value).length;

      this.addTestResult(
        '批量调用',
        successCount > 0,
        `${successCount}/${calls.length} 个调用成功`
      );
    } catch (error) {
      this.addTestResult('批量调用', false, `批量调用异常: ${error.message}`);
    }
  }

  /**
   * 测试单个调用（用于批量测试）
   */
  async testSingleCall(callId, callConfig) {
    return new Promise((resolve) => {
      const requestId = `${callId}_${Date.now()}`;
      const message = {
        id: requestId,
        command: callConfig.command,  // 使用新的 command 格式
        payload: callConfig.payload
      };

      const responseHandler = (response) => {
        if (response.id === requestId) {
          const success = response.type === 'response';
          this.socket.off('message', responseHandler);
          resolve(success);
        }
      };

      this.socket.on('message', responseHandler);
      this.socket.emit('message', message);

      // 较短的超时时间
      setTimeout(() => {
        this.socket.off('message', responseHandler);
        resolve(false);
      }, 3000);
    });
  }

  /**
   * 测试错误处理
   */
  async testErrorHandling() {
    console.log(chalk.yellow('⚠️ 测试错误处理...'));

    // 首先测试HTTP路由错误处理
    await this.testHttpErrorHandling();

    // 然后测试WebSocket微服务调用错误处理
    await this.testWebSocketErrorHandling();
  }

  /**
   * 测试HTTP路由错误处理
   */
  async testHttpErrorHandling() {
    console.log(chalk.gray('  📡 测试HTTP路由错误处理...'));

    try {
      // 1. 测试服务不可用 (user服务未启动)
      try {
        await axios.get(`${CONFIG.GATEWAY_URL}/api/user/health`);
        this.addTestResult('HTTP错误 - 服务不可用', false, '应该返回502');
      } catch (error) {
        this.addTestResult('HTTP错误 - 服务不可用',
          error.response?.status === 502,
          `返回状态: ${error.response?.status}`);
      }

      // 2. 测试旧API文档路径
      try {
        await axios.get(`${CONFIG.GATEWAY_URL}/api/docs`);
        this.addTestResult('HTTP错误 - 旧路径', false, '旧路径/api/docs应该404');
      } catch (error) {
        this.addTestResult('HTTP错误 - 旧路径',
          error.response?.status === 404,
          `返回状态: ${error.response?.status}`);
      }

    } catch (error) {
      this.addTestResult('HTTP错误处理测试', false, error.message);
    }
  }

  /**
   * 测试WebSocket微服务调用错误处理
   */
  async testWebSocketErrorHandling() {
    console.log(chalk.gray('  🔌 测试WebSocket微服务调用错误处理...'));

    if (!this.socket || !this.socket.connected) {
      this.addTestResult('WebSocket错误处理', false, 'WebSocket未连接');
      return;
    }

    return new Promise((resolve) => {
      // 测试不存在的方法
      const requestId = `test_websocket_error_${Date.now()}`;
      const message = {
        id: requestId,
        command: 'auth.nonExistentMethod',  // 使用新的 command 格式
        payload: {}
      };

      console.log(chalk.gray(`  ❌ 测试错误处理: 调用不存在的方法`));

      const responseHandler = (response) => {
        if (response.id === requestId) {
          // 应该返回错误
          const isError = response.type === 'error' || (response.payload && response.payload.error);

          this.addTestResult(
            'WebSocket错误处理',
            isError,
            isError ?
              `正确返回错误: ${response.payload?.error || response.error}` :
              '未返回预期错误'
          );

          this.socket.off('message', responseHandler);
          resolve();
        }
      };

      this.socket.on('message', responseHandler);
      this.socket.emit('message', message);

      setTimeout(() => {
        this.socket.off('message', responseHandler);
        this.addTestResult('WebSocket错误处理', false, '调用超时 (5秒)');
        resolve();
      }, 5000);
    });
  }

  /**
   * 添加测试结果
   */
  addTestResult(testName, success, details) {
    this.testResults.push({
      name: testName,
      success,
      details,
      timestamp: new Date()
    });

    const status = success ? chalk.green('✅') : chalk.red('❌');
    console.log(`  ${status} ${testName}: ${success ? '成功' : '失败'}`);
    if (details && typeof details === 'object') {
      console.log(`     详情: ${JSON.stringify(details, null, 2)}`);
    } else if (details) {
      console.log(`     详情: ${details}`);
    }
  }

  /**
   * 打印测试结果
   */
  printTestResults() {
    console.log('\n' + chalk.blue('📊 测试结果汇总'));
    console.log('='.repeat(50));

    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;

    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${chalk.green(passedTests)}`);
    console.log(`失败: ${chalk.red(failedTests)}`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (failedTests > 0) {
      console.log('\n' + chalk.red('失败的测试:'));
      this.testResults
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`  ❌ ${r.name}: ${r.details}`);
        });
    }

    console.log('\n' + chalk.blue('测试完成!'));
  }

  /**
   * 等待指定时间
   */
  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 运行测试
if (require.main === module) {
  const tester = new MicroserviceIntegrationTester();
  tester.runAllTests().catch(console.error);
}

module.exports = MicroserviceIntegrationTester;
