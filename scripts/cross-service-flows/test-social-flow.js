/**
 * 社交流程测试脚本
 * 测试场景：加入公会→好友互动→公会活动
 * 
 * 涉及服务：
 * - Auth服务：用户认证
 * - Character服务：角色信息
 * - Social服务：好友系统、公会系统、聊天系统
 * - Activity服务：公会活动
 */

const axios = require('axios');
const chalk = require('chalk');
const path = require('path');
const MicroserviceWebSocketClient = require('../common/websocket-client');

// 加载根目录的.env配置
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

// 解析命令行参数
const args = process.argv.slice(2);
const existingCharacterId = args.length > 0 ? args[0] : null;

// 配置
const CONFIG = {
  GATEWAY_WS_URL: 'http://127.0.0.1:3000',
  AUTH_URL: 'http://127.0.0.1:3001',
  TEST_USER_ID: 'social-test-user-' + Date.now(),
  TEST_SERVER_ID: 'server_001',
  TIMEOUT: 30000
};

// 测试数据存储
const TEST_DATA = {
  token: null,
  characterId: existingCharacterId,
  userId: null,
  serverId: CONFIG.TEST_SERVER_ID
};

class SocialFlowTester extends MicroserviceWebSocketClient {
  constructor() {
    super(CONFIG);  // 传递完整的CONFIG对象
    this.testResults = [];
    this.currentTestIndex = 1;  // 添加测试索引
    this.token = null;  // 添加token属性
    this.isConnected = false;  // 添加连接状态
  }

  /**
   * 主测试流程
   */
  async runTests() {
    console.log(chalk.blue('🚀 开始社交流程测试'));
    console.log(chalk.gray(`测试角色ID: ${TEST_DATA.characterId || '需要提供角色ID'}`));

    if (!TEST_DATA.characterId) {
      console.log(chalk.red('❌ 请提供现有角色ID作为参数'));
      console.log(chalk.yellow('用法: node test-social-flow.js <characterId>'));
      process.exit(1);
    }

    try {
      // 第1步：用户认证
      console.log(chalk.blue('\n=== 第1步：用户认证 ==='));
      await this.setupAuthentication();

      // 第2步：搜索并加入公会
      console.log(chalk.blue('\n=== 第2步：搜索并加入公会 ==='));
      await this.testJoinGuild();

      // 第3步：添加好友
      console.log(chalk.blue('\n=== 第3步：添加好友 ==='));
      await this.testAddFriends();

      // 第4步：好友互动
      console.log(chalk.blue('\n=== 第4步：好友互动 ==='));
      await this.testFriendInteraction();

      // 第5步：公会聊天
      console.log(chalk.blue('\n=== 第5步：公会聊天 ==='));
      await this.testGuildChat();

      // 第6步：参与公会活动
      console.log(chalk.blue('\n=== 第6步：参与公会活动 ==='));
      await this.testGuildActivity();

      // 第7步：测试结果汇总
      this.printTestSummary();

    } catch (error) {
      console.error(chalk.red('❌ 社交流程测试失败:'), error.message);
      process.exit(1);
    } finally {
      await this.disconnect();
    }
  }

  /**
   * 设置认证（直接照搬成功的social脚本代码）
   */
  async setupAuthentication() {
    try {
      console.log(chalk.yellow('🔑 获取认证令牌...'));

      // 先注册用户（通过网关）
      const username = `socialtest_${Date.now()}`;
      const email = `socialtest_${Date.now()}@example.com`;
      const password = 'SecureP@ssw0rd!';

      const registerResponse = await axios.post(`${CONFIG.GATEWAY_WS_URL}/api/auth/auth/register`, {
        username: username,
        email: email,
        password: password,
        confirmPassword: password,
        acceptTerms: true,
        profile: {
          firstName: 'Test',
          lastName: 'User'
        }
      }, { timeout: 10000 });

      if (registerResponse.data.success) {
        console.log(chalk.green('✅ 用户注册成功'));

        // 然后登录获取token（通过网关）
        const loginResponse = await axios.post(`${CONFIG.GATEWAY_WS_URL}/api/auth/auth/login`, {
          username: username,  // 使用username而不是username
          password: password
        }, { timeout: 10000 });

        // 正确解析token位置（参考character测试脚本的成功模式）
        const accessToken = loginResponse.data.data?.tokens?.accessToken;
        if (loginResponse.status === 200 && accessToken) {
          this.token = accessToken;
          TEST_DATA.token = accessToken;
          console.log(chalk.green('✅ 认证令牌获取成功'));
          console.log(chalk.gray(`Token长度: ${accessToken.length}`));

          // 建立WebSocket连接
          await this.connectWebSocket();

          this.testResults.push({
            step: '用户认证',
            success: true
          });

          return this.token;
        } else {
          console.log(chalk.red('❌ 登录失败，无法获取令牌'));
          console.log(chalk.red('登录响应:', JSON.stringify(loginResponse.data, null, 2)));
          throw new Error('登录失败或未获取到token');
        }
      } else {
        throw new Error('用户注册失败');
      }
    } catch (error) {
      console.log(chalk.red(`❌ 认证失败: ${error.message}`));
      this.testResults.push({
        step: '用户认证',
        success: false,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 建立WebSocket连接（直接照搬成功的social脚本代码）
   */
  async connectWebSocket() {
    return new Promise((resolve, reject) => {
      console.log(chalk.yellow('🔌 建立 WebSocket 连接...'));

      // 使用正确的认证方式：在连接时传递token
      this.socket = require('socket.io-client')(CONFIG.GATEWAY_WS_URL, {
        auth: { token: this.token },  // 在连接时传递token
        timeout: CONFIG.TIMEOUT,
        transports: ['websocket', 'polling'],  // 支持多种传输方式
        forceNew: true
      });

      this.socket.on('connect', () => {
        console.log(chalk.green('✅ WebSocket 连接和认证成功'));
        this.isConnected = true;
        resolve(true);
      });

      this.socket.on('connect_error', (error) => {
        console.log(chalk.red(`❌ WebSocket 连接失败: ${error.message}`));
        this.isConnected = false;
        reject(error);
      });

      this.socket.on('disconnect', (reason, details) => {
        console.log(chalk.yellow(`⚠️ WebSocket 连接断开: ${reason}`));
        if (details) {
          console.log(chalk.gray(`断开详情: ${JSON.stringify(details)}`));
        }
        this.isConnected = false;
      });
    });
  }

  /**
   * 测试加入公会
   */
  async testJoinGuild() {
    console.log(chalk.yellow('🏰 测试搜索并加入公会...'));

    try {
      // 先搜索公会（参考成功的公会测试脚本）
      const searchResponse = await this.sendMessage('social.guild.search', {
        keyword: '测试',
        page: 1,
        limit: 10
      });

      const searchSuccess = searchResponse?.payload?.data?.code === 0;
      const searchData = searchResponse?.payload?.data?.data;

      if (searchSuccess && searchData) {
        console.log(chalk.green('✅ 搜索公会成功'));
        console.log(chalk.gray(`   找到公会: ${searchData.guilds ? searchData.guilds.length : 0}个`));

        // 尝试申请加入公会（使用正确的接口）
        if (searchData.guilds && searchData.guilds.length > 0) {
          const firstGuild = searchData.guilds[0];
          const joinResponse = await this.sendMessage('social.guild.apply', {
            guildId: firstGuild.guildId,
            playerId: TEST_DATA.characterId,
            playerName: `测试玩家${Date.now()}`,
            gid: `gid_${Date.now()}`
          });

          const joinSuccess = joinResponse?.payload?.data?.code === 0;
          if (joinSuccess) {
            console.log(chalk.green('✅ 申请加入公会成功'));
          } else {
            console.log(chalk.yellow('⚠️ 加入公会申请已发送或已在公会中'));
          }
        }
        
        this.testResults.push({
          step: '加入公会',
          success: true,
          data: searchData
        });
      } else {
        const errorMsg = searchResponse?.payload?.data?.message || '未知错误';
        throw new Error(`搜索公会失败: ${errorMsg}`);
      }
    } catch (error) {
      console.log(chalk.red(`❌ 加入公会失败: ${error.message}`));
      this.testResults.push({
        step: '加入公会',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试添加好友
   */
  async testAddFriends() {
    console.log(chalk.yellow('👥 测试添加好友...'));

    try {
      // 搜索潜在好友
      const searchResponse = await this.sendMessage('social.friend.search', {
        playerId: TEST_DATA.characterId,
        searchDto: {
          keyword: 'test'
        }
      });

      const searchSuccess = searchResponse?.payload?.data?.code === 0;
      const searchData = searchResponse?.payload?.data?.data;

      if (searchSuccess && searchData) {
        console.log(chalk.green('✅ 搜索用户成功'));
        console.log(chalk.gray(`   找到用户: ${searchData.users ? searchData.users.length : 0}个`));

        // 尝试添加第一个用户为好友
        if (searchData.users && searchData.users.length > 0) {
          const firstUser = searchData.users[0];
          const addResponse = await this.sendMessage('social.friend.add', {
            playerId: TEST_DATA.characterId,
            addDto: {
              targetPlayerId: firstUser.playerId,
              message: '你好，我想加你为好友！'
            }
          });

          const addSuccess = addResponse?.payload?.data?.code === 0;
          if (addSuccess) {
            console.log(chalk.green('✅ 发送好友请求成功'));
          }
        }
        
        this.testResults.push({
          step: '添加好友',
          success: true,
          data: searchData
        });
      } else {
        const errorMsg = searchResponse?.payload?.data?.message || '未知错误';
        throw new Error(`搜索用户失败: ${errorMsg}`);
      }
    } catch (error) {
      console.log(chalk.red(`❌ 添加好友失败: ${error.message}`));
      this.testResults.push({
        step: '添加好友',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试好友互动
   */
  async testFriendInteraction() {
    console.log(chalk.yellow('💬 测试好友互动...'));

    try {
      // 获取好友列表
      const friendListResponse = await this.sendMessage('social.friend.getList', {
        playerId: TEST_DATA.characterId
      });

      const listSuccess = friendListResponse?.payload?.data?.code === 0;
      const listData = friendListResponse?.payload?.data?.data;

      if (listSuccess && listData) {
        console.log(chalk.green('✅ 获取好友列表成功'));
        console.log(chalk.gray(`   好友数量: ${listData.friends ? listData.friends.length : 0}`));

        // 尝试发送私聊消息给第一个好友
        if (listData.friends && listData.friends.length > 0) {
          const firstFriend = listData.friends[0];
          const chatResponse = await this.sendMessage('social.chat.sendPrivate', {
            playerId: TEST_DATA.characterId,
            messageDto: {
              targetPlayerId: firstFriend.playerId,
              content: '你好，这是一条测试消息！',
              messageType: 1
            }
          });

          const chatSuccess = chatResponse?.payload?.data?.code === 0;
          if (chatSuccess) {
            console.log(chalk.green('✅ 发送私聊消息成功'));
          }
        }
        
        this.testResults.push({
          step: '好友互动',
          success: true,
          data: listData
        });
      } else {
        const errorMsg = friendListResponse?.payload?.data?.message || '未知错误';
        throw new Error(`好友互动失败: ${errorMsg}`);
      }
    } catch (error) {
      console.log(chalk.red(`❌ 好友互动失败: ${error.message}`));
      this.testResults.push({
        step: '好友互动',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试公会聊天
   */
  async testGuildChat() {
    console.log(chalk.yellow('🏰💬 测试公会聊天...'));

    try {
      // 发送公会消息
      const chatResponse = await this.sendMessage('social.chat.sendGuild', {
        playerId: TEST_DATA.characterId,
        messageDto: {
          content: '大家好，这是一条测试公会消息！',
          messageType: 1
        }
      });

      const chatSuccess = chatResponse?.payload?.data?.code === 0;
      const chatData = chatResponse?.payload?.data?.data;

      if (chatSuccess) {
        console.log(chalk.green('✅ 发送公会消息成功'));

        // 获取公会聊天历史
        const historyResponse = await this.sendMessage('social.chat.getGuildHistory', {
          playerId: TEST_DATA.characterId,
          page: 1,
          limit: 10
        });

        const historySuccess = historyResponse?.payload?.data?.code === 0;
        if (historySuccess) {
          console.log(chalk.green('✅ 获取公会聊天历史成功'));
        }
        
        this.testResults.push({
          step: '公会聊天',
          success: true,
          data: chatData
        });
      } else {
        const errorMsg = chatResponse?.payload?.data?.message || '未知错误';
        throw new Error(`公会聊天失败: ${errorMsg}`);
      }
    } catch (error) {
      console.log(chalk.red(`❌ 公会聊天失败: ${error.message}`));
      this.testResults.push({
        step: '公会聊天',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试公会活动
   */
  async testGuildActivity() {
    console.log(chalk.yellow('🎯 测试参与公会活动...'));

    try {
      // 获取公会活动列表
      const activityResponse = await this.sendMessage('activity.guild.getActivities', {
        characterId: TEST_DATA.characterId,
        serverId: TEST_DATA.serverId,
        token: TEST_DATA.token
      });

      const activitySuccess = activityResponse?.payload?.data?.code === 0;
      const activityData = activityResponse?.payload?.data?.data;

      if (activitySuccess && activityData) {
        console.log(chalk.green('✅ 获取公会活动成功'));
        console.log(chalk.gray(`   活动数量: ${activityData.activities ? activityData.activities.length : 0}`));

        // 尝试参与第一个活动
        if (activityData.activities && activityData.activities.length > 0) {
          const firstActivity = activityData.activities[0];
          const joinResponse = await this.sendMessage('activity.guild.joinActivity', {
            characterId: TEST_DATA.characterId,
            serverId: TEST_DATA.serverId,
            token: TEST_DATA.token,
            activityId: firstActivity.activityId
          });

          const joinSuccess = joinResponse?.payload?.data?.code === 0;
          if (joinSuccess) {
            console.log(chalk.green('✅ 参与公会活动成功'));
          }
        }
        
        this.testResults.push({
          step: '公会活动',
          success: true,
          data: activityData
        });
      } else {
        const errorMsg = activityResponse?.payload?.data?.message || '未知错误';
        throw new Error(`公会活动失败: ${errorMsg}`);
      }
    } catch (error) {
      console.log(chalk.red(`❌ 公会活动失败: ${error.message}`));
      this.testResults.push({
        step: '公会活动',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 打印测试结果汇总
   */
  printTestSummary() {
    console.log(chalk.blue('\n=== 社交流程测试结果汇总 ==='));
    
    let totalSteps = this.testResults.length;
    let successSteps = this.testResults.filter(r => r.success).length;

    console.log(chalk.white(`总测试步骤: ${totalSteps}`));
    console.log(chalk.green(`成功步骤: ${successSteps}`));
    console.log(chalk.red(`失败步骤: ${totalSteps - successSteps}`));

    this.testResults.forEach((result, index) => {
      const status = result.success ? chalk.green('✅') : chalk.red('❌');
      console.log(`${status} ${index + 1}. ${result.step}`);
      if (!result.success && result.error) {
        console.log(chalk.gray(`   错误: ${result.error}`));
      }
    });

    if (successSteps === totalSteps) {
      console.log(chalk.green('\n🎉 社交流程测试完全成功！'));
    } else if (successSteps >= totalSteps * 0.7) {
      console.log(chalk.yellow('\n⚠️ 社交流程基本正常，部分功能需要优化'));
    } else {
      console.log(chalk.red('\n❌ 社交流程存在重大问题，需要修复'));
    }
  }
}

// 运行测试
if (require.main === module) {
  const tester = new SocialFlowTester();
  tester.runTests().catch(error => {
    console.error(chalk.red('社交流程测试执行失败:'), error);
    process.exit(1);
  });
}

module.exports = SocialFlowTester;
