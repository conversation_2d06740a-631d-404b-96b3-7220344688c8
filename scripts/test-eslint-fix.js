#!/usr/bin/env node

/**
 * 测试ESLint配置修复是否生效
 * 验证throw异常的IDE警告是否已解决
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🔍 测试ESLint配置修复...\n');

// 测试文件路径
const testFiles = [
  'apps/social/src/modules/guild/guild.service.ts',
  'apps/character/src/modules/character/character.service.ts',
  'apps/hero/src/modules/hero/hero.service.ts',
];

console.log('📋 测试文件列表:');
testFiles.forEach((file, index) => {
  console.log(`  ${index + 1}. ${file}`);
});
console.log('');

// 运行ESLint检查
try {
  console.log('🚀 运行ESLint检查...');
  
  testFiles.forEach(file => {
    try {
      console.log(`\n📄 检查文件: ${file}`);
      
      // 运行ESLint检查特定文件
      const result = execSync(`npx eslint "${file}" --format=compact`, {
        encoding: 'utf8',
        cwd: process.cwd(),
        stdio: 'pipe'
      });
      
      if (result.trim() === '') {
        console.log('  ✅ 无ESLint错误');
      } else {
        console.log('  ⚠️  ESLint输出:', result.trim());
      }
    } catch (error) {
      if (error.stdout && error.stdout.includes('no-throw-literal')) {
        console.log('  ❌ 仍然存在throw相关的ESLint错误');
        console.log('  错误详情:', error.stdout);
      } else if (error.stdout) {
        console.log('  ⚠️  其他ESLint警告:', error.stdout);
      } else {
        console.log('  ✅ 无throw相关的ESLint错误');
      }
    }
  });

  console.log('\n🎉 ESLint配置测试完成!');
  console.log('\n📝 配置说明:');
  console.log('  - 已禁用 no-throw-literal 规则');
  console.log('  - 已禁用 @typescript-eslint/no-throw-literal 规则');
  console.log('  - 已禁用 @typescript-eslint/only-throw-error 规则');
  console.log('  - 这些规则的禁用解决了"本地捕获异常的 throw"IDE警告');

} catch (error) {
  console.error('❌ ESLint测试失败:', error.message);
  process.exit(1);
}

console.log('\n💡 使用建议:');
console.log('  1. 重启IDE以确保新的ESLint配置生效');
console.log('  2. 如果IDE仍显示警告，请检查IDE的ESLint插件设置');
console.log('  3. 可以运行 "npm run lint" 验证项目级别的ESLint检查');
