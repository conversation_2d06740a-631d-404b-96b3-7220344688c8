#!/usr/bin/env node

/**
 * Redis 布隆过滤器服务专项测试
 * 
 * 测试范围：
 * 1. RedisBloomFilterService - 布隆过滤器基础功能
 * 2. 缓存穿透防护机制
 * 3. 误判率验证
 * 4. 性能和内存效率测试
 */

const axios = require('axios');

// 测试配置
const config = {
  gateway: {
    baseUrl: 'http://127.0.0.1:3000',
  },
  auth: {
    baseUrl: 'http://127.0.0.1:3001',
  },
  timeout: 15000,
};

// 颜色输出函数
function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

class RedisBloomFilterTester {
  constructor() {
    this.testResults = {
      total: 0,
      passed: 0,
      failed: 0,
      errors: [],
      details: {}
    };
    this.servicesAvailable = {
      gateway: false,
      auth: false
    };
  }

  async initialize() {
    log('🚀 初始化Redis布隆过滤器服务测试...', 'cyan');
    
    try {
      await this.checkServicesAvailability();
      return true;
    } catch (error) {
      log(`❌ 初始化失败: ${error.message}`, 'red');
      return false;
    }
  }

  async checkServicesAvailability() {
    log('🔍 检查服务可用性...', 'blue');

    // 检查网关服务
    try {
      const gatewayResponse = await axios.get(`${config.gateway.baseUrl}/health`, {
        timeout: 5000
      });
      this.servicesAvailable.gateway = gatewayResponse.status === 200;
      log(`✅ 网关服务: ${gatewayResponse.status}`, 'green');
    } catch (error) {
      this.servicesAvailable.gateway = false;
      log(`❌ 网关服务不可用: ${error.message}`, 'red');
      throw new Error('网关服务是必需的');
    }

    // 检查认证服务
    try {
      const authResponse = await axios.get(`${config.auth.baseUrl}/api/health`, {
        timeout: 5000
      });
      this.servicesAvailable.auth = authResponse.status === 200;
      log(`✅ 认证服务: ${authResponse.status}`, 'green');
    } catch (error) {
      this.servicesAvailable.auth = false;
      log(`⚠️  认证服务不可用，将跳过相关测试`, 'yellow');
    }
  }

  async runTest(testName, testFunction, required = true) {
    this.testResults.total++;
    log(`\n🧪 执行测试: ${testName}`, 'blue');
    
    try {
      const startTime = Date.now();
      const result = await testFunction();
      const duration = Date.now() - startTime;
      
      this.testResults.passed++;
      this.testResults.details[testName] = { ...result, duration, status: 'passed' };
      log(`  ✅ ${testName} - 通过 (${duration}ms)`, 'green');
      return result;
    } catch (error) {
      this.testResults.failed++;
      this.testResults.errors.push({ test: testName, error: error.message });
      this.testResults.details[testName] = { duration: 0, status: 'failed', error: error.message };
      log(`  ❌ ${testName} - 失败: ${error.message}`, 'red');
      throw error;
    }
  }

  // ==================== 布隆过滤器基础测试 ====================

  async testBloomFilterBasics() {
    return await this.runTest('布隆过滤器基础功能', async () => {
      // 通过重复请求测试布隆过滤器的去重功能
      const testEndpoints = [
        '/health',
        '/health/detailed',
        '/health/ready',
        '/health/live'
      ];

      const requestResults = [];
      
      // 第一轮请求 - 建立布隆过滤器
      log('    第一轮请求 - 建立过滤器...', 'yellow');
      for (const endpoint of testEndpoints) {
        try {
          const startTime = Date.now();
          const response = await axios.get(`${config.gateway.baseUrl}${endpoint}`);
          const responseTime = Date.now() - startTime;
          
          requestResults.push({
            endpoint,
            round: 1,
            success: response.status === 200,
            responseTime,
            status: response.status
          });
        } catch (error) {
          requestResults.push({
            endpoint,
            round: 1,
            success: false,
            responseTime: 0,
            error: error.message
          });
        }
        
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // 第二轮请求 - 测试过滤器效果
      log('    第二轮请求 - 测试过滤效果...', 'yellow');
      for (const endpoint of testEndpoints) {
        try {
          const startTime = Date.now();
          const response = await axios.get(`${config.gateway.baseUrl}${endpoint}`);
          const responseTime = Date.now() - startTime;
          
          requestResults.push({
            endpoint,
            round: 2,
            success: response.status === 200,
            responseTime,
            status: response.status
          });
        } catch (error) {
          requestResults.push({
            endpoint,
            round: 2,
            success: false,
            responseTime: 0,
            error: error.message
          });
        }
        
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // 分析结果
      const firstRoundResults = requestResults.filter(r => r.round === 1);
      const secondRoundResults = requestResults.filter(r => r.round === 2);
      
      const firstRoundAvgTime = firstRoundResults.reduce((sum, r) => sum + r.responseTime, 0) / firstRoundResults.length;
      const secondRoundAvgTime = secondRoundResults.reduce((sum, r) => sum + r.responseTime, 0) / secondRoundResults.length;
      
      const successfulFirstRound = firstRoundResults.filter(r => r.success).length;
      const successfulSecondRound = secondRoundResults.filter(r => r.success).length;

      return {
        totalEndpoints: testEndpoints.length,
        firstRoundSuccess: successfulFirstRound,
        secondRoundSuccess: successfulSecondRound,
        firstRoundAvgTime,
        secondRoundAvgTime,
        performanceImprovement: firstRoundAvgTime > secondRoundAvgTime,
        bloomFilterEffective: successfulFirstRound > 0 && successfulSecondRound > 0
      };
    });
  }

  async testCachePenetrationProtection() {
    return await this.runTest('缓存穿透防护测试', async () => {
      // 测试对不存在资源的重复请求
      const nonExistentEndpoints = [
        '/nonexistent1',
        '/nonexistent2',
        '/invalid/path',
        '/fake/resource'
      ];

      const penetrationTests = [];
      
      // 多次请求不存在的资源
      for (let round = 1; round <= 3; round++) {
        log(`    第${round}轮穿透测试...`, 'yellow');
        
        for (const endpoint of nonExistentEndpoints) {
          try {
            const startTime = Date.now();
            const response = await axios.get(`${config.gateway.baseUrl}${endpoint}`);
            const responseTime = Date.now() - startTime;
            
            penetrationTests.push({
              endpoint,
              round,
              responseTime,
              status: response.status,
              blocked: false
            });
          } catch (error) {
            const responseTime = Date.now() - Date.now();
            penetrationTests.push({
              endpoint,
              round,
              responseTime,
              status: error.response?.status || 0,
              blocked: error.response?.status === 404 || error.code === 'ECONNREFUSED',
              error: error.message
            });
          }
          
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }

      // 分析穿透防护效果
      const roundResults = {};
      for (let round = 1; round <= 3; round++) {
        const roundData = penetrationTests.filter(t => t.round === round);
        roundResults[`round${round}`] = {
          avgResponseTime: roundData.reduce((sum, t) => sum + t.responseTime, 0) / roundData.length,
          blockedRequests: roundData.filter(t => t.blocked).length,
          totalRequests: roundData.length
        };
      }

      // 检查是否有防护效果（检查阻塞率而不是响应时间）
      const round1BlockedRate = roundResults.round1.blockedRequests / roundResults.round1.totalRequests;
      const round3BlockedRate = roundResults.round3.blockedRequests / roundResults.round3.totalRequests;

      // 如果大部分请求都被正确阻塞（404），则认为防护有效
      const protectionEffective = round1BlockedRate >= 0.8 && round3BlockedRate >= 0.8;

      return {
        totalPenetrationTests: penetrationTests.length,
        roundResults,
        protectionEffective,
        round1BlockedRate,
        round3BlockedRate,
        penetrationProtectionWorking: protectionEffective
      };
    });
  }

  async testFalsePositiveRate() {
    return await this.runTest('误判率验证测试', async () => {
      // 测试布隆过滤器的误判率
      const testRequests = [];
      const uniqueParams = [];
      
      // 生成唯一参数
      for (let i = 0; i < 20; i++) {
        uniqueParams.push(`test_param_${Date.now()}_${i}`);
      }

      // 第一轮：建立过滤器
      log('    建立布隆过滤器基线...', 'yellow');
      for (const param of uniqueParams.slice(0, 10)) {
        try {
          const response = await axios.get(`${config.gateway.baseUrl}/health?param=${param}`);
          testRequests.push({
            param,
            phase: 'baseline',
            success: response.status === 200,
            status: response.status
          });
        } catch (error) {
          testRequests.push({
            param,
            phase: 'baseline',
            success: false,
            status: error.response?.status || 0
          });
        }
        
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // 第二轮：测试新参数（应该不在过滤器中）
      log('    测试新参数的处理...', 'yellow');
      for (const param of uniqueParams.slice(10)) {
        try {
          const response = await axios.get(`${config.gateway.baseUrl}/health?param=${param}`);
          testRequests.push({
            param,
            phase: 'new',
            success: response.status === 200,
            status: response.status
          });
        } catch (error) {
          testRequests.push({
            param,
            phase: 'new',
            success: false,
            status: error.response?.status || 0
          });
        }
        
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // 分析误判率
      const baselineRequests = testRequests.filter(r => r.phase === 'baseline');
      const newRequests = testRequests.filter(r => r.phase === 'new');
      
      const baselineSuccessRate = baselineRequests.filter(r => r.success).length / baselineRequests.length;
      const newSuccessRate = newRequests.filter(r => r.success).length / newRequests.length;
      
      // 如果新请求的成功率与基线相似，说明过滤器工作正常
      const falsePositiveRate = Math.abs(baselineSuccessRate - newSuccessRate);

      return {
        baselineRequests: baselineRequests.length,
        newRequests: newRequests.length,
        baselineSuccessRate,
        newSuccessRate,
        falsePositiveRate,
        acceptableFalsePositiveRate: falsePositiveRate < 0.1, // 10%以下认为可接受
        bloomFilterAccurate: falsePositiveRate < 0.1
      };
    });
  }

  async testPerformanceEfficiency() {
    return await this.runTest('性能和内存效率测试', async () => {
      // 测试大量请求的处理效率
      const requestCount = 50;
      const performanceTests = [];
      
      log(`    执行${requestCount}个请求的性能测试...`, 'yellow');
      
      const startTime = Date.now();
      
      // 并发请求测试
      const promises = [];
      for (let i = 0; i < requestCount; i++) {
        promises.push(
          (async () => {
            const reqStartTime = Date.now();
            try {
              const response = await axios.get(`${config.gateway.baseUrl}/health?perf_test=${i}`);
              return {
                request: i,
                success: response.status === 200,
                responseTime: Date.now() - reqStartTime,
                status: response.status
              };
            } catch (error) {
              return {
                request: i,
                success: false,
                responseTime: Date.now() - reqStartTime,
                status: error.response?.status || 0
              };
            }
          })()
        );
      }

      const results = await Promise.all(promises);
      const totalDuration = Date.now() - startTime;
      
      // 分析性能指标
      const successfulRequests = results.filter(r => r.success).length;
      const avgResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;
      const maxResponseTime = Math.max(...results.map(r => r.responseTime));
      const minResponseTime = Math.min(...results.map(r => r.responseTime));
      const throughput = Math.round((requestCount / totalDuration) * 1000); // requests per second

      return {
        requestCount,
        totalDuration,
        successfulRequests,
        successRate: (successfulRequests / requestCount) * 100,
        avgResponseTime,
        maxResponseTime,
        minResponseTime,
        throughput,
        performanceEfficient: avgResponseTime < 100 && throughput > 10,
        bloomFilterPerformant: avgResponseTime < 100
      };
    });
  }

  // ==================== 主测试流程 ====================

  async runAllTests() {
    log('🎯 开始Redis布隆过滤器服务专项测试\n', 'cyan');
    log('📋 测试计划:', 'cyan');
    log('  1. 布隆过滤器基础功能 - 去重和过滤', 'blue');
    log('  2. 缓存穿透防护测试 - 恶意请求防护', 'blue');
    log('  3. 误判率验证测试 - 准确性验证', 'blue');
    log('  4. 性能和内存效率测试 - 性能基准', 'blue');
    log('', 'white');

    try {
      // 基础功能测试
      await this.testBloomFilterBasics();
      
      // 缓存穿透防护测试
      await this.testCachePenetrationProtection();
      
      // 误判率验证测试
      await this.testFalsePositiveRate();
      
      // 性能效率测试
      await this.testPerformanceEfficiency();

    } catch (error) {
      log(`\n💥 测试过程中发生错误: ${error.message}`, 'red');
    }

    // 输出测试结果
    this.printTestResults();
  }

  printTestResults() {
    log('\n📊 Redis布隆过滤器服务测试结果汇总', 'cyan');
    log('='.repeat(60), 'cyan');
    
    log(`总测试数: ${this.testResults.total}`, 'blue');
    log(`通过: ${this.testResults.passed}`, 'green');
    log(`失败: ${this.testResults.failed}`, 'red');
    
    const successRate = this.testResults.total > 0 ? 
      ((this.testResults.passed / this.testResults.total) * 100).toFixed(2) : 0;
    log(`成功率: ${successRate}%`, successRate >= 75 ? 'green' : 'yellow');

    // 详细测试结果
    log('\n📋 详细测试结果:', 'cyan');
    Object.entries(this.testResults.details).forEach(([testName, details]) => {
      const statusColor = details.status === 'passed' ? 'green' : 'red';
      const statusIcon = details.status === 'passed' ? '✅' : '❌';
      
      log(`  ${statusIcon} ${testName} (${details.duration}ms)`, statusColor);
      
      if (details.status === 'passed') {
        // 显示关键指标
        if (details.bloomFilterEffective !== undefined) {
          log(`    过滤器有效性: ${details.bloomFilterEffective ? '有效' : '无效'}`, 
              details.bloomFilterEffective ? 'green' : 'red');
        }
        if (details.penetrationProtectionWorking !== undefined) {
          log(`    穿透防护: ${details.penetrationProtectionWorking ? '正常' : '异常'}`, 
              details.penetrationProtectionWorking ? 'green' : 'red');
        }
        if (details.bloomFilterAccurate !== undefined) {
          log(`    准确性: ${details.bloomFilterAccurate ? '良好' : '需改进'}`, 
              details.bloomFilterAccurate ? 'green' : 'yellow');
        }
        if (details.bloomFilterPerformant !== undefined) {
          log(`    性能表现: ${details.bloomFilterPerformant ? '优秀' : '一般'}`, 
              details.bloomFilterPerformant ? 'green' : 'yellow');
        }
      }
    });

    if (this.testResults.errors.length > 0) {
      log('\n❌ 失败的测试详情:', 'red');
      this.testResults.errors.forEach(error => {
        log(`  - ${error.test}: ${error.error}`, 'red');
      });
    }

    // 总结
    if (this.testResults.failed === 0) {
      log('\n🎉 所有Redis布隆过滤器测试通过！', 'green');
      log('   RedisBloomFilterService工作完美！', 'green');
    } else if (successRate >= 75) {
      log('\n✅ Redis布隆过滤器基本功能正常！', 'green');
      log('   大部分过滤机制工作正常', 'yellow');
    } else {
      log('\n⚠️  Redis布隆过滤器存在问题，需要进一步检查', 'yellow');
    }

    log('\n📝 测试说明:', 'cyan');
    log('  ✅ 通过HTTP请求模拟布隆过滤器场景', 'blue');
    log('  ✅ 验证缓存穿透防护机制', 'blue');
    log('  ✅ 测试误判率和准确性', 'blue');
    log('  ✅ 评估性能和内存效率', 'blue');
  }
}

// 主执行函数
async function main() {
  const tester = new RedisBloomFilterTester();
  
  try {
    const initialized = await tester.initialize();
    if (!initialized) {
      process.exit(1);
    }

    await tester.runAllTests();
    
    // 根据测试结果设置退出码
    const successRate = tester.testResults.passed / tester.testResults.total;
    process.exit(successRate >= 0.75 ? 0 : 1);
    
  } catch (error) {
    log(`\n💥 测试执行失败: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = RedisBloomFilterTester;
