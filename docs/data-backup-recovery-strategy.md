# 数据备份与恢复方案

## 📋 概述

本文档提供一套功能强大但优雅精简的数据备份与恢复解决方案，遵循最小化改动、易于维护的设计原则。

## 🎯 设计原则

- **优雅精简**: 最小化代码和配置，避免过度工程化
- **最小改动**: 基于现有基础设施，无需引入额外组件
- **易于维护**: 统一接口，自动化执行，简单配置
- **功能强大**: 支持增量备份、压缩加密、多存储后端

## 🏗️ 架构设计

### 核心组件

```typescript
// 统一备份管理器
BackupManagerService
├── DatabaseBackupService    // 数据库备份
├── RedisBackupService      // Redis备份  
├── FileBackupService       // 文件备份
└── StorageProviderService  // 存储提供者
```

### 存储策略

```
本地存储 → 云存储 → 异地备份
    ↓         ↓         ↓
  实时备份   定时同步   灾难恢复
```

## 📦 实现方案

### 1. 核心备份服务

```typescript
// libs/common/src/backup/backup-manager.service.ts
@Injectable()
export class BackupManagerService {
  constructor(
    private readonly databaseBackup: DatabaseBackupService,
    private readonly redisBackup: RedisBackupService,
    private readonly fileBackup: FileBackupService,
    private readonly storageProvider: StorageProviderService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 执行完整备份
   */
  async executeFullBackup(): Promise<BackupResult> {
    const backupId = this.generateBackupId();
    const results: BackupResult[] = [];

    try {
      // 并行执行各类备份
      const [dbResult, redisResult, fileResult] = await Promise.all([
        this.databaseBackup.createBackup(backupId),
        this.redisBackup.createBackup(backupId),
        this.fileBackup.createBackup(backupId),
      ]);

      results.push(dbResult, redisResult, fileResult);

      // 上传到云存储
      await this.uploadToCloud(results);

      return this.createSuccessResult(backupId, results);
    } catch (error) {
      return this.createErrorResult(backupId, error, results);
    }
  }

  /**
   * 执行增量备份
   */
  async executeIncrementalBackup(): Promise<BackupResult> {
    const lastBackup = await this.getLastBackupInfo();
    return await this.executeBackupSince(lastBackup.timestamp);
  }

  /**
   * 恢复数据
   */
  async restoreFromBackup(backupId: string, options: RestoreOptions = {}): Promise<RestoreResult> {
    const backupInfo = await this.getBackupInfo(backupId);
    
    if (!backupInfo) {
      throw new Error(`备份 ${backupId} 不存在`);
    }

    const results: RestoreResult[] = [];

    try {
      // 按依赖顺序恢复
      if (options.includeDatabase !== false) {
        results.push(await this.databaseBackup.restore(backupId, options));
      }
      
      if (options.includeRedis !== false) {
        results.push(await this.redisBackup.restore(backupId, options));
      }
      
      if (options.includeFiles !== false) {
        results.push(await this.fileBackup.restore(backupId, options));
      }

      return this.createRestoreSuccessResult(backupId, results);
    } catch (error) {
      return this.createRestoreErrorResult(backupId, error, results);
    }
  }
}
```

### 2. MongoDB备份服务

```typescript
// libs/common/src/backup/database-backup.service.ts
@Injectable()
export class DatabaseBackupService {
  constructor(
    private readonly configService: ConfigService,
    private readonly storageProvider: StorageProviderService,
  ) {}

  /**
   * 创建数据库备份
   */
  async createBackup(backupId: string): Promise<BackupResult> {
    const timestamp = new Date().toISOString();
    const backupPath = this.getBackupPath(backupId, 'mongodb');

    try {
      // 使用 mongodump 创建备份
      const dumpCommand = this.buildMongoDumpCommand(backupPath);
      await this.executeCommand(dumpCommand);

      // 压缩备份文件
      const compressedPath = await this.compressBackup(backupPath);

      // 加密备份文件（可选）
      const finalPath = await this.encryptBackup(compressedPath);

      const metadata = await this.generateBackupMetadata(finalPath, 'mongodb');

      return {
        id: backupId,
        type: 'mongodb',
        status: 'success',
        path: finalPath,
        size: metadata.size,
        timestamp,
        metadata,
      };
    } catch (error) {
      return {
        id: backupId,
        type: 'mongodb',
        status: 'failed',
        error: error.message,
        timestamp,
      };
    }
  }

  /**
   * 恢复数据库
   */
  async restore(backupId: string, options: RestoreOptions): Promise<RestoreResult> {
    const backupInfo = await this.getBackupInfo(backupId, 'mongodb');
    
    try {
      // 下载备份文件
      const localPath = await this.downloadBackup(backupInfo.path);

      // 解密备份文件
      const decryptedPath = await this.decryptBackup(localPath);

      // 解压备份文件
      const extractedPath = await this.extractBackup(decryptedPath);

      // 执行恢复
      const restoreCommand = this.buildMongoRestoreCommand(extractedPath, options);
      await this.executeCommand(restoreCommand);

      // 清理临时文件
      await this.cleanupTempFiles([localPath, decryptedPath, extractedPath]);

      return {
        id: backupId,
        type: 'mongodb',
        status: 'success',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        id: backupId,
        type: 'mongodb',
        status: 'failed',
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  private buildMongoDumpCommand(outputPath: string): string {
    const uri = this.configService.get<string>('MONGODB_URI');
    return `mongodump --uri="${uri}" --out="${outputPath}" --gzip`;
  }

  private buildMongoRestoreCommand(inputPath: string, options: RestoreOptions): string {
    const uri = this.configService.get<string>('MONGODB_URI');
    const dropFlag = options.dropBeforeRestore ? '--drop' : '';
    return `mongorestore --uri="${uri}" ${dropFlag} --gzip "${inputPath}"`;
  }
}
```

### 3. Redis备份服务

```typescript
// libs/common/src/backup/redis-backup.service.ts
@Injectable()
export class RedisBackupService {
  constructor(
    private readonly redisService: RedisService,
    private readonly configService: ConfigService,
    private readonly storageProvider: StorageProviderService,
  ) {}

  /**
   * 创建Redis备份
   */
  async createBackup(backupId: string): Promise<BackupResult> {
    const timestamp = new Date().toISOString();
    const backupPath = this.getBackupPath(backupId, 'redis');

    try {
      // 方法1: 使用 BGSAVE 命令
      await this.createRDBBackup(backupPath);

      // 方法2: 导出关键数据（推荐用于缓存数据）
      await this.exportKeyData(backupPath);

      const compressedPath = await this.compressBackup(backupPath);
      const metadata = await this.generateBackupMetadata(compressedPath, 'redis');

      return {
        id: backupId,
        type: 'redis',
        status: 'success',
        path: compressedPath,
        size: metadata.size,
        timestamp,
        metadata,
      };
    } catch (error) {
      return {
        id: backupId,
        type: 'redis',
        status: 'failed',
        error: error.message,
        timestamp,
      };
    }
  }

  /**
   * 恢复Redis数据
   */
  async restore(backupId: string, options: RestoreOptions): Promise<RestoreResult> {
    const backupInfo = await this.getBackupInfo(backupId, 'redis');
    
    try {
      const localPath = await this.downloadBackup(backupInfo.path);
      const extractedPath = await this.extractBackup(localPath);

      // 恢复RDB文件
      if (options.restoreMethod === 'rdb') {
        await this.restoreFromRDB(extractedPath);
      } else {
        // 恢复导出的键值数据
        await this.restoreKeyData(extractedPath);
      }

      await this.cleanupTempFiles([localPath, extractedPath]);

      return {
        id: backupId,
        type: 'redis',
        status: 'success',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        id: backupId,
        type: 'redis',
        status: 'failed',
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }

  private async exportKeyData(outputPath: string): Promise<void> {
    const client = this.redisService.getClient();
    const exportData = {
      timestamp: new Date().toISOString(),
      keys: {},
    };

    // 导出关键业务数据（非缓存数据）
    const patterns = [
      'user:*',
      'session:*',
      'game:*',
      'club:*',
      // 排除纯缓存数据
      '!cache:*',
      '!temp:*',
    ];

    for (const pattern of patterns) {
      if (pattern.startsWith('!')) continue;
      
      const keys = await client.keys(pattern);
      for (const key of keys) {
        const type = await client.type(key);
        const ttl = await client.ttl(key);
        
        let value;
        switch (type) {
          case 'string':
            value = await client.get(key);
            break;
          case 'hash':
            value = await client.hgetall(key);
            break;
          case 'list':
            value = await client.lrange(key, 0, -1);
            break;
          case 'set':
            value = await client.smembers(key);
            break;
          case 'zset':
            value = await client.zrange(key, 0, -1, 'WITHSCORES');
            break;
        }

        exportData.keys[key] = { type, value, ttl };
      }
    }

    await fs.writeFile(
      path.join(outputPath, 'redis-export.json'),
      JSON.stringify(exportData, null, 2)
    );
  }
}
```

### 4. 存储提供者服务

```typescript
// libs/common/src/backup/storage-provider.service.ts
@Injectable()
export class StorageProviderService {
  constructor(private readonly configService: ConfigService) {}

  /**
   * 上传备份到云存储
   */
  async uploadBackup(localPath: string, remotePath: string): Promise<UploadResult> {
    const provider = this.configService.get<string>('BACKUP_STORAGE_PROVIDER', 'local');

    switch (provider) {
      case 'aws-s3':
        return await this.uploadToS3(localPath, remotePath);
      case 'aliyun-oss':
        return await this.uploadToOSS(localPath, remotePath);
      case 'local':
        return await this.copyToLocal(localPath, remotePath);
      default:
        throw new Error(`不支持的存储提供者: ${provider}`);
    }
  }

  /**
   * 从云存储下载备份
   */
  async downloadBackup(remotePath: string, localPath: string): Promise<string> {
    const provider = this.configService.get<string>('BACKUP_STORAGE_PROVIDER', 'local');

    switch (provider) {
      case 'aws-s3':
        return await this.downloadFromS3(remotePath, localPath);
      case 'aliyun-oss':
        return await this.downloadFromOSS(remotePath, localPath);
      case 'local':
        return await this.copyFromLocal(remotePath, localPath);
      default:
        throw new Error(`不支持的存储提供者: ${provider}`);
    }
  }

  private async uploadToS3(localPath: string, remotePath: string): Promise<UploadResult> {
    // AWS S3 上传实现
    // 使用 @aws-sdk/client-s3
  }

  private async uploadToOSS(localPath: string, remotePath: string): Promise<UploadResult> {
    // 阿里云 OSS 上传实现
    // 使用 ali-oss
  }
}
```

### 5. 定时备份任务

```typescript
// libs/common/src/backup/backup-scheduler.service.ts
@Injectable()
export class BackupSchedulerService {
  constructor(
    private readonly backupManager: BackupManagerService,
    private readonly configService: ConfigService,
  ) {}

  @Cron('0 2 * * *') // 每天凌晨2点执行完整备份
  async dailyFullBackup(): Promise<void> {
    if (!this.configService.get<boolean>('BACKUP_ENABLED', true)) {
      return;
    }

    try {
      const result = await this.backupManager.executeFullBackup();
      this.logBackupResult('daily-full', result);
    } catch (error) {
      this.logBackupError('daily-full', error);
    }
  }

  @Cron('0 */6 * * *') // 每6小时执行增量备份
  async incrementalBackup(): Promise<void> {
    if (!this.configService.get<boolean>('BACKUP_INCREMENTAL_ENABLED', true)) {
      return;
    }

    try {
      const result = await this.backupManager.executeIncrementalBackup();
      this.logBackupResult('incremental', result);
    } catch (error) {
      this.logBackupError('incremental', error);
    }
  }
}
```

## 🔧 配置管理

### 环境变量配置

```bash
# .env
# 备份配置
BACKUP_ENABLED=true
BACKUP_INCREMENTAL_ENABLED=true
BACKUP_RETENTION_DAYS=30
BACKUP_COMPRESSION_ENABLED=true
BACKUP_ENCRYPTION_ENABLED=true
BACKUP_ENCRYPTION_KEY=your-encryption-key

# 存储配置
BACKUP_STORAGE_PROVIDER=local  # local | aws-s3 | aliyun-oss
BACKUP_LOCAL_PATH=/app/backups
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_REGION=us-east-1
BACKUP_OSS_BUCKET=your-backup-bucket
BACKUP_OSS_REGION=oss-cn-hangzhou

# MongoDB配置
MONGODB_URI=mongodb://localhost:27017/football_manager
MONGODUMP_PATH=/usr/bin/mongodump
MONGORESTORE_PATH=/usr/bin/mongorestore

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
```

### 备份配置类

```typescript
// libs/common/src/backup/backup.config.ts
export interface BackupConfig {
  enabled: boolean;
  incrementalEnabled: boolean;
  retentionDays: number;
  compressionEnabled: boolean;
  encryptionEnabled: boolean;
  encryptionKey: string;
  storageProvider: 'local' | 'aws-s3' | 'aliyun-oss';
  localPath: string;
  s3Config?: S3Config;
  ossConfig?: OSSConfig;
}

export default registerAs('backup', (): BackupConfig => ({
  enabled: process.env.BACKUP_ENABLED === 'true',
  incrementalEnabled: process.env.BACKUP_INCREMENTAL_ENABLED === 'true',
  retentionDays: parseInt(process.env.BACKUP_RETENTION_DAYS || '30', 10),
  compressionEnabled: process.env.BACKUP_COMPRESSION_ENABLED === 'true',
  encryptionEnabled: process.env.BACKUP_ENCRYPTION_ENABLED === 'true',
  encryptionKey: process.env.BACKUP_ENCRYPTION_KEY || '',
  storageProvider: (process.env.BACKUP_STORAGE_PROVIDER as any) || 'local',
  localPath: process.env.BACKUP_LOCAL_PATH || '/app/backups',
}));
```

## 🚀 使用方法

### 1. 模块集成

```typescript
// apps/gateway/src/app.module.ts
@Module({
  imports: [
    // 其他模块...
    BackupModule.forRoot({
      enabled: true,
      schedulerEnabled: true,
    }),
  ],
})
export class AppModule {}
```

### 2. 手动备份

```typescript
// 在任何服务中注入使用
@Injectable()
export class SomeService {
  constructor(private readonly backupManager: BackupManagerService) {}

  async createManualBackup(): Promise<BackupResult> {
    return await this.backupManager.executeFullBackup();
  }

  async restoreFromBackup(backupId: string): Promise<RestoreResult> {
    return await this.backupManager.restoreFromBackup(backupId, {
      dropBeforeRestore: true,
      includeDatabase: true,
      includeRedis: true,
      includeFiles: true,
    });
  }
}
```

### 3. CLI命令

```bash
# 创建完整备份
npm run backup:full

# 创建增量备份
npm run backup:incremental

# 恢复备份
npm run backup:restore <backup-id>

# 列出所有备份
npm run backup:list

# 清理过期备份
npm run backup:cleanup
```

## 🔄 灾难恢复方案

### 灾难恢复等级

| 等级 | 恢复时间目标(RTO) | 恢复点目标(RPO) | 适用场景 |
|------|------------------|----------------|----------|
| Level 1 | < 1小时 | < 15分钟 | 生产环境 |
| Level 2 | < 4小时 | < 1小时 | 测试环境 |
| Level 3 | < 24小时 | < 6小时 | 开发环境 |

### 灾难恢复流程

```typescript
// libs/common/src/backup/disaster-recovery.service.ts
@Injectable()
export class DisasterRecoveryService {
  constructor(
    private readonly backupManager: BackupManagerService,
    private readonly healthService: HealthService,
  ) {}

  /**
   * 执行灾难恢复
   */
  async executeDisasterRecovery(options: DisasterRecoveryOptions): Promise<RecoveryResult> {
    const recoveryPlan = await this.createRecoveryPlan(options);

    try {
      // 1. 系统健康检查
      await this.performHealthCheck();

      // 2. 停止相关服务
      await this.stopServices(recoveryPlan.servicesToStop);

      // 3. 恢复数据
      const restoreResult = await this.restoreData(recoveryPlan);

      // 4. 验证数据完整性
      await this.validateDataIntegrity();

      // 5. 重启服务
      await this.startServices(recoveryPlan.servicesToStop);

      // 6. 最终验证
      await this.performFinalValidation();

      return this.createSuccessRecoveryResult(restoreResult);
    } catch (error) {
      return this.createFailedRecoveryResult(error);
    }
  }

  private async createRecoveryPlan(options: DisasterRecoveryOptions): Promise<RecoveryPlan> {
    const latestBackup = await this.backupManager.getLatestBackup();

    return {
      backupId: options.backupId || latestBackup.id,
      recoveryLevel: options.level || 'Level 1',
      servicesToStop: ['gateway', 'auth', 'user', 'game'],
      estimatedTime: this.calculateRecoveryTime(options.level),
      steps: this.generateRecoverySteps(options),
    };
  }
}
```

## 📊 监控与告警

### 备份监控指标

```typescript
// libs/common/src/backup/backup-monitoring.service.ts
@Injectable()
export class BackupMonitoringService {
  constructor(
    private readonly metricsService: MetricsService,
    private readonly alertingService: AlertingService,
  ) {}

  /**
   * 记录备份指标
   */
  recordBackupMetrics(result: BackupResult): void {
    this.metricsService.incrementCounter('backup_total', {
      type: result.type,
      status: result.status,
    });

    if (result.status === 'success') {
      this.metricsService.recordHistogram('backup_duration_seconds', result.duration);
      this.metricsService.recordGauge('backup_size_bytes', result.size);
    }

    this.metricsService.recordGauge('backup_last_success_timestamp',
      result.status === 'success' ? Date.now() / 1000 : 0);
  }

  /**
   * 检查备份健康状态
   */
  async checkBackupHealth(): Promise<BackupHealthStatus> {
    const lastBackup = await this.getLastSuccessfulBackup();
    const now = new Date();
    const timeSinceLastBackup = now.getTime() - lastBackup.timestamp.getTime();
    const maxInterval = 24 * 60 * 60 * 1000; // 24小时

    if (timeSinceLastBackup > maxInterval) {
      await this.alertingService.triggerAlert({
        type: 'backup_overdue',
        severity: 'critical',
        message: `备份已超过24小时未执行`,
        metadata: { lastBackup: lastBackup.timestamp },
      });

      return { status: 'unhealthy', reason: 'backup_overdue' };
    }

    return { status: 'healthy' };
  }
}
```

## 🛠️ 数据迁移工具

### 数据迁移服务

```typescript
// libs/common/src/backup/data-migration.service.ts
@Injectable()
export class DataMigrationService {
  constructor(
    private readonly backupManager: BackupManagerService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 执行数据迁移
   */
  async executeMigration(migrationPlan: MigrationPlan): Promise<MigrationResult> {
    const migrationId = this.generateMigrationId();

    try {
      // 1. 创建迁移前备份
      const preBackup = await this.backupManager.executeFullBackup();

      // 2. 执行数据转换
      const transformResult = await this.transformData(migrationPlan);

      // 3. 验证转换结果
      await this.validateTransformation(transformResult);

      // 4. 应用迁移
      const applyResult = await this.applyMigration(transformResult);

      // 5. 创建迁移后备份
      const postBackup = await this.backupManager.executeFullBackup();

      return {
        id: migrationId,
        status: 'success',
        preBackupId: preBackup.id,
        postBackupId: postBackup.id,
        transformedRecords: transformResult.recordCount,
        duration: Date.now() - migrationPlan.startTime,
      };
    } catch (error) {
      return {
        id: migrationId,
        status: 'failed',
        error: error.message,
        rollbackRequired: true,
      };
    }
  }

  /**
   * 回滚迁移
   */
  async rollbackMigration(migrationId: string): Promise<RollbackResult> {
    const migration = await this.getMigrationInfo(migrationId);

    if (!migration.preBackupId) {
      throw new Error('无法回滚：缺少迁移前备份');
    }

    return await this.backupManager.restoreFromBackup(migration.preBackupId, {
      dropBeforeRestore: true,
      includeDatabase: true,
      includeRedis: true,
    });
  }
}
```

## 📋 最佳实践

### 1. 备份策略建议

- **3-2-1规则**: 3份副本，2种存储介质，1份异地存储
- **增量备份**: 减少存储空间和备份时间
- **压缩加密**: 保护数据安全，节省存储成本
- **定期验证**: 确保备份可用性

### 2. 恢复测试

```bash
# 每月执行恢复测试
npm run backup:test-restore --env=staging

# 验证数据完整性
npm run backup:verify --backup-id=<backup-id>
```

### 3. 监控告警

- 备份失败告警
- 备份超时告警
- 存储空间不足告警
- 数据完整性检查失败告警

## 🔒 安全考虑

### 1. 加密策略

- **传输加密**: HTTPS/TLS传输
- **存储加密**: AES-256加密备份文件
- **密钥管理**: 使用密钥管理服务(KMS)

### 2. 访问控制

- **最小权限原则**: 仅授予必要权限
- **审计日志**: 记录所有备份恢复操作
- **多重验证**: 关键操作需要多重确认

## 📈 性能优化

### 1. 备份优化

- **并行备份**: 同时备份多个数据源
- **增量备份**: 仅备份变更数据
- **压缩算法**: 选择合适的压缩算法

### 2. 恢复优化

- **分片恢复**: 大数据集分片并行恢复
- **索引重建**: 恢复后重建索引
- **缓存预热**: 恢复后预热缓存

## 🎯 总结

本方案提供了一套完整的数据备份与恢复解决方案，具有以下特点：

- ✅ **功能强大**: 支持多种数据源、存储后端、恢复策略
- ✅ **优雅精简**: 统一接口、最小配置、易于使用
- ✅ **最小改动**: 基于现有基础设施，无需大幅重构
- ✅ **易于维护**: 模块化设计、自动化执行、完善监控

通过实施本方案，可以确保系统数据的安全性和可恢复性，为业务连续性提供强有力的保障。
```
