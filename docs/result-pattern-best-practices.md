# Result模式最佳实践

## 🎯 **核心理念**

Result模式的核心是：**所有可能的错误都应该通过返回值表达，而不是通过异常或null**。

## ❌ **违背Result模式的做法**

### **问题1：返回null打破Result模式**

```typescript
// ❌ 错误做法：返回null打破了Result模式
static propagateError<T>(result: Result<any>): Result<T> | null {
  return XResultUtils.isFailure(result) ? (result as Result<T>) : null;
}

// 使用时需要处理null，违背了Result模式
const error = this.propagateError(result);
if (error) return error; // error可能是null，语义不清晰
```

### **问题2：混合异常和Result**

```typescript
// ❌ 错误做法：混合使用异常和Result
async badExample(): Promise<XResult<T>> {
  try {
    const result = await this.repository.operation();
    if (XResultUtils.isFailure(result)) {
      throw new Error('操作失败'); // 违背Result模式
    }
    return result;
  } catch (error) {
    return XResultUtils.error(error.message);
  }
}
```

## ✅ **符合Result模式的最佳实践**

### **实践1：直接使用类型守卫**

```typescript
// ✅ 推荐做法：直接使用类型守卫，语义清晰
async transferCharacter(
  characterId: string,
  guildId: string
): Promise<XResult<CharacterDocument>> {
  return this.executeTransaction(async (session) => {
    // 1. 检查角色存在
    const characterResult = await this.characterRepo.findById(characterId, session);
    if (this.isFailure(characterResult)) {
      return characterResult as Result<CharacterDocument>; // 明确的类型转换
    }

    const character = characterResult.data; // 类型安全地访问数据
    if (!character) {
      return this.error('角色不存在', 'CHARACTER_NOT_FOUND');
    }

    // 2. 检查公会存在
    const guildResult = await this.guildRepo.findById(guildId, session);
    if (this.isFailure(guildResult)) {
      return guildResult as Result<CharacterDocument>;
    }

    if (!guildResult.data) {
      return this.error('公会不存在', 'GUILD_NOT_FOUND');
    }

    // 3. 执行转会
    const transferResult = await this.characterRepo.transferToGuild(characterId, guildId, session);
    if (this.isFailure(transferResult)) {
      return transferResult; // 类型匹配，直接返回
    }

    return transferResult; // 成功返回
  });
}
```

### **实践2：使用辅助函数简化检查**

```typescript
// ✅ 创建辅助函数，保持Result模式的纯粹性
protected checkResult<T, U>(
  result: Result<T>, 
  onFailure: (failure: FailureResult) => Result<U>
): Result<U> | 'CONTINUE' {
  return this.isFailure(result) ? onFailure(result) : 'CONTINUE';
}

// 使用示例
async complexOperation(): Promise<XResult<FinalResult>> {
  return this.executeTransaction(async (session) => {
    const result1 = await this.repo1.operation1(session);
    const check1 = this.checkResult(result1, (failure) => failure as Result<FinalResult>);
    if (check1 !== 'CONTINUE') return check1;

    const result2 = await this.repo2.operation2(session);
    const check2 = this.checkResult(result2, (failure) => failure as Result<FinalResult>);
    if (check2 !== 'CONTINUE') return check2;

    return this.success(finalResult);
  });
}
```

### **实践3：链式操作模式**

```typescript
// ✅ 创建链式操作，避免重复的错误检查
protected async chain<T>(
  operations: Array<() => Promise<XResult<any>>>
): Promise<XResult<T> | 'SUCCESS'> {
  for (const operation of operations) {
    const result = await operation();
    if (this.isFailure(result)) {
      return result as Result<T>;
    }
  }
  return 'SUCCESS';
}

// 使用示例
async batchOperation(): Promise<XResult<void>> {
  return this.executeTransaction(async (session) => {
    const chainResult = await this.chain<void>([
      () => this.repo1.operation1(session),
      () => this.repo2.operation2(session),
      () => this.repo3.operation3(session)
    ]);

    if (chainResult !== 'SUCCESS') {
      return chainResult;
    }

    return this.empty();
  });
}
```

## 🔧 **重构指南**

### **从propagateError迁移到isFailure**

```typescript
// ❌ 旧代码（使用propagateError）
const error = this.propagateError<T>(result);
if (error) return error;

// ✅ 新代码（使用isFailure）
if (this.isFailure(result)) {
  return result as Result<T>;
}
```

### **统一的错误处理模式**

```typescript
// ✅ 标准模式：检查 -> 转换 -> 返回
async standardPattern(): Promise<XResult<T>> {
  return this.executeTransaction(async (session) => {
    // 1. 执行操作
    const result = await this.repository.operation(session);
    
    // 2. 检查结果
    if (this.isFailure(result)) {
      return result as Result<T>; // 明确的类型转换
    }
    
    // 3. 使用数据
    const data = result.data; // 类型安全
    
    // 4. 返回结果
    return this.success(processedData);
  });
}
```

## 📋 **检查清单**

### **Result模式合规性检查**

- [ ] ✅ 所有方法都返回`Result<T>`类型
- [ ] ✅ 不使用`throw`语句抛出异常
- [ ] ✅ 不返回`null`或`undefined`表示错误
- [ ] ✅ 使用`isFailure()`和`isSuccess()`进行类型安全检查
- [ ] ✅ 错误信息包含有意义的错误代码和消息
- [ ] ✅ 成功路径和失败路径都有明确的类型

### **事务处理合规性检查**

- [ ] ✅ 事务内的每个操作都检查Result
- [ ] ✅ 失败时立即返回，触发自动回滚
- [ ] ✅ 不在事务内使用try-catch处理业务逻辑
- [ ] ✅ Session正确传递给所有Repository操作

## 🎯 **总结**

Result模式的价值在于：
1. **类型安全**：编译时就能发现错误处理的遗漏
2. **显式错误**：错误成为API契约的一部分
3. **组合性**：Result可以安全地组合和传递
4. **可测试性**：错误路径和成功路径都易于测试

**核心原则**：如果一个函数可能失败，它就应该返回`Result<T>`，而不是抛出异常或返回null。这样才能真正发挥Result模式的优势。
