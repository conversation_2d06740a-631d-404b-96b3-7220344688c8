# Redis前缀架构v3.0 - 分区分服支持

## 📋 概述

Redis前缀架构v3.0是为支持分区分服系统设计的Redis键空间管理方案。通过扩展现有Redis公共库，实现数据类型感知的缓存操作，支持全局数据(global)、跨服数据(cross)和区服数据(server)的完全隔离。

## 🎯 设计目标

### 核心需求
- ✅ **数据完全隔离**: 同一账号在不同区服的数据完全独立
- ✅ **跨服功能支持**: 支持全局排行榜、跨服战斗等功能
- ✅ **向后兼容**: 现有代码无需修改，默认使用当前区服
- ✅ **单Redis实例**: 避免多实例复杂性，使用前缀隔离
- ✅ **性能优化**: 连接级前缀，底层C++处理

### 数据类型分类
```typescript
type DataType = 'global' | 'cross' | 'server';

// global: 全局数据 - 区服列表、账号信息、系统配置
// cross:  跨服数据 - 全局排行榜、跨服战斗、全局活动  
// server: 区服数据 - 用户数据、俱乐部数据、比赛数据（默认）
```

## 🏗️ 架构设计

### 1. 键前缀格式

```typescript
// 最终键格式（优化后）
区服数据：{环境}:{项目}:server:{区服ID}:{服务}:{具体键}
全局数据：{环境}:{项目}:global:{具体键}                  // 🔥 去掉服务前缀
跨服数据：{环境}:{项目}:cross:{具体键}                   // 🔥 去掉服务前缀

// 示例
'dev:fm:server:001:auth:user:profile:123'   // 区服001的用户数据
'dev:fm:global:auth:servers:list'          // 全局区服列表
'dev:fm:cross:auth:ranking:global'         // 跨服排行榜
```

### 2. 职责分工

```typescript
// RedisModule: 构建基础前缀 {环境}:{项目}:
// RedisService: 动态添加 {global|cross|server{ID}}:{服务}:

interface PrefixResponsibility {
  RedisModule: {
    responsibility: '构建连接级基础前缀',
    format: '{环境}:{项目}:',
    example: 'dev:fm:'
  };
  
  RedisService: {
    responsibility: '动态构建数据类型前缀',
    format: '{global|cross|server{ID}}:{服务}:',
    example: 'server1:auth:' | 'global:auth:' | 'cross:auth:'
  };
}
```

### 3. 判断逻辑

```typescript
// 数据类型判断逻辑
function buildDataTypePrefix(dataType?: DataType, serverId?: string): string {
  if (!dataType) {
    // 未指定dataType，默认使用当前区服
    const currentServerId = process.env.SERVER_ID || '1';
    return `server${currentServerId}`;
  }
  
  switch (dataType) {
    case 'global':
      return 'global';
    case 'cross': 
      return 'cross';
    case 'server':
      const targetServerId = serverId || process.env.SERVER_ID || '1';
      return `server${targetServerId}`;
    default:
      return `server${process.env.SERVER_ID || '1'}`;
  }
}
```

## 🔧 核心接口扩展

### 1. CacheOptions接口扩展

```typescript
// 扩展缓存选项，添加数据类型支持
export interface CacheOptions {
  ttl?: number;
  ttlVariance?: number;
  enableProtection?: boolean;
  enableAvalancheProtection?: boolean;
  enableBreakdownProtection?: boolean;
  enablePenetrationProtection?: boolean;
  useBloomFilter?: boolean;
  cacheNullValues?: boolean;
  nullValueTTL?: number;
  lockTimeout?: number;
  maxRetries?: number;
  retryDelay?: number;
  
  // 新增：数据类型支持
  dataType?: 'global' | 'cross' | 'server';  // 数据类型
  serverId?: string;                          // 指定区服ID（可选）
}
```

### 2. RedisService核心方法扩展

```typescript
export class RedisService {
  /**
   * 构建带数据类型的完整键名
   */
  private buildDataTypeKey(
    key: string, 
    dataType?: 'global' | 'cross' | 'server', 
    serverId?: string
  ): string {
    if (!dataType) {
      return key; // 连接级前缀已包含服务前缀
    }

    const keyPrefix = this.redisConfig?.keyPrefix || '';
    const basePrefix = keyPrefix.replace(/:$/, '');
    const serviceContext = this.getServiceContext();
    
    switch (dataType) {
      case 'global':
        return `${basePrefix}:global:${serviceContext}:${key}`;
      case 'cross':
        return `${basePrefix}:cross:${serviceContext}:${key}`;
      case 'server':
        const targetServerId = serverId || this.getCurrentServerId();
        return `${basePrefix}:server${targetServerId}:${serviceContext}:${key}`;
      default:
        return key;
    }
  }

  // 扩展基础操作方法
  async set(
    key: string, 
    value: any, 
    ttl?: number, 
    dataType?: 'global' | 'cross' | 'server', 
    serverId?: string
  ): Promise<void> {
    const fullKey = this.buildDataTypeKey(key, dataType, serverId);
    // ... 实现逻辑
  }

  async get<T>(
    key: string, 
    dataType?: 'global' | 'cross' | 'server', 
    serverId?: string
  ): Promise<T | null> {
    const fullKey = this.buildDataTypeKey(key, dataType, serverId);
    // ... 实现逻辑
  }

  async del(
    key: string | string[], 
    dataType?: 'global' | 'cross' | 'server', 
    serverId?: string
  ): Promise<number> {
    // ... 实现逻辑
  }
}
```

## 📊 服务组件改造影响

### 改造优先级矩阵

| 服务组件 | 影响程度 | 改造复杂度 | 优先级 | 工作量 |
|---------|---------|-----------|--------|--------|
| RedisService | 🔴 高 | 中等 | P0 | 4人天 |
| RedisCacheService | 🔴 高 | 中等 | P0 | 2人天 |
| CacheManagerService | 🟡 中 | 低 | P1 | 1人天 |
| RedisProtectionService | 🟡 中 | 低 | P1 | 1人天 |
| RedisQueueService | 🟡 中 | 中等 | P2 | 3人天 |
| RedisPubSubService | 🟡 中 | 中等 | P2 | 3人天 |
| RedisLockService | 🟡 中 | 中等 | P2 | 2人天 |
| RedisBloomFilterService | 🟡 中 | 中等 | P3 | 2人天 |
| RedisHealthService | 🟢 低 | 无 | - | 0人天 |

### 核心改造点

#### 1. RedisService (P0 - 核心基础)
- 扩展所有基础方法支持dataType参数
- 实现buildDataTypeKey方法
- 保持向后兼容性

#### 2. RedisCacheService (P0 - 缓存核心)  
- 传递dataType到RedisService
- 扩展get/set/del等方法
- 支持缓存装饰器

#### 3. 其他服务组件 (P1-P3)
- 主要是参数传递和键名构建
- 保持现有API不变
- 添加dataType支持

## 🚀 使用示例

### 1. 基础Redis操作

```typescript
@Injectable()
export class UserService {
  constructor(private readonly redisService: RedisService) {}

  // 区服数据（默认）
  async cacheUserProfile(userId: string, profile: UserProfile): Promise<void> {
    await this.redisService.set(`user:profile:${userId}`, profile, 1800);
    // 实际键: dev:fm:server1:user:user:profile:123
  }

  // 全局数据
  async cacheServerList(servers: ServerInfo[]): Promise<void> {
    await this.redisService.set('servers:list', servers, 300, 'global');
    // 实际键: dev:fm:global:user:servers:list
  }

  // 跨服数据
  async cacheGlobalRanking(ranking: RankingData[]): Promise<void> {
    await this.redisService.set('ranking:global', ranking, 600, 'cross');
    // 实际键: dev:fm:cross:user:ranking:global
  }
}
```

### 2. 缓存装饰器

```typescript
@Controller('users')
export class UserController {
  // 区服数据缓存
  @Get(':id')
  @Cacheable({
    key: 'user:profile:#{id}',
    ttl: 1800,
    dataType: 'server'
  })
  async getUserProfile(@Param('id') id: string): Promise<UserProfile> {
    return await this.characterService.getUserProfile(id);
  }

  // 全局数据缓存
  @Get('servers/list')
  @Cacheable({
    key: 'servers:list',
    ttl: 300,
    dataType: 'global'
  })
  async getServerList(): Promise<ServerInfo[]> {
    return await this.characterService.getServerList();
  }

  // 跨服数据缓存
  @Get('ranking/:type')
  @Cacheable({
    key: 'ranking:#{type}',
    ttl: 600,
    dataType: 'cross'
  })
  async getGlobalRanking(@Param('type') type: string): Promise<RankingData[]> {
    return await this.characterService.getGlobalRanking(type);
  }
}
```

### 3. 手动缓存管理

```typescript
@Injectable()
export class ClubService {
  constructor(private readonly cacheManager: CacheManagerService) {}

  async getClubInfo(clubId: string): Promise<ClubInfo> {
    const repository = this.cacheManager.getRepository<ClubInfo>('clubs');

    return await repository.getOrLoad(
      `club:${clubId}`,
      async () => await this.loadClubFromDatabase(clubId),
      { 
        ttl: 1800, 
        enableProtection: true,
        dataType: 'server' // 区服数据
      }
    );
  }
}
```

## 🔄 实施计划

### 阶段1: 核心接口扩展 (2天)
1. 扩展CacheOptions接口，添加dataType和serverId字段
2. 扩展RedisService核心方法，添加数据类型支持  
3. 更新RedisCacheService，支持新的缓存选项
4. 完成基础功能测试

### 阶段2: 装饰器和拦截器升级 (1天)
1. 扩展缓存装饰器，支持dataType配置
2. 更新CacheInterceptor，处理数据类型路由
3. 扩展CacheManagerService，支持数据类型感知

### 阶段3: 高级服务组件改造 (3天)
1. 改造RedisQueueService，支持队列数据类型隔离
2. 改造RedisPubSubService，支持跨服通信
3. 改造RedisLockService，支持锁数据类型隔离
4. 改造RedisBloomFilterService和RedisProtectionService

### 阶段4: 业务集成和测试 (2天)
1. 在认证服务中集成全局数据缓存
2. 在游戏服务中集成区服数据缓存
3. 实现跨服功能的缓存支持
4. 全面测试数据隔离和功能正确性

## ✅ 验收标准

### 功能验收
- ✅ 支持global/cross/server三种数据类型
- ✅ 现有代码无需修改，向后兼容
- ✅ 缓存装饰器支持dataType配置
- ✅ 手动缓存操作支持dataType参数
- ✅ 数据完全隔离，无交叉污染

### 性能验收  
- ✅ 键构建性能无明显下降
- ✅ 缓存命中率保持稳定
- ✅ 内存使用合理增长
- ✅ 并发性能无回归

### 测试验收
- ✅ 单元测试覆盖率 > 90%
- ✅ 集成测试通过率 100%
- ✅ 性能测试基准达标
- ✅ 数据隔离测试通过

## 🎯 总结

Redis前缀架构v3.0通过扩展现有Redis公共库，实现了分区分服系统的核心需求：

1. **数据隔离**: 通过前缀完全隔离不同类型数据
2. **向后兼容**: 现有代码无需修改，渐进式升级
3. **性能优化**: 单Redis实例，连接级前缀处理
4. **开发友好**: 简单的dataType参数，类型安全

这是一个渐进式、低风险的升级方案，能够完美支持分区分服的所有核心需求。
