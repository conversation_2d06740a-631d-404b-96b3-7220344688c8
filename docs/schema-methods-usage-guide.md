# Schema业务方法使用指南

## 概述

本文档介绍如何使用新增的Schema业务方法，以及如何与项目的Result模式进行集成。

## 核心理念

### 1. 基于RepositoryResultWrapper.wrap的设计
- **利用现有基础设施**: 直接使用项目已有的`RepositoryResultWrapper.wrap`方法
- **统一错误处理**: 所有Schema方法调用都通过统一的Result模式处理
- **简洁的API**: 通过`EnhancedBaseRepository`提供便捷的包装方法

### 2. 三种调用方式
- **直接调用**: 在文档实例上直接调用Schema方法
- **Repository包装**: 通过Repository的包装方法调用
- **批量操作**: 支持批量和条件调用

## 快速开始

### 1. 使用EnhancedBaseRepository（推荐方式）

```typescript
// guild.repository.ts
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { EnhancedBaseRepository } from '@libs/common/base/enhanced-base-repository';
import { Guild, GuildDocument } from '../schemas/guild.schema';

@Injectable()
export class GuildRepository extends EnhancedBaseRepository<GuildDocument> {
  constructor(@InjectModel(Guild.name) guildModel: Model<GuildDocument>) {
    super(guildModel);
  }

  // 方式1: 直接调用Schema方法（推荐）
  async checkCanAddMember(guildId: string): Promise<XResult<boolean>> {
    return this.callSchemaMethod(guildId, 'canAddMember');
  }

  // 方式2: 使用便捷的条件检查方法
  async checkGuildCondition(guildId: string): Promise<XResult<boolean>> {
    return this.checkDocumentCondition(guildId, 'canAdd');
  }

  // 方式3: 批量验证公会数据
  async validateAllGuilds(): Promise<XResult<SchemaMethodBatchResult[]>> {
    return this.callSchemaMethodBatch({}, 'validateGuildData');
  }

  // 方式4: 条件性操作
  async processActiveGuilds(): Promise<XResult<SchemaMethodConditionalResult[]>> {
    return this.callSchemaMethodConditional(
      { isActive: true },
      'canAddMember',
      'updateGuildStats'
    );
  }
}
```

### 2. 在Service中使用

```typescript
// guild.service.ts
import { Injectable } from '@nestjs/common';
import { BaseService } from '@libs/common/base/base-service';
import { XResult } from '@libs/common/types/result.type';

@Injectable()
export class GuildService extends BaseService {

  constructor(private guildRepository: GuildRepository) {
    super();
  }

  // 使用Repository包装方法（推荐）
  async checkCanAddMember(guildId: string): Promise<XResult<boolean>> {
    return this.guildRepository.checkCanAddMember(guildId);
  }

  // 获取公会统计信息
  async getGuildStats(guildId: string): Promise<XResult<any>> {
    return this.guildRepository.getDocumentStats(guildId);
  }

  // 验证公会数据
  async validateGuild(guildId: string): Promise<XResult<ValidationResult>> {
    return this.guildRepository.validateDocument(guildId);
  }

  // 获取客户端数据
  async getGuildClientData(guildId: string): Promise<XResult<any>> {
    return this.guildRepository.toClientData(guildId);
  }
}
```

### 3. 直接在文档上调用（适用于已有文档实例的场景）

```typescript
// 在某个Service方法中
async processGuild(guild: GuildDocument): Promise<XResult<any>> {
  // 直接调用Schema方法，手动包装Result
  return await RepositoryResultWrapper.wrap(async () => {
    // 检查是否可以添加成员
    const canAdd = guild.canAddMember();
    if (!canAdd) {
      throw new Error('公会成员已满');
    }

    // 获取统计信息
    const stats = guild.getGuildStats();

    // 验证数据
    const validation = guild.validateGuildData();
    if (!validation.isValid) {
      throw new Error(`数据验证失败: ${validation.errors.join(', ')}`);
    }

    return { canAdd, stats, validation };
  });
}
```

## 具体使用案例

### 案例1: 公会成员管理

```typescript
// 在Controller中
@Post('guild/:id/members')
async addGuildMember(
  @Param('id') guildId: string,
  @Body() memberInfo: AddMemberDto
): Promise<XResult<boolean>> {
  
  // 1. 检查是否可以添加成员
  const canAddResult = await this.guildService.checkCanAddMember(guildId);
  if (!canAddResult.success) {
    return canAddResult;
  }
  
  if (!canAddResult.data) {
    return XResultUtils.error('公会成员已满', 'GUILD_MEMBER_FULL');
  }
  
  // 2. 执行添加操作
  return this.guildService.addMember(guildId, memberInfo);
}
```

### 案例2: 好友系统位置服务

```typescript
// friend.service.ts
async getNearbyFriends(
  characterId: string, 
  longitude: number, 
  latitude: number, 
  radius: number = 1000
): Promise<XResult<FriendInfo[]>> {
  
  const friendDoc = await this.friendRepository.findByCharacterId(characterId);
  if (!friendDoc.success) {
    return friendDoc;
  }
  
  // 使用Schema方法获取附近好友
  const nearbyFriendsResult = friendDoc.data.getNearbyFriendsResult(longitude, latitude, radius);
  if (!nearbyFriendsResult.success) {
    return nearbyFriendsResult;
  }
  
  return XResultUtils.ok(nearbyFriendsResult.data);
}
```

### 案例3: 邮件系统批量处理

```typescript
// mail.service.ts
async cleanupExpiredMails(): Promise<XResult<number>> {
  try {
    const expiredMails = await this.mailRepository.find({
      expireTime: { $lt: Date.now() }
    });
    
    if (!expiredMails.success) {
      return expiredMails;
    }
    
    let cleanedCount = 0;
    
    for (const mail of expiredMails.data) {
      // 使用Schema方法检查是否可以删除
      const canDeleteResult = mail.canBeDeletedResult();
      if (canDeleteResult.success && canDeleteResult.data) {
        await mail.markAsDeleted();
        cleanedCount++;
      }
    }
    
    return XResultUtils.ok(cleanedCount);
    
  } catch (error) {
    return XResultUtils.error('清理过期邮件失败', 'MAIL_CLEANUP_ERROR', error);
  }
}
```

## 批量启用Schema方法

### 1. 创建统一配置

```typescript
// schemas/index.ts
import { BatchResultMethodGenerator } from '@libs/common/decorators/schema-result.decorator';
import { GuildSchema } from './guild.schema';
import { FriendSchema } from './friend.schema';
import { MailSchema } from './mail.schema';
import { ChatMessageSchema, ChatChannelSchema } from './chat.schema';

// 批量为所有Schema生成Result方法
BatchResultMethodGenerator.generateForSchemas([
  { name: 'Guild', schema: GuildSchema },
  { name: 'Friend', schema: FriendSchema },
  { name: 'Mail', schema: MailSchema },
  { name: 'ChatMessage', schema: ChatMessageSchema },
  { name: 'ChatChannel', schema: ChatChannelSchema }
]);
```

### 2. 在应用启动时调用

```typescript
// app.module.ts 或 main.ts
import './schemas'; // 确保Schema方法被初始化
```

## 错误处理最佳实践

### 1. 统一错误码

```typescript
// 在schema方法中使用统一的错误码格式
export const SCHEMA_ERROR_CODES = {
  GUILD_MEMBER_FULL: 'GUILD_MEMBER_FULL',
  FRIEND_NOT_FOUND: 'FRIEND_NOT_FOUND',
  MAIL_EXPIRED: 'MAIL_EXPIRED',
  CHAT_CHANNEL_FULL: 'CHAT_CHANNEL_FULL'
} as const;
```

### 2. 错误信息国际化

```typescript
// 支持多语言错误信息
const ERROR_MESSAGES = {
  zh: {
    GUILD_MEMBER_FULL: '公会成员已满',
    FRIEND_NOT_FOUND: '好友不存在'
  },
  en: {
    GUILD_MEMBER_FULL: 'Guild member limit reached',
    FRIEND_NOT_FOUND: 'Friend not found'
  }
};
```

## 性能优化建议

### 1. 避免过度使用

```typescript
// ❌ 不推荐：在循环中频繁调用Result方法
for (const guild of guilds) {
  const result = await guild.canAddMemberResult();
  // ...
}

// ✅ 推荐：批量处理
const results = await this.guildRepository.callDocumentMethodBatch(
  { isActive: true },
  'canAddMember'
);
```

### 2. 缓存验证结果

```typescript
// 对于复杂的验证逻辑，考虑缓存结果
@Cacheable('guild-validation', 300) // 缓存5分钟
async validateGuildData(guildId: string): Promise<XResult<ValidationResult>> {
  return this.guildRepository.callDocumentMethodResult(guildId, 'validateGuildData');
}
```

## 总结

通过装饰器模式和增强版Repository，我们实现了：

1. **无侵入式集成**: 现有代码无需修改
2. **双模式支持**: 原方法和Result方法并存
3. **统一错误处理**: 自动包装错误信息
4. **批量操作支持**: 提供批量调用能力
5. **性能优化**: 支持条件调用和缓存

这种设计既保持了代码的简洁性，又提供了强大的错误处理能力，完美适配了项目的Result模式架构。
