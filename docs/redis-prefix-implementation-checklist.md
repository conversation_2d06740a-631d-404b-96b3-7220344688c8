# Redis前缀架构实施检查清单

## 📋 实施前准备

### ✅ 环境准备
- [ ] 确认所有微服务都能正常运行
- [ ] 备份当前Redis数据（如有重要数据）
- [ ] 确认Redis服务器版本兼容性
- [ ] 准备测试环境进行验证

### ✅ 代码准备
- [ ] 创建功能分支 `feature/redis-prefix-v2`
- [ ] 确认当前代码已提交到版本控制
- [ ] 准备回滚方案

## 🔧 核心模块实施

### 架构简化决策
- [x] **删除RedisKeyBuilder** - 移除复杂的键构建器，简化为直接使用业务键名
- [x] **采用连接级前缀** - 使用ioredis的keyPrefix配置，性能最优
- [x] **封装特殊方法** - 对keys()等方法进行封装，保持用户层透明
- [x] **统一前缀处理** - 所有Redis操作使用一致的前缀处理策略

### 阶段1：修改RedisModule
- [ ] 修改 `libs/common/src/redis/redis.module.ts`
  - [ ] 添加服务上下文支持
  - [ ] 实现自动服务名推断
  - [ ] 添加数据库自动分配
  - [ ] 实现前缀自动构建

- [ ] 修改 `libs/common/src/redis/redis.service.ts`
  - [ ] 移除手动前缀处理逻辑
  - [ ] 依赖连接级前缀自动处理
  - [ ] 封装keys()方法处理前缀
  - [ ] 简化工具方法（移除getFullKey、removePrefix）
  - [ ] 保持现有API不变

### 阶段2：更新缓存拦截器
- [ ] 修改 `libs/common/src/redis/cache/cache.interceptor.ts`
  - [ ] 集成服务上下文
  - [ ] 确保装饰器正常工作
  - [ ] 保持现有装饰器API

- [ ] 验证装饰器功能
  - [ ] `@Cacheable` 正常工作
  - [ ] `@CacheEvict` 正常工作
  - [ ] `@CachePut` 正常工作

## 🏗️ 微服务迁移

### 网关服务 (apps/gateway)
- [ ] 修改 `apps/gateway/src/app.module.ts`
  - [ ] 确认现有 `RedisModule.forRootAsync()` 配置
  - [ ] 添加服务上下文（可选，自动推断）
  - [ ] 测试网关功能正常

### 认证服务 (apps/auth)
- [ ] 修改 `apps/auth/src/app.module.ts`
  - [ ] 移除 `CacheModule.registerAsync` 配置
  - [ ] 添加 `RedisModule.forRootAsync` 配置
  - [ ] 指定 `service: 'auth'`
  - [ ] 指定 `database: 1`

- [ ] 验证认证服务功能
  - [ ] 用户登录正常
  - [ ] 会话管理正常
  - [ ] 缓存装饰器正常工作

### 其他微服务
- [ ] 用户服务 (apps/user)
  - [ ] 添加 `RedisModule.forRootAsync` 配置
  - [ ] 设置 `database: 2`

- [ ] 游戏服务 (apps/game)
  - [ ] 添加 `RedisModule.forRootAsync` 配置
  - [ ] 设置 `database: 3`

- [ ] 俱乐部服务 (apps/club)
  - [ ] 添加 `RedisModule.forRootAsync` 配置
  - [ ] 设置 `database: 4`

### 备份系统迁移
- [ ] 更新备份服务配置
  - [ ] 修改 `libs/common/src/backup/services/redis-backup.service.ts`
  - [ ] 适配新的前缀架构
  - [ ] 支持按服务和数据库备份

- [ ] 更新备份配置
  - [ ] 修改 `.env.backup` 配置文件
  - [ ] 更新备份模式匹配规则
  - [ ] 配置服务级别的备份策略

- [ ] 验证备份功能
  - [ ] 测试完整备份功能
  - [ ] 测试增量备份功能
  - [ ] 测试备份验证功能
  - [ ] 测试恢复功能

## 🧪 测试验证

### 单元测试
- [ ] 运行Redis模块单元测试
  ```bash
  npm run test libs/common/src/redis
  ```

- [ ] 运行缓存拦截器测试
  ```bash
  npm run test libs/common/src/redis/cache
  ```

### 集成测试
- [ ] 测试Redis连接
  ```bash
  npm run test:redis-connection
  ```

- [ ] 测试服务上下文
  ```bash
  npm run test:service-context
  ```

### 功能测试
- [ ] 测试网关服务
  - [ ] 启动网关服务
  - [ ] 验证Redis键格式：`dev:fm:gateway:*`
  - [ ] 测试路由缓存功能

- [ ] 测试认证服务
  - [ ] 启动认证服务
  - [ ] 验证Redis键格式：`dev:fm:auth:*`
  - [ ] 测试用户登录和缓存

- [ ] 测试缓存装饰器
  - [ ] 验证 `@Cacheable` 生成正确的键
  - [ ] 验证 `@CacheEvict` 清理正确的键
  - [ ] 验证TTL设置正确

### 端到端测试
- [ ] 运行完整的E2E测试套件
  ```bash
  npm run test:e2e
  ```

- [ ] 验证跨服务隔离
  - [ ] 不同服务的键不会冲突
  - [ ] 数据库隔离正常工作

## 🔍 验证检查

### Redis键格式验证
- [ ] 连接Redis查看实际键格式
  ```bash
  redis-cli -h *************** -p 6379 -a 123456
  keys "*"
  ```

- [ ] 验证键格式符合规范
  - [ ] 格式：`{env}:{project}:{service}:{key}`
  - [ ] 环境前缀正确（dev/test/prod）
  - [ ] 项目前缀正确（fm）
  - [ ] 服务前缀正确（gateway/auth/user等）

### 数据库分离验证
- [ ] 验证不同服务使用不同数据库
  ```bash
  # 检查各数据库的键数量
  redis-cli -h *************** -p 6379 -a 123456 -n 0 dbsize  # gateway
  redis-cli -h *************** -p 6379 -a 123456 -n 1 dbsize  # auth
  redis-cli -h *************** -p 6379 -a 123456 -n 2 dbsize  # user
  ```

### 备份功能验证
- [ ] 验证服务感知备份
  ```bash
  node scripts/backup-cli.js service --services=auth,user --wait
  ```

- [ ] 验证数据库级备份
  ```bash
  node scripts/backup-cli.js database --databases=1,2 --wait
  ```

- [ ] 验证备份键格式
  - [ ] 检查备份文件中的键格式
  - [ ] 确认环境前缀正确
  - [ ] 确认服务前缀正确

- [ ] 验证恢复功能
  ```bash
  node scripts/backup-cli.js restore backup-xxx --dry-run
  ```

### 缓存功能验证
- [ ] 验证缓存命中率
- [ ] 验证缓存过期时间
- [ ] 验证缓存清理功能

## 🚀 部署准备

### 配置文件更新
- [ ] 更新Docker配置（如使用）
- [ ] 更新Kubernetes配置（如使用）
- [ ] 更新CI/CD配置

### 文档更新
- [ ] 更新README文档
- [ ] 更新API文档
- [ ] 更新部署文档
- [ ] 创建迁移指南

## 📊 性能监控

### 监控指标
- [ ] Redis连接数监控
- [ ] 键空间使用监控
- [ ] 缓存命中率监控
- [ ] 响应时间监控

### 告警设置
- [ ] Redis连接异常告警
- [ ] 缓存命中率过低告警
- [ ] 响应时间过长告警

## ✅ 完成确认

### 功能确认
- [ ] 所有微服务正常启动
- [ ] 缓存功能正常工作
- [ ] Redis键格式正确
- [ ] 服务间隔离正常

### 性能确认
- [ ] 响应时间无明显增加
- [ ] 内存使用正常
- [ ] CPU使用正常
- [ ] Redis性能正常

## 📝 实施记录

### 实施日志
- [ ] 记录实施开始时间
- [ ] 记录各阶段完成时间
- [ ] 记录遇到的问题和解决方案
- [ ] 记录性能对比数据

### 问题记录
- [ ] 记录实施过程中的问题
- [ ] 记录解决方案
- [ ] 更新故障排除文档

---

## 🎯 关键成功指标

- ✅ 所有微服务成功迁移到新的Redis前缀架构
- ✅ 缓存装饰器功能完全正常
- ✅ Redis键格式符合新规范
- ✅ 服务间数据完全隔离
- ✅ 性能无明显下降
- ✅ 所有测试通过

---

**检查清单版本**: 1.0  
**创建日期**: 2025-07-03  
**适用于**: Redis前缀架构v2.0实施
