# Schema方法Result模式集成总结

## 解决方案概述

基于项目现有的`RepositoryResultWrapper.wrap`基础设施，我们提供了一个优雅的解决方案来集成新增的Schema业务方法和Result模式。

## 核心设计理念

### 1. 利用现有基础设施
- **不重复造轮子**: 直接使用项目已有的`RepositoryResultWrapper.wrap`方法
- **保持架构一致性**: 与现有的三层架构完美集成
- **统一错误处理**: 所有错误都通过统一的Result模式处理

### 2. 最小侵入性
- **无需修改现有Schema方法**: 保持原有方法的简洁性
- **向后兼容**: 不影响现有代码的正常运行
- **渐进式迁移**: 可以逐步采用新的调用方式

## 技术实现

### 1. EnhancedBaseRepository
创建了增强版的BaseRepository，提供以下核心方法：

```typescript
// 基础Schema方法调用
async callSchemaMethod(id: string, methodName: string, ...args: any[]): Promise<XResult<any>>

// 批量Schema方法调用
async callSchemaMethodBatch(filter: FilterQuery<T>, methodName: string, ...args: any[]): Promise<XResult<SchemaMethodBatchResult[]>>

// 条件Schema方法调用
async callSchemaMethodConditional(filter: FilterQuery<T>, conditionMethod: string, targetMethod: string, ...args: any[]): Promise<XResult<SchemaMethodConditionalResult[]>>

// 便捷方法
async validateDocument(id: string): Promise<XResult<ValidationResult>>
async getDocumentStats(id: string): Promise<XResult<any>>
async checkDocumentCondition(id: string, conditionType: string): Promise<XResult<boolean>>
async toClientData(id: string): Promise<XResult<any>>
```

### 2. 自动方法映射
系统自动识别常见的Schema方法模式：

- **验证方法**: `validateData`, `validate`, `isValid`, `validateGuildData`, `validateFriendData`
- **统计方法**: `getStats`, `getStatistics`, `getSummary`, `getGuildStats`, `getFriendStats`
- **条件检查**: `canAdd`, `canDelete`, `canUpdate`, `canRead`, `canClaim`, `isExpired`
- **客户端转换**: `toClientData`, `toClient`, `toClientInfo`, `toClientGuildData`

## 使用方式对比

### 方式1: 传统方式（需要手动错误处理）
```typescript
async checkCanAddMember(guildId: string): Promise<XResult<boolean>> {
  try {
    const guild = await this.guildRepository.findById(guildId);
    if (!guild.success) return guild;
    
    const canAdd = guild.data.canAddMember();
    return XResultUtils.ok(canAdd);
  } catch (error) {
    return XResultUtils.error('检查失败', 'CHECK_ERROR', error);
  }
}
```

### 方式2: 新方式（自动错误处理）
```typescript
async checkCanAddMember(guildId: string): Promise<XResult<boolean>> {
  return this.guildRepository.callSchemaMethod(guildId, 'canAddMember');
}
```

## 实际应用场景

### 1. 单个文档操作
```typescript
// 检查公会是否可以添加成员
const result = await guildRepository.callSchemaMethod(guildId, 'canAddMember');

// 验证好友数据
const validation = await friendRepository.validateDocument(friendId);

// 获取邮件统计
const stats = await mailRepository.getDocumentStats(mailId);
```

### 2. 批量操作
```typescript
// 批量验证所有活跃公会
const results = await guildRepository.callSchemaMethodBatch(
  { isActive: true }, 
  'validateGuildData'
);

// 批量清理过期邮件
const cleanupResults = await mailRepository.callSchemaMethodBatch(
  { expireTime: { $lt: Date.now() } },
  'markAsDeleted'
);
```

### 3. 条件操作
```typescript
// 只对满足条件的文档执行操作
const results = await guildRepository.callSchemaMethodConditional(
  { isActive: true },
  'canLevelUp',        // 条件检查方法
  'updateGuildStats'   // 目标执行方法
);
```

## 性能优化

### 1. 批量处理
- 避免在循环中频繁调用单个方法
- 使用批量方法处理多个文档
- 支持条件性批量操作

### 2. 错误处理优化
- 统一的错误格式和错误码
- 详细的错误上下文信息
- 支持错误链追踪

### 3. 内存优化
- 避免加载不必要的文档数据
- 支持投影查询
- 合理使用索引

## 错误处理策略

### 1. 统一错误码
```typescript
export const SCHEMA_METHOD_ERROR_CODES = {
  DOCUMENT_NOT_FOUND: 'DOCUMENT_NOT_FOUND',
  METHOD_NOT_FOUND: 'METHOD_NOT_FOUND',
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  CONDITION_NOT_MET: 'CONDITION_NOT_MET'
} as const;
```

### 2. 详细错误信息
- 包含方法名和参数信息
- 提供错误发生的上下文
- 支持错误堆栈追踪

### 3. 优雅降级
- 方法不存在时的备选方案
- 部分失败时的处理策略
- 批量操作中的错误隔离

## 最佳实践

### 1. Repository层
- 继承`EnhancedBaseRepository`
- 为常用操作创建便捷方法
- 使用批量操作提高性能

### 2. Service层
- 专注于业务逻辑组合
- 利用Repository的包装方法
- 处理复杂的业务场景

### 3. Controller层
- 直接返回Result格式
- 保持接口的简洁性
- 提供清晰的API文档

## 迁移建议

### 1. 渐进式迁移
- 新功能优先使用新方式
- 逐步重构现有代码
- 保持向后兼容

### 2. 团队培训
- 理解Result模式的优势
- 掌握新的调用方式
- 建立最佳实践规范

### 3. 监控和测试
- 添加相应的单元测试
- 监控错误率和性能
- 收集使用反馈

## 总结

通过这种设计，我们实现了：

1. **完美集成**: 新增的Schema方法与现有的Result模式无缝集成
2. **简洁API**: 提供了简洁而强大的调用接口
3. **统一处理**: 所有错误都通过统一的机制处理
4. **高性能**: 支持批量操作和条件操作
5. **易维护**: 代码结构清晰，易于理解和维护

这种方案既保持了代码的简洁性，又提供了强大的功能，是一个优雅而实用的解决方案。
