# Gateway多实例部署指南

## 📋 **概述**

本文档描述如何部署和管理Gateway服务的多实例配置，以应对高负载和提供高可用性。

## 🎯 **适用场景**

### **何时使用多实例**
- WebSocket连接数超过5000个
- HTTP请求QPS超过1000
- 需要零停机部署
- 需要故障隔离和容错

### **何时使用单实例**
- 开发和测试环境
- 小规模部署（< 1000用户）
- 资源受限环境

## ⚙️ **配置说明**

### **环境变量配置**

#### **单实例模式（默认）**
```bash
# .env
GATEWAY_MULTI_INSTANCE=false
GATEWAY_PORT=3000
```

#### **多实例模式**
```bash
# 基础配置
GATEWAY_MULTI_INSTANCE=true
GATEWAY_BASE_PORT=3000
GATEWAY_MAX_INSTANCES=5

# 实例1配置
GATEWAY_INSTANCE_ID=0
# 实际端口: 3000 + 0 = 3000

# 实例2配置  
GATEWAY_INSTANCE_ID=1
# 实际端口: 3000 + 1 = 3001

# 实例3配置
GATEWAY_INSTANCE_ID=2
# 实际端口: 3000 + 2 = 3002
```

## 🚀 **部署方案**

### **方案1: Docker Compose部署**

```yaml
# docker-compose.yml
version: '3.8'

services:
  # 负载均衡器
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - gateway-1
      - gateway-2
      - gateway-3

  # Gateway实例1
  gateway-1:
    build: .
    environment:
      - GATEWAY_MULTI_INSTANCE=true
      - GATEWAY_INSTANCE_ID=0
      - GATEWAY_BASE_PORT=3000
      - GATEWAY_MAX_INSTANCES=3
    ports:
      - "3000:3000"
    
  # Gateway实例2
  gateway-2:
    build: .
    environment:
      - GATEWAY_MULTI_INSTANCE=true
      - GATEWAY_INSTANCE_ID=1
      - GATEWAY_BASE_PORT=3000
      - GATEWAY_MAX_INSTANCES=3
    ports:
      - "3001:3001"
      
  # Gateway实例3
  gateway-3:
    build: .
    environment:
      - GATEWAY_MULTI_INSTANCE=true
      - GATEWAY_INSTANCE_ID=2
      - GATEWAY_BASE_PORT=3000
      - GATEWAY_MAX_INSTANCES=3
    ports:
      - "3002:3002"
```

### **方案2: Kubernetes部署**

```yaml
# gateway-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gateway
spec:
  replicas: 3
  selector:
    matchLabels:
      app: gateway
  template:
    metadata:
      labels:
        app: gateway
    spec:
      containers:
      - name: gateway
        image: gateway:latest
        env:
        - name: GATEWAY_MULTI_INSTANCE
          value: "true"
        - name: GATEWAY_BASE_PORT
          value: "3000"
        - name: GATEWAY_MAX_INSTANCES
          value: "3"
        - name: GATEWAY_INSTANCE_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        ports:
        - containerPort: 3000
        livenessProbe:
          httpGet:
            path: /health/live
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: gateway-service
spec:
  selector:
    app: gateway
  ports:
  - port: 80
    targetPort: 3000
  type: LoadBalancer
```

## 🔧 **负载均衡器配置**

### **Nginx配置示例**

```nginx
# nginx.conf
upstream gateway_cluster {
    # 轮询策略
    server localhost:3000 weight=1 max_fails=3 fail_timeout=30s;
    server localhost:3001 weight=1 max_fails=3 fail_timeout=30s;
    server localhost:3002 weight=1 max_fails=3 fail_timeout=30s;
    
    # 保持连接
    keepalive 32;
}

# WebSocket支持的upstream
upstream gateway_websocket {
    ip_hash; # WebSocket需要会话保持
    server localhost:3000;
    server localhost:3001;
    server localhost:3002;
}

server {
    listen 80;
    server_name your-domain.com;

    # HTTP API请求
    location /api/ {
        proxy_pass http://gateway_cluster;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 连接池优化
        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }

    # WebSocket连接
    location /socket.io/ {
        proxy_pass http://gateway_websocket;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket超时设置
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }

    # 健康检查
    location /health {
        proxy_pass http://gateway_cluster;
        proxy_set_header Host $host;
    }
}
```

## 📊 **监控和管理**

### **健康检查端点**

每个Gateway实例都提供增强的健康检查信息：

```bash
# 检查实例1
curl http://localhost:3000/health
{
  "status": "ok",
  "timestamp": "2025-01-03T10:00:00.000Z",
  "service": "gateway",
  "version": "1.0.0",
  "uptime": 3600,
  "instance": {
    "id": 0,
    "port": 3000,
    "mode": "multi",
    "basePort": 3000,
    "calculatedPort": 3000,
    "maxInstances": 3
  }
}

# 检查实例2
curl http://localhost:3001/health
{
  "instance": {
    "id": 1,
    "port": 3001,
    "calculatedPort": 3001
  }
}
```

### **实例管理脚本**

```bash
#!/bin/bash
# start-gateway-cluster.sh

# 启动多个Gateway实例
for i in {0..2}; do
    export GATEWAY_MULTI_INSTANCE=true
    export GATEWAY_INSTANCE_ID=$i
    export GATEWAY_BASE_PORT=3000
    export GATEWAY_MAX_INSTANCES=3
    
    echo "启动Gateway实例 $i (端口: $((3000 + i)))"
    npm run start:gateway &
    
    sleep 5
done

echo "所有Gateway实例已启动"
```

## 🔍 **故障排除**

### **常见问题**

#### **Q: 端口冲突错误**
```
Error: 网关实例1端口3001不可用，请检查端口占用情况
```
**解决方案**: 检查端口占用，调整GATEWAY_BASE_PORT或停止占用端口的进程

#### **Q: 实例ID超出限制**
```
Error: 网关实例ID 5 超出最大限制 3
```
**解决方案**: 增加GATEWAY_MAX_INSTANCES或减少GATEWAY_INSTANCE_ID

#### **Q: WebSocket连接不稳定**
**解决方案**: 确保负载均衡器配置了会话保持（ip_hash）

### **调试命令**

```bash
# 检查所有实例状态
for port in 3000 3001 3002; do
    echo "检查端口 $port:"
    curl -s http://localhost:$port/health | jq '.instance'
done

# 检查端口占用
netstat -tulpn | grep :300

# 查看实例日志
docker-compose logs gateway-1
docker-compose logs gateway-2
docker-compose logs gateway-3
```

## 📈 **性能优化建议**

### **实例数量规划**
- **轻负载**: 1-2个实例
- **中等负载**: 3-5个实例  
- **高负载**: 5-10个实例

### **资源分配**
- **CPU**: 每实例2-4核
- **内存**: 每实例2-4GB
- **网络**: 确保足够带宽

### **监控指标**
- 每实例连接数
- 响应时间
- 错误率
- 资源使用率

---

**Gateway多实例部署现已完全支持，可以根据负载需求灵活扩展！**
