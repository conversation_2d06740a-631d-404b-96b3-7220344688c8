# CentOS虚拟机测试环境配置指南

## 🎯 **概述**

本指南将帮助您在CentOS虚拟机(***************)上配置测试环境，并从Windows主机远程执行测试。

## 📋 **需要您配合的操作清单**

### **阶段1: 虚拟机基础环境准备**

#### **1.1 检查虚拟机基础信息**
请在CentOS虚拟机上执行以下命令并提供结果：

```bash
# 检查系统版本
cat /etc/centos-release

# 检查内存和CPU
free -h
nproc

# 检查磁盘空间
df -h

# 检查网络配置
ip addr show
```

#### **1.2 检查网络连通性**
```bash
# 检查是否能访问外网
ping -c 3 *******

# 检查DNS解析
nslookup google.com
```

### **阶段2: 安装必要软件**

#### **2.1 更新系统并安装基础工具**
```bash
# 更新系统
sudo yum update -y

# 安装基础工具
sudo yum install -y curl wget git vim unzip

# 安装网络工具
sudo yum install -y net-tools telnet nc
```

#### **2.2 安装Docker和Docker Compose**
```bash
# 安装Docker
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install -y docker-ce docker-ce-cli containerd.io

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 将当前用户添加到docker组
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker --version
docker-compose --version
```

#### **2.3 安装Node.js**
```bash
# 安装Node.js 18.x
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# 验证安装
node --version
npm --version
```

### **阶段3: 配置防火墙和网络**

#### **3.1 配置防火墙规则**
```bash
# 检查防火墙状态
sudo systemctl status firewalld

# 开放必要端口
sudo firewall-cmd --permanent --add-port=3001/tcp  # Gateway服务
sudo firewall-cmd --permanent --add-port=3002/tcp  # Auth服务
sudo firewall-cmd --permanent --add-port=6380/tcp  # Redis测试
sudo firewall-cmd --permanent --add-port=27018/tcp # MongoDB测试

# 重新加载防火墙规则
sudo firewall-cmd --reload

# 验证端口开放
sudo firewall-cmd --list-ports
```

#### **3.2 配置SELinux（如果启用）**
```bash
# 检查SELinux状态
getenforce

# 如果是Enforcing，临时设置为Permissive
sudo setenforce 0

# 永久禁用SELinux（可选）
sudo sed -i 's/SELINUX=enforcing/SELINUX=disabled/' /etc/selinux/config
```

### **阶段4: 部署项目代码**

#### **4.1 创建项目目录**
```bash
# 创建项目目录
mkdir -p ~/football-manager-test
cd ~/football-manager-test
```

#### **4.2 传输项目文件**
有几种方式可以将项目文件传输到虚拟机：

**方式1: 使用Git克隆（推荐）**
```bash
# 如果项目在Git仓库中
git clone <your-project-repo> .
```

**方式2: 使用SCP从Windows传输**
在Windows PowerShell中执行：
```powershell
# 压缩项目文件
Compress-Archive -Path "E:\football manager\server-new\*" -DestinationPath "E:\football manager\project.zip"

# 使用SCP传输（需要安装OpenSSH客户端）
scp "E:\football manager\project.zip" username@***************:~/football-manager-test/
```

然后在虚拟机上解压：
```bash
cd ~/football-manager-test
unzip project.zip
```

**方式3: 使用共享文件夹**
如果虚拟机支持共享文件夹，可以直接访问Windows主机的文件。

### **阶段5: 配置测试环境**

#### **5.1 安装项目依赖**
```bash
cd ~/football-manager-test

# 安装依赖
npm install

# 构建项目
npm run build
```

#### **5.2 配置环境变量**
```bash
# 创建测试环境配置
cat > .env.test << 'EOF'
NODE_ENV=test

# 数据库配置
REDIS_HOST=localhost
REDIS_PORT=6380
REDIS_PASSWORD=test123
MONGODB_URI=************************************************

# JWT配置
JWT_SECRET=test-jwt-secret-key-for-testing-only
JWT_EXPIRES_IN=1h
JWT_ALGORITHM=HS256

# 服务端口
AUTH_PORT=3001
GATEWAY_PORT=3000

# 其他配置
LOG_LEVEL=debug
ENABLE_SWAGGER=true
EOF
```

#### **5.3 修改Docker Compose配置**
```bash
# 修改docker-compose.test.yml中的端口映射，确保服务可以从外部访问
# 将所有的 "127.0.0.1:port:port" 改为 "0.0.0.0:port:port"

sed -i 's/127\.0\.0\.1:/0.0.0.0:/g' docker-compose.test.yml
```

### **阶段6: 启动测试服务**

#### **6.1 启动测试环境**
```bash
# 启动测试数据库
docker-compose -f docker-compose.test.yml up -d redis-test mongodb-test

# 等待数据库启动
sleep 30

# 验证数据库连接
docker exec $(docker-compose -f docker-compose.test.yml ps -q redis-test) redis-cli -a test123 ping
docker exec $(docker-compose -f docker-compose.test.yml ps -q mongodb-test) mongosh --eval "db.runCommand('ping')"

# 构建并启动应用服务
docker-compose -f docker-compose.test.yml build
docker-compose -f docker-compose.test.yml up -d

# 等待服务启动
sleep 60
```

#### **6.2 验证服务状态**
```bash
# 检查容器状态
docker-compose -f docker-compose.test.yml ps

# 检查服务健康状态
curl http://localhost:3002/health
curl http://localhost:3001/health

# 检查端口监听
netstat -tlnp | grep -E ':(3001|3002|6380|27018)'
```

## 🔧 **Windows主机测试执行**

### **步骤1: 验证网络连通性**
在Windows PowerShell中执行：
```powershell
# 测试虚拟机连通性
Test-Connection -ComputerName *************** -Count 3

# 测试服务端口
Test-NetConnection -ComputerName *************** -Port 3001
Test-NetConnection -ComputerName *************** -Port 3002
```

### **步骤2: 执行远程测试**
```powershell
# 在项目根目录执行
.\scripts\vm-remote-test.ps1

# 或指定虚拟机地址
.\scripts\vm-remote-test.ps1 -VMHost "***************"
```

### **步骤3: 执行完整测试套件**
```powershell
# 修改测试配置指向虚拟机
$env:AUTH_BASE_URL = "http://***************:3002"
$env:GATEWAY_BASE_URL = "http://***************:3001"
$env:REDIS_HOST = "***************"
$env:REDIS_PORT = "6380"
$env:MONGODB_URI = "******************************************************"

# 执行本地测试（连接到虚拟机服务）
npm run test:integration
```

## 📊 **监控和调试**

### **虚拟机端监控**
```bash
# 查看容器日志
docker-compose -f docker-compose.test.yml logs -f

# 查看特定服务日志
docker-compose -f docker-compose.test.yml logs -f auth-test
docker-compose -f docker-compose.test.yml logs -f gateway-test

# 监控系统资源
top
htop  # 如果安装了
```

### **Windows端监控**
```powershell
# 监控网络连接
netstat -an | findstr ***************

# 查看测试日志
Get-Content test-results\vm-remote-test-report.txt
```

## 🚨 **故障排除**

### **常见问题及解决方案**

#### **1. 网络连通性问题**
```bash
# 检查虚拟机网络配置
ip route show
cat /etc/resolv.conf

# 检查防火墙状态
sudo firewall-cmd --list-all
```

#### **2. Docker服务问题**
```bash
# 重启Docker服务
sudo systemctl restart docker

# 检查Docker状态
sudo systemctl status docker

# 清理Docker资源
docker system prune -f
```

#### **3. 端口占用问题**
```bash
# 检查端口占用
sudo netstat -tlnp | grep -E ':(3001|3002)'

# 杀死占用端口的进程
sudo fuser -k 3001/tcp
sudo fuser -k 3002/tcp
```

#### **4. 内存不足问题**
```bash
# 检查内存使用
free -h

# 清理系统缓存
sudo sync && sudo sysctl vm.drop_caches=3
```

## 📝 **检查清单**

在开始测试前，请确认以下项目：

- [ ] CentOS虚拟机可以正常访问
- [ ] 虚拟机有足够的资源（至少4GB内存，20GB磁盘）
- [ ] Docker和Docker Compose已安装
- [ ] Node.js已安装
- [ ] 防火墙端口已开放
- [ ] 项目代码已传输到虚拟机
- [ ] 测试服务已启动
- [ ] Windows主机可以访问虚拟机服务

## 🎯 **下一步**

完成以上配置后，您就可以：
1. 从Windows主机远程测试虚拟机上的服务
2. 执行完整的功能测试套件
3. 进行性能和压力测试
4. 调试和优化服务配置

有任何问题请随时告诉我，我会协助您解决！
