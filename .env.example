# WebSocket网关配置
# 启用统一Result格式转换（推荐）- 将所有微服务响应转换为统一的{code, message, data, timestamp}格式
WEBSOCKET_UNIFIED_RESULT_FORMAT=true
# 禁用统一Result格式转换（向后兼容模式）- 保持原始响应格式
# WEBSOCKET_UNIFIED_RESULT_FORMAT=false

# MongoDB 数据库配置
# 认证服务数据库
AUTH_MONGODB_URI=mongodb://***************:27017/auth_db

# 角色服务数据库
CHARACTER_MONGODB_URI=mongodb://***************:27017/character_db

# 经济服务数据库
ECONOMY_MONGODB_URI=mongodb://***************:27017/economy_db

# 社交服务数据库
SOCIAL_MONGODB_URI=mongodb://***************:27017/social_db

# 活动服务数据库
ACTIVITY_MONGODB_URI=mongodb://***************:27017/activity_db

# Redis 配置
REDIS_HOST=***************
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 环境配置
NODE_ENV=development
PROJECT_NAME=football-manager
SERVER_PREFIX=dev

# JWT 配置
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=7d

# 微服务通信配置
MICROSERVICE_SECRET=your-microservice-secret
MICROSERVICE_TIMEOUT=30000

# 日志配置
LOG_LEVEL=debug
LOG_FILE_PATH=./logs

# 游戏配置
GAME_CONFIG_PATH=./config/game
GAME_DATA_PATH=./data/game

# 安全配置
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# 邮件配置（可选）
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
