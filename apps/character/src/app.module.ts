import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';

// 配置
import appConfig from './config/app.config';

// 功能模块
import { HealthModule } from '@character/modules/health/health.module';
import { CharacterModule } from '@character/modules/character/character.module';
import { FormationModule } from '@character/modules/formation/formation.module';
import { InventoryModule } from '@character/modules/inventory/inventory.module'; // 统一的物品背包模块
import { TacticModule } from '@character/modules/tactic/tactic.module';
import { HeroModule } from '@character/modules/hero/hero.module'; // 英雄统一模块
import { BeliefModule } from '@character/modules/belief/belief.module'; // 信仰系统模块
import { OrchestratorModule } from '@character/modules/orchestrator/orchestrator.module'; // 业务聚合编排模块

// 基础设施模块
import { createMongoConfig, setupDatabaseEvents } from '@libs/database/mongodb.config';
import { RedisModule } from '@libs/redis';
import { GameConfigModule } from "@libs/game-config";
import { ServiceMeshModule, ServiceConfigurationBuilder } from '@libs/service-mesh';
import { MICROSERVICE_NAMES } from '@libs/shared/constants';

@Module({
  imports: [
    // 全局配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      expandVariables: true,
      load: [appConfig],
      envFilePath: [
        '.env',                                           // 1. 基础配置
        `.env.${process.env.NODE_ENV || 'development'}`,  // 2. 环境特定配置
        '.env.local',                                     // 3. 本地覆盖

        // 业务模块特定配置
        'apps/character/.env.server',                    // 4. 区服配置
        'apps/character/.env.redis',                     // 5. Redis业务配置
        'apps/character/.env.database',                  // 6. 数据库配置
        'apps/character/.env.security',                  // 7. 安全业务配置

        // 环境特定的业务配置（如果存在）
        `apps/character/.env.server.${process.env.NODE_ENV || 'development'}`,
        `apps/character/.env.redis.${process.env.NODE_ENV || 'development'}`,
        `apps/character/.env.database.${process.env.NODE_ENV || 'development'}`,
        `apps/character/.env.security.${process.env.NODE_ENV || 'development'}`,

        // 最终覆盖
        'apps/character/.env.local',                     // 8. 服务本地覆盖
      ],
    }),

    // 数据库模块
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        ...createMongoConfig(configService, 'character'),
        ...setupDatabaseEvents('character'),
      }),
      inject: [ConfigService],
    }),

    // Redis模块
    RedisModule.forRootAsync({
      service: 'character',
      serverId: process.env.SERVER_ID || 'server_001', // 从环境变量获取区服ID
    }),

    // 游戏配置模块（核心基础设施）
    GameConfigModule.forRootAsync(),

    // 🚀 统一ServiceMesh架构 - 一行配置替代复杂的双重配置
    // 自动推断：role=server, services=[economy,activity], serverId=server_001
    // 注意：移除hero服务依赖，因为hero功能已合并到character服务中
    ServiceMeshModule.register('character'),

    // 功能模块
    HealthModule,
    CharacterModule,
    FormationModule,
    InventoryModule,  // 统一的物品背包模块，替代ItemModule和InventoryModule
    TacticModule,

    // 英雄管理模块（原hero服务功能）
    HeroModule,       // 🎯 英雄统一模块：包含所有英雄相关功能

    // 信仰系统模块（重大遗漏功能补充）
    BeliefModule,     // 🏛️ 信仰系统：信仰管理、技能、捐献等核心功能

    // 业务编排模块（跨域流程统一入口）
    OrchestratorModule,
  ],
})
export class AppModule {}
