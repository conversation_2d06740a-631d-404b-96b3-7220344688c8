/**
 * 游戏常量配置
 * 基于old项目的配置参数
 */

export const GAME_CONSTANTS = {
  CHARACTER: {
    MAX_LEVEL: 100,
    MAX_ENERGY: 100, // 基于old项目的体力上限
    INITIAL_CASH: 10000,
    INITIAL_GOLD: 0,
    INITIAL_ENERGY: 100,
    MAX_NAME_LENGTH: 20,
    MIN_NAME_LENGTH: 2,
    
    // VIP相关
    MAX_VIP_LEVEL: 15,
    
    // 货币相关
    MAX_CASH: 999999999,
    MAX_GOLD: 999999999,
    
    // 等级相关
    LEVEL_UP_BASE_EXP: 1000,
    LEVEL_UP_EXP_MULTIPLIER: 1.2,
    
    // 体力恢复相关（基于old项目配置）
    ENERGY_RECOVER_INTERVAL: 5 * 60 * 1000, // 5分钟恢复间隔
    ENERGY_RECOVER_AMOUNT: 1, // 每次恢复1点体力
    DAILY_ENERGY_REWARD: 20, // 每日免费体力奖励
    LATE_ENERGY_COST: 50, // 补领体力消耗金币
    
    // 体力购买相关
    MAX_BUY_ENERGY_COUNT: 10, // 每日最大购买次数（VIP可增加）
  },

  HERO: {
    MAX_LEVEL: 100,
    MAX_STAR: 6,
    MAX_FATIGUE: 100,
    FATIGUE_RECOVER_RATE: 1, // 每小时恢复1点疲劳
    
    // 球探相关
    SCOUT_MAX_ENERGY: 100,
    SCOUT_ENERGY_RECOVER_INTERVAL: 5 * 60 * 1000, // 5分钟
    SCOUT_ENERGY_RECOVER_AMOUNT: 1,
  },

  FORMATION: {
    MAX_FORMATIONS: 10,
    MIN_PLAYERS: 11,
    MAX_PLAYERS: 18,
  },

  INVENTORY: {
    MAX_ITEMS: 1000,
    DEFAULT_CAPACITY: 100,
  },

  TACTIC: {
    MAX_TACTICS: 20,
    MAX_LEVEL: 10,
  }
} as const;

// 错误码常量
export const ERROR_CODES = {
  // 角色相关
  CHARACTER_NOT_FOUND: 'CHARACTER_NOT_FOUND',
  CHARACTER_NAME_EXISTS: 'CHARACTER_NAME_EXISTS',
  INVALID_CHARACTER_NAME: 'INVALID_CHARACTER_NAME',
  
  // 体力相关
  ENERGY_INSUFFICIENT: 'ENERGY_INSUFFICIENT',
  ENERGY_REWARD_ALREADY_CLAIMED: 'ENERGY_REWARD_ALREADY_CLAIMED',
  ENERGY_REWARD_TIME_INVALID: 'ENERGY_REWARD_TIME_INVALID',
  
  // 货币相关
  INSUFFICIENT_CASH: 'INSUFFICIENT_CASH',
  INSUFFICIENT_GOLD: 'INSUFFICIENT_GOLD',
  
  // 等级相关
  MAX_LEVEL_REACHED: 'MAX_LEVEL_REACHED',
  INSUFFICIENT_EXP: 'INSUFFICIENT_EXP',
} as const;

// 时间常量
export const TIME_CONSTANTS = {
  MINUTE: 60 * 1000,
  HOUR: 60 * 60 * 1000,
  DAY: 24 * 60 * 60 * 1000,
  WEEK: 7 * 24 * 60 * 60 * 1000,
  
  // 每日体力奖励时间
  FIRST_ENERGY_START: 12, // 12:00
  FIRST_ENERGY_END: 14,   // 14:00
  SECOND_ENERGY_START: 18, // 18:00
  SECOND_ENERGY_END: 20,   // 20:00
} as const;
