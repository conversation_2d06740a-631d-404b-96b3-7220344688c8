/**
 * 角色相关的数据传输对象
 */

import { IsString, IsNumber, IsOptional, IsBoolean, IsArray, Min, <PERSON>, Length } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

// 创建角色DTO
export class CreateCharacterDto {
  @ApiPropertyOptional({ description: '角色ID（由Auth服务提供）' })
  @IsString()
  characterId: string;

  @ApiProperty({ description: '用户ID' })
  @IsString()
  userId: string;

  @ApiProperty({ description: '区服ID' })
  @IsString()
  serverId: string;

  @ApiProperty({ description: '角色名称', minLength: 2, maxLength: 20 })
  @IsString()
  @Length(2, 20)
  name: string;

  @ApiPropertyOptional({ description: '头像' })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiPropertyOptional({ description: '头像ID' })
  @IsOptional()
  @IsNumber()
  faceIcon?: number;
}

// 更新角色DTO
export class UpdateCharacterDto {
  @ApiPropertyOptional({ description: '角色名称', minLength: 2, maxLength: 20 })
  @IsOptional()
  @IsString()
  @Length(2, 20)
  name?: string;

  @ApiPropertyOptional({ description: '头像' })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiPropertyOptional({ description: '头像ID' })
  @IsOptional()
  @IsNumber()
  faceIcon?: number;

  @ApiPropertyOptional({ description: '头像URL' })
  @IsOptional()
  @IsString()
  faceUrl?: string;

  @ApiPropertyOptional({ description: '俱乐部头像' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  clubFaceIcon?: number[];

  @ApiPropertyOptional({ description: '国家队头像' })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  countryFaceIcon?: number[];

  @ApiPropertyOptional({ description: '战术显示设置' })
  @IsOptional()
  @IsNumber()
  isShowTactics?: number;
}

// 登录DTO
export class LoginCharacterDto {
  @ApiProperty({ description: '用户ID' })
  @IsString()
  userId: string;

  @ApiProperty({ description: '角色ID' })
  @IsString()
  characterId: string;

  @ApiProperty({ description: '区服ID' })
  @IsString()
  serverId: string;

  @ApiPropertyOptional({ description: '客户端IP' })
  @IsOptional()
  @IsString()
  ip?: string;

  @ApiPropertyOptional({ description: '前端ID' })
  @IsOptional()
  @IsString()
  frontendId?: string;

  @ApiPropertyOptional({ description: '会话ID' })
  @IsOptional()
  @IsString()
  sessionId?: string;
}

// 货币操作DTO
export class CurrencyOperationDto {
  @ApiProperty({ description: '货币类型', enum: ['cash', 'gold', 'energy', 'worldCoin', 'chip', 'integral'] })
  @IsString()
  currencyType: 'cash' | 'gold' | 'energy' | 'worldCoin' | 'chip' | 'integral';

  @ApiProperty({ description: '数量', minimum: 1 })
  @IsNumber()
  @Min(1)
  amount: number;

  @ApiPropertyOptional({ description: '操作原因' })
  @IsOptional()
  @IsString()
  reason?: string;
}

// 购买体力DTO
export class BuyEnergyDto {
  @ApiPropertyOptional({ description: '购买次数', minimum: 1, maximum: 10 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  count?: number;
}

// 升级DTO
export class LevelUpDto {
  @ApiProperty({ description: '角色ID' })
  @IsString()
  characterId: string;

  @ApiPropertyOptional({ description: '目标等级' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  targetLevel?: number;
}

// 角色信息响应DTO
export class CharacterInfoDto {
  @ApiProperty({ description: '角色ID' })
  characterId: string;

  @ApiProperty({ description: '用户ID' })
  userId: string;

  @ApiProperty({ description: '区服ID' })
  serverId: string;

  @ApiProperty({ description: '角色名称' })
  name: string;

  @ApiProperty({ description: '头像' })
  avatar: string;

  @ApiProperty({ description: '头像ID' })
  faceIcon: number;

  @ApiProperty({ description: '头像URL' })
  faceUrl: string;

  @ApiProperty({ description: '角色等级' })
  level: number;

  @ApiProperty({ description: '欧元数量' })
  cash: number;

  @ApiProperty({ description: '金币数量' })
  gold: number;

  @ApiProperty({ description: '体力值' })
  energy: number;

  @ApiProperty({ description: '声望' })
  fame: number;

  @ApiProperty({ description: '总声望' })
  allFame: number;

  @ApiProperty({ description: '奖杯数' })
  trophy: number;

  @ApiProperty({ description: '世界币数量' })
  worldCoin: number;

  @ApiProperty({ description: '筹码数量' })
  chip: number;

  @ApiProperty({ description: '积分数量' })
  integral: number;

  @ApiProperty({ description: 'VIP等级' })
  vip: number;

  @ApiProperty({ description: 'VIP经验' })
  vipExp: number;

  @ApiProperty({ description: '球场等级' })
  fieldLevel: number;

  @ApiProperty({ description: '联赛等级' })
  league: number;

  @ApiProperty({ description: '是否在线' })
  isOnline: boolean;

  @ApiProperty({ description: '是否新手' })
  isNewer: boolean;

  @ApiProperty({ description: '创角步骤' })
  createRoleStep: number;

  @ApiProperty({ description: '活跃天数' })
  activeDay: number;

  @ApiProperty({ description: '创建时间' })
  createTime: number;

  @ApiProperty({ description: '最后登录时间' })
  lastLoginTime: number;

  @ApiProperty({ description: '信仰ID' })
  beliefId: number;

  @ApiProperty({ description: '荣誉值' })
  honor: number;

  @ApiProperty({ description: '信仰技能数据', required: false })
  beliefSkill?: any;

  @ApiProperty({ description: '足球场数据', required: false })
  footballGround?: any;
}

// 登录结果DTO
export class LoginResultDto {
  @ApiProperty({ description: '角色信息' })
  character: CharacterInfoDto;

  @ApiProperty({ description: '是否新角色' })
  isNewCharacter: boolean;

  @ApiProperty({ description: '登录令牌' })
  token?: string;

  @ApiPropertyOptional({ description: '消息' })
  message?: string;

  @ApiPropertyOptional({ description: '需要初始化的数据' })
  initData?: {
    needCreateTeam: boolean;
    needGuide: boolean;
    hasInitHeroes: boolean;
  };
}

// 等级提升结果DTO
export class LevelUpResultDto {
  @ApiProperty({ description: '旧等级' })
  oldLevel: number;

  @ApiProperty({ description: '新等级' })
  newLevel: number;

  @ApiProperty({ description: '获得的奖励' })
  rewards: Array<{
    type: string;
    amount: number;
  }>;
}

// 购买体力结果DTO
export class BuyEnergyResultDto {
  @ApiProperty({ description: '消耗的金币' })
  goldCost: number;

  @ApiProperty({ description: '获得的体力' })
  energyGained: number;

  @ApiProperty({ description: '当前体力' })
  currentEnergy: number;

  @ApiProperty({ description: '今日购买次数' })
  todayBuyCount: number;

  @ApiProperty({ description: '下次购买费用' })
  nextBuyCost: number;
}

// 角色列表查询DTO
export class GetCharacterListDto {
  @ApiPropertyOptional({ description: '用户ID' })
  @IsOptional()
  @IsString()
  userId?: string;

  @ApiPropertyOptional({ description: '区服ID' })
  @IsOptional()
  @IsString()
  serverId?: string;

  @ApiPropertyOptional({ description: '页码', minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', minimum: 1, maximum: 100 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;
}
