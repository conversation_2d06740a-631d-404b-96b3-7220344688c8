import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery, ClientSession } from 'mongoose';
import { SkillType, SkillPosition } from '../types';
import { Skill, SkillDocument, SkillActiveStatus } from '../schemas/skill.schema';
import { GetSkillListDto, GetSkillConfigListDto } from '../../modules/hero/dto/skill.dto';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { XResult, PaginationResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 技能数据访问层
 * 继承BaseRepository，提供统一的数据访问接口和Result模式错误处理
 *
 * 🎯 核心功能：
 * - 技能CRUD操作
 * - 技能查询和搜索
 * - 技能装备管理
 * - 技能统计分析
 * - 批量操作支持
 *
 * 🚀 性能优化：
 * - 使用BaseRepository的Lean查询优化
 * - 自动性能监控和慢查询检测
 * - 智能缓存策略
 */
@Injectable()
export class SkillRepository extends BaseRepository<SkillDocument> {
  constructor(
    @InjectModel(Skill.name) skillModel: Model<SkillDocument>
  ) {
    super(skillModel, 'SkillRepository');
  }



  // ========== 业务特定方法 ==========

  /**
   * 创建英雄技能实例
   * 使用BaseRepository的createOne方法，自动应用Result模式和性能监控
   */
  async createSkill(skillData: Partial<Skill>): Promise<XResult<SkillDocument>> {
    const skillWithTime = {
      ...skillData,
      obtainTime: Date.now(),
    };
    return this.createOne(skillWithTime);
  }

  /**
   * 根据ID查找英雄技能实例
   * 使用BaseRepository的findOne方法优化性能
   */
  async findSkillById(skillId: string): Promise<XResult<SkillDocument | null>> {
    return this.findOne({ skillId });
  }

  /**
   * 根据ID查找英雄技能实例（Lean查询优化版本）
   */
  async findSkillByIdLean(skillId: string): Promise<XResult<any | null>> {
    return this.findOneLean({ skillId });
  }

  /**
   * 根据英雄ID查找技能列表
   * 使用BaseRepository的findMany方法优化性能
   */
  async findSkillsByHeroId(heroId:string,activeStatus?:string,equippedOnly?:boolean): Promise<XResult<SkillDocument[]>> {
    const filter: FilterQuery<SkillDocument> = { heroId: heroId };

    if (activeStatus !== undefined) {
      filter.activeStatus = activeStatus;
    }
    if (equippedOnly) {
      filter.slotPosition = { $gt: 0 };
    }

    return this.findMany(filter, {
      sort: { slotPosition: 1, level: -1, obtainTime: 1 }
    });
  }

  /**
   * 根据英雄ID查找技能列表（Lean查询优化版本）
   */
  async findSkillsByHeroIdLean(query: GetSkillListDto): Promise<XResult<any[]>> {
    const filter: FilterQuery<SkillDocument> = { heroId: query.heroId };

    if (query.activeStatus !== undefined) {
      filter.activeStatus = query.activeStatus;
    }
    if (query.equippedOnly) {
      filter.slotPosition = { $gt: 0 };
    }

    return this.findManyLean(filter, {
      sort: { slotPosition: 1, level: -1, obtainTime: 1 },
      select: ['skillId', 'configId', 'level', 'slotPosition', 'activeStatus', 'heroId']
    });
  }

  /**
   * 更新英雄技能实例
   * 使用BaseRepository的updateOne方法优化性能
   */
  async updateSkill(
    skillId: string,
    updateData: UpdateQuery<SkillDocument>,
    session?: ClientSession
  ): Promise<XResult<SkillDocument | null>> {
    return this.updateOne({ skillId }, updateData, session);
  }

  /**
   * 删除英雄技能实例
   * 使用BaseRepository的deleteOne方法优化性能
   */
  async deleteSkill(skillId: string): Promise<XResult<boolean>> {
    return this.deleteOne({ skillId });
  }

  /**
   * 检查英雄是否已拥有指定技能
   * 使用BaseRepository的count方法优化性能
   */
  async hasSkill(heroId: string, configId: number): Promise<XResult<boolean>> {
    const countResult = await this.count({ heroId, configId });
    if (XResultUtils.isFailure(countResult)) {
      return XResultUtils.error('检查技能失败', 'SKILL_CHECK_FAILED');
    }
    return XResultUtils.ok(countResult.data > 0);
  }

  /**
   * 获取英雄已激活的技能
   * 使用BaseRepository的findMany方法优化性能
   */
  async findActiveSkills(heroId: string): Promise<XResult<SkillDocument[]>> {
    return this.findMany({
      heroId,
      activeStatus: SkillActiveStatus.ACTIVE,
      slotPosition: { $gt: 0 }
    }, {
      sort: { slotPosition: 1 }
    });
  }

  /**
   * 获取英雄已激活的技能（Lean查询优化版本）
   */
  async findActiveSkillsLean(heroId: string): Promise<XResult<any[]>> {
    return this.findManyLean({
      heroId,
      activeStatus: SkillActiveStatus.ACTIVE,
      slotPosition: { $gt: 0 }
    }, {
      sort: { slotPosition: 1 },
      select: ['skillId', 'configId', 'level', 'slotPosition', 'activeStatus']
    });
  }

  /**
   * 清空指定槽位的技能
   * 使用BaseRepository的updateMany方法优化性能
   */
  async clearSkillSlot(heroId: string, slotPosition: number): Promise<XResult<any>> {
    return this.updateMany(
      { heroId, slotPosition },
      {
        $set: {
          slotPosition: 0,
          activeStatus: SkillActiveStatus.INACTIVE
        }
      }
    );
  }

  /**
   * 批量更新英雄技能
   * 使用BaseRepository的批量操作方法优化性能
   */
  async bulkUpdateSkills(updates: Array<{
    filter: FilterQuery<SkillDocument>;
    update: UpdateQuery<SkillDocument>;
  }>): Promise<XResult<any>> {
    // 如果只有一个更新操作，使用updateMany优化
    if (updates.length === 1) {
      const { filter, update } = updates[0];
      return this.updateMany(filter, update);
    }

    // 多个更新操作，使用事务确保数据一致性
    return this.withTransaction(async (session) => {
      const bulkOps = updates.map(({ filter, update }) => ({
        updateMany: {
          filter,
          update: update as any,
          session
        }
      }));

      const result = await this.mongooseModel.bulkWrite(bulkOps, { session });
      return XResultUtils.ok({ success: true, result });
    });
  }

  /**
   * 获取英雄技能统计
   * 使用BaseRepository的aggregate方法优化性能和错误处理
   */
  async getSkillStats(heroId: string): Promise<XResult<any>> {
    const pipeline = [
      {
        $match: { heroId }
      },
      {
        $group: {
          _id: null,
          totalSkills: { $sum: 1 },
          activeSkills: {
            $sum: {
              $cond: [{ $eq: ['$activeStatus', SkillActiveStatus.ACTIVE] }, 1, 0]
            }
          },
          averageLevel: { $avg: '$level' },
          totalUpgradeCost: { $sum: '$totalUpgradeCost' },
          totalUsage: { $sum: '$usageCount' }
        }
      }
    ];

    const result = await this.aggregate(pipeline);
    if (XResultUtils.isSuccess(result) && result.data.length > 0) {
      return XResultUtils.ok(result.data[0]);
    }

    // 返回默认统计信息
    return XResultUtils.ok({
      totalSkills: 0,
      activeSkills: 0,
      averageLevel: 0,
      totalUpgradeCost: 0,
      totalUsage: 0
    });
  }

  /**
   * 根据配置ID获取技能使用统计
   * 使用BaseRepository的aggregate方法优化性能和错误处理
   */
  async getConfigUsageStats(configId: number): Promise<XResult<any>> {
    const pipeline = [
      {
        $match: { configId }
      },
      {
        $group: {
          _id: null,
          totalUsers: { $sum: 1 },
          totalUsage: { $sum: '$usageCount' },
          totalDamage: { $sum: '$totalDamage' },
          totalHealing: { $sum: '$totalHealing' },
          averageLevel: { $avg: '$level' },
          maxLevel: { $max: '$level' },
          activeUsers: {
            $sum: {
              $cond: [{ $eq: ['$activeStatus', SkillActiveStatus.ACTIVE] }, 1, 0]
            }
          }
        }
      }
    ];

    const result = await this.aggregate(pipeline);
    if (XResultUtils.isSuccess(result) && result.data.length > 0) {
      return XResultUtils.ok(result.data[0]);
    }

    // 返回默认统计信息
    return XResultUtils.ok({
      totalUsers: 0,
      totalUsage: 0,
      totalDamage: 0,
      totalHealing: 0,
      averageLevel: 0,
      maxLevel: 0,
      activeUsers: 0
    });
  }

  // ========== 重写BaseRepository方法以添加业务特定逻辑 ==========

  /**
   * 重写数据验证方法，添加技能特定的验证规则
   */
  protected validateData(data: Partial<SkillDocument>, operation: 'create' | 'update'): XResult<void> {
    if (operation === 'create') {
      if (!data.heroId) {
        return XResultUtils.error('英雄ID不能为空', 'HERO_ID_REQUIRED');
      }

      if (!data.configId) {
        return XResultUtils.error('技能配置ID不能为空', 'CONFIG_ID_REQUIRED');
      }

      if (!data.level || data.level < 1) {
        return XResultUtils.error('技能等级不能小于1', 'INVALID_SKILL_LEVEL');
      }
    }

    if (data.level !== undefined && data.level > 10) {
      return XResultUtils.error('技能等级不能超过10', 'SKILL_LEVEL_TOO_HIGH');
    }

    if (data.slotPosition !== undefined && (data.slotPosition < 0 || data.slotPosition > 6)) {
      return XResultUtils.error('技能槽位必须在0-6之间', 'INVALID_SLOT_POSITION');
    }

    if (data.usageCount !== undefined && data.usageCount < 0) {
      return XResultUtils.error('技能使用次数不能为负数', 'INVALID_USAGE_COUNT');
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 重写缓存TTL配置，针对技能数据的访问模式优化
   */
  protected getCacheTTL(operation: string): number {
    const customTTLs: Record<string, number> = {
      'findSkillById': 600,              // 技能详情缓存10分钟
      'findSkillByIdLean': 300,          // 技能简介缓存5分钟
      'findSkillsByHeroId': 180,         // 英雄技能列表缓存3分钟
      'findActiveSkills': 120,           // 激活技能缓存2分钟
      'hasSkill': 300,                   // 技能检查缓存5分钟
      'getSkillStats': 300,              // 技能统计缓存5分钟
      'getConfigUsageStats': 600,        // 配置统计缓存10分钟
    };

    return customTTLs[operation] || super.getCacheTTL(operation);
  }
}
