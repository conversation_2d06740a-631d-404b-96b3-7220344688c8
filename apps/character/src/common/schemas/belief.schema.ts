import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

// 领导者信息子文档
@Schema({ _id: false })
export class BeliefLeader {
  @Prop({ required: true })
  uid: string; // 玩家ID

  @Prop({ required: true })
  gid: string; // 公会ID（预留）

  @Prop({ required: true })
  name: string; // 玩家名称

  @Prop({ required: true })
  faceUrl: string; // 头像URL

  // 职位: 1-董事长, 2-副董事长, 3-总经理, 4-总监
  @Prop({ required: true, min: 1, max: 4 })
  pos: number;

  @Prop({ default: () => Date.now() })
  appointTime: number; // 任命时间
}

// 动态信息子文档
@Schema({ _id: false })
export class BeliefNotify {
  @Prop({ required: true })
  time: number; // 时间戳

  @Prop({ required: true })
  msg: string; // 动态消息

  @Prop({ default: 'system' })
  type: string; // 消息类型：system, player, battle

  @Prop()
  playerId?: string; // 相关玩家ID（可选）
}

// 成员信息子文档
@Schema({ _id: false })
export class BeliefMember {
  @Prop({ required: true })
  playerId: string; // 玩家ID

  @Prop({ required: true })
  playerName: string; // 玩家名称

  @Prop({ required: true })
  faceUrl: string; // 头像URL

  @Prop({ default: 0 })
  contribution: number; // 贡献值

  @Prop({ default: 0 })
  weekContribution: number; // 周贡献

  @Prop({ default: () => Date.now() })
  joinTime: number; // 加入时间

  @Prop({ default: () => Date.now() })
  lastActiveTime: number; // 最后活跃时间

  @Prop({ default: false })
  isActive: boolean; // 是否活跃
}

// 信仰加入申请子文档
@Schema({ _id: false })
export class BeliefApplication {
  @Prop({ required: true })
  playerId: string; // 申请者ID

  @Prop({ required: true, maxlength: 50 })
  playerName: string; // 申请者姓名

  @Prop({ default: '' })
  faceUrl: string; // 申请者头像

  @Prop({ default: () => Date.now() })
  applyTime: number; // 申请时间

  @Prop({ default: 'pending' })
  status: string; // 申请状态：pending, approved, rejected

  @Prop({ default: '', maxlength: 200 })
  message: string; // 申请留言

  @Prop({ default: '' })
  reviewerId: string; // 审批者ID

  @Prop()
  reviewTime?: number; // 审批时间

  @Prop({ default: '', maxlength: 200 })
  reviewReason: string; // 审批理由
}

@Schema({ collection: 'beliefs', timestamps: true })
export class Belief extends Document {
  @Prop({ required: true, unique: true, index: true })
  beliefId: number; // 信仰ID

  @Prop({ required: true, maxlength: 50 })
  name: string; // 信仰名称

  @Prop({ default: '', maxlength: 500 })
  notice: string; // 信仰公告

  @Prop({ default: 0, min: 0 })
  beliefGold: number; // 信仰资金

  @Prop({ type: [BeliefLeader], default: [] })
  leader: BeliefLeader[]; // 领导者列表

  @Prop({ type: [BeliefNotify], default: [] })
  notify: BeliefNotify[]; // 信仰动态

  @Prop({ type: [BeliefMember], default: [] })
  playerList: BeliefMember[]; // 成员列表

  @Prop({ type: [BeliefApplication], default: [] })
  applications: BeliefApplication[]; // 加入申请列表

  @Prop({ default: 1, min: 1, max: 20 })
  level: number; // 信仰等级

  @Prop({ default: 0, min: 0 })
  beliefExp: number; // 信仰经验

  @Prop({ default: 0, min: 0 })
  todayGoldNum: number; // 当日捐献球币数量

  @Prop({ default: 0, min: 0 })
  weekExp: number; // 周贡献

  @Prop({ default: 0, min: 0 })
  beliefRank: number; // 信仰排名

  @Prop({ default: () => Date.now() })
  clearTime: number; // 清空当日球币捐赠的时间戳

  @Prop({ default: () => Date.now() })
  createTime: number; // 创建时间

  @Prop({ default: 'active' })
  status: string; // 状态：active, inactive, disbanded

  @Prop({ default: 50, min: 1, max: 100 })
  maxMembers: number; // 最大成员数

  @Prop({ default: '' })
  creatorId: string; // 创建者ID

  @Prop({ default: 0 })
  totalDonation: number; // 总捐献金额

  @Prop({ default: 0 })
  battleWins: number; // 信仰之战胜利次数

  @Prop({ default: 0 })
  battleLosses: number; // 信仰之战失败次数
}

export type BeliefDocument = Belief & Document & BeliefMethods;
export const BeliefSchema = SchemaFactory.createForClass(Belief);

// 创建复合索引
BeliefSchema.index({ beliefId: 1 }, { unique: true });
BeliefSchema.index({ name: 1 }, { unique: true });
BeliefSchema.index({ level: -1, beliefExp: -1 }); // 排行榜索引
BeliefSchema.index({ 'playerList.playerId': 1 }); // 成员查询索引
BeliefSchema.index({ status: 1, createTime: -1 }); // 状态和创建时间索引

// 添加虚拟字段
BeliefSchema.virtual('memberCount').get(function() {
  return this.playerList ? this.playerList.length : 0;
});

BeliefSchema.virtual('isMaxLevel').get(function() {
  return this.level >= 20;
});

// 定义实例方法接口
export interface BeliefMethods {
  addMember(memberData: Partial<BeliefMember>): void;
  removeMember(playerId: string): void;
  addNotify(message: string, type?: string, playerId?: string): void;
  addExperience(exp: number): { levelUp: boolean; newLevel: number; originalLevel?: number };
  addApplication(applicationData: Partial<BeliefApplication>): void;
  removeApplication(playerId: string): void;
  approveApplication(playerId: string, reviewerId: string, reviewReason?: string): BeliefApplication | null;
  rejectApplication(playerId: string, reviewerId: string, reviewReason?: string): BeliefApplication | null;
  getPendingApplications(): BeliefApplication[];
}

// 添加实例方法
BeliefSchema.methods.addMember = function(memberData: Partial<BeliefMember>) {
  if (this.playerList.length >= this.maxMembers) {
    throw new Error('信仰成员已满');
  }

  const existingMember = this.playerList.find((m: BeliefMember) => m.playerId === memberData.playerId);
  if (existingMember) {
    throw new Error('玩家已是信仰成员');
  }

  this.playerList.push({
    ...memberData,
    joinTime: Date.now(),
    lastActiveTime: Date.now(),
    isActive: true,
  } as BeliefMember);
};

BeliefSchema.methods.removeMember = function(playerId: string) {
  const memberIndex = this.playerList.findIndex((m: BeliefMember) => m.playerId === playerId);
  if (memberIndex === -1) {
    throw new Error('玩家不是信仰成员');
  }

  this.playerList.splice(memberIndex, 1);
};

BeliefSchema.methods.addNotify = function(message: string, type: string = 'system', playerId?: string) {
  this.notify.unshift({
    time: Date.now(),
    msg: message,
    type,
    playerId,
  } as BeliefNotify);

  // 保持最多100条动态
  if (this.notify.length > 100) {
    this.notify = this.notify.slice(0, 100);
  }
};

BeliefSchema.methods.addExperience = function(exp: number) {
  if (this.level >= 20) {
    return { levelUp: false, newLevel: this.level };
  }

  this.beliefExp += exp;
  let levelUp = false;
  let originalLevel = this.level;

  // 这里需要根据BeliefExp配置表计算升级
  // 暂时使用简单的升级逻辑
  const expRequired = this.level * 1000;
  if (this.beliefExp >= expRequired && this.level < 20) {
    this.level += 1;
    this.beliefExp -= expRequired;
    levelUp = true;
  }

  return { levelUp, newLevel: this.level, originalLevel };
};

// 申请管理方法
BeliefSchema.methods.addApplication = function(applicationData: Partial<BeliefApplication>) {
  // 检查是否已有待审批的申请
  const existingApplication = this.applications.find((app: BeliefApplication) =>
    app.playerId === applicationData.playerId && app.status === 'pending'
  );
  if (existingApplication) {
    throw new Error('该玩家已有待审批的申请');
  }

  // 检查是否已是成员
  const existingMember = this.playerList.find((m: BeliefMember) => m.playerId === applicationData.playerId);
  if (existingMember) {
    throw new Error('该玩家已是信仰成员');
  }

  this.applications.push({
    ...applicationData,
    applyTime: Date.now(),
    status: 'pending',
  } as BeliefApplication);
};

BeliefSchema.methods.removeApplication = function(playerId: string) {
  const applicationIndex = this.applications.findIndex((app: BeliefApplication) => app.playerId === playerId);
  if (applicationIndex === -1) {
    throw new Error('未找到该玩家的申请');
  }

  this.applications.splice(applicationIndex, 1);
};

BeliefSchema.methods.approveApplication = function(playerId: string, reviewerId: string, reviewReason?: string): BeliefApplication | null {
  const application = this.applications.find((app: BeliefApplication) =>
    app.playerId === playerId && app.status === 'pending'
  );
  if (!application) {
    return null;
  }

  application.status = 'approved';
  application.reviewerId = reviewerId;
  application.reviewTime = Date.now();
  application.reviewReason = reviewReason || '';

  return application;
};

BeliefSchema.methods.rejectApplication = function(playerId: string, reviewerId: string, reviewReason?: string): BeliefApplication | null {
  const application = this.applications.find((app: BeliefApplication) =>
    app.playerId === playerId && app.status === 'pending'
  );
  if (!application) {
    return null;
  }

  application.status = 'rejected';
  application.reviewerId = reviewerId;
  application.reviewTime = Date.now();
  application.reviewReason = reviewReason || '';

  return application;
};

BeliefSchema.methods.getPendingApplications = function(): BeliefApplication[] {
  return this.applications.filter((app: BeliefApplication) => app.status === 'pending');
};

// 设置toJSON选项
BeliefSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    delete ret._id;
    delete ret.__v;
    return ret;
  },
});
