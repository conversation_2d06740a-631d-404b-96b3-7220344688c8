import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { BeliefService } from './belief.service';
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';
import { Cacheable, CacheEvict } from '@libs/redis';
import {
  CreateBeliefPayloadDto,
  ApplyJoinBeliefPayloadDto,
  JoinBeliefPayloadDto,
  LeaveBeliefPayloadDto,
  DonateBeliefPayloadDto,
  UpdateBeliefNoticePayloadDto,
  GetBeliefInfoPayloadDto,
  GetBeliefListPayloadDto,
  SetBeliefLeaderPayloadDto,
  GetBeliefMembersPayloadDto,
  GetBeliefRankPayloadDto,
  GetMyBeliefPayloadDto,
  SearchBeliefPayloadDto,
  GetBeliefStatsPayloadDto,
  KickBeliefMemberPayloadDto,
  DisbandBeliefPayloadDto,
  TransferChairmanPayloadDto,
  GetBeliefNotificationsPayloadDto,
  ReviewApplicationPayloadDto,
  GetApplicationsPayloadDto
} from './dto/belief-payload.dto';

/**
 * 信仰系统控制器
 * 严格基于old项目的信仰系统接口设计
 * 
 * 核心接口：
 * - belief.create: 创建信仰
 * - belief.join: 加入信仰
 * - belief.leave: 退出信仰
 * - belief.donate: 信仰捐献
 * - belief.getInfo: 获取信仰信息
 * - belief.getList: 获取信仰列表
 * - belief.updateNotice: 更新信仰公告
 * - belief.setLeader: 设置领导者
 * - belief.getMembers: 获取成员列表
 * - belief.getRank: 获取信仰排行
 */
@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class BeliefController extends BaseController {
  constructor(private readonly beliefService: BeliefService) {
    super('BeliefController');
  }

  /**
   * 创建信仰
   * 基于old项目: 玩家创建新的信仰组织
   */
  @MessagePattern('belief.create')
  @CacheEvict({
    key: 'belief:list:#{payload.serverId}:*',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async createBelief(payload: CreateBeliefPayloadDto) {
    const result = await this.beliefService.createBelief(payload);
    return this.fromResult(result, '创建信仰成功');
  }

  /**
   * 申请加入信仰
   * 基于old项目: 玩家申请加入指定信仰（需要审批）
   */
  @MessagePattern('belief.apply')
  async applyJoinBelief(payload: ApplyJoinBeliefPayloadDto) {
    const result = await this.beliefService.applyJoinBelief(payload);
    return this.fromResult(result, '申请提交成功');
  }

  /**
   * 审批申请
   */
  @MessagePattern('belief.reviewApplication')
  async reviewApplication(payload: ReviewApplicationPayloadDto) {
    const result = await this.beliefService.reviewApplication(payload);
    return this.fromResult(result, '审批完成');
  }

  /**
   * 获取申请列表
   */
  @MessagePattern('belief.getApplications')
  async getApplications(payload: GetApplicationsPayloadDto) {
    const result = await this.beliefService.getApplications(payload);
    return this.fromResult(result, '获取申请列表成功');
  }

  /**
   * 退出信仰
   * 基于old项目: 玩家主动退出当前信仰
   */
  @MessagePattern('belief.leave')
  async leaveBelief(payload: LeaveBeliefPayloadDto) {
    const result = await this.beliefService.leaveBelief(payload);
    return this.fromResult(result, '退出信仰成功');
  }

  /**
   * 信仰捐献
   * 基于old项目: donateBeliefGold接口
   */
  @MessagePattern('belief.donate')
  async donateBelief(payload: DonateBeliefPayloadDto) {
    const result = await this.beliefService.donateBelief(payload);
    return this.fromResult(result, '信仰捐献成功');
  }

  /**
   * 获取信仰信息
   * 基于old项目: 获取指定信仰的详细信息
   */
  @MessagePattern('belief.getInfo')
  @Cacheable({
    key: 'belief:info:#{payload.serverId}:#{payload.beliefId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 30 * 60 // 30分钟缓存
  })
  async getBeliefInfo(payload: GetBeliefInfoPayloadDto) {
    const result = await this.beliefService.getBeliefInfo(payload);
    return this.fromResult(result, '获取信仰信息成功');
  }

  /**
   * 获取信仰列表
   * 基于old项目: 获取信仰列表（支持分页和搜索）
   */
  @MessagePattern('belief.getList')
  @Cacheable({
    key: 'belief:list:#{payload.serverId}:#{payload.page || 1}:#{payload.limit || 20}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 15 * 60 // 15分钟缓存
  })
  async getBeliefList(payload: GetBeliefListPayloadDto) {
    const result = await this.beliefService.getBeliefList(payload);
    return this.fromResult(result, '获取信仰列表成功');
  }

  /**
   * 更新信仰公告
   * 基于old项目: 董事长更新信仰公告
   */
  @MessagePattern('belief.updateNotice')
  async updateBeliefNotice(payload: UpdateBeliefNoticePayloadDto) {
    const result = await this.beliefService.updateBeliefNotice(payload);
    return this.fromResult(result, '更新信仰公告成功');
  }

  /**
   * 设置信仰领导者
   * 基于old项目: 董事长任命或撤销领导职位
   */
  @MessagePattern('belief.setLeader')
  async setBeliefLeader(payload: SetBeliefLeaderPayloadDto) {
    const result = await this.beliefService.setBeliefLeader(payload);
    return this.fromResult(result, '设置信仰领导者成功');
  }

  /**
   * 获取信仰成员列表
   * 基于old项目: 获取信仰的所有成员信息
   */
  @MessagePattern('belief.getMembers')
  async getBeliefMembers(payload: GetBeliefMembersPayloadDto) {
    const result = await this.beliefService.getBeliefMembers(payload);
    return this.fromResult(result, '获取信仰成员成功');
  }

  /**
   * 获取信仰排行榜
   * 基于old项目: 获取信仰等级和活跃度排行
   */
  @MessagePattern('belief.getRank')
  async getBeliefRank(payload: GetBeliefRankPayloadDto) {
    const result = await this.beliefService.getBeliefRank(payload);
    return this.fromResult(result, '获取信仰排行成功');
  }

  /**
   * 获取我的信仰信息
   * 基于old项目: 获取角色当前加入的信仰信息
   */
  @MessagePattern('belief.getMyBelief')
  async getMyBelief(payload: GetMyBeliefPayloadDto) {
    const result = await this.beliefService.getBelief(payload);
    return this.fromResult(result, '获取我的信仰信息成功');
  }

  /**
   * 搜索信仰
   * 基于old项目: 按名称搜索信仰
   */
  @MessagePattern('belief.search')
  async searchBelief(payload: SearchBeliefPayloadDto) {
    const result = await this.beliefService.searchBelief(payload);
    return this.fromResult(result, '搜索信仰成功');
  }

  /**
   * 获取信仰统计信息
   * 基于old项目: 获取信仰的各种统计数据
   */
  @MessagePattern('belief.getStats')
  async getBeliefStats(payload: GetBeliefStatsPayloadDto) {
    const result = await this.beliefService.getBeliefStats(payload);
    return this.fromResult(result, '获取信仰统计成功');
  }

  /**
   * 踢出信仰成员
   * 基于old项目: 董事长踢出信仰成员
   */
  @MessagePattern('belief.kickMember')
  async kickBeliefMember(payload: KickBeliefMemberPayloadDto) {
    const result = await this.beliefService.kickBeliefMember(payload);
    return this.fromResult(result, '踢出信仰成员成功');
  }

  /**
   * 解散信仰
   * 基于old项目: 董事长解散信仰
   */
  @MessagePattern('belief.disband')
  async disbandBelief(payload: DisbandBeliefPayloadDto) {
    const result = await this.beliefService.disbandBelief(payload);
    return this.fromResult(result, '解散信仰成功');
  }

  /**
   * 转让董事长职位
   * 基于old项目: 董事长转让职位给其他成员
   */
  @MessagePattern('belief.transferChairman')
  async transferChairman(payload: TransferChairmanPayloadDto) {
    const result = await this.beliefService.transferChairman(payload);
    return this.fromResult(result, '转让董事长成功');
  }

  /**
   * 获取信仰动态
   * 基于old项目: 获取信仰的最新动态消息
   */
  @MessagePattern('belief.getNotifications')
  async getBeliefNotifications(payload: GetBeliefNotificationsPayloadDto) {
    const result = await this.beliefService.getBeliefNotifications(payload);
    return this.fromResult(result, '获取信仰动态成功');
  }
}
