import { IsString, Is<PERSON><PERSON>ber, IsOptional, IsBoolean, IsA<PERSON>y, <PERSON>, <PERSON>, Max<PERSON>ength, IsIn } from 'class-validator';
import { Type } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

/**
 * 创建信仰载荷DTO
 * 基于old项目: 创建新的信仰组织
 */
export class CreateBeliefPayloadDto extends BasePayloadDto {
  @IsString()
  @MaxLength(20, { message: '信仰名称不能超过20个字符' })
  name: string; // 信仰名称

  @IsOptional()
  @IsString()
  @MaxLength(500, { message: '信仰公告不能超过500个字符' })
  notice?: string; // 信仰公告
}

/**
 * 申请加入信仰载荷DTO
 * 基于old项目: 玩家申请加入指定信仰
 */
export class ApplyJoinBeliefPayloadDto extends BasePayloadDto {
  @IsNumber()
  @Min(1, { message: '信仰ID必须大于0' })
  beliefId: number; // 信仰ID

  @IsOptional()
  @IsString()
  @MaxLength(200, { message: '申请留言不能超过200个字符' })
  message?: string; // 申请留言
}

/**
 * 加入信仰载荷DTO（保留兼容性）
 * 基于old项目: 玩家申请加入指定信仰
 */
export class JoinBeliefPayloadDto extends BasePayloadDto {
  @IsNumber()
  @Min(1, { message: '信仰ID必须大于0' })
  beliefId: number; // 信仰ID
}

/**
 * 退出信仰载荷DTO
 * 基于old项目: 玩家主动退出当前信仰
 */
export class LeaveBeliefPayloadDto extends BasePayloadDto {
  @IsOptional()
  @IsString()
  @MaxLength(100, { message: '退出原因不能超过100个字符' })
  reason?: string; // 退出原因
}

/**
 * 信仰捐献载荷DTO
 * 基于old项目: donateBeliefGold接口
 */
export class DonateBeliefPayloadDto extends BasePayloadDto {
  @IsNumber()
  @Min(1, { message: '捐献类型必须为1或2' })
  @Max(2, { message: '捐献类型必须为1或2' })
  type: number; // 捐献类型：1-现金捐献，2-球币捐献

  @IsNumber()
  @Min(1, { message: '捐献数量必须大于0' })
  amount: number; // 捐献数量
}

/**
 * 更新信仰公告载荷DTO
 * 基于old项目: 董事长更新信仰公告
 */
export class UpdateBeliefNoticePayloadDto extends BasePayloadDto {
  @IsString()
  @MaxLength(500, { message: '信仰公告不能超过500个字符' })
  notice: string; // 新的信仰公告
}

/**
 * 获取信仰信息载荷DTO
 * 基于old项目: 获取指定信仰的详细信息
 */
export class GetBeliefInfoPayloadDto extends BasePayloadDto {
  @IsNumber()
  @Min(1, { message: '信仰ID必须大于0' })
  beliefId: number; // 信仰ID
}

/**
 * 获取信仰列表载荷DTO
 * 基于old项目: 获取信仰列表（支持分页和搜索）
 */
export class GetBeliefListPayloadDto extends BasePayloadDto {
  @IsOptional()
  @IsNumber()
  @Min(1, { message: '页码必须大于0' })
  page?: number; // 页码，默认1

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '每页数量必须大于0' })
  @Max(100, { message: '每页数量不能超过100' })
  limit?: number; // 每页数量，默认20

  @IsOptional()
  @IsString()
  @MaxLength(50, { message: '搜索关键词不能超过50个字符' })
  keyword?: string; // 搜索关键词

  @IsOptional()
  @IsString()
  sortBy?: string; // 排序字段：level, memberCount, createTime

  @IsOptional()
  @IsString()
  sortOrder?: string; // 排序方向：asc, desc
}

/**
 * 设置信仰领导者载荷DTO
 * 基于old项目: 董事长任命或撤销领导职位
 */
export class SetBeliefLeaderPayloadDto extends BasePayloadDto {
  @IsString()
  targetPlayerId: string; // 目标玩家ID

  @IsNumber()
  @Min(1, { message: '职位必须在1-4之间' })
  @Max(4, { message: '职位必须在1-4之间' })
  position: number; // 职位：1-董事长，2-副董事长，3-总经理，4-总监

  @IsBoolean()
  isAppoint: boolean; // true-任命，false-撤销
}

/**
 * 获取信仰成员列表载荷DTO
 * 基于old项目: 获取信仰的所有成员信息
 */
export class GetBeliefMembersPayloadDto extends BasePayloadDto {
  @IsOptional()
  @IsNumber()
  @Min(1, { message: '信仰ID必须大于0' })
  beliefId?: number; // 信仰ID，不传则获取自己信仰的成员

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '页码必须大于0' })
  page?: number; // 页码，默认1

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '每页数量必须大于0' })
  @Max(100, { message: '每页数量不能超过100' })
  limit?: number; // 每页数量，默认50

  @IsOptional()
  @IsString()
  sortBy?: string; // 排序字段：contribution, joinTime, lastActiveTime

  @IsOptional()
  @IsString()
  sortOrder?: string; // 排序方向：asc, desc
}

/**
 * 获取信仰排行载荷DTO
 * 基于old项目: 获取信仰等级和活跃度排行
 */
export class GetBeliefRankPayloadDto extends BasePayloadDto {
  @IsOptional()
  @IsString()
  rankType?: string; // 排行类型：level, activity, donation

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '页码必须大于0' })
  page?: number; // 页码，默认1

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '每页数量必须大于0' })
  @Max(100, { message: '每页数量不能超过100' })
  limit?: number; // 每页数量，默认50
}

/**
 * 升级信仰技能载荷DTO
 * 基于old项目: upgradeBeliefSkill接口
 */
export class UpgradeBeliefSkillPayloadDto extends BasePayloadDto {
  @IsNumber()
  @Min(1, { message: '技能ID必须大于0' })
  skillId: number; // 技能ID
}

/**
 * 获取信仰技能列表载荷DTO
 * 基于old项目: 获取角色的所有信仰技能信息
 */
export class GetBeliefSkillListPayloadDto extends BasePayloadDto {
  // 只需要基础的characterId和serverId，无需额外参数
}



/**
 * 获取我的信仰信息载荷DTO
 * 基于old项目: 获取角色当前加入的信仰信息
 */
export class GetMyBeliefPayloadDto extends BasePayloadDto {
  // 只需要基础的characterId和serverId，无需额外参数
}

/**
 * 搜索信仰载荷DTO
 * 基于old项目: 按名称搜索信仰
 */
export class SearchBeliefPayloadDto extends BasePayloadDto {
  @IsString()
  @MaxLength(50, { message: '搜索关键词不能超过50个字符' })
  keyword: string; // 搜索关键词

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '每页数量必须大于0' })
  @Max(50, { message: '每页数量不能超过50' })
  limit?: number; // 返回数量限制，默认20
}

/**
 * 踢出信仰成员载荷DTO
 * 基于old项目: 董事长踢出信仰成员
 */
export class KickBeliefMemberPayloadDto extends BasePayloadDto {
  @IsString()
  targetPlayerId: string; // 目标玩家ID

  @IsOptional()
  @IsString()
  @MaxLength(100, { message: '踢出原因不能超过100个字符' })
  reason?: string; // 踢出原因
}

/**
 * 解散信仰载荷DTO
 * 基于old项目: 董事长解散信仰
 */
export class DisbandBeliefPayloadDto extends BasePayloadDto {
  @IsOptional()
  @IsString()
  @MaxLength(100, { message: '解散原因不能超过100个字符' })
  reason?: string; // 解散原因
}

/**
 * 转让董事长载荷DTO
 * 基于old项目: 董事长转让职位给其他成员
 */
export class TransferChairmanPayloadDto extends BasePayloadDto {
  @IsString()
  targetPlayerId: string; // 目标玩家ID
}

/**
 * 获取信仰动态载荷DTO
 * 基于old项目: 获取信仰的最新动态消息
 */
export class GetBeliefNotificationsPayloadDto extends BasePayloadDto {
  @IsOptional()
  @IsNumber()
  @Min(1, { message: '信仰ID必须大于0' })
  beliefId?: number; // 信仰ID，不传则获取自己信仰的动态

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '页码必须大于0' })
  page?: number; // 页码，默认1

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '每页数量必须大于0' })
  @Max(100, { message: '每页数量不能超过100' })
  limit?: number; // 每页数量，默认20

  @IsOptional()
  @IsString()
  messageType?: string; // 消息类型：system, player, battle
}

/**
 * 获取信仰统计DTO
 */
export class GetBeliefStatsPayloadDto extends BasePayloadDto {
  // 仅继承BasePayloadDto，无需额外字段
}

/**
 * 获取信仰技能详情载荷DTO
 */
export class GetBeliefSkillDetailPayloadDto extends BasePayloadDto {
  @IsNumber()
  @Min(1, { message: '技能ID必须大于0' })
  skillId: number;
}

/**
 * 检查信仰技能升级条件载荷DTO
 */
export class CheckBeliefSkillUpgradeConditionPayloadDto extends BasePayloadDto {
  @IsNumber()
  @Min(1, { message: '技能ID必须大于0' })
  skillId: number;
}

/**
 * 重置信仰技能载荷DTO
 */
export class ResetBeliefSkillPayloadDto extends BasePayloadDto {
  @IsNumber()
  @Min(1, { message: '技能ID必须大于0' })
  skillId: number;

  @IsOptional()
  @IsString()
  @IsIn(['single', 'all'], { message: '重置类型只能是single或all' })
  resetType?: 'single' | 'all';
}

/**
 * 获取信仰技能加成载荷DTO
 */
export class GetBeliefSkillBonusPayloadDto extends BasePayloadDto {
  // 只需要基础的characterId和serverId
}

/**
 * 技能升级项DTO
 */
export class SkillUpgradeItemDto {
  @IsNumber()
  @Min(1, { message: '技能ID必须大于0' })
  skillId: number;

  @IsNumber()
  @Min(1, { message: '目标等级必须大于0' })
  @Max(100, { message: '目标等级不能超过100' })
  targetLevel: number;
}

/**
 * 批量升级信仰技能载荷DTO
 */
export class BatchUpgradeBeliefSkillPayloadDto extends BasePayloadDto {
  @IsArray()
  @Type(() => SkillUpgradeItemDto)
  skillUpgrades: SkillUpgradeItemDto[];

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '最大消耗必须大于0' })
  maxCost?: number;
}

/**
 * 获取信仰技能升级历史载荷DTO
 */
export class GetBeliefSkillUpgradeHistoryPayloadDto extends BasePayloadDto {
  @IsOptional()
  @IsNumber()
  @Min(1, { message: '页码必须大于0' })
  page?: number;

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '每页数量必须大于0' })
  @Max(100, { message: '每页数量不能超过100' })
  limit?: number;

  @IsOptional()
  @IsNumber()
  startTime?: number;

  @IsOptional()
  @IsNumber()
  endTime?: number;
}

/**
 * 审批申请载荷DTO
 */
export class ReviewApplicationPayloadDto extends BasePayloadDto {
  @IsString()
  targetPlayerId: string; // 申请者ID

  @IsString()
  @IsIn(['approve', 'reject'], { message: '操作类型只能是approve或reject' })
  action: string; // 操作类型：approve-同意，reject-拒绝

  @IsOptional()
  @IsString()
  @MaxLength(200, { message: '审批理由不能超过200个字符' })
  reason?: string; // 审批理由
}

/**
 * 获取申请列表载荷DTO
 */
export class GetApplicationsPayloadDto extends BasePayloadDto {
  @IsOptional()
  @IsString()
  @IsIn(['pending', 'approved', 'rejected', 'all'], { message: '状态只能是pending、approved、rejected或all' })
  status?: string; // 申请状态，默认pending

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '页码必须大于0' })
  page?: number; // 页码，默认1

  @IsOptional()
  @IsNumber()
  @Min(1, { message: '每页数量必须大于0' })
  @Max(50, { message: '每页数量不能超过50' })
  limit?: number; // 每页数量，默认20
}
