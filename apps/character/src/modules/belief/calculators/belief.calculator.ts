/**
 * 信仰系统计算器
 * 从BeliefService和BeliefSkillService中剥离的计算函数
 * 基于old项目的信仰系统计算逻辑，无外部依赖
 */

/**
 * 信仰技能计算器
 * 基于old项目的信仰技能计算逻辑
 */
export class BeliefSkillCalculator {
  /**
   * 获取下一级消耗
   * 基于技能等级计算升级费用
   */
  static getNextLevelCost(level: number): number {
    // 基于old项目的消耗递增公式
    return level * 100;
  }

  /**
   * 计算技能效果
   * 基于技能配置和等级计算效果描述
   */
  static calculateSkillEffect(skillConfig: any, level: number): string {
    if (level === 0) {
      return '未学习';
    }

    // 基于old项目的效果计算逻辑
    const baseEffect = skillConfig.baseEffect || 10;
    const effectValue = baseEffect * level;
    
    return `${skillConfig.effectType || '属性'}+${effectValue}`;
  }

  /**
   * 计算技能重置退还金额
   * 基于old项目的重置逻辑，退还80%的升级费用
   */
  static calculateSkillResetRefund(skillLevel: number): number {
    // 计算总升级费用
    let totalCost = 0;
    for (let level = 2; level <= skillLevel; level++) {
      totalCost += this.getNextLevelCost(level);
    }
    
    // 退还80%
    return Math.floor(totalCost * 0.8);
  }

  /**
   * 计算技能加成
   * 基于old项目的技能效果计算
   */
  static calculateSkillBonus(skillConfig: any, skillLevel: number): any {
    const baseBonus = {
      attack: 0,
      defense: 0,
      speed: 0,
      technique: 0,
      physical: 0
    };

    if (!skillConfig || !skillConfig.effects) {
      return baseBonus;
    }

    // 根据技能配置和等级计算加成
    for (const effect of skillConfig.effects) {
      const bonusValue = (effect.baseValue || 0) + (effect.levelMultiplier || 0) * (skillLevel - 1);

      switch (effect.type) {
        case 'attack':
          baseBonus.attack += bonusValue;
          break;
        case 'defense':
          baseBonus.defense += bonusValue;
          break;
        case 'speed':
          baseBonus.speed += bonusValue;
          break;
        case 'technique':
          baseBonus.technique += bonusValue;
          break;
        case 'physical':
          baseBonus.physical += bonusValue;
          break;
      }
    }

    return baseBonus;
  }

  /**
   * 检查技能是否按等级解锁
   * 基于信仰等级判断技能解锁状态
   */
  static isSkillUnlockedByLevel(skillId: number, beliefLevel: number): boolean {
    // 基于old项目的解锁等级配置
    const unlockLevels: { [key: number]: number } = {
      1: 1, 2: 3, 3: 5, 4: 7, 5: 10,
      6: 12, 7: 15, 8: 18, 9: 20, 10: 20
    };
    
    return beliefLevel >= (unlockLevels[skillId] || 1);
  }

  /**
   * 计算批量升级总费用
   * 基于多个技能的升级计划计算总费用
   */
  static calculateBatchUpgradeCost(skillUpgrades: Array<{ skillId: number; currentLevel: number; targetLevel: number }>): number {
    let totalCost = 0;

    for (const upgrade of skillUpgrades) {
      for (let level = upgrade.currentLevel + 1; level <= upgrade.targetLevel; level++) {
        totalCost += this.getNextLevelCost(level);
      }
    }

    return totalCost;
  }

  /**
   * 计算技能升级后的总加成
   * 用于预览升级效果
   */
  static calculateTotalBonusAfterUpgrade(
    currentSkills: { [skillId: number]: number },
    skillConfigs: { [skillId: number]: any },
    upgrades: Array<{ skillId: number; targetLevel: number }>
  ): any {
    const totalBonus = {
      attack: 0,
      defense: 0,
      speed: 0,
      technique: 0,
      physical: 0
    };

    // 复制当前技能等级
    const newSkillLevels = { ...currentSkills };

    // 应用升级
    for (const upgrade of upgrades) {
      newSkillLevels[upgrade.skillId] = upgrade.targetLevel;
    }

    // 计算总加成
    for (const [skillId, level] of Object.entries(newSkillLevels)) {
      if (level > 0) {
        const skillConfig = skillConfigs[parseInt(skillId)];
        if (skillConfig) {
          const skillBonus = this.calculateSkillBonus(skillConfig, level);
          totalBonus.attack += skillBonus.attack || 0;
          totalBonus.defense += skillBonus.defense || 0;
          totalBonus.speed += skillBonus.speed || 0;
          totalBonus.technique += skillBonus.technique || 0;
          totalBonus.physical += skillBonus.physical || 0;
        }
      }
    }

    return totalBonus;
  }
}

/**
 * 信仰捐献计算器
 * 基于old项目的捐献系统计算逻辑
 */
export class BeliefDonationCalculator {
  /**
   * 计算捐献收益
   * 基于old项目逻辑：不同捐献类型有不同的收益率
   */
  static calculateDonation(type: number, amount: number): {
    beliefGoldGain: number;
    expGain: number;
    costAmount: number;
    currencyType: string;
  } {
    let beliefGoldGain = 0;
    let expGain = 0;
    let costAmount = amount;
    let currencyType = '';

    switch (type) {
      case 1: // 欧元捐献
        currencyType = 'cash';
        beliefGoldGain = amount; // 1:1兑换
        expGain = Math.floor(amount / 100); // 每100欧元1点经验
        break;
        
      case 2: // 球币捐献
        currencyType = 'gold';
        beliefGoldGain = Math.floor(amount * 0.8); // 80%兑换率
        expGain = Math.floor(amount / 50); // 每50球币1点经验
        break;
        
      default:
        currencyType = 'cash';
        beliefGoldGain = amount;
        expGain = Math.floor(amount / 100);
        break;
    }

    return {
      beliefGoldGain,
      expGain,
      costAmount,
      currencyType
    };
  }

  /**
   * 计算信仰等级升级所需经验
   * 基于old项目的等级经验表
   */
  static calculateLevelUpExp(currentLevel: number): number {
    // 基于old项目的等级经验公式
    const baseExp = 1000;
    const levelMultiplier = Math.pow(1.2, currentLevel - 1);
    
    return Math.floor(baseExp * levelMultiplier);
  }

  /**
   * 计算经验增加后的等级变化
   * 返回新等级和是否升级
   */
  static calculateLevelChange(currentLevel: number, currentExp: number, addExp: number): {
    newLevel: number;
    newExp: number;
    levelUp: boolean;
    levelsGained: number;
  } {
    let newLevel = currentLevel;
    let newExp = currentExp + addExp;
    let levelUp = false;
    let levelsGained = 0;

    // 检查是否可以升级
    while (newLevel < 20) { // 最高20级
      const requiredExp = this.calculateLevelUpExp(newLevel);
      
      if (newExp >= requiredExp) {
        newExp -= requiredExp;
        newLevel++;
        levelUp = true;
        levelsGained++;
      } else {
        break;
      }
    }

    return {
      newLevel,
      newExp,
      levelUp,
      levelsGained
    };
  }

  /**
   * 计算捐献排行积分
   * 基于捐献金额和类型计算排行积分
   */
  static calculateDonationScore(donations: Array<{ type: number; amount: number; timestamp: number }>): {
    todayScore: number;
    weekScore: number;
    totalScore: number;
  } {
    const now = Date.now();
    const oneDayMs = 24 * 60 * 60 * 1000;
    const oneWeekMs = 7 * oneDayMs;

    let todayScore = 0;
    let weekScore = 0;
    let totalScore = 0;

    for (const donation of donations) {
      const timeDiff = now - donation.timestamp;
      const score = this.getDonationScore(donation.type, donation.amount);

      totalScore += score;

      if (timeDiff <= oneDayMs) {
        todayScore += score;
      }

      if (timeDiff <= oneWeekMs) {
        weekScore += score;
      }
    }

    return {
      todayScore,
      weekScore,
      totalScore
    };
  }

  /**
   * 获取单次捐献的积分
   * 私有辅助方法
   */
  private static getDonationScore(type: number, amount: number): number {
    switch (type) {
      case 1: // 欧元捐献
        return amount; // 1欧元 = 1积分
      case 2: // 球币捐献
        return Math.floor(amount * 0.5); // 1球币 = 0.5积分
      default:
        return amount;
    }
  }
}

/**
 * 信仰管理计算器
 * 基于old项目的信仰管理计算逻辑
 */
export class BeliefManagementCalculator {
  /**
   * 获取信仰创建费用
   * 基于系统配置计算创建费用
   */
  static getBeliefCreateCost(baseCreateCost: number = 10000): number {
    // 基于old项目：信仰创建需要消耗现金
    return baseCreateCost;
  }

  /**
   * 计算职位统计
   * 统计各个职位的人数分布
   */
  static calculatePositionCounts(members: any[], leaders: any[]): any {
    const counts = { 1: 0, 2: 0, 3: 0, 4: 0 }; // 1-董事长, 2-副董事长, 3-总经理, 4-总监

    // 统计领导者职位
    leaders.forEach(leader => {
      counts[leader.pos] = (counts[leader.pos] || 0) + 1;
    });

    // 普通成员算作总监
    const regularMembers = members.length - leaders.length;
    counts[4] += regularMembers;

    return {
      chairman: counts[1],        // 董事长
      viceChairman: counts[2],    // 副董事长
      generalManager: counts[3],  // 总经理
      director: counts[4],        // 总监
      totalMembers: members.length,
      totalLeaders: leaders.length
    };
  }

  /**
   * 计算信仰容量和成员限制
   * 基于信仰等级计算最大成员数
   */
  static calculateMemberCapacity(beliefLevel: number, baseCapacity: number = 20): number {
    // 基于old项目：信仰等级越高，可容纳成员越多
    const levelBonus = Math.floor(beliefLevel / 2) * 5; // 每2级增加5个名额
    const maxCapacity = baseCapacity + levelBonus;

    return Math.min(maxCapacity, 100); // 最大100人
  }

  /**
   * 计算信仰排名积分
   * 基于多个维度计算信仰的综合排名积分
   */
  static calculateBeliefRankingScore(belief: {
    level: number;
    totalDonation: number;
    memberCount: number;
    weekActivity: number;
    battleWins: number;
    battleLosses: number;
  }): number {
    // 基于old项目的排名算法
    let score = 0;

    // 等级权重 (40%)
    score += belief.level * 100;

    // 总捐献权重 (30%)
    score += Math.floor(belief.totalDonation / 1000) * 10;

    // 成员数量权重 (15%)
    score += belief.memberCount * 5;

    // 周活跃度权重 (10%)
    score += Math.floor(belief.weekActivity / 100) * 2;

    // 战斗胜率权重 (5%)
    const totalBattles = belief.battleWins + belief.battleLosses;
    if (totalBattles > 0) {
      const winRate = belief.battleWins / totalBattles;
      score += Math.floor(winRate * 100);
    }

    return score;
  }

  /**
   * 计算信仰活跃度衰减
   * 基于时间计算活跃度的自然衰减
   */
  static calculateActivityDecay(lastActivityTime: number, currentActivity: number): number {
    const now = Date.now();
    const timeDiff = now - lastActivityTime;
    const daysPassed = Math.floor(timeDiff / (24 * 60 * 60 * 1000));

    // 每天衰减5%的活跃度
    const decayRate = 0.05;
    const decayFactor = Math.pow(1 - decayRate, daysPassed);

    return Math.floor(currentActivity * decayFactor);
  }

  /**
   * 计算信仰福利发放
   * 基于信仰等级和资金计算可发放的福利
   */
  static calculateBeliefWelfare(beliefLevel: number, beliefGold: number): {
    canDistribute: boolean;
    maxDistribution: number;
    perMemberAmount: number;
    memberCount: number;
  } {
    // 基于old项目：信仰可以向成员发放福利
    const minDistributionLevel = 5; // 5级以上才能发放福利
    const canDistribute = beliefLevel >= minDistributionLevel;

    if (!canDistribute) {
      return {
        canDistribute: false,
        maxDistribution: 0,
        perMemberAmount: 0,
        memberCount: 0
      };
    }

    // 最多发放80%的信仰资金
    const maxDistribution = Math.floor(beliefGold * 0.8);

    // 假设平均成员数（实际应该传入真实成员数）
    const estimatedMembers = Math.min(beliefLevel * 2, 50);
    const perMemberAmount = Math.floor(maxDistribution / estimatedMembers);

    return {
      canDistribute: true,
      maxDistribution,
      perMemberAmount,
      memberCount: estimatedMembers
    };
  }

  /**
   * 计算信仰升级奖励
   * 基于新等级计算升级奖励
   */
  static calculateLevelUpRewards(newLevel: number): Array<{ type: string; amount: number; description: string }> {
    const rewards = [];

    // 基础奖励：每级都有
    rewards.push({
      type: 'beliefGold',
      amount: newLevel * 1000,
      description: `信仰资金+${newLevel * 1000}`
    });

    // 里程碑奖励
    if (newLevel % 5 === 0) {
      rewards.push({
        type: 'memberCapacity',
        amount: 5,
        description: '成员容量+5'
      });
    }

    if (newLevel === 10) {
      rewards.push({
        type: 'specialSkill',
        amount: 1,
        description: '解锁特殊技能'
      });
    }

    if (newLevel === 20) {
      rewards.push({
        type: 'maxLevel',
        amount: 1,
        description: '达到最高等级'
      });
    }

    return rewards;
  }
}
