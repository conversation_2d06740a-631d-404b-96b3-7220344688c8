/**
 * 统一的角色阵容业务逻辑层
 * 继承BaseService，提供专业的Result模式错误处理和业务逻辑管理
 * 基于old项目的TeamFormations实体，提供完整的阵容管理功能
 *
 * 🎯 核心功能：
 * - 阵容创建、删除、查询
 * - 球员位置管理和交换
 * - 自动布阵算法
 * - 阵容属性计算
 * - 战术配置管理
 *
 * 🚀 性能优化：
 * - 继承BaseService的业务操作框架和性能监控
 * - 使用Repository层的Result模式错误处理
 * - 微服务调用的错误处理和重试机制
 * - 智能缓存机制优化查询性能
 */

import { Injectable, Logger, NotFoundException, Inject, forwardRef } from '@nestjs/common';
import { TeamFormations, TeamFormationsDocument, TeamFormation, FormationType } from '@character/common/schemas/formation.schema';
import { FormationRepository } from '@character/common/repositories/formation.repository';
import { GameConfigFacade } from '@libs/game-config';
import { MicroserviceClientService } from '@libs/service-mesh';
import { MICROSERVICE_NAMES } from '@libs/shared/constants';
import {SwapHerosDto} from "@character/common/dto/formation.dto";
import { CharacterService } from '../character/character.service';
import { HeroService } from '../hero/services/hero.service';
import {
  CoachCalculator,
  TacticsCalculator,
  SkillCalculator,
  PositionRateCalculator,
  FormationAttributeCalculator
} from './calculators';

import { XResult, XResultUtils, ServiceResultHandler } from '@libs/common/types/result.type';
import { BaseService } from '@libs/common/service';

@Injectable()
export class FormationService extends BaseService {
  constructor(
    private readonly formationRepository: FormationRepository,
    private readonly gameConfig: GameConfigFacade,
    microserviceClient: MicroserviceClientService,
    @Inject(forwardRef(() => CharacterService))
    private readonly characterService: CharacterService,
    @Inject(forwardRef(() => HeroService))
    private readonly heroService: HeroService,
  ) {
    super('FormationService', microserviceClient);
  }

  /**
   * 获取角色的阵容数据
   * 基于old项目: TeamFormations实体的初始化
   * 使用Result模式，无需try/catch包装
   * Controller层已验证参数，无需重复验证
   */
  async getFormations(characterId: string): Promise<XResult<TeamFormationsDocument | null>> {
    const result = await this.formationRepository.findByCharacterId(characterId);
    return this.handleRepositoryResult(result, '获取角色阵容数据失败');
  }

  /**
   * 初始化角色阵容数据
   * 基于old项目: TeamFormations构造函数和initByDB
   * 使用Result模式，无需try/catch包装
   * Controller层已验证参数，无需重复验证
   */
  async initializeFormations(characterId: string): Promise<XResult<TeamFormationsDocument>> {
    // 检查是否已存在
    const existingResult = await this.getFormations(characterId);
    if (XResultUtils.isSuccess(existingResult) && existingResult.data) {
      this.logger.log(`角色阵容数据已存在: ${characterId}`);
      return existingResult;
    }

    const uid = characterId; // 在新架构中，uid就是characterId

    // 初始化阵型列表 - 基于old项目initFormationList
    const allFormationsResult = await this.initFormationList();
    if (XResultUtils.isFailure(allFormationsResult)) {
      return XResultUtils.error(`初始化阵型列表失败: ${allFormationsResult.message}`, allFormationsResult.code);
    }

    // 初始化防守战术 - 基于old项目initDefTactics
    const allDefTacticsResult = this.initDefTactics();
    if (XResultUtils.isFailure(allDefTacticsResult)) {
      return XResultUtils.error(`初始化防守战术失败: ${allDefTacticsResult.message}`, allDefTacticsResult.code);
    }

    // 初始化进攻战术 - 基于old项目checkAllTactics
    const allTacticsResult = this.initTactics(uid);
    if (XResultUtils.isFailure(allTacticsResult)) {
      return XResultUtils.error(`初始化进攻战术失败: ${allTacticsResult.message}`, allTacticsResult.code);
    }

    const teamFormationsData = {
      uid,
      characterId,
      teamFormations: [],
      currTeamFormationId: '',
      leagueTeamFormationId: '',
      warOfFaithTeamFormationId: '',
      allTactics: allTacticsResult.data,
      allDefTactics: allDefTacticsResult.data,
      allFormations: allFormationsResult.data,
      fixId: 0,
    };

    const result = await this.formationRepository.createInternal(teamFormationsData);
    return this.handleRepositoryResult(result, '初始化角色阵容数据失败');
  }

  /**
   * 创建阵容
   * 基于old项目: newTeamFormation方法，优化命名为createFormation
   * 这是复杂业务操作，使用executeBusinessOperation进行完整监控
   * Controller层已验证参数，无需重复验证
   */
  async createFormation(characterId: string, resId: number, type?: number): Promise<XResult<TeamFormation>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`新建阵容: ${characterId}, 阵型ID: ${resId}, 类型: ${type}`);

      // 获取角色的阵容数据
      const teamFormationsResult = await this.getFormations(characterId);
      if (XResultUtils.isFailure(teamFormationsResult)) {
        return XResultUtils.error(`获取角色阵容数据失败: ${teamFormationsResult.message}`, teamFormationsResult.code);
      }

      const teamFormations = teamFormationsResult.data;
      if (!teamFormations) {
        return XResultUtils.error(`角色阵容数据不存在: ${characterId}`, 'FORMATIONS_NOT_FOUND');
      }

      // 计算阵容序号
      const existingCount = teamFormations.teamFormations.length;
      const teamId = existingCount + 1;

      const teamFormation: TeamFormation = {
        uid: this.generateUid(),                    // Id(不是配置表的ID，而是玩家生成阵容的顺序)
        resId: resId,                               // 配置阵型Id
        name: `阵容${teamId}`,                       // 根据序号生成名称
        // 团队基础属性
        attack: 0,                                  // 球队进攻值
        defend: 0,                                  // 球队防守值
        actualStrength: 0,                          // 球队的实力
        isInitName: 0,                              // 是否是初始名字 0是 1不是
        isInitFormation: 0,                         // 是否初始阵容一 0不是 1是
        isLeague: 0,                                // 是否为联赛专用阵容 0为不是 1是为
        teamId: teamId,                             // 阵容Id 1,2,3,4 阵容1,2,3,4
        teamType: 1,                                // 小队类型 1主力队 2替补队 3预备队
        // 初始化位置球员映射 - 严格对应old项目
        positionToHerosObject: {
          GK: [], DL: [], DC: [], DR: [], ML: [], MC: [],
          MR: [], WL: [], ST: [], WR: [], AM: [], DM: []
        },
        scenceUse: [],                              // 阵型使用场景标记
        inspireRate: 0,                             // 鼓舞加成比例,暂定为0
        useTactics: 101,                            // 当前阵容使用的战术
        useDefTactics: 1101,                        // 当前阵容使用防守的战术
        freeKickHero: '',                         // 指定的任意球球员
        penaltiesHero: '',                        // 指定的点球球员
        cornerKickHero: '',                       // 指定的角球球员
        trainers: this.initTrainers(),              // 教练
        type: type || FormationType.COMMON,        // 阵容类型(用途)
      };

      // 添加到阵容列表并保存到数据库
      teamFormations.teamFormations.push(teamFormation);
      await teamFormations.save();

      this.logger.log(`阵容创建成功: ${teamFormation.uid}, 名称: ${teamFormation.name}`);
      return XResultUtils.ok(teamFormation);
    }, { reason: 'create_formation', metadata: { resId, type } });
  }

  /**
   * 添加球员到阵容位置
   * 基于old项目: addHeroInTeam方法，优化命名为addPlayerToPosition
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async addHeroToPosition(characterId: string, formationId: string, position: string, index: number, heroId: string): Promise<XResult<boolean>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`添加球员到阵容: ${characterId}, 阵容: ${formationId}, 位置: ${position}, 索引: ${index}, 球员: ${heroId}`);

      const teamFormationsResult = await this.getFormations(characterId);
      if (XResultUtils.isFailure(teamFormationsResult)) {
        return XResultUtils.error(`获取角色阵容数据失败: ${teamFormationsResult.message}`, teamFormationsResult.code);
      }

      const teamFormations = teamFormationsResult.data;
      if (!teamFormations) {
        return XResultUtils.error(`角色阵容数据不存在: ${characterId}`, 'FORMATIONS_NOT_FOUND');
      }

      const formation = teamFormations.teamFormations.find(f => f.uid === formationId);
      if (!formation) {
        return XResultUtils.error(`阵容不存在: ${formationId}`, 'FORMATION_NOT_FOUND');
      }

      // 查重 - 基于old项目_InnercheckHeroIsSameResID
      const isSame = this.checkHeroExists(formation, heroId);
      if (isSame) {
        this.logger.error('球员已在阵容中', formationId, heroId);
        return XResultUtils.error(`球员已在阵容中: ${heroId}`, 'HERO_ALREADY_EXISTS');
      }

      // 检查位置是否存在并添加球员
      const positionKey = position as keyof typeof formation.positionToHerosObject;
      if (formation.positionToHerosObject[positionKey] !== undefined) {
        formation.positionToHerosObject[positionKey].splice(index, 0, heroId);
        await teamFormations.save();
        this.logger.log('球员添加成功', formationId, heroId);
        return XResultUtils.ok(true);
      }

      return XResultUtils.error(`无效的位置: ${position}`, 'INVALID_POSITION');
    }, { reason: 'add_hero_to_position', metadata: { formationId, position, index, heroId } });
  }

  /**
   * 从阵容位置移除球员
   * 基于old项目: deleteHeroFromTeam方法，优化命名为removePlayerFromPosition
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async removeHeroFromPosition(characterId: string, formationId: string, position: string, heroId: string): Promise<XResult<boolean>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`从阵容删除球员: ${characterId}, 阵容: ${formationId}, 位置: ${position}, 球员: ${heroId}`);

      const teamFormationsResult = await this.getFormations(characterId);
      if (XResultUtils.isFailure(teamFormationsResult)) {
        return XResultUtils.error(`获取角色阵容数据失败: ${teamFormationsResult.message}`, teamFormationsResult.code);
      }

      const teamFormations = teamFormationsResult.data;
      if (!teamFormations) {
        return XResultUtils.error(`角色阵容数据不存在: ${characterId}`, 'FORMATIONS_NOT_FOUND');
      }

      const formation = teamFormations.teamFormations.find(f => f.uid === formationId);
      if (!formation) {
        return XResultUtils.error(`阵容不存在: ${formationId}`, 'FORMATION_NOT_FOUND');
      }

      const positionKey = position as keyof typeof formation.positionToHerosObject;
      if (formation.positionToHerosObject[positionKey] !== undefined) {
        const heroArr = formation.positionToHerosObject[positionKey];
        for (let i = 0; i < heroArr.length; i++) {
          if (heroArr[i] === heroId) {
            heroArr.splice(i, 1);
            await teamFormations.save();
            // 删除球员身上的attack和defend数据
            await this.removeHeroAttackAndDefendData(heroId, formationId);
            this.logger.log('球员删除成功', formationId, heroId);
            return XResultUtils.ok(true);
          }
        }
      }

      return XResultUtils.error(`球员不在指定位置: ${heroId} at ${position}`, 'HERO_NOT_FOUND_IN_POSITION');
    }, { reason: 'remove_hero_from_position', metadata: { formationId, position, heroId } });
  }

  /**
   * 设置当前激活阵容
   * 基于old项目: setCurrTeamFormationId方法，优化命名为setActiveFormation
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async setActiveFormation(characterId: string, formationId: string): Promise<XResult<boolean>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`设置当前阵容: ${characterId}, 阵容: ${formationId}`);

      const teamFormationsResult = await this.getFormations(characterId);
      if (XResultUtils.isFailure(teamFormationsResult)) {
        return XResultUtils.error(`获取角色阵容数据失败: ${teamFormationsResult.message}`, teamFormationsResult.code);
      }

      const teamFormations = teamFormationsResult.data;
      if (!teamFormations) {
        return XResultUtils.error(`角色阵容数据不存在: ${characterId}`, 'FORMATIONS_NOT_FOUND');
      }

      // 检查阵容是否存在
      const formation = teamFormations.teamFormations.find(f => f.uid === formationId);
      if (!formation) {
        return XResultUtils.error(`阵容不存在: ${formationId}`, 'FORMATION_NOT_FOUND');
      }

      teamFormations.currTeamFormationId = formationId;
      await teamFormations.save();

      this.logger.log('设置当前阵容成功', formationId);
      return XResultUtils.ok(true);
    }, { reason: 'set_active_formation', metadata: { formationId } });
  }

  /**
   * 设置联赛专用阵容
   * 基于old项目: setLeagueTeamFormationId方法，优化命名为setLeagueFormation
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async setLeagueFormation(characterId: string, formationId: string): Promise<XResult<boolean>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`设置联赛阵容: ${characterId}, 阵容: ${formationId}`);

      const teamFormationsResult = await this.getFormations(characterId);
      if (XResultUtils.isFailure(teamFormationsResult)) {
        return XResultUtils.error(`获取角色阵容数据失败: ${teamFormationsResult.message}`, teamFormationsResult.code);
      }

      const teamFormations = teamFormationsResult.data;
      if (!teamFormations) {
        return XResultUtils.error(`角色阵容数据不存在: ${characterId}`, 'FORMATIONS_NOT_FOUND');
      }

      teamFormations.leagueTeamFormationId = formationId;
      await this.formationRepository.update(teamFormations._id.toString(), { leagueTeamFormationId: formationId });

      return XResultUtils.ok(true);
    }, { reason: 'set_league_formation', metadata: { formationId } });
  }

  /**
   * 设置信仰之战专用阵容
   * 基于old项目: setWarOfFaithTeamFormationId方法，优化命名为setWarOfFaithFormation
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async setWarOfFaithFormation(characterId: string, formationId: string): Promise<XResult<boolean>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`设置信仰之战阵容: ${characterId}, 阵容: ${formationId}`);

      const teamFormationsResult = await this.getFormations(characterId);
      if (XResultUtils.isFailure(teamFormationsResult)) {
        return XResultUtils.error(`获取角色阵容数据失败: ${teamFormationsResult.message}`, teamFormationsResult.code);
      }

      const teamFormations = teamFormationsResult.data;
      if (!teamFormations) {
        return XResultUtils.error(`角色阵容数据不存在: ${characterId}`, 'FORMATIONS_NOT_FOUND');
      }

      teamFormations.warOfFaithTeamFormationId = formationId;
      await this.formationRepository.update(teamFormations._id.toString(), { warOfFaithTeamFormationId: formationId });

      return XResultUtils.ok(true);
    }, { reason: 'set_war_of_faith_formation', metadata: { formationId } });
  }

  /**
   * 删除阵容
   * 基于old项目: delTeamFormationByUid方法，优化命名为deleteFormation
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async deleteFormation(characterId: string, formationId: string): Promise<XResult<boolean>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`删除阵容: ${characterId}, 阵容: ${formationId}`);

      const teamFormationsResult = await this.getFormations(characterId);
      if (XResultUtils.isFailure(teamFormationsResult)) {
        return XResultUtils.error(`获取角色阵容数据失败: ${teamFormationsResult.message}`, teamFormationsResult.code);
      }

      const teamFormations = teamFormationsResult.data;
      if (!teamFormations) {
        return XResultUtils.error(`角色阵容数据不存在: ${characterId}`, 'FORMATIONS_NOT_FOUND');
      }

      const formation = teamFormations.teamFormations.find(f => f.uid === formationId);
      if (!formation) {
        return XResultUtils.error(`阵容不存在: ${formationId}`, 'FORMATION_NOT_FOUND');
      }

      // 基于old项目的删除规则检查
      const currentFormationId = teamFormations.currTeamFormationId;
      const leagueFormationId = teamFormations.leagueTeamFormationId;
      const warOfFaithFormationId = teamFormations.warOfFaithTeamFormationId;

      // 不能删除当前使用的阵容
      if (formationId === currentFormationId) {
        return XResultUtils.error('不能删除当前使用的阵容', 'CANNOT_DELETE_ACTIVE_FORMATION');
      }

      // 不能删除联赛专用阵容
      if (formationId === leagueFormationId) {
        return XResultUtils.error('不能删除联赛专用阵容', 'CANNOT_DELETE_LEAGUE_FORMATION');
      }

      // 不能删除信仰之战专用阵容
      if (formationId === warOfFaithFormationId) {
        return XResultUtils.error('不能删除信仰之战专用阵容', 'CANNOT_DELETE_WAR_FORMATION');
      }

      // 不能删除初始阵容
      if (formation.isInitFormation === 1) {
        return XResultUtils.error('不能删除初始阵容', 'CANNOT_DELETE_INIT_FORMATION');
      }

      // 从阵容列表中移除
      const formationIndex = teamFormations.teamFormations.findIndex(f => f.uid === formationId);
      if (formationIndex > -1) {
        teamFormations.teamFormations.splice(formationIndex, 1);
        await teamFormations.save();
        this.logger.log(`阵容删除成功: ${formationId}`);
        return XResultUtils.ok(true);
      }

      return XResultUtils.error(`阵容删除失败: ${formationId}`, 'DELETE_FORMATION_FAILED');
    }, { reason: 'delete_formation', metadata: { formationId } });
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 生成UID
   * 基于old项目: utils.syncCreateUid
   */
  private generateUid(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 初始化阵型列表
   * 严格基于old项目: initFormationList函数
   * 返回Result类型，支持错误处理
   */
  private async initFormationList(): Promise<XResult<any[]>> {
    try {
      const teamFormationConfigs = await this.gameConfig.teamFormation.getAll();
      const formationList = [];
      let index = 0;

      for (const config of teamFormationConfigs) {
        if (config.level === 1 && config.needLevel === 0) {
          formationList[index] = {
            ResId: config.id,
            Name: config.name,
          };
          index++;
        }
      }

      return XResultUtils.ok(formationList);
    } catch (error) {
      this.logger.error('初始化阵型列表失败', error);
      return XResultUtils.error('初始化阵型列表失败', 'INIT_FORMATION_LIST_FAILED');
    }
  }

  /**
   * 初始化防守战术
   * 严格基于old项目: initDefTactics方法
   * 返回Result类型，支持错误处理
   */
  private initDefTactics(): XResult<Record<string, any>> {
    try {
      // 基于old项目commonEnum.TEAM_DEF_TACTICS_LIST初始化防守战术
      const defTactics = {
        "11": 1101, // 防守战术11: 压迫式防守
        "12": 1201, // 防守战术12: 区域防守
        "13": 1301, // 防守战术13: 人盯人防守
        "14": 1401, // 防守战术14: 混合防守
        "15": 1501, // 防守战术15: 反击防守
      };
      return XResultUtils.ok(defTactics);
    } catch (error) {
      this.logger.error('初始化防守战术失败', error);
      return XResultUtils.error('初始化防守战术失败', 'INIT_DEF_TACTICS_FAILED');
    }
  }

  /**
   * 初始化进攻战术
   * 严格基于old项目: checkAllTactics方法
   * 返回Result类型，支持错误处理
   */
  private initTactics(uid: string): XResult<Record<string, any>> {
    try {
      // 基于old项目commonEnum.TEAM_TACTICS_LIST初始化进攻战术
      const tactics = {
        "1": 101, // 进攻战术1: 边路进攻
        "2": 201, // 进攻战术2: 中路进攻
        "3": 301, // 进攻战术3: 快速反击
        "4": 401, // 进攻战术4: 控球进攻
        "5": 501, // 进攻战术5: 直接进攻
      };

      return XResultUtils.ok({ [uid]: tactics });
    } catch (error) {
      this.logger.error('初始化进攻战术失败', error);
      return XResultUtils.error('初始化进攻战术失败', 'INIT_TACTICS_FAILED');
    }
  }

  /**
   * 初始化教练
   * 基于old项目: initTrainers方法
   */
  private initTrainers(): any[] {
    return [
      { uid: '', resId: 0, type: 1, level: 1, tactics: [] },
      { uid: '', resId: 0, type: 2, level: 1, tactics: [] },
      { uid: '', resId: 0, type: 3, level: 1, tactics: [] },
    ];
  }

  /**
   * 检查球员是否已在阵容中
   * 基于old项目: _InnercheckHeroIsSameResID方法，优化命名为checkPlayerExists
   */
  private checkHeroExists(formation: TeamFormation, heroId: string): boolean {
    for (const position in formation.positionToHerosObject) {
      const heroList = formation.positionToHerosObject[position as keyof typeof formation.positionToHerosObject];
      if (heroList.includes(heroId)) {
        return true;
      }
    }
    return false;
  }

  /**
   * 复制阵容
   * 基于old项目: copyTeamFormation方法，优化命名为copyFormation
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async copyFormation(characterId: string, sourceFormationId: string): Promise<XResult<{ formationId: string }>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`复制阵容: ${characterId}, 源阵容: ${sourceFormationId}`);

      const teamFormationsResult = await this.getFormations(characterId);
      if (XResultUtils.isFailure(teamFormationsResult)) {
        return XResultUtils.error(`获取角色阵容数据失败: ${teamFormationsResult.message}`, teamFormationsResult.code);
      }

      const teamFormations = teamFormationsResult.data;
      if (!teamFormations) {
        return XResultUtils.error(`角色阵容数据不存在: ${characterId}`, 'FORMATIONS_NOT_FOUND');
      }

      const sourceFormation = teamFormations.teamFormations.find(f => f.uid === sourceFormationId);
      if (!sourceFormation) {
        return XResultUtils.error(`源阵容不存在: ${sourceFormationId}`, 'SOURCE_FORMATION_NOT_FOUND');
      }

      // 创建阵容 - 深拷贝源阵容
      const copyTeam = JSON.parse(JSON.stringify(sourceFormation));
      const newFormationResult = await this.createFormation(characterId, copyTeam.resId);

      if (XResultUtils.isFailure(newFormationResult)) {
        return XResultUtils.error(`创建新阵容失败: ${newFormationResult.message}`, newFormationResult.code);
      }

      const newFormation = newFormationResult.data;

      // 复制所有属性
      newFormation.positionToHerosObject = copyTeam.positionToHerosObject;
      newFormation.useTactics = copyTeam.useTactics;
      newFormation.useDefTactics = copyTeam.useDefTactics;
      newFormation.trainers = copyTeam.trainers;
      newFormation.isInitFormation = 0; // 不是初始阵容

      // 设置名称
      const teamCount = this.getTeamCountByType(teamFormations, copyTeam.type);
      newFormation.name = this.getNameByType(teamCount - 1, copyTeam.type);

      // 更新阵容到数据库
      const formationIndex = teamFormations.teamFormations.findIndex(f => f.uid === newFormation.uid);
      if (formationIndex > -1) {
        teamFormations.teamFormations[formationIndex] = newFormation;
        await teamFormations.save();
      }

      // 重新计算属性
      await this.recalculateFormationAttributes(newFormation, characterId);

      return XResultUtils.ok({ formationId: newFormation.uid });
    }, { reason: 'copy_formation', metadata: { sourceFormationId } });
  }

  /**
   * 设置阵容战术
   * 严格基于old项目: setFormationTactics方法
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async setFormationTactics(characterId: string, uid: string, resId: number, tacticsType: string): Promise<XResult<boolean>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`设置阵容战术: ${characterId}, 阵容: ${uid}, 战术: ${resId}, 类型: ${tacticsType}`);

      const teamFormationsResult = await this.getFormations(characterId);
      if (XResultUtils.isFailure(teamFormationsResult)) {
        return XResultUtils.error(`获取角色阵容数据失败: ${teamFormationsResult.message}`, teamFormationsResult.code);
      }

      const teamFormations = teamFormationsResult.data;
      if (!teamFormations) {
        return XResultUtils.error(`角色阵容数据不存在: ${characterId}`, 'FORMATIONS_NOT_FOUND');
      }

      const formation = teamFormations.teamFormations.find(f => f.uid === uid);
      if (!formation) {
        return XResultUtils.error(`阵容不存在: ${uid}`, 'FORMATION_NOT_FOUND');
      }

      if (tacticsType === 'tactics') {
        // 进攻战术
        if (formation.useTactics === resId) {
          this.logger.log('战术已经相同，无需更改');
          return XResultUtils.ok(true);
        }

        // 校验resId是否是自己的战术
        const allTactics = teamFormations.allTactics[teamFormations.uid];
        if (!allTactics) {
          return XResultUtils.error('进攻战术配置不存在', 'TACTICS_CONFIG_NOT_FOUND');
        }

        let isHas = false;
        for (const key in allTactics) {
          if (allTactics[key] === resId) {
            isHas = true;
            break;
          }
        }

        if (!isHas) {
          this.logger.warn('setFormationTactics error param 1: uid, resId: ', uid, resId);
          return XResultUtils.error(`无效的进攻战术ID: ${resId}`, 'INVALID_TACTICS_ID');
        }

        formation.useTactics = resId;
      } else {
        // 防守战术
        if (formation.useDefTactics === resId) {
          this.logger.log('防守战术已经相同，无需更改');
          return XResultUtils.ok(true);
        }

        // 校验resId是否是自己的战术
        let isHas = false;
        for (const key in teamFormations.allDefTactics) {
          if (teamFormations.allDefTactics[key] === resId) {
            isHas = true;
            break;
          }
        }

        if (!isHas) {
          this.logger.warn('setFormationTactics error param 2: uid, resId: ', uid, resId);
          return XResultUtils.error(`无效的防守战术ID: ${resId}`, 'INVALID_DEF_TACTICS_ID');
        }

        formation.useDefTactics = resId;
      }

      await this.formationRepository.update(teamFormations._id.toString(), teamFormations.toObject());

      // 重新计算阵容属性
      await this.reCalcTeamFormationAttrByUid(uid);
      // 重新计算球员战术加成 - TODO 暂时跳过，需要球员ID和阵容ID
      // await this.calcHeroTacticsAttr(heroId, uid);

      return XResultUtils.ok(true);
    }, { reason: 'set_formation_tactics', metadata: { uid, resId, tacticsType } });
  }

  /**
   * 获取指定类型的阵容数量
   * 基于old项目: getTeamCountByType方法
   */
  private getTeamCountByType(teamFormations: TeamFormationsDocument, type: number): number {
    return teamFormations.teamFormations.filter(f => f.type === type).length;
  }

  /**
   * 根据类型获取阵容名称
   * 基于old项目: getNameByType方法
   */
  private getNameByType(index: number, type: number): string {
    const typeNames = {
      [FormationType.COMMON]: '阵容',
      [FormationType.LEAGUE]: '联赛阵容',
      [FormationType.WAR_OF_FAITH]: '信仰阵容',
      [FormationType.TOURNAMENT]: '锦标赛阵容',
      [FormationType.FRIENDLY]: '友谊赛阵容',
    };

    const baseName = typeNames[type] || '阵容';
    return `${baseName}${index + 1}`;
  }

  // ==================== 核心功能补全 ====================

  /**
   * 自动布阵算法
   * 基于old项目: autoFormation方法
   * 使用executeBusinessOperation进行完整的业务操作监控
   * Controller层已验证参数，无需重复验证
   */
  async autoFormation(characterId: string, formationId: string, heroIds: string[]): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`自动布阵: ${characterId}, 阵容: ${formationId}, 球员数: ${heroIds.length}`);

      const teamFormationsResult = await this.getFormations(characterId);
      if (XResultUtils.isFailure(teamFormationsResult)) {
        return XResultUtils.error(`获取角色阵容数据失败: ${teamFormationsResult.message}`, teamFormationsResult.code);
      }

      const teamFormations = teamFormationsResult.data;
      if (!teamFormations) {
        return XResultUtils.error(`角色阵容数据不存在: ${characterId}`, 'FORMATIONS_NOT_FOUND');
      }

      const formation = teamFormations.teamFormations.find(f => f.uid === formationId);
      if (!formation) {
        return XResultUtils.error(`阵容不存在: ${formationId}`, 'FORMATION_NOT_FOUND');
      }

      // 获取阵型配置 - 需要通过TeamFormation获取阵型名称，然后查找FormationCoordinate
      const teamFormationConfig = await this.gameConfig.teamFormation.get(formation.resId);
      if (!teamFormationConfig) {
        return XResultUtils.error(`阵型基础配置不存在: ${formation.resId}`, 'TEAM_FORMATION_CONFIG_NOT_FOUND');
      }

      // 通过阵型名称查找坐标配置
      const allFormationCoordinates = await this.gameConfig.formationCoordinate.getAll();
      const formationConfig = allFormationCoordinates.find(config => config.type === teamFormationConfig.name);
      if (!formationConfig) {
        return XResultUtils.error(`阵型坐标配置不存在: ${teamFormationConfig.name}`, 'FORMATION_COORDINATE_NOT_FOUND');
      }

      // 获取球员信息（需要调用Hero服务）
      const heroesInfo = await this.getHeroesInfo(heroIds);
      if (heroesInfo.length === 0) {
        return XResultUtils.error('没有可用球员', 'NO_AVAILABLE_HEROES');
      }

      // 执行自动布阵算法
      const autoFormationResult = this.executeAutoFormation(formationConfig, heroesInfo);

      // 更新阵容
      formation.positionToHerosObject = autoFormationResult.positionMapping;
      await this.formationRepository.updateFormation(
        teamFormations.characterId,
        formation.uid,
        formation
      );

      // 重新计算属性
      await this.recalculateFormationAttributes(formation, characterId);

      return XResultUtils.ok({
        formation,
        positionMapping: autoFormationResult.positionMapping,
        unassignedHeroes: autoFormationResult.unassignedHeroes,
      });
    }, { reason: 'auto_formation', metadata: { formationId, heroCount: heroIds.length } });
  }

  /**
   * 重新计算阵容属性
   * 基于old项目: reCalcTeamFormationAttrByUid方法
   *
   * 实现逻辑：
   * 1. 检查是否为主力阵容
   * 2. 重新计算每个球员的属性
   * 3. 计算球员的进攻和防守值
   * 4. 计算阵容基础进攻/防守值
   * 5. 应用鼓舞加成
   * 6. 计算实际战力
   * 7. 计算球队身价
   * 8. 触发相关任务
   */
  async recalculateFormationAttributes(formation: TeamFormation, characterId: string): Promise<XResult<void>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`重新计算阵容属性: ${formation.uid}`);

      // 1. 检查阵容是否存在
      if (!formation) {
        return XResultUtils.error('阵容不存在', 'FORMATION_NOT_FOUND');
      }

      // 2. 检查是否为主力阵容（基于old项目checkIsMainTeamByUid逻辑）
      const isMainFormationResult = await this.checkIsMainFormation(characterId, formation.uid);
      if (XResultUtils.isFailure(isMainFormationResult)) {
        return XResultUtils.error(`检查主力阵容失败: ${isMainFormationResult.message}`, isMainFormationResult.code);
      }

      if (!isMainFormationResult.data) {
        this.logger.log('非主力阵容，跳过属性计算', formation.uid);
        return XResultUtils.ok(undefined);
      }

      // 3. 重新计算每个球员的属性和进攻防守值
      const recalcHeroesResult = await this.recalculateHeroesAttributes(formation);
      if (XResultUtils.isFailure(recalcHeroesResult)) {
        return XResultUtils.error(`重新计算球员属性失败: ${recalcHeroesResult.message}`, recalcHeroesResult.code);
      }

      // 4. 计算阵容基础进攻值
      const baseAttackResult = await this.calculateTeamBaseAttack(formation);
      if (XResultUtils.isFailure(baseAttackResult)) {
        return XResultUtils.error(`计算基础进攻值失败: ${baseAttackResult.message}`, baseAttackResult.code);
      }

      // 5. 计算阵容基础防守值
      const baseDefendResult = await this.calculateTeamBaseDefend(formation);
      if (XResultUtils.isFailure(baseDefendResult)) {
        return XResultUtils.error(`计算基础防守值失败: ${baseDefendResult.message}`, baseDefendResult.code);
      }

      // 6. 计算实际战力
      const actualStrengthResult = await this.calculateTotalRating(formation);
      if (XResultUtils.isFailure(actualStrengthResult)) {
        return XResultUtils.error(`计算实际战力失败: ${actualStrengthResult.message}`, actualStrengthResult.code);
      }

      const baseAttack = baseAttackResult.data;
      const baseDefend = baseDefendResult.data;
      const actualStrength = actualStrengthResult.data;

      // 7. 应用鼓舞加成（基于old项目InspireRate）
      const inspireRate = (formation as any).inspireRate || 0;
      const finalAttack = baseAttack + (baseAttack * inspireRate);
      const finalDefend = baseDefend + (baseDefend * inspireRate);

      // 8. 更新阵容属性
      await this.formationRepository.update(formation.uid, {
        attack: Math.round(finalAttack),
        defend: Math.round(finalDefend),
        actualStrength: Math.round(actualStrength),
        lastCalculated: new Date(),
      });

      // 9. 计算球队身价
      const teamValueResult = await this.calculateTeamValue(formation);
      if (XResultUtils.isFailure(teamValueResult)) {
        this.logger.warn(`计算球队身价失败: ${teamValueResult.message}`);
      }

      // 10. 触发相关任务（基于old项目triggerTask逻辑）
      const triggerTaskResult = await this.triggerStrengthUpTask(characterId, actualStrength);
      if (XResultUtils.isFailure(triggerTaskResult)) {
        this.logger.warn(`触发任务失败: ${triggerTaskResult.message}`);
      }

      // 11. 更新联赛实力（如果是当前使用阵容）
      const updateLeagueResult = await this.updateLeagueStrength(characterId, formation.uid, actualStrength);
      if (XResultUtils.isFailure(updateLeagueResult)) {
        this.logger.warn(`更新联赛实力失败: ${updateLeagueResult.message}`);
      }

      this.logger.log(`阵容属性计算完成: ${formation.uid}, 进攻: ${finalAttack}, 防守: ${finalDefend}, 战力: ${actualStrength}`);
      return XResultUtils.ok(undefined);
    }, { reason: 'recalculate_formation_attributes', metadata: { formationId: formation.uid } });
  }

  /**
   * 检查是否为主力阵容
   * 基于old项目: checkIsMainTeamByUid方法
   * 使用Result模式，正确处理微服务调用结果
   */
  private async checkIsMainFormation(characterId: string, formationId: string): Promise<XResult<boolean>> {
    try {
      // TODO: 调用Character服务获取当前主力阵容ID
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'character.getCurrentFormationId',
      //   { characterId }
      // );
      //
      // if (result && result.code === 0) {
      //   return XResultUtils.ok(result.data.currentFormationId === formationId);
      // } else {
      //   return XResultUtils.error('获取当前主力阵容ID失败', 'GET_CURRENT_FORMATION_FAILED');
      // }

      // 暂时返回true，假设都是主力阵容
      return XResultUtils.ok(true);
    } catch (error) {
      this.logger.error('检查主力阵容失败', error);
      return XResultUtils.error('检查主力阵容失败', 'CHECK_MAIN_FORMATION_ERROR');
    }
  }

  /**
   * 重新计算球员属性
   * 基于old项目: reCalcAttr + calcBallerAttackAndDefend逻辑
   * 使用Result模式，正确处理每个步骤的结果
   */
  private async recalculateHeroesAttributes(formation: any): Promise<XResult<void>> {
    try {
      const positionToHeroes = formation.positionToHerosObject || {};

      for (const position in positionToHeroes) {
        const heroIds = positionToHeroes[position];
        if (Array.isArray(heroIds)) {
          for (const heroId of heroIds) {
            // 1. 重新计算球员基础属性
            const baseAttrResult = await this.recalculateHeroBaseAttributes(heroId);
            if (XResultUtils.isFailure(baseAttrResult)) {
              this.logger.warn(`重新计算球员基础属性失败: ${heroId}, ${baseAttrResult.message}`);
              continue; // 继续处理其他球员
            }

            // 2. 计算球员在该阵容中的进攻和防守值
            const attackDefendResult = await this.calculateHeroAttackAndDefend(heroId, formation.uid, position);
            if (XResultUtils.isFailure(attackDefendResult)) {
              this.logger.warn(`计算球员攻防值失败: ${heroId}, ${attackDefendResult.message}`);
              continue; // 继续处理其他球员
            }
          }
        }
      }

      return XResultUtils.ok(undefined);
    } catch (error) {
      this.logger.error('重新计算球员属性失败', error);
      return XResultUtils.error('重新计算球员属性失败', 'RECALC_HEROES_ATTR_ERROR');
    }
  }

  /**
   * 重新计算球员基础属性
   * 基于old项目: reCalcAttr方法
   * 使用Result模式，正确处理微服务调用结果
   */
  private async recalculateHeroBaseAttributes(heroId: string): Promise<XResult<void>> {
    try {
      // TODO: 调用Hero服务重新计算球员属性
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.HERO_SERVICE,
      //   'hero.cultivation.reCalcAttrRevision',
      //   { heroId }
      // );
      //
      // if (result && result.code === 0) {
      //   this.logger.debug(`重新计算球员基础属性成功: ${heroId}`);
      //   return XResultUtils.ok(undefined);
      // } else {
      //   return XResultUtils.error('重新计算球员基础属性失败', 'RECALC_HERO_BASE_ATTR_FAILED');
      // }

      this.logger.debug(`重新计算球员基础属性: ${heroId}`);
      return XResultUtils.ok(undefined);
    } catch (error) {
      this.logger.error(`重新计算球员基础属性失败: ${heroId}`, error);
      return XResultUtils.error('重新计算球员基础属性失败', 'RECALC_HERO_BASE_ATTR_ERROR');
    }
  }

  /**
   * 计算球员进攻和防守值
   * 基于old项目: calcBallerAttackAndDefend方法
   * 使用Result模式，正确处理微服务调用结果
   */
  private async calculateHeroAttackAndDefend(heroId: string, formationId: string, position: string): Promise<XResult<void>> {
    try {
      // TODO: 调用Hero服务计算球员进攻防守值
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.HERO_SERVICE,
      //   'hero.calculateAttackAndDefend',
      //   { heroId, formationId, position }
      // );
      //
      // if (result && result.code === 0) {
      //   this.logger.debug(`计算球员进攻防守值成功: ${heroId}, 位置: ${position}`);
      //   return XResultUtils.ok(undefined);
      // } else {
      //   return XResultUtils.error('计算球员进攻防守值失败', 'CALC_HERO_ATTACK_DEFEND_FAILED');
      // }

      this.logger.debug(`计算球员进攻防守值: ${heroId}, 位置: ${position}`);
      return XResultUtils.ok(undefined);
    } catch (error) {
      this.logger.error(`计算球员进攻防守值失败: ${heroId}`, error);
      return XResultUtils.error('计算球员进攻防守值失败', 'CALC_HERO_ATTACK_DEFEND_ERROR');
    }
  }

  /**
   * 计算阵容基础进攻值
   * 基于old项目: calcTeamBaseAttack方法
   *
   * 计算规则：
   * 上场球员1球员_进攻值当前值*上场球员1阵型位置进攻系数/1000
   * +上场球员2球员_进攻值当前值*上场球员2阵型位置进攻系数/1000
   * +...
   * +上场球员11球员_进攻值当前值*上场球员11阵型位置进攻系数/1000
   * +经理等级*10
   */
  private async calculateTeamBaseAttack(formation: TeamFormation): Promise<XResult<number>> {
    try {
      // TODO: 完整实现阵容基础进攻值计算
      // let totalAttack = 0;
      // const positionToHeroes = formation.positionToHerosObject || {};
      // const formationResId = formation.resId;
      //
      // // 遍历每个位置的球员
      // for (const position in positionToHeroes) {
      //   const heroIds = positionToHeroes[position];
      //   if (Array.isArray(heroIds)) {
      //     // 获取位置进攻系数
      //     const formationConfig = await this.gameConfig.teamFormation?.get(formationResId);
      //     const attackRateResult = PositionRateCalculator.getAttackRate(formationConfig, position);
      //     const attackRate = XResultUtils.isSuccess(attackRateResult) ? attackRateResult.data : 1000;
      //
      //     for (const heroId of heroIds) {
      //       // 获取球员在该阵容中的进攻值
      //       const heroAttackValue = await this.getHeroAttackValue(heroId, formation.uid);
      //
      //       // 计算位置加成后的进攻值
      //       const positionAttackValue = (heroAttackValue * 100 * attackRate) / 1000 / 100;
      //       totalAttack += positionAttackValue;
      //     }
      //   }
      // }
      //
      // // 添加经理等级加成（基于old项目：经理等级*10）
      // const managerLevel = await this.getCharacterLevel(formation.characterId);
      // totalAttack += managerLevel * 10;
      //
      // return XResultUtils.ok(Math.round(totalAttack));

      // 暂时返回模拟值
      const mockAttackValue = 1000;
      return XResultUtils.ok(mockAttackValue);
    } catch (error) {
      this.logger.error('计算阵容基础进攻值失败', error);
      return XResultUtils.error('计算阵容基础进攻值失败', 'CALC_TEAM_BASE_ATTACK_ERROR');
    }
  }

  /**
   * 计算阵容基础防守值
   * 基于old项目: calcTeamBaseDefend方法
   *
   * 计算规则：
   * 上场球员1球员_防守值当前值*上场球员1阵型位置防守系数/1000
   * +上场球员2球员_防守值当前值*上场球员2阵型位置防守系数/1000
   * +...
   * +经理等级*8
   */
  private async calculateTeamBaseDefend(formation: TeamFormation): Promise<XResult<number>> {
    try {
      // TODO: 完整实现阵容基础防守值计算
      // let totalDefend = 0;
      // const positionToHeroes = formation.positionToHerosObject || {};
      // const formationResId = formation.resId;
      //
      // // 遍历每个位置的球员
      // for (const position in positionToHeroes) {
      //   const heroIds = positionToHeroes[position];
      //   if (Array.isArray(heroIds)) {
      //     // 获取位置防守系数
      //     const formationConfig = await this.gameConfig.teamFormation?.get(formationResId);
      //     const defendRateResult = PositionRateCalculator.getDefendRate(formationConfig, position);
      //     const defendRate = XResultUtils.isSuccess(defendRateResult) ? defendRateResult.data : 1000;
      //
      //     for (const heroId of heroIds) {
      //       // 获取球员在该阵容中的防守值
      //       const heroDefendValue = await this.getHeroDefendValue(heroId, formation.uid);
      //
      //       // 计算位置加成后的防守值
      //       const positionDefendValue = (heroDefendValue * 100 * defendRate) / 1000 / 100;
      //       totalDefend += positionDefendValue;
      //     }
      //   }
      // }
      //
      // // 添加经理等级加成（基于old项目：经理等级*8）
      // const managerLevel = await this.getCharacterLevel(formation.characterId);
      // totalDefend += managerLevel * 8;
      //
      // return XResultUtils.ok(Math.round(totalDefend));

      // 暂时返回模拟值
      const mockDefendValue = 800;
      return XResultUtils.ok(mockDefendValue);
    } catch (error) {
      this.logger.error('计算阵容基础防守值失败', error);
      return XResultUtils.error('计算阵容基础防守值失败', 'CALC_TEAM_BASE_DEFEND_ERROR');
    }
  }

  // 已迁移到PositionRateCalculator.getAttackRate

  // 已迁移到PositionRateCalculator.getDefendRate

  /**
   * 获取位置ID
   * 基于old项目: TEAM_FORMATION_CONFIG_POSITION_TO_ID映射
   */
  private getPositionId(position: string): number {
    const positionMapping = {
      'GK': 1,   // 门将
      'DL': 2,   // 左后卫
      'DC': 3,   // 中后卫
      'DR': 4,   // 右后卫
      'ML': 5,   // 左中场
      'MC': 6,   // 中场
      'MR': 7,   // 右中场
      'WL': 8,   // 左边锋
      'ST': 9,   // 前锋
      'WR': 10,  // 右边锋
      'AM': 11,  // 前腰
      'DM': 12,  // 后腰
    };

    return positionMapping[position] || 6; // 默认中场
  }

  /**
   * 获取球员在阵容中的进攻值
   * 基于old项目: hero.AttackAndDefend[uid].attack
   */
  private async getHeroAttackValue(heroId: string, formationId: string): Promise<XResult<number>> {
    try {
      // TODO: 调用Hero服务获取球员在该阵容中的进攻值
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.HERO_SERVICE,
      //   'hero.getAttackValueInFormation',
      //   { heroId, formationId }
      // );
      // return result.data.attackValue || 0;

      // 暂时返回模拟值
      return XResultUtils.ok(50);// 默认
    } catch (error) {
      this.logger.error(`获取球员进攻值失败: ${heroId}`, error);
      return XResultUtils.ok(0);// 默认
    }
  }

  /**
   * 获取球员在阵容中的防守值
   * 基于old项目: hero.AttackAndDefend[uid].defend
   */
  private async getHeroDefendValue(heroId: string, formationId: string): Promise<XResult<number>> {
    try {
      // TODO: 调用Hero服务获取球员在该阵容中的防守值
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.HERO_SERVICE,
      //   'hero.getDefendValueInFormation',
      //   { heroId, formationId }
      // );
      // return result.data.defendValue || 0;

      // 暂时返回模拟值
      return XResultUtils.ok(45);// 默认
    } catch (error) {
      this.logger.error(`获取球员防守值失败: ${heroId}`, error);
      return XResultUtils.ok(0);// 默认
    }
  }

  /**
   * 计算实际战力
   * 基于old项目: calcTotalRating方法
   */
  private async calculateTotalRating(formation: TeamFormation): Promise<XResult<number>> {
    try {
      let totalRating = 0;
      const positionToHeroes = formation.positionToHerosObject || {};

      // 遍历每个位置的球员，累加实力值
      for (const position in positionToHeroes) {
        const heroIds = positionToHeroes[position];
        if (Array.isArray(heroIds)) {
          for (const heroId of heroIds) {
            const heroRatingResult = await this.getHeroTotalPower(heroId);
            if (XResultUtils.isFailure(heroRatingResult)) {
              this.logger.warn(`获取球员总实力失败: ${heroId}, ${heroRatingResult.message}`);
              continue; // 继续处理其他球员
            }
            totalRating += heroRatingResult.data;
          }
        }
      }

      return XResultUtils.ok(Math.round(totalRating));
    } catch (error) {
      this.logger.error('计算实际战力失败', error);
      return XResultUtils.error('计算实际战力失败', 'CALC_TOTAL_RATING_ERROR');
    }
  }

  /**
   * 获取球员总实力
   * 基于old项目: hero.TotalPower
   * 使用Result模式，正确处理微服务调用结果
   */
  private async getHeroTotalPower(heroId: string): Promise<XResult<number>> {
    try {
      // TODO: 调用Hero服务获取球员总实力
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.HERO_SERVICE,
      //   'hero.getTotalPower',
      //   { heroId }
      // );
      //
      // if (result && result.code === 0) {
      //   return XResultUtils.ok(result.data.totalPower || 0);
      // } else {
      //   return XResultUtils.error('获取球员总实力失败', 'GET_HERO_TOTAL_POWER_FAILED');
      // }

      // 暂时返回模拟值
      return XResultUtils.ok(100);
    } catch (error) {
      this.logger.error(`获取球员总实力失败: ${heroId}`, error);
      return XResultUtils.error('获取球员总实力失败', 'GET_HERO_TOTAL_POWER_ERROR');
    }
  }

  /**
   * 计算球队身价
   * 基于old项目: calcTeamValue方法
   * 使用Result模式，正确处理每个步骤的结果
   */
  private async calculateTeamValue(formation: any): Promise<XResult<void>> {
    try {
      let totalValue = 0;
      const positionToHeroes = formation.positionToHerosObject || {};

      // 遍历每个位置的球员，累加身价
      for (const position in positionToHeroes) {
        const heroIds = positionToHeroes[position];
        if (Array.isArray(heroIds)) {
          for (const heroId of heroIds) {
            const heroValueResult = await this.getHeroMarketValue(heroId);
            if (XResultUtils.isFailure(heroValueResult)) {
              this.logger.warn(`获取球员市场价值失败: ${heroId}, ${heroValueResult.message}`);
              continue; // 继续处理其他球员
            }
            totalValue += heroValueResult.data;
          }
        }
      }

      // 更新阵容身价
      await this.formationRepository.update(formation.uid, {
        teamValue: Math.round(totalValue),
      });

      this.logger.log(`球队身价计算完成: ${formation.uid}, 总身价: ${totalValue}`);
      return XResultUtils.ok(undefined);
    } catch (error) {
      this.logger.error('计算球队身价失败', error);
      return XResultUtils.error('计算球队身价失败', 'CALC_TEAM_VALUE_ERROR');
    }
  }

  /**
   * 获取球员市场价值
   * 基于old项目: hero.MarketValue
   * 使用Result模式，正确处理微服务调用结果
   */
  private async getHeroMarketValue(heroId: string): Promise<XResult<number>> {
    try {
      // TODO: 调用Hero服务获取球员市场价值
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.HERO_SERVICE,
      //   'hero.getMarketValue',
      //   { heroId }
      // );
      //
      // if (result && result.code === 0) {
      //   return XResultUtils.ok(result.data.marketValue || 0);
      // } else {
      //   return XResultUtils.error('获取球员市场价值失败', 'GET_HERO_MARKET_VALUE_FAILED');
      // }

      // 暂时返回模拟值
      return XResultUtils.ok(10000);
    } catch (error) {
      this.logger.error(`获取球员市场价值失败: ${heroId}`, error);
      return XResultUtils.error('获取球员市场价值失败', 'GET_HERO_MARKET_VALUE_ERROR');
    }
  }

  /**
   * 触发实力提升任务
   * 基于old项目: newerTask.triggerTask(NEWER_TASK.STRENGTH_UP)
   * 使用Result模式，正确处理微服务调用结果
   */
  private async triggerStrengthUpTask(characterId: string, actualStrength: number): Promise<XResult<void>> {
    try {
      // TODO: 调用Activity服务触发实力提升任务
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.ACTIVITY_SERVICE,
      //   'task.triggerStrengthUpTask',
      //   { characterId, actualStrength }
      // );
      //
      // if (result && result.code === 0) {
      //   this.logger.log(`触发实力提升任务成功: ${characterId}, 实力: ${actualStrength}`);
      //   return XResultUtils.ok(undefined);
      // } else {
      //   return XResultUtils.error('触发实力提升任务失败', 'TRIGGER_STRENGTH_UP_TASK_FAILED');
      // }

      this.logger.log(`触发实力提升任务: ${characterId}, 实力: ${actualStrength}`);
      return XResultUtils.ok(undefined);
    } catch (error) {
      this.logger.error('触发实力提升任务失败', error);
      return XResultUtils.error('触发实力提升任务失败', 'TRIGGER_STRENGTH_UP_TASK_ERROR');
    }
  }

  /**
   * 更新联赛实力
   * 基于old项目: updateStrength逻辑
   * 使用Result模式，正确处理微服务调用结果
   */
  private async updateLeagueStrength(characterId: string, formationId: string, actualStrength: number): Promise<XResult<void>> {
    try {
      // 检查是否为当前使用阵容
      const isCurrentFormationResult = await this.checkIsCurrentFormation(characterId, formationId);
      if (XResultUtils.isFailure(isCurrentFormationResult)) {
        return XResultUtils.error(`检查当前阵容失败: ${isCurrentFormationResult.message}`, isCurrentFormationResult.code);
      }

      if (!isCurrentFormationResult.data) {
        this.logger.log('非当前使用阵容，跳过联赛实力更新');
        return XResultUtils.ok(undefined);
      }

      // TODO: 调用League服务更新联赛实力
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.LEAGUE_SERVICE,
      //   'league.updatePlayerStrength',
      //   { characterId, actualStrength }
      // );
      //
      // if (result && result.code === 0) {
      //   this.logger.log(`更新联赛实力成功: ${characterId}, 实力: ${actualStrength}`);
      //   return XResultUtils.ok(undefined);
      // } else {
      //   return XResultUtils.error('更新联赛实力失败', 'UPDATE_LEAGUE_STRENGTH_FAILED');
      // }

      this.logger.log(`更新联赛实力: ${characterId}, 实力: ${actualStrength}`);
      return XResultUtils.ok(undefined);
    } catch (error) {
      this.logger.error('更新联赛实力失败', error);
      return XResultUtils.error('更新联赛实力失败', 'UPDATE_LEAGUE_STRENGTH_ERROR');
    }
  }

  /**
   * 检查是否为当前使用阵容
   * 基于old项目: uid === this.currTeamFormationId
   * 使用Result模式，正确处理微服务调用结果
   */
  private async checkIsCurrentFormation(characterId: string, formationId: string): Promise<XResult<boolean>> {
    try {
      // TODO: 调用Character服务检查当前阵容
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'character.getCurrentFormationId',
      //   { characterId }
      // );
      //
      // if (result && result.code === 0) {
      //   return XResultUtils.ok(result.data.currentFormationId === formationId);
      // } else {
      //   return XResultUtils.error('获取当前阵容ID失败', 'GET_CURRENT_FORMATION_ID_FAILED');
      // }

      // 暂时返回true
      return XResultUtils.ok(true);
    } catch (error) {
      this.logger.error('检查当前阵容失败', error);
      return XResultUtils.error('检查当前阵容失败', 'CHECK_CURRENT_FORMATION_ERROR');
    }
  }

  /**
   * 执行自动布阵算法核心逻辑
   * 基于old项目的智能布阵算法
   */
  private executeAutoFormation(formationConfig: any, heroesInfo: any[]): any {
    const positionMapping = {
      GK: [], DL: [], DC: [], DR: [], ML: [], MC: [],
      MR: [], WL: [], ST: [], WR: [], AM: [], DM: []
    };
    const unassignedHeroes: string[] = [];

    // 按位置优先级和球员适应性进行布阵
    // 注意：位置字段必须与Schema定义和old项目完全一致（大写）
    const positionPriority = ['GK', 'DL', 'DC', 'DR', 'ML', 'MC', 'MR', 'WL', 'ST', 'WR', 'AM', 'DM'];

    this.logger.log(`开始自动布阵，位置优先级: ${positionPriority.join(', ')}`);
    this.logger.log(`可用球员数量: ${heroesInfo.length}`);

    for (const position of positionPriority) {
      const requiredCount = this.getPositionRequiredCount(formationConfig, position);
      this.logger.log(`处理位置 ${position}，需要球员数: ${requiredCount}`);

      if (requiredCount > 0) {
        const suitableHeroes = this.findSuitableHeroes(heroesInfo, position, requiredCount);
        this.logger.log(`位置 ${position} 找到合适球员: ${suitableHeroes.length}个`);

        if (suitableHeroes.length > 0) {
          const heroIds = suitableHeroes.map(hero => hero.uid);
          positionMapping[position] = heroIds;
          this.logger.log(`位置 ${position} 分配球员: ${heroIds.join(', ')}`);

          // 从可用球员中移除已分配的球员
          suitableHeroes.forEach(hero => {
            const index = heroesInfo.findIndex(h => h.uid === hero.uid);
            if (index > -1) {
              heroesInfo.splice(index, 1);
            }
          });
          this.logger.log(`剩余可用球员数量: ${heroesInfo.length}`);
        } else {
          this.logger.warn(`位置 ${position} 没有找到合适的球员`);
        }
      } else {
        this.logger.log(`位置 ${position} 不需要球员`);
      }
    }

    this.logger.log(`自动布阵完成，最终位置映射: ${JSON.stringify(positionMapping)}`);

    // 剩余球员加入未分配列表
    unassignedHeroes.push(...heroesInfo.map(hero => hero.uid));

    return { positionMapping, unassignedHeroes };
  }

  /**
   * 获取角色的所有球员
   * 使用Result模式，正确处理微服务调用结果
   * Controller层已验证参数，无需重复验证
   */
  async getCharacterHeroes(characterId: string): Promise<XResult<any[]>> {
    this.logger.log(`获取角色球员列表: ${characterId}`);

    // 使用内部HeroService获取角色球员列表
    const result = await this.heroService.getHeroes(characterId);

    if (XResultUtils.isSuccess(result)) {
      return XResultUtils.ok(result.data || []);
    } else {
      this.logger.warn(`获取球员列表失败: ${result.message}`);
      return XResultUtils.error(`获取球员列表失败: ${result.message}`, result.code);
    }
  }

  /**
   * 获取球员信息 - 微服务通信参考实现
   * 调用Hero服务获取球员详细信息
   *
   * 使用说明：
   * 1. 确保Hero服务已启动并可通信
   * 2. 取消注释下面的微服务调用代码
   * 3. 确保Hero服务有getBatchHeroes接口
   */
  private async getHeroesInfo(heroIds: string[]): Promise<any[]> {
    this.logger.log(`获取球员详细信息: ${heroIds.length}个球员`);

    // 使用内部HeroService逐个获取球员信息
    const heroes = [];
    for (const heroId of heroIds) {
      const heroResult = await this.heroService.getHeroInfo(heroId);

      if (XResultUtils.isSuccess(heroResult) && heroResult.data) {
        heroes.push(heroResult.data);
        this.logger.log(`成功获取球员${heroId}信息`);
      } else {
        this.logger.warn(`获取球员${heroId}信息失败: ${heroResult.message}`);
      }
    }

    if (heroes.length > 0) {
      this.logger.log(`成功获取${heroes.length}/${heroIds.length}个球员信息`);
    }

    return heroes;
  }

  /**
   * 获取位置所需球员数量
   */
  private getPositionRequiredCount(formationConfig: any, position: string): number {
    // TODO: 根据阵型配置获取每个位置的球员数量
    const defaultCounts = {
      GK: 1, DC: 2, DL: 1, DR: 1,
      MC: 2, DM: 1, AM: 1,
      ML: 1, MR: 1, ST: 1, WL: 1, WR: 1
    };
    return defaultCounts[position] || 0;
  }

  /**
   * 查找适合位置的球员
   */
  private findSuitableHeroes(heroesInfo: any[], position: string, requiredCount: number): any[] {
    this.logger.log(`查找位置 ${position} 的合适球员，需要 ${requiredCount} 个`);
    this.logger.log(`可选球员数量: ${heroesInfo.length}`);

    // 检查球员的适应性数据
    heroesInfo.forEach((hero, index) => {
      const adaptability = hero.adaptability ? hero.adaptability[position] : 'undefined';
      this.logger.log(`球员${index + 1} ${hero.name} (${hero.uid}) 在位置 ${position} 的适应性: ${adaptability}`);
    });

    // 首先尝试找适应性大于50的球员
    let suitableHeroes = heroesInfo
      .filter(hero => hero.adaptability && hero.adaptability[position] > 50) // 适应性大于50
      .sort((a, b) => b.adaptability[position] - a.adaptability[position])
      .slice(0, requiredCount);

    this.logger.log(`适应性>50的球员: ${suitableHeroes.length}个`);

    // 如果没有找到足够的适应性大于50的球员，降低要求到30
    if (suitableHeroes.length < requiredCount) {
      const additionalHeroes = heroesInfo
        .filter(hero => hero.adaptability && hero.adaptability[position] >= 30 && hero.adaptability[position] <= 50) // 适应性30-50
        .sort((a, b) => b.adaptability[position] - a.adaptability[position])
        .slice(0, requiredCount - suitableHeroes.length);

      this.logger.log(`适应性30-50的球员: ${additionalHeroes.length}个`);
      suitableHeroes = [...suitableHeroes, ...additionalHeroes];
    }

    this.logger.log(`最终选择的球员: ${suitableHeroes.map(hero => `${hero.name}(${hero.adaptability[position]})`).join(', ')}`);
    return suitableHeroes;
  }

  // ==================== 微服务通信参考代码实现 ====================
  // 以下方法提供完整的微服务通信参考代码，包含详细的使用说明

  /**
   * 删除球员攻防数据 - 微服务通信参考实现
   * 基于old项目: DelPlayerAttackAndDefend方法
   *
   * 使用说明：
   * 1. 确保Hero服务已启动并可通信
   * 2. 在上面的TODO位置取消注释调用此方法
   * 3. 确保Hero服务有updateHeroFormationData接口
   */
  private async removeHeroAttackAndDefendData(heroId: string, formationId: string): Promise<XResult<void>> {
    this.logger.log(`删除球员攻防数据: ${heroId}, 阵容: ${formationId}`);

    try {
      // 调用Hero服务清除球员的阵容相关数据
      // 参考代码：完整的微服务调用实现
      /*
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.HERO_SERVICE,
        'hero.updateHeroFormationData',
        {
          heroId,
          formationId,
          action: 'remove',
          clearAttackDefend: true
        }
      );

      if (result.code === 0) {
        this.logger.log(`球员攻防数据清除成功: ${heroId}`);
        return XResultUtils.ok(undefined);
      } else {
        this.logger.error('球员攻防数据清除失败', result);
        return XResultUtils.error('球员攻防数据清除失败', 'CLEAR_HERO_DATA_FAILED');
      }
      */

      this.logger.debug('球员攻防数据清除成功（模拟）');
      return XResultUtils.ok(undefined);
    } catch (error) {
      this.logger.error('删除球员攻防数据失败', error);
      return XResultUtils.error('删除球员攻防数据失败', 'REMOVE_HERO_DATA_ERROR');
    }
  }

  /**
   * 重新计算阵容属性 - 参考实现
   * 基于old项目: reCalcTeamFormationAttrByUid方法
   *
   * 使用说明：
   * 1. 确保Hero服务可以提供球员详细属性
   * 2. 在上面的TODO位置取消注释调用此方法
   * 3. 根据实际需求调整属性计算逻辑
   */
  private async reCalcTeamFormationAttrByUid(formationId: string): Promise<XResult<void>> {
    this.logger.log(`重新计算阵容属性: ${formationId}`);

    try {
      // 获取阵容信息
      // 参考代码：完整的属性计算实现
      /*
      const formation = await this.getFormationById(formationId);
      if (!formation) {
        this.logger.error('阵容不存在', formationId);
        return XResultUtils.error('阵容不存在', 'FORMATION_NOT_FOUND');
      }

      // 获取阵容中所有球员的详细信息
      const allHeroIds = Object.values(formation.positionToHerosObject).flat();
      const heroesInfo = await this.getHeroesInfo(allHeroIds);

      // 计算总体属性
      let totalAttack = 0;
      let totalDefend = 0;
      let totalSpeed = 0;
      let totalPassing = 0;

      for (const hero of heroesInfo) {
        totalAttack += hero.attributes?.shooting || 0;
        totalDefend += hero.attributes?.defending || 0;
        totalSpeed += hero.attributes?.speed || 0;
        totalPassing += hero.attributes?.passing || 0;
      }

      // 计算战术加成
      const tacticsBonus = await this.calculateTacticsBonus(formation);

      // 计算教练加成
      const coachBonus = await this.calculateCoachBonus(formation);

      // 更新阵容属性
      const updatedAttributes = {
        totalAttack: totalAttack + tacticsBonus.attack + coachBonus.attack,
        totalDefend: totalDefend + tacticsBonus.defend + coachBonus.defend,
        totalSpeed: totalSpeed + tacticsBonus.speed + coachBonus.speed,
        totalPassing: totalPassing + tacticsBonus.passing + coachBonus.passing,
        overallRating: Math.floor((totalAttack + totalDefend + totalSpeed + totalPassing) / 4),
        lastCalculated: new Date()
      };

      // 保存更新后的属性
      await this.updateFormationAttributes(formationId, updatedAttributes);

      this.logger.log(`阵容属性计算完成: ${formationId}, 总评: ${updatedAttributes.overallRating}`);
      */

      this.logger.debug('阵容属性重新计算完成（模拟）');
      return XResultUtils.ok(undefined);
    } catch (error) {
      this.logger.error('重新计算阵容属性失败', error);
      return XResultUtils.error('重新计算阵容属性失败', 'RECALC_FORMATION_ATTR_ERROR');
    }
  }

  // ========== 球员阵容相关查询方法 ==========

  /**
   * 获取球员在主力阵容中的位置信息
   * 基于old项目: getHeroInMainTeamUid方法
   * 严格遵循Result模式，无异常抛出
   *
   * @param heroId 球员ID
   * @returns 球员阵容位置信息
   */
  async getHeroInMainTeamId(heroId: string): Promise<XResult<{
    isInMainFormation: boolean;
    teamId: string | null;
    position: string | null;
    formationType: FormationType | null;
  }>> {
    this.logger.log(`获取球员主力阵容信息: ${heroId}`);

    // 1. 获取球员所属角色ID
    const heroResult = await this.heroService.getHeroInfo(heroId);

    if (XResultUtils.isFailure(heroResult)) {
      return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
    }

    const characterId = heroResult.data?.characterId;
    if (!characterId) {
      return XResultUtils.error('球员未关联角色', 'HERO_NO_CHARACTER');
    }

    // 2. 获取角色阵容数据
    const formationsResult = await this.getFormations(characterId);
    if (XResultUtils.isFailure(formationsResult) || !formationsResult.data) {
      return XResultUtils.ok({
        isInMainFormation: false,
        teamId: null,
        position: null,
        formationType: null
      });
    }

    const formations = formationsResult.data;

    // 3. 遍历所有阵容查找球员位置（基于old项目逻辑）
    for (const teamFormation of formations.teamFormations) {
      // 检查是否为主力阵容
      if (!this.checkIsMainTeamByUid(formations, teamFormation.uid)) {
        continue;
      }

      // 在阵容中查找球员
      const positionToHerosObject = teamFormation.positionToHerosObject;
      for (const [position, heroIds] of Object.entries(positionToHerosObject)) {
        if (heroIds.includes(heroId)) {
          return XResultUtils.ok({
            isInMainFormation: true,
            teamId: teamFormation.uid,
            position: position,
            formationType: teamFormation.type as FormationType
          });
        }
      }
    }

    // 4. 未找到球员
    return XResultUtils.ok({
      isInMainFormation: false,
      teamId: null,
      position: null,
      formationType: null
    });
  }

  /**
   * 从所有阵容中移除球员
   * 基于old项目: 球员退役、转会时的阵容清理逻辑
   * 严格遵循Result模式，支持事务
   *
   * @param characterId 角色ID
   * @param heroId 球员ID
   * @returns 移除结果
   */
  async removeHeroFromAllFormations(characterId: string, heroId: string): Promise<XResult<{
    removedFromFormations: string[];
    totalRemoved: number;
  }>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`从所有阵容中移除球员: ${characterId}, 球员: ${heroId}`);

      // 1. 获取角色阵容数据
      const formationsResult = await this.getFormations(characterId);
      if (XResultUtils.isFailure(formationsResult) || !formationsResult.data) {
        return XResultUtils.ok({
          removedFromFormations: [],
          totalRemoved: 0
        });
      }

      const formations = formationsResult.data;
      const removedFromFormations: string[] = [];
      let totalRemoved = 0;

      // 2. 遍历所有阵容移除球员
      for (const teamFormation of formations.teamFormations) {
        let removedFromThisFormation = false;

        // 遍历所有位置
        for (const [position, heroIds] of Object.entries(teamFormation.positionToHerosObject)) {
          const heroIndex = heroIds.indexOf(heroId);
          if (heroIndex !== -1) {
            // 移除球员
            heroIds.splice(heroIndex, 1);
            removedFromThisFormation = true;
            totalRemoved++;
            this.logger.log(`从阵容 ${teamFormation.uid} 位置 ${position} 移除球员 ${heroId}`);
          }
        }

        if (removedFromThisFormation) {
          removedFromFormations.push(teamFormation.uid);
        }
      }

      // 3. 保存更新后的阵容数据
      if (totalRemoved > 0) {
        const updateResult = await this.formationRepository.update(formations._id.toString(), formations.toObject());
        if (XResultUtils.isFailure(updateResult)) {
          return XResultUtils.error(`保存阵容数据失败: ${updateResult.message}`, updateResult.code);
        }

        // 4. 重新计算受影响阵容的属性
        for (const formationId of removedFromFormations) {
          await this.reCalcTeamFormationAttrByUid(formationId);
        }
      }

      return XResultUtils.ok({
        removedFromFormations,
        totalRemoved
      });
    }, { reason: 'remove_hero_from_formations', metadata: { characterId, heroId } });
  }

  // ========== 阵容加成计算方法 ==========

  /**
   * 计算教练属性加成
   * 基于old项目: calcHeroTrainerAttr方法
   * 严格遵循Result模式，无异常抛出
   *
   * @param heroId 球员ID
   * @param teamId 阵容ID
   * @returns 教练属性加成
   */
  async calcHeroTrainerAttr(heroId: string, teamId: string): Promise<XResult<{
    speed: number;
    shooting: number;
    passing: number;
    defending: number;
    physical: number;
  }>> {
    this.logger.log(`计算教练属性加成: 球员${heroId}, 阵容${teamId}`);

    // 1. 获取球员信息
    const heroResult = await this.heroService.getHeroInfo(heroId);

    if (XResultUtils.isFailure(heroResult)) {
      return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
    }

    const characterId = heroResult.data?.characterId;
    if (!characterId) {
      return XResultUtils.error('球员未关联角色', 'HERO_NO_CHARACTER');
    }

    // 2. 获取阵容数据
    const formationsResult = await this.getFormations(characterId);
    if (XResultUtils.isFailure(formationsResult) || !formationsResult.data) {
      return XResultUtils.ok({ speed: 0, shooting: 0, passing: 0, defending: 0, physical: 0 });
    }

    const formations = formationsResult.data;
    const teamFormation = formations.teamFormations.find(tf => tf.uid === teamId);
    if (!teamFormation) {
      return XResultUtils.ok({ speed: 0, shooting: 0, passing: 0, defending: 0, physical: 0 });
    }

    // 3. 计算教练属性加成（基于old项目逻辑）
    const attributeBonus = {
      speed: 0,
      shooting: 0,
      passing: 0,
      defending: 0,
      physical: 0
    };

    // 遍历阵容中的所有教练
    for (const trainer of teamFormation.trainers) {
      if (!trainer.uid || trainer.uid === '') {
        continue;
      }

      // 获取教练信息
      const trainerResult = await this.callMicroservice(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'trainer.getTrainerInfo',
        { trainerId: trainer.uid }
      );

      if (XResultUtils.isFailure(trainerResult)) {
        continue;
      }

      const trainerData = trainerResult.data;
      if (!trainerData || !trainerData.oneLevelAttr) {
        continue;
      }

      // 根据教练位置计算相应的加成（基于old项目calcTrainerPropertyAttr逻辑）
      CoachCalculator.calcTrainerPropertyAttr(attributeBonus, trainerData.oneLevelAttr, trainer.type);
    }

    return XResultUtils.ok(attributeBonus);
  }

  /**
   * 计算球员战术加成
   * 基于old项目: calcHeroTacticsAttr方法
   * 改为public方法供外部调用，严格遵循Result模式
   *
   * @param heroId 球员ID
   * @param teamId 阵容ID
   * @returns 战术属性加成
   */
  async calcHeroTacticsAttr(heroId: string, teamId: string): Promise<XResult<{
    attack: number;
    defend: number;
  }>> {
    this.logger.log(`计算球员战术加成: 球员${heroId}, 阵容${teamId}`);

    // 1. 获取球员信息
    const heroResult = await this.heroService.getHeroInfo(heroId);

    if (XResultUtils.isFailure(heroResult)) {
      return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
    }

    const characterId = heroResult.data?.characterId;
    if (!characterId) {
      return XResultUtils.error('球员未关联角色', 'HERO_NO_CHARACTER');
    }

    // 2. 获取阵容数据
    const formationsResult = await this.getFormations(characterId);
    if (XResultUtils.isFailure(formationsResult) || !formationsResult.data) {
      return XResultUtils.ok({ attack: 0, defend: 0 });
    }

    const formations = formationsResult.data;
    const teamFormation = formations.teamFormations.find(tf => tf.uid === teamId);
    if (!teamFormation) {
      return XResultUtils.ok({ attack: 0, defend: 0 });
    }

    // 3. 计算战术加成（基于old项目逻辑）
    const tacticsBonus = { attack: 0, defend: 0 };

    // 获取阵容使用的战术配置
    const useTactics = teamFormation.useTactics;
    const useDefTactics = teamFormation.useDefTactics;

    if (useTactics) {
      const tacticsConfig = await this.gameConfig.tactic.get(useTactics);
      if (tacticsConfig) {
        // 基于old项目逻辑：从addType和addValue中计算攻击加成
        tacticsBonus.attack += TacticsCalculator.calculateTacticBonus(tacticsConfig, 'attack');
      }
    }

    if (useDefTactics) {
      const defTacticsConfig = await this.gameConfig.tactic.get(useDefTactics);
      if (defTacticsConfig) {
        // 基于old项目逻辑：从addType和addValue中计算防守加成
        tacticsBonus.defend += TacticsCalculator.calculateTacticBonus(defTacticsConfig, 'defend');
      }
    }

    // 4. 计算教练战术加成（基于old项目getCurrFormationTrainerTacticsAttr逻辑）
    const trainerTacticsBonus = await this.getCurrFormationTrainerTacticsAttr(teamFormation);
    tacticsBonus.attack += trainerTacticsBonus.attack;
    tacticsBonus.defend += trainerTacticsBonus.defend;

    return XResultUtils.ok(tacticsBonus);
  }

  /**
   * 计算教练技能加成
   * 基于old项目: calcTrainerSkillAttr方法
   * 严格遵循Result模式，无异常抛出
   *
   * @param heroId 球员ID
   * @param teamId 阵容ID
   * @returns 教练技能加成
   */
  async calcTrainerSkillAttr(heroId: string, teamId: string): Promise<XResult<{
    [attributeName: string]: number;
  }>> {
    this.logger.log(`计算教练技能加成: 球员${heroId}, 阵容${teamId}`);

    // 1. 获取球员信息
    const heroResult = await this.heroService.getHeroInfo(heroId);

    if (XResultUtils.isFailure(heroResult)) {
      return XResultUtils.error(`获取球员信息失败: ${heroResult.message}`, heroResult.code);
    }

    const characterId = heroResult.data?.characterId;
    if (!characterId) {
      return XResultUtils.error('球员未关联角色', 'HERO_NO_CHARACTER');
    }

    // 2. 获取阵容数据
    const formationsResult = await this.getFormations(characterId);
    if (XResultUtils.isFailure(formationsResult) || !formationsResult.data) {
      return XResultUtils.ok({});
    }

    const formations = formationsResult.data;
    const teamFormation = formations.teamFormations.find(tf => tf.uid === teamId);
    if (!teamFormation) {
      return XResultUtils.ok({});
    }

    // 3. 计算教练技能加成（基于old项目逻辑）
    const skillBonus: { [attributeName: string]: number } = {};
    const canAttrId = Math.floor(teamFormation.resId / 1000); // 阵型ID

    // 遍历阵容中的所有教练
    for (const trainer of teamFormation.trainers) {
      if (!trainer.uid || trainer.uid === '') {
        continue;
      }

      // 主教练和助理教练享受技能加成（type !== 2 && type !== 3）
      if (trainer.type === 2 || trainer.type === 3) {
        continue;
      }

      // 获取教练信息
      const trainerResult = await this.callMicroservice(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'trainer.getTrainerInfo',
        { trainerId: trainer.uid }
      );

      if (XResultUtils.isFailure(trainerResult)) {
        continue;
      }

      const trainerData = trainerResult.data;
      if (!trainerData || !trainerData.skillId) {
        continue;
      }

      // 获取教练技能配置
      const skillConfig = await this.gameConfig.coachSkill.get(trainerData.skillId);
      if (!skillConfig) {
        continue;
      }

      // 检查当前阵型是否有加成（1表示任意阵型都有加成）
      if (skillConfig.formationId !== 1 && canAttrId !== skillConfig.formationId) {
        continue;
      }

      // 检查球员是否有加成
      const hasSkillBonus = await this.checkHeroIsHaveTrainerSkillAttr(heroId, trainerData.skillId);
      if (!hasSkillBonus) {
        continue;
      }

      // 应用技能加成（基于old项目逻辑：从addType和addValue中计算）
      const skillAttributeBonus = SkillCalculator.calculateCoachSkillBonus(skillConfig);
      for (const [attrName, bonusValue] of Object.entries(skillAttributeBonus)) {
        skillBonus[attrName] = (skillBonus[attrName] || 0) + bonusValue;
      }
    }

    return XResultUtils.ok(skillBonus);
  }

  // ========== 辅助方法 ==========

  /**
   * 检查是否为主力阵容
   * 基于old项目: checkIsMainTeamByUid方法
   */
  private checkIsMainTeamByUid(formations: TeamFormationsDocument, teamUid: string): boolean {
    return formations.currTeamFormationId === teamUid;
  }

  // 已迁移到CoachCalculator.calcTrainerPropertyAttr

  // 已迁移到CoachCalculator.getTrainerBonusRate

  /**
   * 获取当前阵容教练战术加成
   * 基于old项目: getCurrFormationTrainerTacticsAttr方法
   */
  private async getCurrFormationTrainerTacticsAttr(teamFormation: TeamFormation): Promise<{
    attack: number;
    defend: number;
  }> {
    const tacticsBonus = { attack: 0, defend: 0 };

    for (const trainer of teamFormation.trainers) {
      if (!trainer.uid || trainer.uid === '') {
        continue;
      }

      const trainerResult = await this.callMicroservice(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'trainer.getTrainerInfo',
        { trainerId: trainer.uid }
      );

      if (XResultUtils.isFailure(trainerResult)) {
        continue;
      }

      const trainerData = trainerResult.data;
      if (!trainerData || !trainerData.tactics) {
        continue;
      }

      // 计算教练战术加成
      const trainerTacticsBonus = CoachCalculator.calcTrainerTacticsAttr(trainerData, trainer.type);
      tacticsBonus.attack += trainerTacticsBonus.attack;
      tacticsBonus.defend += trainerTacticsBonus.defend;
    }

    return tacticsBonus;
  }

  // 已迁移到CoachCalculator.calcTrainerTacticsAttr

  /**
   * 检查球员是否有教练技能加成
   * 基于old项目: checkHeroIsHaveTrainerSkillAttr方法
   */
  private async checkHeroIsHaveTrainerSkillAttr(heroId: string, skillId: number): Promise<boolean> {
    // 获取技能配置
    const skillConfig = await this.gameConfig.coachSkill.get(skillId);
    if (!skillConfig) {
      return false;
    }

    // 获取球员信息
    const heroResult = await this.heroService.getHeroInfo(heroId);

    if (XResultUtils.isFailure(heroResult)) {
      return false;
    }

    const heroData = heroResult.data;
    if (!heroData) {
      return false;
    }

    // 检查球员是否满足技能条件（基于old项目逻辑）
    // 这里需要根据具体的技能配置来判断
    // 例如：位置限制、属性要求等

    return true; // 简化实现，实际需要根据技能配置判断
  }

  // 已迁移到TacticsCalculator.calculateTacticBonus

  // 已迁移到TacticsCalculator.isMatchingBonusType

  // 已迁移到SkillCalculator.calculateCoachSkillBonus

  // 已迁移到SkillCalculator.mapAddTypeToAttribute

}
