import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { BaseController } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes/microservice-validation.pipe';
import { XResponse } from '@libs/common/types/result.type';
import { FormationOrchestratorService } from '../services/formation-orchestrator.service';
import { AutoSetupFormationPayloadDto } from '../dto/formation-orchestrator.dto';

@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class OrchestratorFormationController extends BaseController {
  constructor(private readonly orchestrator: FormationOrchestratorService) {
    super('OrchestratorFormationController');
  }

  @MessagePattern('orchestrator.formation.autoSetup')
  async autoSetup(@Payload() payload: AutoSetupFormationPayloadDto): Promise<XResponse<any>> {
    const result = await this.orchestrator.autoSetup(payload);
    return this.fromResult(result);
  }
}
