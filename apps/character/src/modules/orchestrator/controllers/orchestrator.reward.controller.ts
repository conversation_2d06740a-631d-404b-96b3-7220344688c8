import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { BaseController } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes/microservice-validation.pipe';
import { XResponse } from '@libs/common/types/result.type';
import { RewardOrchestratorService } from '../services/reward-orchestrator.service';
import { GrantRewardPayloadDto } from '../dto/character-orchestrator.dto';

@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class OrchestratorRewardController extends BaseController {
  constructor(private readonly orchestrator: RewardOrchestratorService) {
    super('OrchestratorRewardController');
  }

  @MessagePattern('orchestrator.reward.grant')
  async grant(@Payload() payload: GrantRewardPayloadDto): Promise<XResponse<any>> {
    const result = await this.orchestrator.grant(payload);
    return this.fromResult(result);
  }
}
