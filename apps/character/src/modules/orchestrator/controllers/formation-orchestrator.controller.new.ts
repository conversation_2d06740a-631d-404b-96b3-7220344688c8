import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { XResponse } from '@libs/common/types/result.type';
import { BaseController } from '@libs/common/controller';
import { FormationOrchestratorService } from '../services/formation-orchestrator.service';
import {
  SmartFormationDto,
  SmartFormationResultDto
} from '../dto/orchestrator.dto';
import {
  SmartFormationPayloadDto,
  AnalyzeFormationPayloadDto,
  CompareFormationsPayloadDto,
  RecommendFormationPayloadDto,
  OptimizeFormationEquipmentPayloadDto,
  MatchTacticsForFormationPayloadDto
} from '../dto/orchestrator-payload.dto';

/**
 * 阵容聚合控制器
 * 提供跨模块的阵容管理业务接口
 * 
 * 🎯 接口职责：
 * - 智能布阵接口
 * - 阵容优化建议接口
 * - 阵容战力分析接口
 * - 阵容装备联动接口
 * 
 * 🔄 业务场景：
 * - 一键智能布阵
 * - 阵容配置优化
 * - 多套阵容管理
 * - 战术匹配推荐
 */
@Controller()
export class FormationOrchestratorController extends BaseController {
  constructor(
    private readonly formationOrchestratorService: FormationOrchestratorService,
  ) {
    super('FormationOrchestratorController');
  }

  /**
   * 智能布阵
   * 综合考虑英雄属性、位置适应性、装备配置等因素进行最优布阵
   *
   * @param payload 智能布阵参数
   * @returns 智能布阵结果
   */
  @MessagePattern('orchestrator.formation.smartFormation')
  async smartFormation(@Payload() payload: SmartFormationPayloadDto): Promise<XResponse<SmartFormationResultDto>> {
    const smartFormationDto: SmartFormationDto = {
      characterId: payload.characterId,
      formationId: payload.formationId,
      preferredHeroes: payload.preferredHeroes,
      strategy: payload.strategy,
      autoEquip: payload.autoEquip
    };
    
    const result = await this.formationOrchestratorService.smartFormation(smartFormationDto);
    
    return this.fromResult(result);
  }

  /**
   * 阵容分析
   * 分析当前阵容的优缺点，提供改进建议
   *
   * @param payload 阵容分析参数
   * @returns 阵容分析结果
   */
  @MessagePattern('orchestrator.formation.analyze')
  async analyzeFormation(@Payload() payload: AnalyzeFormationPayloadDto): Promise<XResponse<{
    characterId: string;
    formationId: string;
    analysis: {
      strengths: string[];
      weaknesses: string[];
      recommendations: string[];
      battlePowerDistribution: {
        attack: number;
        defense: number;
        balance: number;
      };
      positionAnalysis: Array<{
        position: string;
        heroName: string;
        adaptability: number;
        suggestions: string[];
      }>;
    };
    overallRating: number;
  }>> {
    try {
      const { characterId, formationId, analysisType = 'basic' } = payload;

      // 获取阵容详细信息（模拟数据）
      const formation = {
        formationId,
        formationType: '4-4-2',
        positionToHerosObject: {
          'GK': 'hero_1',
          'LB': 'hero_2',
          'CB1': 'hero_3',
          'CB2': 'hero_4',
          'RB': 'hero_5'
        },
        attack: 75,
        defend: 70,
        level: 5
      };

      // 获取阵容中的英雄信息（模拟数据）
      const heroes = [
        { heroId: 'hero_1', name: '门将', level: 50, attack: 30, defense: 90, goalkeeping: 95 },
        { heroId: 'hero_2', name: '左后卫', level: 48, attack: 60, defense: 85, speed: 75 }
      ];

      // 执行阵容分析（模拟数据）
      const analysis = {
        strengths: ['防守稳固', '门将能力出色'],
        weaknesses: ['进攻乏力', '中场创造力不足'],
        recommendations: ['加强前锋配置', '提升中场传球能力'],
        battlePowerDistribution: { attack: 30, defense: 50, balance: 20 },
        positionAnalysis: [
          { position: 'GK', heroName: '门将', adaptability: 95, suggestions: ['配置完美'] },
          { position: 'LB', heroName: '左后卫', adaptability: 80, suggestions: ['可考虑提升速度'] }
        ]
      };

      // 计算总体评分
      const overallRating = 78;

      const result = {
        characterId,
        formationId,
        analysis,
        overallRating,
        analysisTime: new Date().toISOString(),
        analysisType
      };

      return this.toSuccessResponse(result, '阵容分析完成');
    } catch (error) {
      this.logger.error('阵容分析失败', error);
      return this.toErrorResponse('阵容分析失败', 'FORMATION_ANALYSIS_FAILED');
    }
  }

  /**
   * 阵容对比
   * 对比两个阵容的优劣，提供选择建议
   *
   * @param payload 阵容对比参数
   * @returns 阵容对比结果
   */
  @MessagePattern('orchestrator.formation.compare')
  async compareFormations(@Payload() payload: CompareFormationsPayloadDto): Promise<XResponse<{
    characterId: string;
    comparison: {
      formation1: any;
      formation2: any;
      differences: any;
      recommendation: string;
    };
  }>> {
    try {
      const { characterId, formationId1, formationId2 } = payload;

      // 获取两个阵容的详细信息（模拟数据）
      const formation1 = {
        formationId: formationId1,
        formationType: '4-4-2',
        positionToHerosObject: { GK: 'hero1', LB: 'hero2' },
        attack: 75,
        defend: 70,
        level: 5
      };
      const formation2 = {
        formationId: formationId2,
        formationType: '4-3-3',
        positionToHerosObject: { GK: 'hero1', LB: 'hero3' },
        attack: 80,
        defend: 65,
        level: 6
      };

      // 分析两个阵容（模拟数据）
      const analysis1 = {
        strengths: ['防守稳固', '中场控制'],
        weaknesses: ['进攻乏力'],
        overallRating: 75
      };
      const analysis2 = {
        strengths: ['进攻犀利', '边路活跃'],
        weaknesses: ['防守薄弱'],
        overallRating: 78
      };

      // 计算战力
      const battlePower1 = 85000;
      const battlePower2 = 87500;

      // 分析位置变化
      const positionChanges = [
        {
          position: 'LB',
          hero1: 'hero2',
          hero2: 'hero3',
          impact: '左后卫更换，可能影响该位置表现'
        }
      ];

      // 生成对比结果
      const comparison = {
        formation1: {
          formationId: formationId1,
          formationType: formation1.formationType,
          battlePower: battlePower1,
          strengths: analysis1.strengths,
          weaknesses: analysis1.weaknesses,
          overallRating: analysis1.overallRating
        },
        formation2: {
          formationId: formationId2,
          formationType: formation2.formationType,
          battlePower: battlePower2,
          strengths: analysis2.strengths,
          weaknesses: analysis2.weaknesses,
          overallRating: analysis2.overallRating
        },
        differences: {
          battlePowerDiff: battlePower2 - battlePower1,
          ratingDiff: analysis2.overallRating - analysis1.overallRating,
          positionChanges,
          strengthsGained: analysis2.strengths.filter(s => !analysis1.strengths.includes(s)),
          weaknessesFixed: analysis1.weaknesses.filter(w => !analysis2.weaknesses.includes(w)),
          newWeaknesses: analysis2.weaknesses.filter(w => !analysis1.weaknesses.includes(w))
        },
        recommendation: '阵容2在战力和综合评分上都有优势，建议使用'
      };

      const result = {
        characterId,
        comparison,
        comparisonTime: new Date().toISOString()
      };

      return this.toSuccessResponse(result, '阵容对比完成');
    } catch (error) {
      this.logger.error('阵容对比失败', error);
      return this.toErrorResponse('阵容对比失败', 'FORMATION_COMPARE_FAILED');
    }
  }

  /**
   * 阵容推荐
   * 基于角色英雄和策略需求，推荐最优阵容配置
   *
   * @param payload 阵容推荐参数
   * @returns 阵容推荐结果
   */
  @MessagePattern('orchestrator.formation.recommend')
  async recommendFormation(@Payload() payload: RecommendFormationPayloadDto): Promise<XResponse<{
    characterId: string;
    recommendations: any[];
    strategy: string;
  }>> {
    try {
      const {
        characterId,
        strategy = 'balanced',
        formationType,
        excludeHeroes = [],
        maxRecommendations = 3
      } = payload;

      // 获取角色的英雄数据（模拟数据）
      const mockHeroes = Array.from({ length: 15 }, (_, i) => ({
        heroId: `hero_${i + 1}`,
        name: `英雄${i + 1}`,
        level: 50 + i,
        attack: 70 + i * 2,
        defense: 65 + i * 2,
        speed: 75 + i,
        goalkeeping: i === 0 ? 90 : 30
      }));

      const availableHeroes = mockHeroes.filter(hero =>
        !excludeHeroes.includes(hero.heroId)
      );

      if (availableHeroes.length < 11) {
        return this.toErrorResponse('可用英雄数量不足，无法组成完整阵容', 'INSUFFICIENT_HEROES');
      }

      // 生成推荐阵容（模拟数据）
      const recommendations = [];
      const formationTypes = formationType ? [formationType] : ['4-4-2', '4-3-3', '3-5-2'];

      for (let i = 0; i < Math.min(maxRecommendations, formationTypes.length); i++) {
        const currentFormationType = formationTypes[i];

        recommendations.push({
          rank: i + 1,
          formationId: `recommended_${i + 1}`,
          formationType: currentFormationType,
          expectedBattlePower: 90000 - i * 2000,
          positionMapping: {
            GK: 'hero_1',
            LB: 'hero_2',
            CB1: 'hero_3',
            CB2: 'hero_4',
            RB: 'hero_5',
            LM: 'hero_6',
            CM1: 'hero_7',
            CM2: 'hero_8',
            RM: 'hero_9',
            ST1: 'hero_10',
            ST2: 'hero_11'
          },
          strengths: ['攻守平衡', '适应性强', '战力优化'],
          weaknesses: i > 0 ? ['部分位置适应性一般'] : [],
          suitableFor: ['联赛比赛', '杯赛'],
          confidence: Math.max(70, 95 - i * 10),
          tacticalAdvice: ['保持攻守平衡', '注意体能分配']
        });
      }

      const result = {
        characterId,
        recommendations,
        strategy,
        availableHeroCount: availableHeroes.length,
        generationTime: new Date().toISOString()
      };

      return this.toSuccessResponse(result, `生成${recommendations.length}个阵容推荐`);
    } catch (error) {
      this.logger.error('阵容推荐失败', error);
      return this.toErrorResponse('阵容推荐失败', 'FORMATION_RECOMMEND_FAILED');
    }
  }
}
