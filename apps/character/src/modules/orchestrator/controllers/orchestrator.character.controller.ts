import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { BaseController } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes/microservice-validation.pipe';
import { UsePipes } from '@nestjs/common';
import { XResponse } from '@libs/common/types/result.type';
import { CharacterOrchestratorService } from '../services/character-orchestrator.service';
import { InitializeCharacterPayloadDto, GetCompositeInfoPayloadDto } from '../dto/character-orchestrator.dto';

@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class OrchestratorCharacterController extends BaseController {
  constructor(private readonly orchestrator: CharacterOrchestratorService) {
    super('OrchestratorCharacterController');
  }

  @MessagePattern('orchestrator.character.initialize')
  async initialize(@Payload() payload: InitializeCharacterPayloadDto): Promise<XResponse<any>> {
    const result = await this.orchestrator.initialize(payload);
    return this.fromResult(result);
  }

  @MessagePattern('orchestrator.character.getCompositeInfo')
  async getCompositeInfo(@Payload() payload: GetCompositeInfoPayloadDto): Promise<XResponse<any>> {
    const result = await this.orchestrator.getCompositeInfo(payload);
    return this.fromResult(result);
  }
}
