import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { XResponse } from '@libs/common/types/result.type';
import { BaseController } from '@libs/common/controller';
import { FormationOrchestratorService } from '../services/formation-orchestrator.service';
import {
  SmartFormationDto,
  SmartFormationResultDto
} from '../dto/orchestrator.dto';
import {
  SmartFormationPayloadDto,
  AnalyzeFormationPayloadDto,
  CompareFormationsPayloadDto,
  RecommendFormationPayloadDto,
  OptimizeFormationEquipmentPayloadDto,
  MatchTacticsForFormationPayloadDto
} from '../dto/orchestrator-payload.dto';

/**
 * 阵容聚合控制器
 * 提供跨模块的阵容管理业务接口
 * 
 * 🎯 接口职责：
 * - 智能布阵接口
 * - 阵容优化建议接口
 * - 阵容战力分析接口
 * - 阵容装备联动接口
 * 
 * 🔄 业务场景：
 * - 一键智能布阵
 * - 阵容配置优化
 * - 多套阵容管理
 * - 战术匹配推荐
 */
@Controller()
export class FormationOrchestratorController extends BaseController {
  constructor(
    private readonly formationOrchestratorService: FormationOrchestratorService,
  ) {
    super('FormationOrchestratorController');
  }

  /**
   * 智能布阵
   * 综合考虑英雄属性、位置适应性、装备配置等因素进行最优布阵
   *
   * @param payload 智能布阵参数
   * @returns 智能布阵结果
   */
  @MessagePattern('orchestrator.formation.smartFormation')
  async smartFormation(@Payload() payload: SmartFormationPayloadDto): Promise<XResponse<SmartFormationResultDto>> {
    const smartFormationDto: SmartFormationDto = {
      characterId: payload.characterId,
      formationId: payload.formationId,
      preferredHeroes: payload.preferredHeroes,
      strategy: payload.strategy,
      autoEquip: payload.autoEquip
    };
    
    const result = await this.formationOrchestratorService.smartFormation(smartFormationDto);
    
    return this.fromResult(result);
  }

  /**
   * 阵容分析
   * 分析当前阵容的优缺点，提供改进建议
   *
   * @param payload 阵容分析参数
   * @returns 阵容分析结果
   */
  @MessagePattern('orchestrator.formation.analyze')
  async analyzeFormation(@Payload() payload: AnalyzeFormationPayloadDto): Promise<XResponse<{
    characterId: string;
    formationId: string;
    analysis: {
      strengths: string[];
      weaknesses: string[];
      recommendations: string[];
      battlePowerDistribution: {
        attack: number;
        defense: number;
        balance: number;
      };
      positionAnalysis: Array<{
        position: string;
        heroName: string;
        adaptability: number;
        suggestions: string[];
      }>;
    };
    overallRating: number;
  }>> {
    try {
      const { characterId, formationId, analysisType = 'basic' } = payload;

      // 获取阵容详细信息（模拟数据）
      const formation = {
        formationId,
        formationType: '4-4-2',
        positionToHerosObject: {
          'GK': 'hero_1',
          'LB': 'hero_2',
          'CB1': 'hero_3',
          'CB2': 'hero_4',
          'RB': 'hero_5'
        },
        attack: 75,
        defend: 70,
        level: 5
      };

      // 获取阵容中的英雄信息（模拟数据）
      const heroes = [
        { heroId: 'hero_1', name: '门将', level: 50, attack: 30, defense: 90, goalkeeping: 95 },
        { heroId: 'hero_2', name: '左后卫', level: 48, attack: 60, defense: 85, speed: 75 }
      ];

      // 执行阵容分析（模拟数据）
      const analysis = {
        strengths: ['防守稳固', '门将能力出色'],
        weaknesses: ['进攻乏力', '中场创造力不足'],
        recommendations: ['加强前锋配置', '提升中场传球能力'],
        battlePowerDistribution: { attack: 30, defense: 50, balance: 20 },
        positionAnalysis: [
          { position: 'GK', heroName: '门将', adaptability: 95, suggestions: ['配置完美'] },
          { position: 'LB', heroName: '左后卫', adaptability: 80, suggestions: ['可考虑提升速度'] }
        ]
      };

      // 计算总体评分
      const overallRating = 78;

      const result = {
        characterId,
        formationId,
        analysis,
        overallRating,
        analysisTime: new Date().toISOString(),
        analysisType
      };

      return this.toSuccessResponse(result, '阵容分析完成');
    } catch (error) {
      this.logger.error('阵容分析失败', error);
      return this.toErrorResponse('阵容分析失败', 'FORMATION_ANALYSIS_FAILED');
    }
  }

  /**
   * 阵容对比
   * 对比两个阵容的优劣，提供选择建议
   *
   * @param payload 阵容对比参数
   * @returns 阵容对比结果
   */
  @MessagePattern('orchestrator.formation.compare')
  async compareFormations(@Payload() payload: CompareFormationsPayloadDto): Promise<XResponse<{
    characterId: string;
    comparison: {
      formation1: any;
      formation2: any;
      differences: any;
      recommendation: string;
    };
  }>> {
    try {
      const { characterId, formationId1, formationId2 } = payload;

      // 获取两个阵容的详细信息（模拟数据）
      const formation1 = {
        formationId: formationId1,
        formationType: '4-4-2',
        positionToHerosObject: { GK: 'hero1', LB: 'hero2' },
        attack: 75,
        defend: 70,
        level: 5
      };
      const formation2 = {
        formationId: formationId2,
        formationType: '4-3-3',
        positionToHerosObject: { GK: 'hero1', LB: 'hero3' },
        attack: 80,
        defend: 65,
        level: 6
      };

      // 分析两个阵容（模拟数据）
      const analysis1 = {
        strengths: ['防守稳固', '中场控制'],
        weaknesses: ['进攻乏力'],
        overallRating: 75
      };
      const analysis2 = {
        strengths: ['进攻犀利', '边路活跃'],
        weaknesses: ['防守薄弱'],
        overallRating: 78
      };

      // 计算战力
      const battlePower1 = 85000;
      const battlePower2 = 87500;

      // 分析位置变化
      const positionChanges = [
        {
          position: 'LB',
          hero1: 'hero2',
          hero2: 'hero3',
          impact: '左后卫更换，可能影响该位置表现'
        }
      ];

      // 生成对比结果
      const comparison = {
        formation1: {
          formationId: formationId1,
          formationType: formation1.formationType,
          battlePower: battlePower1,
          strengths: analysis1.strengths,
          weaknesses: analysis1.weaknesses,
          overallRating: analysis1.overallRating
        },
        formation2: {
          formationId: formationId2,
          formationType: formation2.formationType,
          battlePower: battlePower2,
          strengths: analysis2.strengths,
          weaknesses: analysis2.weaknesses,
          overallRating: analysis2.overallRating
        },
        differences: {
          battlePowerDiff: battlePower2 - battlePower1,
          ratingDiff: analysis2.overallRating - analysis1.overallRating,
          positionChanges,
          strengthsGained: analysis2.strengths.filter(s => !analysis1.strengths.includes(s)),
          weaknessesFixed: analysis1.weaknesses.filter(w => !analysis2.weaknesses.includes(w)),
          newWeaknesses: analysis2.weaknesses.filter(w => !analysis1.weaknesses.includes(w))
        },
        recommendation: '阵容2在战力和综合评分上都有优势，建议使用'
      };

      const result = {
        characterId,
        comparison,
        comparisonTime: new Date().toISOString()
      };

      return this.toSuccessResponse(result, '阵容对比完成');
    } catch (error) {
      this.logger.error('阵容对比失败', error);
      return this.toErrorResponse('阵容对比失败', 'FORMATION_COMPARE_FAILED');
    }
  }

  /**
   * 阵容推荐
   * 基于角色英雄和策略需求，推荐最优阵容配置
   *
   * @param payload 阵容推荐参数
   * @returns 阵容推荐结果
   */
  @MessagePattern('orchestrator.formation.recommend')
  async recommendFormation(@Payload() payload: RecommendFormationPayloadDto): Promise<XResponse<{
    characterId: string;
    recommendations: any[];
    strategy: string;
  }>> {
    try {
      const {
        characterId,
        strategy = 'balanced',
        formationType,
        excludeHeroes = [],
        maxRecommendations = 3
      } = payload;

      // 获取角色的英雄数据（模拟数据）
      const mockHeroes = Array.from({ length: 15 }, (_, i) => ({
        heroId: `hero_${i + 1}`,
        name: `英雄${i + 1}`,
        level: 50 + i,
        attack: 70 + i * 2,
        defense: 65 + i * 2,
        speed: 75 + i,
        goalkeeping: i === 0 ? 90 : 30
      }));

      const availableHeroes = mockHeroes.filter(hero =>
        !excludeHeroes.includes(hero.heroId)
      );

      if (availableHeroes.length < 11) {
        return this.toErrorResponse('可用英雄数量不足，无法组成完整阵容', 'INSUFFICIENT_HEROES');
      }

      // 生成推荐阵容（模拟数据）
      const recommendations = [];
      const formationTypes = formationType ? [formationType] : ['4-4-2', '4-3-3', '3-5-2'];

      for (let i = 0; i < Math.min(maxRecommendations, formationTypes.length); i++) {
        const currentFormationType = formationTypes[i];

        recommendations.push({
          rank: i + 1,
          formationId: `recommended_${i + 1}`,
          formationType: currentFormationType,
          expectedBattlePower: 90000 - i * 2000,
          positionMapping: {
            GK: 'hero_1',
            LB: 'hero_2',
            CB1: 'hero_3',
            CB2: 'hero_4',
            RB: 'hero_5',
            LM: 'hero_6',
            CM1: 'hero_7',
            CM2: 'hero_8',
            RM: 'hero_9',
            ST1: 'hero_10',
            ST2: 'hero_11'
          },
          strengths: ['攻守平衡', '适应性强', '战力优化'],
          weaknesses: i > 0 ? ['部分位置适应性一般'] : [],
          suitableFor: ['联赛比赛', '杯赛'],
          confidence: Math.max(70, 95 - i * 10),
          tacticalAdvice: ['保持攻守平衡', '注意体能分配']
        });
      }

      const result = {
        characterId,
        recommendations,
        strategy,
        availableHeroCount: availableHeroes.length,
        generationTime: new Date().toISOString()
      };

      return this.toSuccessResponse(result, `生成${recommendations.length}个阵容推荐`);
    } catch (error) {
      this.logger.error('阵容推荐失败', error);
      return this.toErrorResponse('阵容推荐失败', 'FORMATION_RECOMMEND_FAILED');
    }
  }

  /**
   * 阵容装备优化
   * 为阵容中的英雄推荐最优装备配置
   *
   * @param payload 装备优化参数
   * @returns 装备优化结果
   */
  @MessagePattern('orchestrator.formation.optimizeEquipment')
  async optimizeFormationEquipment(@Payload() payload: OptimizeFormationEquipmentPayloadDto): Promise<XResponse<{
    characterId: string;
    formationId: string;
    optimizationResults: any[];
    totalBattlePowerChange: number;
    applied: boolean;
  }>> {
    try {
      const { characterId, formationId, optimizationGoal = 'battlePower', autoApply = false } = payload;

      // 获取阵容信息（模拟数据）
      const formation = {
        formationId,
        positionToHerosObject: {
          'GK': 'hero_1',
          'LB': 'hero_2',
          'CB1': 'hero_3',
          'CB2': 'hero_4',
          'RB': 'hero_5',
          'LM': 'hero_6',
          'CM1': 'hero_7',
          'CM2': 'hero_8',
          'RM': 'hero_9',
          'ST1': 'hero_10',
          'ST2': 'hero_11'
        }
      };

      // 获取可用装备（模拟数据）
      const availableEquipment = [
        { itemId: 'eq_001', name: '传奇球鞋', type: 'boots', level: 10, quality: 'legendary', attack: 50, speed: 80 },
        { itemId: 'eq_002', name: '专业球衣', type: 'jersey', level: 8, quality: 'epic', defense: 60, stamina: 70 },
        { itemId: 'eq_003', name: '守门员手套', type: 'gloves', level: 9, quality: 'epic', accuracy: 90, goalkeeping: 85 },
        { itemId: 'eq_004', name: '碳纤维护腿板', type: 'shin_guards', level: 7, quality: 'rare', defense: 75, speed: 20 }
      ];

      // 为阵容中的每个英雄优化装备（模拟数据）
      const optimizationResults = [
        {
          heroId: 'hero_1',
          heroName: '门将',
          position: 'GK',
          currentEquipment: [
            { itemId: 'old_001', name: '普通手套', type: 'gloves', level: 3, quality: 'common' }
          ],
          recommendedEquipment: [
            { itemId: 'eq_003', name: '守门员手套', type: 'gloves', level: 9, quality: 'epic', accuracy: 90, goalkeeping: 85 }
          ],
          battlePowerChange: 1500,
          reasons: ['门将专用装备', '大幅提升守门能力']
        },
        {
          heroId: 'hero_2',
          heroName: '左后卫',
          position: 'LB',
          currentEquipment: [
            { itemId: 'old_002', name: '普通球鞋', type: 'boots', level: 2, quality: 'common' }
          ],
          recommendedEquipment: [
            { itemId: 'eq_001', name: '传奇球鞋', type: 'boots', level: 10, quality: 'legendary', attack: 50, speed: 80 }
          ],
          battlePowerChange: 2000,
          reasons: ['传奇品质装备', '大幅提升速度和攻击']
        }
      ];

      // 如果设置了自动应用，则应用装备更改
      let applied = false;
      if (autoApply && optimizationResults.length > 0) {
        // 模拟应用成功
        applied = true;
        this.logger.log(`为角色${characterId}应用装备优化`, {
          changesCount: optimizationResults.length
        });
      }

      const totalBattlePowerChange = optimizationResults.reduce(
        (sum, result) => sum + (result.battlePowerChange || 0),
        0
      );

      const result = {
        characterId,
        formationId,
        optimizationResults,
        totalBattlePowerChange,
        applied,
        optimizationGoal,
        timestamp: new Date().toISOString()
      };

      return this.toSuccessResponse(result, `装备优化完成，预计战力提升${totalBattlePowerChange}`);
    } catch (error) {
      this.logger.error('阵容装备优化失败', error);
      return this.toErrorResponse('阵容装备优化失败', 'FORMATION_EQUIPMENT_OPTIMIZE_FAILED');
    }
  }

  /**
   * 阵容战术匹配
   * 为指定阵容推荐最适合的战术配置
   *
   * @param payload 战术匹配参数
   * @returns 战术匹配结果
   */
  @MessagePattern('orchestrator.formation.matchTactics')
  async matchTacticsForFormation(@Payload() payload: MatchTacticsForFormationPayloadDto): Promise<XResponse<{
    characterId: string;
    formationId: string;
    tacticRecommendations: any[];
    matchingScore: number;
  }>> {
    try {
      const { characterId, formationId, preferredTacticTypes = [], maxRecommendations = 5 } = payload;

      // 获取阵容信息（模拟数据）
      const formation = {
        formationId,
        formationType: '4-4-2',
        attack: 75,
        defend: 70,
        balance: 80
      };

      // 生成战术推荐（模拟数据）
      const allTactics = [
        { tacticKey: 'pressing', name: '高位逼抢', type: 'attack', matchScore: 85, description: '适合攻击型阵容' },
        { tacticKey: 'counter_attack', name: '防守反击', type: 'defense', matchScore: 78, description: '适合防守稳固的阵容' },
        { tacticKey: 'possession', name: '控球战术', type: 'balanced', matchScore: 82, description: '适合平衡型阵容' },
        { tacticKey: 'wing_play', name: '边路进攻', type: 'attack', matchScore: 80, description: '利用边路优势' },
        { tacticKey: 'park_bus', name: '铁桶阵', type: 'defense', matchScore: 75, description: '极致防守战术' }
      ];

      // 根据偏好过滤和排序
      let tacticRecommendations = allTactics;
      if (preferredTacticTypes.length > 0) {
        tacticRecommendations = allTactics.filter(tactic =>
          preferredTacticTypes.includes(tactic.type)
        );
      }

      // 按匹配度排序并限制数量
      tacticRecommendations = tacticRecommendations
        .sort((a, b) => b.matchScore - a.matchScore)
        .slice(0, maxRecommendations)
        .map((tactic, index) => ({
          ...tactic,
          rank: index + 1,
          reasons: this.generateTacticMatchReasons(tactic, formation),
          expectedEffect: this.generateTacticEffect(tactic, formation)
        }));

      const matchingScore = tacticRecommendations.length > 0 ?
        tacticRecommendations.reduce((sum, t) => sum + t.matchScore, 0) / tacticRecommendations.length : 0;

      const result = {
        characterId,
        formationId,
        tacticRecommendations,
        matchingScore: Math.floor(matchingScore),
        formationType: formation.formationType,
        analysisTime: new Date().toISOString()
      };

      return this.toSuccessResponse(result, `找到${tacticRecommendations.length}个匹配的战术`);
    } catch (error) {
      this.logger.error('战术匹配失败', error);
      return this.toErrorResponse('战术匹配失败', 'TACTIC_MATCH_FAILED');
    }
  }

  /**
   * 生成战术匹配原因
   */
  private generateTacticMatchReasons(tactic: any, formation: any): string[] {
    const reasons = [];

    if (tactic.type === 'attack' && formation.attack > 70) {
      reasons.push('阵容攻击力强，适合攻击型战术');
    }
    if (tactic.type === 'defense' && formation.defend > 70) {
      reasons.push('阵容防守稳固，适合防守型战术');
    }
    if (tactic.type === 'balanced' && formation.balance > 75) {
      reasons.push('阵容攻守平衡，适合平衡型战术');
    }

    if (reasons.length === 0) {
      reasons.push('基于阵容特点推荐');
    }

    return reasons;
  }

  /**
   * 生成战术效果
   */
  private generateTacticEffect(tactic: any, formation: any): string {
    const effects = {
      'pressing': '提升前场压迫力，增加抢断机会',
      'counter_attack': '快速反击，利用对手失误',
      'possession': '控制比赛节奏，减少失误',
      'wing_play': '加强边路进攻，创造更多机会',
      'park_bus': '极致防守，减少失球'
    };

    return effects[tactic.tacticKey] || '提升阵容整体表现';
  }
}
