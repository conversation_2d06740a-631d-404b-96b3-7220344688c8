import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { XResponse } from '@libs/common/types/result.type';
import { BaseController } from '@libs/common/controller';
import { FormationOrchestratorService } from '../services/formation-orchestrator.service';
import {
  SmartFormationDto,
  SmartFormationResultDto
} from '../dto/orchestrator.dto';
import {
  SmartFormationPayloadDto,
  AnalyzeFormationPayloadDto,
  CompareFormationsPayloadDto,
  RecommendFormationPayloadDto,
  OptimizeFormationEquipmentPayloadDto,
  MatchTacticsForFormationPayloadDto
} from '../dto/orchestrator-payload.dto';

/**
 * 阵容聚合控制器
 * 提供跨模块的阵容管理业务接口
 * 
 * 🎯 接口职责：
 * - 智能布阵接口
 * - 阵容优化建议接口
 * - 阵容战力分析接口
 * - 阵容装备联动接口
 * 
 * 🔄 业务场景：
 * - 一键智能布阵
 * - 阵容配置优化
 * - 多套阵容管理
 * - 战术匹配推荐
 */
@Controller()
export class FormationOrchestratorController extends BaseController {
  constructor(
    private readonly formationOrchestratorService: FormationOrchestratorService,
  ) {
    super('FormationOrchestratorController');
  }

  /**
   * 智能布阵
   * 综合考虑英雄属性、位置适应性、装备配置等因素进行最优布阵
   *
   * @param payload 智能布阵参数
   * @returns 智能布阵结果
   */
  @MessagePattern('orchestrator.formation.smartFormation')
  async smartFormation(@Payload() payload: SmartFormationPayloadDto): Promise<XResponse<SmartFormationResultDto>> {
    return this.handleRequest(async () => {
      this.logger.log(`智能布阵: ${payload.characterId}`, {
        formationId: payload.formationId,
        strategy: payload.strategy,
        autoEquip: payload.autoEquip,
        preferredHeroesCount: payload.preferredHeroes?.length || 0
      });

      const smartFormationDto: SmartFormationDto = {
        characterId: payload.characterId,
        formationId: payload.formationId,
        preferredHeroes: payload.preferredHeroes,
        strategy: payload.strategy,
        autoEquip: payload.autoEquip
      };

      const result = await this.formationOrchestratorService.smartFormation(smartFormationDto);

      return result;
    });
  }

  /**
   * 阵容分析
   * 分析当前阵容的优缺点，提供改进建议
   *
   * @param payload 阵容分析参数
   * @returns 阵容分析结果
   */
  @MessagePattern('orchestrator.formation.analyze')
  async analyzeFormation(@Payload() payload: AnalyzeFormationPayloadDto): Promise<XResponse<{
    characterId: string;
    formationId: string;
    analysis: {
      strengths: string[];
      weaknesses: string[];
      recommendations: string[];
      battlePowerDistribution: {
        attack: number;
        defense: number;
        balance: number;
      };
      positionAnalysis: Array<{
        position: string;
        heroName: string;
        adaptability: number;
        suggestions: string[];
      }>;
    };
    overallRating: number;
  }>> {
    return this.handleRequest(async () => {
      this.logger.log(`阵容分析: ${payload.characterId}`, {
        formationId: payload.formationId,
        analysisType: payload.analysisType
      });
      
      const { characterId, formationId, analysisType = 'basic' } = payload;

      try {
        // 获取阵容详细信息
        const formation = {
          formationId,
          formationType: '4-4-2',
          positionToHerosObject: {},
          attack: 75,
          defend: 70,
          level: 5
        };

        // 获取阵容中的英雄信息
        const heroes = [];

        // 执行阵容分析
        const analysis = await this.performFormationAnalysis(formation, heroes, analysisType);

        // 计算总体评分
        const overallRating = this.calculateOverallRating(analysis);

        const result = {
          characterId,
          formationId,
          analysis,
          overallRating,
          analysisTime: new Date().toISOString(),
          analysisType
        };
      
      return { success: true, data: result, message: '阵容分析完成' };
    });
  }

  /**
   * 阵容对比
   * 对比两个阵容的配置和战力
   *
   * @param payload 阵容对比参数
   * @returns 阵容对比结果
   */
  @MessagePattern('orchestrator.formation.compare')
  async compareFormations(@Payload() payload: CompareFormationsPayloadDto): Promise<XResponse<{
    characterId: string;
    comparison: {
      formation1: {
        formationId: string;
        battlePower: number;
        strengths: string[];
        weaknesses: string[];
      };
      formation2: {
        formationId: string;
        battlePower: number;
        strengths: string[];
        weaknesses: string[];
      };
      differences: {
        battlePowerDiff: number;
        positionChanges: Array<{
          position: string;
          hero1: string;
          hero2: string;
          impact: string;
        }>;
      };
      recommendation: string;
    };
  }>> {
    return this.handleRequest(async () => {
      this.logger.log(`阵容对比: ${payload.characterId}`, {
        formationId1: payload.formationId1,
        formationId2: payload.formationId2
      });
      
      const { characterId, formationId1, formationId2 } = payload;

      try {
        // 获取两个阵容的详细信息（模拟数据）
        const formation1 = {
          formationId: formationId1,
          formationType: '4-4-2',
          positionToHerosObject: { GK: 'hero1', LB: 'hero2' },
          attack: 75,
          defend: 70,
          level: 5
        };
        const formation2 = {
          formationId: formationId2,
          formationType: '4-3-3',
          positionToHerosObject: { GK: 'hero1', LB: 'hero3' },
          attack: 80,
          defend: 65,
          level: 6
        };

        // 分析两个阵容
        const [analysis1, analysis2] = await Promise.all([
          this.performFormationAnalysis(formation1, [], 'basic'),
          this.performFormationAnalysis(formation2, [], 'basic')
        ]);

        // 计算战力
        const battlePower1 = this.calculateFormationBattlePower(formation1);
        const battlePower2 = this.calculateFormationBattlePower(formation2);

        // 分析位置变化
        const positionChanges = this.analyzePositionChanges(formation1, formation2);

        // 生成对比结果
        const comparison = {
          formation1: {
            formationId: formationId1,
            formationType: formation1.formationType || '4-4-2',
            battlePower: battlePower1,
            strengths: analysis1.strengths,
            weaknesses: analysis1.weaknesses,
            overallRating: this.calculateOverallRating(analysis1)
          },
          formation2: {
            formationId: formationId2,
            formationType: formation2.formationType || '4-4-2',
            battlePower: battlePower2,
            strengths: analysis2.strengths,
            weaknesses: analysis2.weaknesses,
            overallRating: this.calculateOverallRating(analysis2)
          },
          differences: {
            battlePowerDiff: battlePower2 - battlePower1,
            ratingDiff: this.calculateOverallRating(analysis2) - this.calculateOverallRating(analysis1),
            positionChanges,
            strengthsGained: analysis2.strengths.filter(s => !analysis1.strengths.includes(s)),
            weaknessesFixed: analysis1.weaknesses.filter(w => !analysis2.weaknesses.includes(w)),
            newWeaknesses: analysis2.weaknesses.filter(w => !analysis1.weaknesses.includes(w))
          },
          recommendation: this.generateComparisonRecommendation(battlePower1, battlePower2, analysis1, analysis2)
        };

        const result = {
          characterId,
          comparison,
          comparisonTime: new Date().toISOString()
        };
      
      return { success: true, data: result, message: '阵容对比完成' };
    });
  }

  /**
   * 推荐阵容配置
   * 基于角色的英雄库推荐最优阵容配置
   *
   * @param payload 推荐参数
   * @returns 推荐的阵容配置
   */
  @MessagePattern('orchestrator.formation.recommend')
  async recommendFormation(@Payload() payload: RecommendFormationPayloadDto): Promise<XResponse<{
    characterId: string;
    recommendations: Array<{
      rank: number;
      formationId: string;
      formationType: string;
      expectedBattlePower: number;
      positionMapping: any;
      strengths: string[];
      suitableFor: string[];
      confidence: number;
    }>;
    strategy: string;
  }>> {
    return this.handleRequest(async () => {
      this.logger.log(`推荐阵容配置: ${payload.characterId}`, {
        strategy: payload.strategy,
        formationType: payload.formationType,
        maxRecommendations: payload.maxRecommendations
      });
      
      const {
        characterId,
        strategy = 'balanced',
        formationType,
        excludeHeroes = [],
        maxRecommendations = 3
      } = payload;

      try {
        // 获取角色的英雄数据（模拟数据）
        const mockHeroes = Array.from({ length: 15 }, (_, i) => ({
          heroId: `hero_${i + 1}`,
          name: `英雄${i + 1}`,
          level: 50 + i,
          attack: 70 + i * 2,
          defense: 65 + i * 2,
          speed: 75 + i,
          goalkeeping: i === 0 ? 90 : 30
        }));

        const availableHeroes = mockHeroes.filter(hero =>
          !excludeHeroes.includes(hero.heroId)
        );

        if (availableHeroes.length < 11) {
          throw new Error('可用英雄数量不足，无法组成完整阵容');
        }

        // 生成推荐阵容
        const recommendations = await this.generateFormationRecommendations(
          characterId,
          availableHeroes,
          strategy,
          formationType,
          maxRecommendations
        );

        const result = {
          characterId,
          recommendations,
          strategy,
          availableHeroCount: availableHeroes.length,
          generationTime: new Date().toISOString()
        };
      
      return { success: true, data: result, message: `生成${recommendations.length}个推荐阵容` };
    });
  }

  /**
   * 阵容装备优化
   * 为指定阵容优化装备配置
   *
   * @param payload 装备优化参数
   * @returns 装备优化结果
   */
  @MessagePattern('orchestrator.formation.optimizeEquipment')
  async optimizeFormationEquipment(@Payload() payload: OptimizeFormationEquipmentPayloadDto): Promise<XResponse<{
    characterId: string;
    formationId: string;
    optimizationResults: Array<{
      heroId: string;
      heroName: string;
      currentEquipment: any[];
      recommendedEquipment: any[];
      battlePowerChange: number;
      reasons: string[];
    }>;
    totalBattlePowerChange: number;
    applied: boolean;
  }>> {
    return this.handleRequest(async () => {
      this.logger.log(`阵容装备优化: ${payload.characterId}`, {
        formationId: payload.formationId,
        optimizationGoal: payload.optimizationGoal,
        autoApply: payload.autoApply
      });
      
      const { 
        characterId, 
        formationId, 
        optimizationGoal = 'battlePower',
        autoApply = false 
      } = payload;
      
      // 简化版本：返回模拟优化结果
      const optimizationResults = [
        {
          heroId: 'hero_1',
          heroName: '前锋A',
          currentEquipment: [
            { itemId: 'item_1', name: '普通球鞋', level: 1 }
          ],
          recommendedEquipment: [
            { itemId: 'item_2', name: '高级球鞋', level: 3 }
          ],
          battlePowerChange: 500,
          reasons: ['提升速度属性', '增强射门精度']
        },
        {
          heroId: 'hero_2',
          heroName: '中场B',
          currentEquipment: [
            { itemId: 'item_3', name: '基础护腿板', level: 1 }
          ],
          recommendedEquipment: [
            { itemId: 'item_4', name: '专业护腿板', level: 2 }
          ],
          battlePowerChange: 300,
          reasons: ['提升防守能力', '增加传球精度']
        }
      ];
      
      const totalBattlePowerChange = optimizationResults.reduce(
        (sum, result) => sum + result.battlePowerChange, 
        0
      );
      
      const result = {
        characterId,
        formationId,
        optimizationResults,
        totalBattlePowerChange,
        applied: autoApply
      };
      
      return { 
        success: true, 
        data: result, 
        message: `装备优化完成，预计战力提升${totalBattlePowerChange}点${autoApply ? '，已自动应用' : ''}` 
      };
    });
  }

  /**
   * 阵容战术匹配
   * 为指定阵容推荐最适合的战术配置
   *
   * @param payload 战术匹配参数
   * @returns 战术匹配结果
   */
  @MessagePattern('orchestrator.formation.matchTactics')
  async matchTacticsForFormation(@Payload() payload: MatchTacticsForFormationPayloadDto): Promise<XResponse<{
    characterId: string;
    formationId: string;
    tacticRecommendations: Array<{
      tacticId: string;
      tacticName: string;
      compatibility: number;
      expectedBonus: number;
      description: string;
      pros: string[];
      cons: string[];
    }>;
    currentTactics: any[];
  }>> {
    return this.handleRequest(async () => {
      this.logger.log(`阵容战术匹配: ${payload.characterId}`, {
        formationId: payload.formationId,
        tacticType: payload.tacticType
      });
      
      const { 
        characterId, 
        formationId, 
        tacticType = 'balanced',
        maxRecommendations = 3 
      } = payload;
      
      // 简化版本：返回模拟战术推荐
      const tacticRecommendations = [
        {
          tacticId: 'tactic_1',
          tacticName: '快速反击',
          compatibility: 92,
          expectedBonus: 1200,
          description: '利用速度优势快速转换攻防',
          pros: ['反击速度快', '出其不意', '适合速度型球员'],
          cons: ['需要较高体能', '对传球精度要求高']
        },
        {
          tacticId: 'tactic_2',
          tacticName: '控球战术',
          compatibility: 85,
          expectedBonus: 1000,
          description: '通过控球主导比赛节奏',
          pros: ['控制比赛节奏', '减少失误', '消耗对手体能'],
          cons: ['需要技术型球员', '进攻效率可能较低']
        },
        {
          tacticId: 'tactic_3',
          tacticName: '高位逼抢',
          compatibility: 78,
          expectedBonus: 800,
          description: '在前场积极逼抢制造机会',
          pros: ['制造更多机会', '压迫对手', '快速夺回球权'],
          cons: ['体能消耗大', '容易被反击', '需要整体配合']
        }
      ];
      
      const result = {
        characterId,
        formationId,
        tacticRecommendations: tacticRecommendations.slice(0, maxRecommendations),
        currentTactics: [] // 这里应该获取当前使用的战术
      };
      
      return { success: true, data: result, message: `找到${result.tacticRecommendations.length}个匹配的战术` };
    });
  }

  /**
   * 执行阵容分析
   */
  private async performFormationAnalysis(formation: any, heroes: any[], analysisType: string): Promise<any> {
    const analysis = {
      strengths: [],
      weaknesses: [],
      recommendations: [],
      battlePowerDistribution: { attack: 0, defense: 0, balance: 0 },
      positionAnalysis: []
    };

    // 1. 分析阵容基础属性
    const formationStats = this.analyzeFormationStats(formation, heroes);

    // 2. 分析位置配置
    const positionAnalysis = this.analyzePositions(formation, heroes);
    analysis.positionAnalysis = positionAnalysis;

    // 3. 分析战力分布
    analysis.battlePowerDistribution = this.analyzeBattlePowerDistribution(heroes);

    // 4. 识别优势
    analysis.strengths = this.identifyStrengths(formationStats, positionAnalysis);

    // 5. 识别弱点
    analysis.weaknesses = this.identifyWeaknesses(formationStats, positionAnalysis);

    // 6. 生成建议
    analysis.recommendations = this.generateRecommendations(analysis.strengths, analysis.weaknesses, analysisType);

    return analysis;
  }

  /**
   * 分析阵容统计数据
   */
  private analyzeFormationStats(formation: any, heroes: any[]): any {
    const stats = {
      totalAttack: 0,
      totalDefense: 0,
      totalSpeed: 0,
      averageLevel: 0,
      averageStar: 0,
      positionCount: 0,
      emptyPositions: 0
    };

    heroes.forEach(hero => {
      stats.totalAttack += hero.attack || 0;
      stats.totalDefense += hero.defense || 0;
      stats.totalSpeed += hero.speed || 0;
      stats.averageLevel += hero.level || 1;
      stats.averageStar += hero.star || 1;
    });

    const heroCount = heroes.length;
    if (heroCount > 0) {
      stats.averageLevel = Math.floor(stats.averageLevel / heroCount);
      stats.averageStar = Math.floor(stats.averageStar / heroCount);
    }

    stats.positionCount = Object.keys(formation.positionToHerosObject || {}).length;
    stats.emptyPositions = 11 - stats.positionCount; // 标准11人阵容

    return stats;
  }

  /**
   * 分析位置配置
   */
  private analyzePositions(formation: any, heroes: any[]): any[] {
    const positionAnalysis = [];
    const positions = formation.positionToHerosObject || {};

    // 标准位置配置
    const standardPositions = [
      { id: 'GK', name: '门将', type: 'goalkeeper' },
      { id: 'LB', name: '左后卫', type: 'defender' },
      { id: 'CB1', name: '中后卫1', type: 'defender' },
      { id: 'CB2', name: '中后卫2', type: 'defender' },
      { id: 'RB', name: '右后卫', type: 'defender' },
      { id: 'LM', name: '左中场', type: 'midfielder' },
      { id: 'CM1', name: '中场1', type: 'midfielder' },
      { id: 'CM2', name: '中场2', type: 'midfielder' },
      { id: 'RM', name: '右中场', type: 'midfielder' },
      { id: 'ST1', name: '前锋1', type: 'forward' },
      { id: 'ST2', name: '前锋2', type: 'forward' }
    ];

    standardPositions.forEach(position => {
      const heroId = positions[position.id];
      const hero = heroes.find(h => h.heroId === heroId);

      if (hero) {
        const adaptability = this.calculatePositionAdaptability(hero, position);
        const suggestions = this.generatePositionSuggestions(hero, position, adaptability);

        positionAnalysis.push({
          position: position.id,
          positionName: position.name,
          heroId: hero.heroId,
          heroName: hero.name || `英雄${hero.heroId}`,
          heroLevel: hero.level || 1,
          heroStar: hero.star || 1,
          adaptability,
          suggestions
        });
      } else {
        positionAnalysis.push({
          position: position.id,
          positionName: position.name,
          heroId: null,
          heroName: '空位',
          adaptability: 0,
          suggestions: [`${position.name}位置空缺，需要配置英雄`]
        });
      }
    });

    return positionAnalysis;
  }

  /**
   * 计算位置适应性
   */
  private calculatePositionAdaptability(hero: any, position: any): number {
    let adaptability = 50; // 基础适应性

    // 基于英雄属性计算适应性
    switch (position.type) {
      case 'goalkeeper':
        adaptability += (hero.goalkeeping || 0) * 0.8 + (hero.defense || 0) * 0.2;
        break;
      case 'defender':
        adaptability += (hero.defense || 0) * 0.6 + (hero.speed || 0) * 0.3 + (hero.stamina || 0) * 0.1;
        break;
      case 'midfielder':
        adaptability += (hero.passing || 0) * 0.4 + (hero.stamina || 0) * 0.3 + (hero.attack || 0) * 0.2 + (hero.defense || 0) * 0.1;
        break;
      case 'forward':
        adaptability += (hero.attack || 0) * 0.5 + (hero.shooting || 0) * 0.3 + (hero.speed || 0) * 0.2;
        break;
    }

    return Math.min(100, Math.max(0, Math.floor(adaptability)));
  }

  /**
   * 生成位置建议
   */
  private generatePositionSuggestions(hero: any, position: any, adaptability: number): string[] {
    const suggestions = [];

    if (adaptability >= 90) {
      suggestions.push('位置配置完美，无需调整');
    } else if (adaptability >= 70) {
      suggestions.push('位置配置良好，可考虑微调');
    } else if (adaptability >= 50) {
      suggestions.push('位置配置一般，建议寻找更适合的英雄');
    } else {
      suggestions.push('位置配置不佳，强烈建议更换英雄');
    }

    // 基于英雄属性给出具体建议
    if (position.type === 'goalkeeper' && (hero.goalkeeping || 0) < 70) {
      suggestions.push('门将技能偏低，影响防守效果');
    }
    if (position.type === 'forward' && (hero.attack || 0) < 70) {
      suggestions.push('攻击力不足，影响进球效率');
    }

    return suggestions;
  }

  /**
   * 分析战力分布
   */
  private analyzeBattlePowerDistribution(heroes: any[]): any {
    let totalAttack = 0;
    let totalDefense = 0;
    let totalBalance = 0;

    heroes.forEach(hero => {
      totalAttack += hero.attack || 0;
      totalDefense += hero.defense || 0;
      totalBalance += (hero.passing || 0) + (hero.stamina || 0);
    });

    const total = totalAttack + totalDefense + totalBalance;

    return {
      attack: total > 0 ? Math.floor((totalAttack / total) * 100) : 0,
      defense: total > 0 ? Math.floor((totalDefense / total) * 100) : 0,
      balance: total > 0 ? Math.floor((totalBalance / total) * 100) : 0
    };
  }

  /**
   * 识别优势
   */
  private identifyStrengths(stats: any, positionAnalysis: any[]): string[] {
    const strengths = [];

    if (stats.averageLevel >= 80) {
      strengths.push('英雄等级普遍较高，整体实力强劲');
    }
    if (stats.averageStar >= 4) {
      strengths.push('英雄星级较高，属性加成显著');
    }
    if (stats.emptyPositions === 0) {
      strengths.push('阵容配置完整，无空位');
    }

    const highAdaptabilityCount = positionAnalysis.filter(p => p.adaptability >= 80).length;
    if (highAdaptabilityCount >= 8) {
      strengths.push('大部分位置适应性良好，阵容协调性强');
    }

    if (stats.totalAttack > stats.totalDefense * 1.2) {
      strengths.push('攻击力突出，进攻威胁大');
    }
    if (stats.totalDefense > stats.totalAttack * 1.2) {
      strengths.push('防守稳固，不易失球');
    }

    return strengths;
  }

  /**
   * 识别弱点
   */
  private identifyWeaknesses(stats: any, positionAnalysis: any[]): string[] {
    const weaknesses = [];

    if (stats.averageLevel < 50) {
      weaknesses.push('英雄等级偏低，需要加强训练');
    }
    if (stats.averageStar < 3) {
      weaknesses.push('英雄星级不足，影响属性发挥');
    }
    if (stats.emptyPositions > 0) {
      weaknesses.push(`有${stats.emptyPositions}个位置空缺，影响阵容完整性`);
    }

    const lowAdaptabilityCount = positionAnalysis.filter(p => p.adaptability < 50).length;
    if (lowAdaptabilityCount >= 3) {
      weaknesses.push('多个位置适应性不佳，影响整体发挥');
    }

    const goalkeepers = positionAnalysis.filter(p => p.position === 'GK');
    if (goalkeepers.length > 0 && goalkeepers[0].adaptability < 70) {
      weaknesses.push('门将位置配置不理想，防守存在隐患');
    }

    return weaknesses;
  }

  /**
   * 生成建议
   */
  private generateRecommendations(strengths: string[], weaknesses: string[], analysisType: string): string[] {
    const recommendations = [];

    // 基于弱点生成改进建议
    if (weaknesses.some(w => w.includes('等级偏低'))) {
      recommendations.push('优先提升核心位置英雄的等级');
    }
    if (weaknesses.some(w => w.includes('星级不足'))) {
      recommendations.push('通过英雄升星提升属性上限');
    }
    if (weaknesses.some(w => w.includes('空缺'))) {
      recommendations.push('尽快配置空缺位置的英雄');
    }
    if (weaknesses.some(w => w.includes('适应性不佳'))) {
      recommendations.push('调整英雄位置或更换更适合的英雄');
    }

    // 基于分析类型生成专业建议
    if (analysisType === 'detailed') {
      recommendations.push('建议定期分析阵容表现，根据比赛结果调整配置');
      recommendations.push('关注英雄间的配合度，优化传球路线');
    }
    if (analysisType === 'tactical') {
      recommendations.push('根据对手特点选择合适的战术配置');
      recommendations.push('考虑英雄的体能分配，避免关键时刻体力不支');
    }

    return recommendations;
  }

  /**
   * 计算总体评分
   */
  private calculateOverallRating(analysis: any): number {
    let rating = 50; // 基础评分

    // 基于优势加分
    rating += analysis.strengths.length * 5;

    // 基于弱点减分
    rating -= analysis.weaknesses.length * 3;

    // 基于位置适应性调整
    const avgAdaptability = analysis.positionAnalysis.reduce((sum, p) => sum + p.adaptability, 0) / analysis.positionAnalysis.length;
    rating += (avgAdaptability - 50) * 0.5;

    return Math.min(100, Math.max(0, Math.floor(rating)));
  }

  /**
   * 计算阵容战力
   */
  private calculateFormationBattlePower(formation: any): number {
    let battlePower = 0;

    // 基础阵容属性
    battlePower += (formation.attack || 0) * 100;
    battlePower += (formation.defend || 0) * 100;

    // 阵容等级加成
    battlePower += (formation.level || 1) * 500;

    // 位置完整度加成
    const positionCount = Object.keys(formation.positionToHerosObject || {}).length;
    battlePower += positionCount * 1000;

    return Math.floor(battlePower);
  }

  /**
   * 分析位置变化
   */
  private analyzePositionChanges(formation1: any, formation2: any): any[] {
    const changes = [];
    const positions1 = formation1.positionToHerosObject || {};
    const positions2 = formation2.positionToHerosObject || {};

    const allPositions = new Set([...Object.keys(positions1), ...Object.keys(positions2)]);

    allPositions.forEach(position => {
      const hero1 = positions1[position];
      const hero2 = positions2[position];

      if (hero1 !== hero2) {
        changes.push({
          position,
          hero1: hero1 || '空位',
          hero2: hero2 || '空位',
          impact: this.analyzePositionChangeImpact(position, hero1, hero2)
        });
      }
    });

    return changes;
  }

  /**
   * 分析位置变化影响
   */
  private analyzePositionChangeImpact(position: string, hero1: string, hero2: string): string {
    if (!hero1 && hero2) {
      return `${position}位置从空位配置了英雄，阵容完整性提升`;
    } else if (hero1 && !hero2) {
      return `${position}位置英雄被移除，阵容完整性下降`;
    } else if (hero1 && hero2) {
      return `${position}位置英雄更换，可能影响该位置表现`;
    }
    return '无明显影响';
  }

  /**
   * 生成对比建议
   */
  private generateComparisonRecommendation(battlePower1: number, battlePower2: number, analysis1: any, analysis2: any): string {
    const powerDiff = battlePower2 - battlePower1;
    const rating1 = this.calculateOverallRating(analysis1);
    const rating2 = this.calculateOverallRating(analysis2);
    const ratingDiff = rating2 - rating1;

    if (powerDiff > 5000 && ratingDiff > 10) {
      return `阵容2在战力(+${powerDiff})和综合评分(+${ratingDiff})上都有显著优势，强烈推荐使用`;
    } else if (powerDiff > 2000 && ratingDiff > 5) {
      return `阵容2在战力(+${powerDiff})和综合评分(+${ratingDiff})上有优势，建议使用`;
    } else if (powerDiff < -2000 && ratingDiff < -5) {
      return `阵容1在战力和综合评分上更优，建议继续使用阵容1`;
    } else if (Math.abs(powerDiff) < 1000 && Math.abs(ratingDiff) < 3) {
      return '两个阵容实力相近，可根据对手特点和战术需要选择';
    } else if (powerDiff > 0 && ratingDiff < 0) {
      return '阵容2战力更高但综合评分较低，需要权衡考虑';
    } else if (powerDiff < 0 && ratingDiff > 0) {
      return '阵容2综合评分更高但战力较低，建议根据比赛重要性选择';
    } else {
      return '建议根据具体比赛情况和对手特点选择合适的阵容';
    }
  }

  /**
   * 生成阵容推荐
   */
  private async generateFormationRecommendations(
    characterId: string,
    availableHeroes: any[],
    strategy: string,
    formationType?: string,
    maxRecommendations: number = 3
  ): Promise<any[]> {
    const recommendations = [];

    // 定义可用的阵型
    const formationTypes = formationType ? [formationType] : ['4-4-2', '4-3-3', '3-5-2', '5-4-1'];

    for (let i = 0; i < Math.min(maxRecommendations, formationTypes.length); i++) {
      const currentFormationType = formationTypes[i];
      const positions = this.getFormationPositions({ formationType: currentFormationType });

      // 为每个位置选择最适合的英雄
      const positionMapping = await this.optimizeHeroPositions(availableHeroes, positions, strategy);

      // 计算预期战力
      const expectedBattlePower = this.calculateExpectedBattlePower(positionMapping, positions);

      // 分析阵容特点
      const analysis = this.analyzeRecommendedFormation(positionMapping, positions, strategy);

      recommendations.push({
        rank: i + 1,
        formationId: `recommended_${i + 1}`,
        formationType: currentFormationType,
        expectedBattlePower,
        positionMapping,
        strengths: analysis.strengths,
        weaknesses: analysis.weaknesses,
        suitableFor: analysis.suitableFor,
        confidence: analysis.confidence,
        tacticalAdvice: analysis.tacticalAdvice
      });
    }

    // 按预期战力排序
    recommendations.sort((a, b) => b.expectedBattlePower - a.expectedBattlePower);

    // 重新分配排名
    recommendations.forEach((rec, index) => {
      rec.rank = index + 1;
    });

    return recommendations;
  }

  /**
   * 优化英雄位置分配
   */
  private async optimizeHeroPositions(heroes: any[], positions: any[], strategy: string): Promise<any> {
    const positionMapping = {};
    const usedHeroes = new Set();

    // 按位置优先级排序（门将最重要）
    const sortedPositions = [...positions].sort((a, b) => a.priority - b.priority);

    for (const position of sortedPositions) {
      const availableHeroes = heroes.filter(hero => !usedHeroes.has(hero.heroId));

      if (availableHeroes.length === 0) break;

      // 为当前位置选择最适合的英雄
      const bestHero = this.selectBestHeroForPosition(availableHeroes, position, strategy);

      if (bestHero) {
        positionMapping[position.id] = bestHero.heroId;
        usedHeroes.add(bestHero.heroId);
      }
    }

    return positionMapping;
  }

  /**
   * 为位置选择最佳英雄
   */
  private selectBestHeroForPosition(heroes: any[], position: any, strategy: string): any {
    let bestHero = null;
    let bestScore = -1;

    for (const hero of heroes) {
      const score = this.calculateHeroPositionScore(hero, position, strategy);

      if (score > bestScore) {
        bestScore = score;
        bestHero = hero;
      }
    }

    return bestHero;
  }

  /**
   * 计算预期战力
   */
  private calculateExpectedBattlePower(positionMapping: any, positions: any[]): number {
    let totalBattlePower = 0;

    // 基础战力（基于位置数量）
    const filledPositions = Object.keys(positionMapping).length;
    totalBattlePower += filledPositions * 5000;

    // 位置完整度加成
    if (filledPositions === positions.length) {
      totalBattlePower += 10000; // 完整阵容加成
    }

    // 阵型基础战力
    totalBattlePower += 30000;

    return Math.floor(totalBattlePower);
  }

  /**
   * 分析推荐阵容
   */
  private analyzeRecommendedFormation(positionMapping: any, positions: any[], strategy: string): any {
    const analysis = {
      strengths: [],
      weaknesses: [],
      suitableFor: [],
      confidence: 80,
      tacticalAdvice: []
    };

    const filledPositions = Object.keys(positionMapping).length;
    const totalPositions = positions.length;

    // 分析完整度
    if (filledPositions === totalPositions) {
      analysis.strengths.push('阵容配置完整');
      analysis.confidence += 10;
    } else {
      analysis.weaknesses.push(`${totalPositions - filledPositions}个位置未配置`);
      analysis.confidence -= (totalPositions - filledPositions) * 5;
    }

    // 基于策略分析
    switch (strategy) {
      case 'attack':
        analysis.strengths.push('攻击配置优化');
        analysis.suitableFor.push('需要进攻的比赛');
        analysis.tacticalAdvice.push('重点加强前场配合');
        break;
      case 'defense':
        analysis.strengths.push('防守配置稳固');
        analysis.suitableFor.push('防守反击战术');
        analysis.tacticalAdvice.push('注意后场协防');
        break;
      case 'balanced':
        analysis.strengths.push('攻守平衡');
        analysis.suitableFor.push('各类比赛');
        analysis.tacticalAdvice.push('保持攻守平衡');
        break;
    }

    // 通用适用场景
    analysis.suitableFor.push('联赛比赛', '杯赛');

    return analysis;
  }
}
