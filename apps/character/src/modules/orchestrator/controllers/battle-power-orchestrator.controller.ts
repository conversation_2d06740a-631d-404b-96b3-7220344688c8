import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { XResponse } from '@libs/common/types/response.type';
import { BaseController } from '@libs/common/controller';
import { BattlePowerOrchestratorService } from '../services/battle-power-orchestrator.service';
import {
  BattlePowerCalculationDto,
  BattlePowerResultDto
} from '../dto/orchestrator.dto';

/**
 * 战力聚合控制器
 * 提供跨模块的战力计算业务接口
 * 
 * 🎯 接口职责：
 * - 综合战力计算接口
 * - 战力构成分析接口
 * - 战力对比和变化追踪接口
 * - 战力优化建议接口
 * 
 * 🔄 业务场景：
 * - 角色面板显示总战力
 * - 阵容配置时的战力预览
 * - 装备更换时的战力对比
 * - 战力排行榜计算
 */
@Controller()
export class BattlePowerOrchestratorController extends BaseController {
  constructor(
    private readonly battlePowerOrchestratorService: BattlePowerOrchestratorService,
  ) {
    super('BattlePowerOrchestratorController');
  }

  /**
   * 计算综合战力
   * 聚合所有影响战力的因素，计算角色或阵容的总战力
   * 
   * @param payload 战力计算参数
   * @returns 详细的战力计算结果
   */
  @MessagePattern('orchestrator.battlePower.calculate')
  async calculateBattlePower(@Payload() payload: BattlePowerCalculationDto): Promise<XResponse<BattlePowerResultDto>> {
    return this.handleRequest(async () => {
      this.logger.log(`计算综合战力: ${payload.characterId}`, {
        formationId: payload.formationId,
        includeEquipment: payload.includeEquipment,
        includeTactics: payload.includeTactics,
        includeBelief: payload.includeBelief
      });
      
      const result = await this.battlePowerOrchestratorService.calculateBattlePower(payload);
      
      return result;
    });
  }

  /**
   * 快速战力计算
   * 只计算基础战力，不包含复杂的加成计算
   * 
   * @param payload 快速计算参数
   * @returns 简化的战力结果
   */
  @MessagePattern('orchestrator.battlePower.quickCalculate')
  async quickCalculateBattlePower(@Payload() payload: {
    characterId: string;
    formationId?: string;
  }): Promise<XResponse<{
    characterId: string;
    formationId: string;
    totalBattlePower: number;
    heroBattlePower: number;
    calculationTime: number;
  }>> {
    return this.handleRequest(async () => {
      this.logger.log(`快速战力计算: ${payload.characterId}`);
      
      const startTime = Date.now();
      
      // 使用简化的计算参数
      const calculationDto: BattlePowerCalculationDto = {
        characterId: payload.characterId,
        formationId: payload.formationId,
        includeEquipment: false,
        includeTactics: false,
        includeBelief: false
      };
      
      const result = await this.battlePowerOrchestratorService.calculateBattlePower(calculationDto);
      
      if (result.success) {
        const quickResult = {
          characterId: result.data.characterId,
          formationId: result.data.formationId,
          totalBattlePower: result.data.heroBattlePower + result.data.formationBonus,
          heroBattlePower: result.data.heroBattlePower,
          calculationTime: Date.now() - startTime
        };
        
        return { success: true, data: quickResult, message: '快速战力计算完成' };
      }
      
      return result;
    });
  }

  /**
   * 批量战力计算
   * 为多个角色或阵容批量计算战力
   * 
   * @param payload 批量计算参数
   * @returns 批量战力计算结果
   */
  @MessagePattern('orchestrator.battlePower.batchCalculate')
  async batchCalculateBattlePower(@Payload() payload: {
    calculations: Array<{
      characterId: string;
      formationId?: string;
      includeEquipment?: boolean;
      includeTactics?: boolean;
      includeBelief?: boolean;
    }>;
  }): Promise<XResponse<{
    results: BattlePowerResultDto[];
    failedCalculations: Array<{
      characterId: string;
      error: string;
    }>;
    totalCalculations: number;
    successCount: number;
    failedCount: number;
  }>> {
    return this.handleRequest(async () => {
      this.logger.log(`批量战力计算`, { count: payload.calculations.length });
      
      const results: BattlePowerResultDto[] = [];
      const failedCalculations: Array<{ characterId: string; error: string }> = [];
      
      // 并行计算所有战力
      const promises = payload.calculations.map(async (calc) => {
        try {
          const result = await this.battlePowerOrchestratorService.calculateBattlePower(calc);
          if (result.success) {
            return { success: true, data: result.data };
          } else {
            return { success: false, characterId: calc.characterId, error: result.message };
          }
        } catch (error) {
          return { success: false, characterId: calc.characterId, error: error.message };
        }
      });
      
      const batchResults = await Promise.allSettled(promises);
      
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          if (result.value.success) {
            results.push(result.value.data);
          } else {
            failedCalculations.push({
              characterId: result.value.characterId,
              error: result.value.error
            });
          }
        } else {
          failedCalculations.push({
            characterId: payload.calculations[index].characterId,
            error: result.reason?.message || '计算失败'
          });
        }
      });
      
      const batchResult = {
        results,
        failedCalculations,
        totalCalculations: payload.calculations.length,
        successCount: results.length,
        failedCount: failedCalculations.length
      };
      
      return { success: true, data: batchResult, message: `批量计算完成，成功${results.length}个，失败${failedCalculations.length}个` };
    });
  }

  /**
   * 战力对比
   * 对比两个配置的战力差异
   * 
   * @param payload 战力对比参数
   * @returns 战力对比结果
   */
  @MessagePattern('orchestrator.battlePower.compare')
  async compareBattlePower(@Payload() payload: {
    characterId: string;
    baseConfiguration: {
      formationId?: string;
      includeEquipment?: boolean;
      includeTactics?: boolean;
      includeBelief?: boolean;
    };
    compareConfiguration: {
      formationId?: string;
      includeEquipment?: boolean;
      includeTactics?: boolean;
      includeBelief?: boolean;
    };
  }): Promise<XResponse<{
    characterId: string;
    baseBattlePower: BattlePowerResultDto;
    compareBattlePower: BattlePowerResultDto;
    difference: {
      totalDifference: number;
      heroDifference: number;
      equipmentDifference: number;
      tacticDifference: number;
      beliefDifference: number;
      formationDifference: number;
    };
    percentageChange: number;
    recommendation: string;
  }>> {
    return this.handleRequest(async () => {
      this.logger.log(`战力对比: ${payload.characterId}`);
      
      const { characterId, baseConfiguration, compareConfiguration } = payload;
      
      // 并行计算两个配置的战力
      const [baseResult, compareResult] = await Promise.allSettled([
        this.battlePowerOrchestratorService.calculateBattlePower({
          characterId,
          ...baseConfiguration
        }),
        this.battlePowerOrchestratorService.calculateBattlePower({
          characterId,
          ...compareConfiguration
        })
      ]);
      
      if (baseResult.status === 'rejected' || !baseResult.value.success) {
        throw new Error('基础配置战力计算失败');
      }
      
      if (compareResult.status === 'rejected' || !compareResult.value.success) {
        throw new Error('对比配置战力计算失败');
      }
      
      const basePower = baseResult.value.data;
      const comparePower = compareResult.value.data;
      
      // 计算差异
      const difference = {
        totalDifference: comparePower.totalBattlePower - basePower.totalBattlePower,
        heroDifference: comparePower.heroBattlePower - basePower.heroBattlePower,
        equipmentDifference: comparePower.equipmentBonus - basePower.equipmentBonus,
        tacticDifference: comparePower.tacticBonus - basePower.tacticBonus,
        beliefDifference: comparePower.beliefBonus - basePower.beliefBonus,
        formationDifference: comparePower.formationBonus - basePower.formationBonus
      };
      
      const percentageChange = basePower.totalBattlePower > 0 
        ? (difference.totalDifference / basePower.totalBattlePower) * 100 
        : 0;
      
      // 生成建议
      let recommendation = '';
      if (difference.totalDifference > 0) {
        recommendation = `对比配置战力提升${difference.totalDifference}点（+${percentageChange.toFixed(2)}%），建议采用`;
      } else if (difference.totalDifference < 0) {
        recommendation = `对比配置战力下降${Math.abs(difference.totalDifference)}点（${percentageChange.toFixed(2)}%），不建议采用`;
      } else {
        recommendation = '两个配置战力相同，可根据其他因素选择';
      }
      
      const result = {
        characterId,
        baseBattlePower: basePower,
        compareBattlePower: comparePower,
        difference,
        percentageChange,
        recommendation
      };
      
      return { success: true, data: result, message: '战力对比完成' };
    });
  }

  /**
   * 战力排行榜
   * 获取服务器或全服的战力排行榜
   * 
   * @param payload 排行榜参数
   * @returns 战力排行榜结果
   */
  @MessagePattern('orchestrator.battlePower.ranking')
  async getBattlePowerRanking(@Payload() payload: {
    serverId?: string;
    limit?: number;
    offset?: number;
    includeDetails?: boolean;
  }): Promise<XResponse<{
    rankings: Array<{
      rank: number;
      characterId: string;
      characterName: string;
      totalBattlePower: number;
      level: number;
      details?: BattlePowerResultDto;
    }>;
    total: number;
    serverInfo?: {
      serverId: string;
      serverName: string;
    };
  }>> {
    return this.handleRequest(async () => {
      this.logger.log(`获取战力排行榜`, payload);
      
      const { serverId, limit = 100, offset = 0, includeDetails = false } = payload;
      
      // 简化版本：返回模拟排行榜数据
      const mockRankings = [];
      for (let i = 1; i <= Math.min(limit, 50); i++) {
        const rank = offset + i;
        const mockRanking = {
          rank,
          characterId: `character_${rank}`,
          characterName: `玩家${rank}`,
          totalBattlePower: 100000 - (rank - 1) * 1000,
          level: Math.max(1, 100 - Math.floor((rank - 1) / 10))
        };
        
        if (includeDetails) {
          // 这里应该调用实际的战力计算
          mockRanking['details'] = null;
        }
        
        mockRankings.push(mockRanking);
      }
      
      const result = {
        rankings: mockRankings,
        total: 1000, // 模拟总数
        serverInfo: serverId ? {
          serverId,
          serverName: `服务器${serverId}`
        } : undefined
      };
      
      return { success: true, data: result, message: '获取排行榜成功' };
    });
  }
}
