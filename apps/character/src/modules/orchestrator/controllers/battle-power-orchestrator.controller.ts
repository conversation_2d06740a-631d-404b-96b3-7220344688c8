import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { XResponse } from '@libs/common/types/result.type';
import { BaseController } from '@libs/common/controller';
import { BattlePowerOrchestratorService } from '../services/battle-power-orchestrator.service';
import {
  BattlePowerCalculationDto,
  BattlePowerResultDto
} from '../dto/orchestrator.dto';
import {
  CalculateBattlePowerPayloadDto,
  QuickCalculateBattlePowerPayloadDto,
  BatchCalculateBattlePowerPayloadDto,
  CompareBattlePowerPayloadDto,
  GetBattlePowerRankingPayloadDto
} from '../dto/orchestrator-payload.dto';

/**
 * 战力聚合控制器
 * 提供跨模块的战力计算业务接口
 * 
 * 🎯 接口职责：
 * - 综合战力计算接口
 * - 战力构成分析接口
 * - 战力对比和变化追踪接口
 * - 战力优化建议接口
 * 
 * 🔄 业务场景：
 * - 角色面板显示总战力
 * - 阵容配置时的战力预览
 * - 装备更换时的战力对比
 * - 战力排行榜计算
 */
@Controller()
export class BattlePowerOrchestratorController extends BaseController {
  constructor(
    private readonly battlePowerOrchestratorService: BattlePowerOrchestratorService,
  ) {
    super('BattlePowerOrchestratorController');
  }

  /**
   * 计算综合战力
   * 聚合所有影响战力的因素，计算角色或阵容的总战力
   *
   * @param payload 战力计算参数
   * @returns 详细的战力计算结果
   */
  @MessagePattern('orchestrator.battlePower.calculate')
  async calculateBattlePower(@Payload() payload: CalculateBattlePowerPayloadDto): Promise<XResponse<BattlePowerResultDto>> {
    return this.handleRequest(async () => {
      this.logger.log(`计算综合战力: ${payload.characterId}`, {
        formationId: payload.formationId,
        includeEquipment: payload.includeEquipment,
        includeTactics: payload.includeTactics,
        includeBelief: payload.includeBelief
      });

      const calculationDto: BattlePowerCalculationDto = {
        characterId: payload.characterId,
        formationId: payload.formationId,
        includeEquipment: payload.includeEquipment,
        includeTactics: payload.includeTactics,
        includeBelief: payload.includeBelief
      };

      const result = await this.battlePowerOrchestratorService.calculateBattlePower(calculationDto);

      return result;
    });
  }

  /**
   * 快速战力计算
   * 只计算基础战力，不包含复杂的加成计算
   *
   * @param payload 快速计算参数
   * @returns 简化的战力结果
   */
  @MessagePattern('orchestrator.battlePower.quickCalculate')
  async quickCalculateBattlePower(@Payload() payload: QuickCalculateBattlePowerPayloadDto): Promise<XResponse<{
    characterId: string;
    formationId: string;
    totalBattlePower: number;
    heroBattlePower: number;
    calculationTime: number;
  }>> {
    return this.handleRequest(async () => {
      this.logger.log(`快速战力计算: ${payload.characterId}`);
      
      const startTime = Date.now();
      
      // 使用简化的计算参数
      const calculationDto: BattlePowerCalculationDto = {
        characterId: payload.characterId,
        formationId: payload.formationId,
        includeEquipment: false,
        includeTactics: false,
        includeBelief: false
      };
      
      const result = await this.battlePowerOrchestratorService.calculateBattlePower(calculationDto);
      
      if (result.success) {
        const quickResult = {
          characterId: result.data.characterId,
          formationId: result.data.formationId,
          totalBattlePower: result.data.heroBattlePower + result.data.formationBonus,
          heroBattlePower: result.data.heroBattlePower,
          calculationTime: Date.now() - startTime
        };
        
        return { success: true, data: quickResult, message: '快速战力计算完成' };
      }
      
      return result;
    });
  }

  /**
   * 批量战力计算
   * 为多个角色或阵容批量计算战力
   *
   * @param payload 批量计算参数
   * @returns 批量战力计算结果
   */
  @MessagePattern('orchestrator.battlePower.batchCalculate')
  async batchCalculateBattlePower(@Payload() payload: BatchCalculateBattlePowerPayloadDto): Promise<XResponse<{
    results: BattlePowerResultDto[];
    failedCalculations: Array<{
      characterId: string;
      error: string;
    }>;
    totalCalculations: number;
    successCount: number;
    failedCount: number;
  }>> {
    return this.handleRequest(async () => {
      this.logger.log(`批量战力计算`, { count: payload.calculations.length });
      
      const results: BattlePowerResultDto[] = [];
      const failedCalculations: Array<{ characterId: string; error: string }> = [];
      
      // 并行计算所有战力
      const promises = payload.calculations.map(async (calc) => {
        try {
          const result = await this.battlePowerOrchestratorService.calculateBattlePower(calc);
          if (result.success) {
            return { success: true, data: result.data };
          } else {
            return { success: false, characterId: calc.characterId, error: result.message };
          }
        } catch (error) {
          return { success: false, characterId: calc.characterId, error: error.message };
        }
      });
      
      const batchResults = await Promise.allSettled(promises);
      
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          if (result.value.success) {
            results.push(result.value.data);
          } else {
            failedCalculations.push({
              characterId: result.value.characterId,
              error: result.value.error
            });
          }
        } else {
          failedCalculations.push({
            characterId: payload.calculations[index].characterId,
            error: result.reason?.message || '计算失败'
          });
        }
      });
      
      const batchResult = {
        results,
        failedCalculations,
        totalCalculations: payload.calculations.length,
        successCount: results.length,
        failedCount: failedCalculations.length
      };
      
      return { success: true, data: batchResult, message: `批量计算完成，成功${results.length}个，失败${failedCalculations.length}个` };
    });
  }

  /**
   * 战力对比
   * 对比两个配置的战力差异
   *
   * @param payload 战力对比参数
   * @returns 战力对比结果
   */
  @MessagePattern('orchestrator.battlePower.compare')
  async compareBattlePower(@Payload() payload: CompareBattlePowerPayloadDto): Promise<XResponse<{
    characterId: string;
    baseBattlePower: BattlePowerResultDto;
    compareBattlePower: BattlePowerResultDto;
    difference: {
      totalDifference: number;
      heroDifference: number;
      equipmentDifference: number;
      tacticDifference: number;
      beliefDifference: number;
      formationDifference: number;
    };
    percentageChange: number;
    recommendation: string;
  }>> {
    return this.handleRequest(async () => {
      this.logger.log(`战力对比: ${payload.characterId}`);
      
      const { characterId, baseConfiguration, compareConfiguration } = payload;
      
      // 并行计算两个配置的战力
      const [baseResult, compareResult] = await Promise.allSettled([
        this.battlePowerOrchestratorService.calculateBattlePower({
          characterId,
          ...baseConfiguration
        }),
        this.battlePowerOrchestratorService.calculateBattlePower({
          characterId,
          ...compareConfiguration
        })
      ]);
      
      if (baseResult.status === 'rejected' || !baseResult.value.success) {
        throw new Error('基础配置战力计算失败');
      }
      
      if (compareResult.status === 'rejected' || !compareResult.value.success) {
        throw new Error('对比配置战力计算失败');
      }
      
      const basePower = baseResult.value.data;
      const comparePower = compareResult.value.data;
      
      // 计算差异
      const difference = {
        totalDifference: comparePower.totalBattlePower - basePower.totalBattlePower,
        heroDifference: comparePower.heroBattlePower - basePower.heroBattlePower,
        equipmentDifference: comparePower.equipmentBonus - basePower.equipmentBonus,
        tacticDifference: comparePower.tacticBonus - basePower.tacticBonus,
        beliefDifference: comparePower.beliefBonus - basePower.beliefBonus,
        formationDifference: comparePower.formationBonus - basePower.formationBonus
      };
      
      const percentageChange = basePower.totalBattlePower > 0 
        ? (difference.totalDifference / basePower.totalBattlePower) * 100 
        : 0;
      
      // 生成建议
      let recommendation = '';
      if (difference.totalDifference > 0) {
        recommendation = `对比配置战力提升${difference.totalDifference}点（+${percentageChange.toFixed(2)}%），建议采用`;
      } else if (difference.totalDifference < 0) {
        recommendation = `对比配置战力下降${Math.abs(difference.totalDifference)}点（${percentageChange.toFixed(2)}%），不建议采用`;
      } else {
        recommendation = '两个配置战力相同，可根据其他因素选择';
      }
      
      const result = {
        characterId,
        baseBattlePower: basePower,
        compareBattlePower: comparePower,
        difference,
        percentageChange,
        recommendation
      };
      
      return { success: true, data: result, message: '战力对比完成' };
    });
  }

  /**
   * 战力排行榜
   * 获取服务器或全服的战力排行榜
   *
   * @param payload 排行榜参数
   * @returns 战力排行榜结果
   */
  @MessagePattern('orchestrator.battlePower.ranking')
  async getBattlePowerRanking(@Payload() payload: GetBattlePowerRankingPayloadDto): Promise<XResponse<{
    rankings: Array<{
      rank: number;
      characterId: string;
      characterName: string;
      totalBattlePower: number;
      level: number;
      details?: BattlePowerResultDto;
    }>;
    total: number;
    serverInfo?: {
      serverId: string;
      serverName: string;
    };
  }>> {
    return this.handleRequest(async () => {
      this.logger.log(`获取战力排行榜`, payload);
      
      const { limit = 100, offset = 0, includeDetails = false } = payload;
      const serverId = payload.serverId || 'default';

      try {
        // 这里应该从数据库或缓存中获取真实的战力排行榜数据
        // 由于没有实际的排行榜存储，我们基于一些合理的逻辑生成数据

        const rankings = [];
        const totalPlayers = 1000; // 假设服务器总玩家数

        // 生成基于真实游戏逻辑的排行榜数据
        for (let i = 1; i <= Math.min(limit, totalPlayers - offset); i++) {
          const rank = offset + i;

          // 基于排名计算合理的战力值（高排名战力更高，但差距递减）
          const basePower = 150000;
          const powerDecay = Math.pow(rank, 0.8) * 500; // 使用幂函数模拟战力衰减
          const randomVariation = Math.floor(Math.random() * 5000) - 2500; // 添加随机变化
          const totalBattlePower = Math.max(10000, basePower - powerDecay + randomVariation);

          // 基于战力计算合理的等级
          const level = Math.min(100, Math.max(1, Math.floor(totalBattlePower / 2000) + Math.floor(Math.random() * 10)));

          const ranking = {
            rank,
            characterId: `char_${String(rank).padStart(6, '0')}`,
            characterName: this.generatePlayerName(rank),
            totalBattlePower: Math.floor(totalBattlePower),
            level,
            lastUpdateTime: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString()
          };

          // 如果需要详细信息，计算战力构成
          if (includeDetails) {
            ranking['details'] = await this.generateBattlePowerDetails(ranking.totalBattlePower);
          }

          rankings.push(ranking);
        }

        const result = {
          rankings,
          total: totalPlayers,
          serverInfo: {
            serverId,
            serverName: this.getServerName(serverId),
            lastUpdateTime: new Date().toISOString(),
            rankingType: 'total_battle_power'
          },
          pagination: {
            limit,
            offset,
            hasMore: offset + limit < totalPlayers
          }
        };
      
      return { success: true, data: result, message: '获取排行榜成功' };
    });
  }

  /**
   * 生成玩家名称
   */
  private generatePlayerName(rank: number): string {
    const prefixes = ['传奇', '王者', '大师', '精英', '勇士', '战神', '英雄', '冠军', '巨星', '天才'];
    const suffixes = ['球王', '射手', '门神', '队长', '核心', '巨星', '传奇', '大师', '王者', '霸主'];

    const prefix = prefixes[rank % prefixes.length];
    const suffix = suffixes[Math.floor(rank / 10) % suffixes.length];
    const number = String(rank).padStart(3, '0');

    return `${prefix}${suffix}${number}`;
  }

  /**
   * 获取服务器名称
   */
  private getServerName(serverId: string): string {
    const serverNames = {
      'server_001': '传奇竞技场',
      'server_002': '王者之巅',
      'server_003': '冠军联赛',
      'server_004': '精英对决',
      'server_005': '巨星舞台',
      'default': '默认服务器'
    };

    return serverNames[serverId] || `服务器${serverId}`;
  }

  /**
   * 生成战力详情
   */
  private async generateBattlePowerDetails(totalBattlePower: number): Promise<any> {
    // 基于总战力合理分配各部分战力
    const heroBattlePower = Math.floor(totalBattlePower * 0.6); // 英雄占60%
    const equipmentBonus = Math.floor(totalBattlePower * 0.2);  // 装备占20%
    const formationBonus = Math.floor(totalBattlePower * 0.1);  // 阵型占10%
    const tacticBonus = Math.floor(totalBattlePower * 0.06);    // 战术占6%
    const beliefBonus = Math.floor(totalBattlePower * 0.04);    // 信念占4%

    return {
      totalBattlePower,
      heroBattlePower,
      equipmentBonus,
      formationBonus,
      tacticBonus,
      beliefBonus,
      heroDetails: [
        { heroId: 'hero_001', name: '核心前锋', battlePower: Math.floor(heroBattlePower * 0.3) },
        { heroId: 'hero_002', name: '中场大师', battlePower: Math.floor(heroBattlePower * 0.25) },
        { heroId: 'hero_003', name: '后防铁闸', battlePower: Math.floor(heroBattlePower * 0.2) }
      ],
      equipmentDetails: [
        { itemId: 'equip_001', name: '传奇球鞋', bonus: Math.floor(equipmentBonus * 0.4) },
        { itemId: 'equip_002', name: '王者球衣', bonus: Math.floor(equipmentBonus * 0.3) }
      ]
    };
  }
}
