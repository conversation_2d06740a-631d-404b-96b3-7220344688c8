import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { XResponse } from '@libs/common/types/response.type';
import { BaseController } from '@libs/common/controller';
import { CharacterOrchestratorService } from '../services/character-orchestrator.service';
import {
  CompleteCharacterInfoDto,
  InitializeCharacterDto,
  InitializeCharacterResultDto
} from '../dto/orchestrator.dto';

/**
 * 角色聚合控制器
 * 提供跨模块的角色相关业务接口
 * 
 * 🎯 接口职责：
 * - 角色完整信息聚合接口
 * - 角色初始化业务编排接口
 * - 角色登录业务编排接口
 * - 角色数据同步接口
 * 
 * 🔄 业务场景：
 * - 客户端登录时获取完整角色信息
 * - 新角色创建时的完整初始化流程
 * - 角色数据不一致时的修复流程
 * - 跨模块业务操作的统一入口
 */
@Controller()
export class CharacterOrchestratorController extends BaseController {
  constructor(
    private readonly characterOrchestratorService: CharacterOrchestratorService,
  ) {
    super('CharacterOrchestratorController');
  }

  /**
   * 获取角色完整信息
   * 聚合所有子模块的角色相关数据
   * 
   * @param payload 包含characterId和可选的数据范围配置
   * @returns 完整的角色信息，包括基础数据、阵容、英雄、背包等
   */
  @MessagePattern('orchestrator.character.getCompleteInfo')
  async getCompleteCharacterInfo(@Payload() payload: {
    characterId: string;
    includeFormation?: boolean;
    includeHeroes?: boolean;
    includeInventory?: boolean;
    includeBelief?: boolean;
    includeTactics?: boolean;
    includeBattlePower?: boolean;
  }): Promise<XResponse<CompleteCharacterInfoDto>> {
    return this.handleRequest(async () => {
      this.logger.log(`获取角色完整信息: ${payload.characterId}`);
      
      const { characterId, ...options } = payload;
      const result = await this.characterOrchestratorService.getCompleteCharacterInfo(
        characterId,
        options
      );
      
      return result;
    });
  }

  /**
   * 角色初始化
   * 确保角色在所有子系统中都有完整的数据结构
   * 
   * @param payload 角色初始化参数
   * @returns 初始化结果摘要
   */
  @MessagePattern('orchestrator.character.initialize')
  async initializeCharacter(@Payload() payload: InitializeCharacterDto): Promise<XResponse<InitializeCharacterResultDto>> {
    return this.handleRequest(async () => {
      this.logger.log(`角色初始化: ${payload.characterId}`);
      
      const result = await this.characterOrchestratorService.initializeCharacter(payload);
      
      return result;
    });
  }

  /**
   * 角色登录业务编排
   * 在角色登录时进行数据完整性检查和补偿初始化
   * 
   * @param payload 登录参数
   * @returns 增强的登录结果，包含完整角色信息
   */
  @MessagePattern('orchestrator.character.login')
  async orchestrateCharacterLogin(@Payload() payload: {
    characterId: string;
    userId: string;
    serverId: string;
    sessionId: string;
  }): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`角色登录业务编排: ${payload.characterId}`);
      
      const { characterId, userId, serverId, sessionId } = payload;
      const result = await this.characterOrchestratorService.orchestrateCharacterLogin(
        characterId,
        userId,
        serverId,
        sessionId
      );
      
      return result;
    });
  }

  /**
   * 角色数据完整性检查
   * 检查角色在各子系统中的数据完整性，并执行必要的修复
   * 
   * @param payload 包含characterId
   * @returns 检查和修复结果
   */
  @MessagePattern('orchestrator.character.checkIntegrity')
  async checkCharacterDataIntegrity(@Payload() payload: {
    characterId: string;
    autoRepair?: boolean;
  }): Promise<XResponse<{
    characterId: string;
    integrityStatus: {
      character: boolean;
      formation: boolean;
      heroes: boolean;
      inventory: boolean;
      belief: boolean;
      tactics: boolean;
    };
    repairedSystems: string[];
    issues: string[];
  }>> {
    return this.handleRequest(async () => {
      this.logger.log(`角色数据完整性检查: ${payload.characterId}`);
      
      const { characterId, autoRepair = true } = payload;
      
      // 检查各子系统数据完整性
      const integrityStatus = {
        character: false,
        formation: false,
        heroes: false,
        inventory: false,
        belief: false,
        tactics: false
      };
      
      const issues: string[] = [];
      let repairedSystems: string[] = [];

      try {
        // 这里应该调用各子系统检查数据完整性
        // 简化版本：返回模拟结果
        integrityStatus.character = true;
        integrityStatus.formation = true;
        integrityStatus.heroes = true;
        integrityStatus.inventory = true;
        integrityStatus.belief = true;
        integrityStatus.tactics = true;

        if (autoRepair) {
          // 执行自动修复
          // 这里应该调用characterOrchestratorService的修复方法
          repairedSystems = [];
        }

        const result = {
          characterId,
          integrityStatus,
          repairedSystems,
          issues
        };

        return { success: true, data: result, message: '数据完整性检查完成' };
      } catch (error) {
        this.logger.error('数据完整性检查失败', error);
        throw error;
      }
    });
  }

  /**
   * 批量角色信息获取
   * 为多个角色获取完整信息，用于排行榜、好友列表等场景
   * 
   * @param payload 包含角色ID列表和数据范围配置
   * @returns 多个角色的完整信息
   */
  @MessagePattern('orchestrator.character.batchGetCompleteInfo')
  async batchGetCompleteCharacterInfo(@Payload() payload: {
    characterIds: string[];
    includeFormation?: boolean;
    includeHeroes?: boolean;
    includeInventory?: boolean;
    includeBelief?: boolean;
    includeTactics?: boolean;
    includeBattlePower?: boolean;
  }): Promise<XResponse<CompleteCharacterInfoDto[]>> {
    return this.handleRequest(async () => {
      this.logger.log(`批量获取角色完整信息`, { count: payload.characterIds.length });
      
      const { characterIds, ...options } = payload;
      
      // 并行获取多个角色的完整信息
      const promises = characterIds.map(characterId =>
        this.characterOrchestratorService.getCompleteCharacterInfo(characterId, options)
      );
      
      const results = await Promise.allSettled(promises);
      
      // 过滤成功的结果
      const successResults: CompleteCharacterInfoDto[] = [];
      const failedCharacterIds: string[] = [];
      
      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.success) {
          successResults.push(result.value.data);
        } else {
          failedCharacterIds.push(characterIds[index]);
        }
      });
      
      if (failedCharacterIds.length > 0) {
        this.logger.warn(`部分角色信息获取失败`, { failedCharacterIds });
      }
      
      return { success: true, data: successResults, message: `成功获取${successResults.length}个角色信息` };
    });
  }

  /**
   * 角色数据同步
   * 强制同步角色在各子系统中的数据，确保一致性
   * 
   * @param payload 包含characterId和同步选项
   * @returns 同步结果
   */
  @MessagePattern('orchestrator.character.syncData')
  async syncCharacterData(@Payload() payload: {
    characterId: string;
    syncFormation?: boolean;
    syncHeroes?: boolean;
    syncInventory?: boolean;
    syncBelief?: boolean;
    syncTactics?: boolean;
    forceSync?: boolean;
  }): Promise<XResponse<{
    characterId: string;
    syncedSystems: string[];
    syncResults: any[];
    errors: string[];
  }>> {
    return this.handleRequest(async () => {
      this.logger.log(`角色数据同步: ${payload.characterId}`);
      
      const { characterId, forceSync = false, ...syncOptions } = payload;
      
      const syncedSystems: string[] = [];
      const syncResults: any[] = [];
      const errors: string[] = [];
      
      try {
        // 这里应该实现具体的数据同步逻辑
        // 简化版本：返回模拟结果
        
        if (syncOptions.syncFormation !== false) {
          syncedSystems.push('formation');
          syncResults.push({ system: 'formation', status: 'success' });
        }
        
        if (syncOptions.syncHeroes !== false) {
          syncedSystems.push('heroes');
          syncResults.push({ system: 'heroes', status: 'success' });
        }
        
        if (syncOptions.syncInventory !== false) {
          syncedSystems.push('inventory');
          syncResults.push({ system: 'inventory', status: 'success' });
        }
        
        if (syncOptions.syncBelief !== false) {
          syncedSystems.push('belief');
          syncResults.push({ system: 'belief', status: 'success' });
        }
        
        if (syncOptions.syncTactics !== false) {
          syncedSystems.push('tactics');
          syncResults.push({ system: 'tactics', status: 'success' });
        }
        
        const result = {
          characterId,
          syncedSystems,
          syncResults,
          errors
        };
        
        return { success: true, data: result, message: `数据同步完成，同步了${syncedSystems.length}个系统` };
      } catch (error) {
        this.logger.error('角色数据同步失败', error);
        throw error;
      }
    });
  }
}
