import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { BaseController } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes/microservice-validation.pipe';
import { XResponse } from '@libs/common/types/result.type';
import { PowerOrchestratorService } from '../services/power-orchestrator.service';
import { CalculatePowerPayloadDto } from '../dto/power-orchestrator.dto';

@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class OrchestratorPowerController extends BaseController {
  constructor(private readonly orchestrator: PowerOrchestratorService) {
    super('OrchestratorPowerController');
  }

  @MessagePattern('orchestrator.power.calculate')
  async calculate(@Payload() payload: CalculatePowerPayloadDto): Promise<XResponse<any>> {
    const result = await this.orchestrator.calculate(payload);
    return this.fromResult(result);
  }
}
