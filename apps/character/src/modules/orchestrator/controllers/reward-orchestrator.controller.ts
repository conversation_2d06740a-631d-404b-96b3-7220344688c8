import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { XResponse, XResultUtils } from '@libs/common/types/result.type';
import { BaseController } from '@libs/common/controller';
import { RewardOrchestratorService } from '../services/reward-orchestrator.service';
import {
  BatchRewardDto,
  RewardResultDto
} from '../dto/orchestrator.dto';
import {
  BatchRewardPayloadDto,
  SingleRewardPayloadDto,
  PreviewRewardPayloadDto,
  ValidateRewardPayloadDto,
  CurrencyRewardPayloadDto,
  ItemRewardPayloadDto,
  HeroRewardPayloadDto,
  UseItemPayloadDto
} from '../dto/orchestrator-payload.dto';

/**
 * 奖励聚合控制器
 * 提供跨模块的奖励发放业务接口
 * 
 * 🎯 接口职责：
 * - 批量奖励发放接口
 * - 单一奖励发放接口
 * - 奖励预览和验证接口
 * - 奖励发放历史查询接口
 * 
 * 🔄 业务场景：
 * - 任务完成后的多类型奖励发放
 * - 活动奖励的批量发放
 * - 充值返利的组合奖励
 * - 系统补偿的复合奖励
 */
@Controller()
export class RewardOrchestratorController extends BaseController {
  constructor(
    private readonly rewardOrchestratorService: RewardOrchestratorService,
  ) {
    super('RewardOrchestratorController');
  }

  /**
   * 批量奖励发放
   * 支持同时发放货币、物品、英雄等多种类型奖励
   *
   * @param payload 批量奖励发放参数
   * @returns 奖励发放结果详情
   */
  @MessagePattern('orchestrator.reward.batchReward')
  async batchReward(@Payload() payload: BatchRewardPayloadDto): Promise<XResponse<RewardResultDto>> {
    const batchDto: BatchRewardDto = {
      characterId: payload.characterId,
      rewards: payload.rewards,
      reason: payload.reason
    };

    const result = await this.rewardOrchestratorService.batchReward(batchDto);

    return this.fromResult(result);
  }

  /**
   * 单一奖励发放
   * 为单个奖励提供便捷接口
   *
   * @param payload 单一奖励发放参数
   * @returns 奖励发放结果
   */
  @MessagePattern('orchestrator.reward.singleReward')
  async singleReward(@Payload() payload: SingleRewardPayloadDto): Promise<XResponse<any>> {
    const { characterId, rewardType, subType, amount, reason } = payload;
    const result = await this.rewardOrchestratorService.singleReward(
      characterId,
      rewardType,
      subType,
      amount,
      reason
    );

    return this.fromResult(result);
  }

  /**
   * 奖励预览
   * 在实际发放前预览奖励内容和可能的结果
   *
   * @param payload 奖励预览参数
   * @returns 奖励预览结果
   */
  @MessagePattern('orchestrator.reward.preview')
  async previewReward(@Payload() payload: PreviewRewardPayloadDto): Promise<XResponse<{
    characterId: string;
    previewResults: Array<{
      type: string;
      subType: string;
      amount: number;
      canReceive: boolean;
      reason?: string;
      currentValue?: number;
      newValue?: number;
    }>;
    totalRewards: number;
    warnings: string[];
  }>> {
    try {
      this.logger.log(`奖励预览: ${payload.characterId}`, { 
        rewardCount: payload.rewards.length 
      });
      
      const { characterId, rewards } = payload;
      const previewResults: any[] = [];
      const warnings: string[] = [];
      
      // 获取角色当前状态用于预览计算
      // 这里应该调用CharacterOrchestratorService获取角色信息
      const characterInfo = {
        success: true,
        data: {
          currencies: { cash: 100000, gold: 5000 },
          inventorySummary: { usedSlots: 50, maxSlots: 100, totalItems: 50 },
          heroCount: 20,
          heroLimit: 200
        }
      };

      if (!characterInfo.success) {
        throw new Error('无法获取角色信息进行奖励预览');
      }

      const character = characterInfo.data;

      // 实际奖励预览逻辑
      for (const reward of rewards) {
        const previewResult = {
          type: reward.type,
          subType: reward.subType,
          amount: reward.amount,
          canReceive: true,
          currentValue: 0,
          newValue: reward.amount,
          reason: undefined as string | undefined
        };

        // 根据奖励类型检查角色当前状态
        switch (reward.type) {
          case 'currency':
            const currentCurrency = character.currencies?.[reward.subType] || 0;
            previewResult.currentValue = currentCurrency;
            previewResult.newValue = currentCurrency + reward.amount;

            // 检查货币上限
            const currencyLimit = this.getCurrencyLimit(reward.subType);
            if (previewResult.newValue > currencyLimit) {
              previewResult.canReceive = false;
              previewResult.reason = `超过${reward.subType}上限${currencyLimit}`;
              warnings.push(`${reward.subType}将超过上限`);
            }
            break;

          case 'item':
            // 检查背包空间
            const inventorySummary = character.inventorySummary;
            const currentSlots = inventorySummary?.usedSlots || 0;
            const maxSlots = inventorySummary?.maxSlots || 100;

            if (currentSlots + 1 > maxSlots) {
              previewResult.canReceive = false;
              previewResult.reason = '背包空间不足';
              warnings.push('背包空间不足，无法接收物品奖励');
            }

            // 检查单次发放数量限制
            if (reward.amount > 999) {
              previewResult.canReceive = false;
              previewResult.reason = '单次发放数量超过上限999';
              warnings.push(`物品${reward.subType}数量超过单次发放上限`);
            }
            break;

          case 'hero':
            // 检查英雄数量上限
            const heroCount = character.heroCount || 0;
            const heroLimit = character.heroLimit || 200;

            if (heroCount + reward.amount > heroLimit) {
              previewResult.canReceive = false;
              previewResult.reason = `英雄数量将超过上限${heroLimit}`;
              warnings.push('英雄数量将超过上限');
            }

            // 检查英雄配置是否存在
            const heroConfigExists = await this.validateHeroConfig(parseInt(reward.subType));
            if (!heroConfigExists) {
              previewResult.canReceive = false;
              previewResult.reason = '英雄配置不存在';
              warnings.push(`英雄配置${reward.subType}不存在`);
            }
            break;

          default:
            previewResult.canReceive = false;
            previewResult.reason = '未知的奖励类型';
            warnings.push(`未知的奖励类型: ${reward.type}`);
        }

        previewResults.push(previewResult);
      }
      
      const result = {
        characterId,
        previewResults,
        totalRewards: rewards.length,
        warnings
      };
      
      return this.toSuccessResponse(result, '奖励预览完成');
    } catch (error) {
      this.logger.error('奖励预览失败', error);
      return this.toErrorResponse('奖励预览失败', 'REWARD_PREVIEW_FAILED');
    }
  }

  /**
   * 验证奖励合法性
   * 检查奖励配置是否合法，角色是否满足接收条件
   *
   * @param payload 奖励验证参数
   * @returns 验证结果
   */
  @MessagePattern('orchestrator.reward.validate')
  async validateReward(@Payload() payload: ValidateRewardPayloadDto): Promise<XResponse<{
    characterId: string;
    isValid: boolean;
    validRewards: any[];
    invalidRewards: any[];
    errors: string[];
  }>> {
    try {
      this.logger.log(`奖励验证: ${payload.characterId}`, { 
        rewardCount: payload.rewards.length 
      });
      
      const { characterId, rewards } = payload;
      const validRewards: any[] = [];
      const invalidRewards: any[] = [];
      const errors: string[] = [];
      
      // 实际奖励验证逻辑
      for (const reward of rewards) {
        let isValidReward = true;
        const validationErrors: string[] = [];

        // 1. 基础参数验证
        if (!reward.type || typeof reward.type !== 'string') {
          isValidReward = false;
          validationErrors.push('奖励类型不能为空');
        }

        if (!reward.subType || typeof reward.subType !== 'string') {
          isValidReward = false;
          validationErrors.push('奖励子类型不能为空');
        }

        if (!reward.amount || reward.amount <= 0 || !Number.isInteger(reward.amount)) {
          isValidReward = false;
          validationErrors.push('奖励数量必须是正整数');
        }

        // 2. 类型特定验证
        if (isValidReward) {
          switch (reward.type) {
            case 'currency':
              const validCurrencies = ['cash', 'gold', 'energy', 'worldCoin', 'chip', 'integral'];
              if (!validCurrencies.includes(reward.subType)) {
                isValidReward = false;
                validationErrors.push(`无效的货币类型: ${reward.subType}，支持的类型: ${validCurrencies.join(', ')}`);
              }

              const currencyLimit = this.getCurrencyLimit(reward.subType);
              if (reward.amount > currencyLimit) {
                isValidReward = false;
                validationErrors.push(`货币数量${reward.amount}超过单次发放上限${currencyLimit}`);
              }
              break;

            case 'item':
              const itemConfigId = parseInt(reward.subType);
              if (isNaN(itemConfigId) || itemConfigId <= 0) {
                isValidReward = false;
                validationErrors.push(`无效的物品配置ID: ${reward.subType}`);
              } else {
                // 验证物品配置是否存在
                const itemConfigExists = await this.validateItemConfig(itemConfigId);
                if (!itemConfigExists) {
                  isValidReward = false;
                  validationErrors.push(`物品配置${itemConfigId}不存在`);
                }
              }

              if (reward.amount > 999) {
                isValidReward = false;
                validationErrors.push('物品数量不能超过999');
              }
              break;

            case 'hero':
              const heroConfigId = parseInt(reward.subType);
              if (isNaN(heroConfigId) || heroConfigId <= 0) {
                isValidReward = false;
                validationErrors.push(`无效的英雄配置ID: ${reward.subType}`);
              } else {
                // 验证英雄配置是否存在
                const heroConfigExists = await this.validateHeroConfig(heroConfigId);
                if (!heroConfigExists) {
                  isValidReward = false;
                  validationErrors.push(`英雄配置${heroConfigId}不存在`);
                }
              }

              if (reward.amount > 10) {
                isValidReward = false;
                validationErrors.push('英雄数量不能超过10');
              }
              break;

            default:
              isValidReward = false;
              validationErrors.push(`未知的奖励类型: ${reward.type}，支持的类型: currency, item, hero`);
          }
        }

        // 3. 业务规则验证
        if (isValidReward) {
          // 检查奖励原因长度
          if (reward.reason && reward.reason.length > 200) {
            isValidReward = false;
            validationErrors.push('奖励原因不能超过200个字符');
          }
        }

        if (isValidReward) {
          validRewards.push(reward);
        } else {
          invalidRewards.push({
            ...reward,
            errors: validationErrors
          });
          errors.push(...validationErrors);
        }
      }
      
      const result = {
        characterId,
        isValid: invalidRewards.length === 0,
        validRewards,
        invalidRewards,
        errors
      };
      
      return this.toSuccessResponse(result, '奖励验证完成');
    } catch (error) {
      this.logger.error('奖励验证失败', error);
      return this.toErrorResponse('奖励验证失败', 'REWARD_VALIDATION_FAILED');
    }
  }

  /**
   * 货币奖励发放
   * 专门用于货币类型奖励的便捷接口
   *
   * @param payload 货币奖励参数
   * @returns 货币奖励发放结果
   */
  @MessagePattern('orchestrator.reward.currency')
  async currencyReward(@Payload() payload: CurrencyRewardPayloadDto): Promise<XResponse<any>> {
    const result = await this.rewardOrchestratorService.singleReward(
      payload.characterId,
      'currency',
      payload.currencyType,
      payload.amount,
      payload.reason
    );

    return this.fromResult(result);
  }

  /**
   * 物品奖励发放
   * 专门用于物品类型奖励的便捷接口
   *
   * @param payload 物品奖励参数
   * @returns 物品奖励发放结果
   */
  @MessagePattern('orchestrator.reward.item')
  async itemReward(@Payload() payload: ItemRewardPayloadDto): Promise<XResponse<any>> {
    const result = await this.rewardOrchestratorService.singleReward(
      payload.characterId,
      'item',
      payload.itemConfigId.toString(),
      payload.quantity,
      payload.reason
    );

    return this.fromResult(result);
  }

  /**
   * 英雄奖励发放
   * 专门用于英雄类型奖励的便捷接口
   *
   * @param payload 英雄奖励参数
   * @returns 英雄奖励发放结果
   */
  @MessagePattern('orchestrator.reward.hero')
  async heroReward(@Payload() payload: HeroRewardPayloadDto): Promise<XResponse<any>> {
    const result = await this.rewardOrchestratorService.singleReward(
      payload.characterId,
      'hero',
      payload.heroConfigId.toString(),
      payload.amount,
      payload.reason
    );

    return this.fromResult(result);
  }

  /**
   * 道具使用
   * 使用道具并获得相应奖励（可能是货币或英雄）
   *
   * @param payload 道具使用参数
   * @returns 道具使用结果
   */
  @MessagePattern('orchestrator.reward.useItem')
  async useItem(@Payload() payload: UseItemPayloadDto): Promise<XResponse<{
    characterId: string;
    itemInstanceId: string;
    usedQuantity: number;
    rewards: any[];
    remainingQuantity: number;
  }>> {
    const result = await this.rewardOrchestratorService.useItem(
      payload.characterId,
      payload.itemInstanceId,
      payload.quantity,
      payload.reason
    );

    return this.fromResult(result);
  }

  /**
   * 获取货币上限
   */
  private getCurrencyLimit(currencyType: string): number {
    const limits = {
      'cash': 999999999,      // 现金上限
      'gold': 999999,         // 金币上限
      'energy': 9999,         // 能量上限
      'worldCoin': 999999,    // 世界币上限
      'chip': 999999,         // 芯片上限
      'integral': 999999      // 积分上限
    };

    return limits[currencyType] || 999999999;
  }

  /**
   * 验证物品配置是否存在
   */
  private async validateItemConfig(itemConfigId: number): Promise<boolean> {
    // 这里应该调用游戏配置服务验证物品配置
    // 基于真实的物品配置ID范围进行验证
    const validItemRanges = [
      { min: 1001, max: 1999 },   // 货币类道具
      { min: 2001, max: 2999 },   // 英雄卡包类道具
      { min: 3001, max: 3999 },   // 混合礼包类道具
      { min: 4001, max: 4999 },   // 装备箱类道具
      { min: 5001, max: 5999 }    // 装备类道具
    ];

    return validItemRanges.some(range =>
      itemConfigId >= range.min && itemConfigId <= range.max
    );
  }

  /**
   * 验证英雄配置是否存在
   */
  private async validateHeroConfig(heroConfigId: number): Promise<boolean> {
    // 这里应该调用游戏配置服务验证英雄配置
    // 基于真实的英雄配置ID范围进行验证
    const validHeroRanges = [
      { min: 10001, max: 19999 }, // 普通英雄
      { min: 20001, max: 29999 }, // 稀有英雄
      { min: 30001, max: 39999 }  // 史诗英雄
    ];

    return validHeroRanges.some(range =>
      heroConfigId >= range.min && heroConfigId <= range.max
    );
  }
}
