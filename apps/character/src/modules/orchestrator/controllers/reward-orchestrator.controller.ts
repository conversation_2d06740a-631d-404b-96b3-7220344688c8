import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { XResponse } from '@libs/common/types/response.type';
import { BaseController } from '@libs/common/controller';
import { RewardOrchestratorService } from '../services/reward-orchestrator.service';
import {
  BatchRewardDto,
  RewardResultDto
} from '../dto/orchestrator.dto';

/**
 * 奖励聚合控制器
 * 提供跨模块的奖励发放业务接口
 * 
 * 🎯 接口职责：
 * - 批量奖励发放接口
 * - 单一奖励发放接口
 * - 奖励预览和验证接口
 * - 奖励发放历史查询接口
 * 
 * 🔄 业务场景：
 * - 任务完成后的多类型奖励发放
 * - 活动奖励的批量发放
 * - 充值返利的组合奖励
 * - 系统补偿的复合奖励
 */
@Controller()
export class RewardOrchestratorController extends BaseController {
  constructor(
    private readonly rewardOrchestratorService: RewardOrchestratorService,
  ) {
    super('RewardOrchestratorController');
  }

  /**
   * 批量奖励发放
   * 支持同时发放货币、物品、英雄等多种类型奖励
   * 
   * @param payload 批量奖励发放参数
   * @returns 奖励发放结果详情
   */
  @MessagePattern('orchestrator.reward.batchReward')
  async batchReward(@Payload() payload: BatchRewardDto): Promise<XResponse<RewardResultDto>> {
    return this.handleRequest(async () => {
      this.logger.log(`批量奖励发放: ${payload.characterId}`, { 
        rewardCount: payload.rewards.length,
        reason: payload.reason 
      });
      
      const result = await this.rewardOrchestratorService.batchReward(payload);
      
      return result;
    });
  }

  /**
   * 单一奖励发放
   * 为单个奖励提供便捷接口
   * 
   * @param payload 单一奖励发放参数
   * @returns 奖励发放结果
   */
  @MessagePattern('orchestrator.reward.singleReward')
  async singleReward(@Payload() payload: {
    characterId: string;
    rewardType: string;
    subType: string;
    amount: number;
    reason?: string;
  }): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`单一奖励发放: ${payload.characterId}`, {
        type: payload.rewardType,
        subType: payload.subType,
        amount: payload.amount,
        reason: payload.reason
      });
      
      const { characterId, rewardType, subType, amount, reason } = payload;
      const result = await this.rewardOrchestratorService.singleReward(
        characterId,
        rewardType,
        subType,
        amount,
        reason
      );
      
      return result;
    });
  }

  /**
   * 奖励预览
   * 在实际发放前预览奖励内容和可能的结果
   * 
   * @param payload 奖励预览参数
   * @returns 奖励预览结果
   */
  @MessagePattern('orchestrator.reward.preview')
  async previewReward(@Payload() payload: {
    characterId: string;
    rewards: Array<{
      type: string;
      subType: string;
      amount: number;
    }>;
  }): Promise<XResponse<{
    characterId: string;
    previewResults: Array<{
      type: string;
      subType: string;
      amount: number;
      canReceive: boolean;
      reason?: string;
      currentValue?: number;
      newValue?: number;
    }>;
    totalRewards: number;
    warnings: string[];
  }>> {
    return this.handleRequest(async () => {
      this.logger.log(`奖励预览: ${payload.characterId}`, { 
        rewardCount: payload.rewards.length 
      });
      
      const { characterId, rewards } = payload;
      const previewResults: any[] = [];
      const warnings: string[] = [];
      
      // 简化版本：模拟奖励预览逻辑
      for (const reward of rewards) {
        const previewResult = {
          type: reward.type,
          subType: reward.subType,
          amount: reward.amount,
          canReceive: true,
          currentValue: 0,
          newValue: reward.amount
        };
        
        // 这里应该检查角色当前状态，验证是否可以接收奖励
        switch (reward.type) {
          case 'currency':
            // 检查货币上限等
            previewResult.currentValue = 100000; // 模拟当前值
            previewResult.newValue = previewResult.currentValue + reward.amount;
            break;
          case 'item':
            // 检查背包空间等
            if (reward.amount > 100) {
              previewResult.canReceive = false;
              previewResult.reason = '数量超过单次发放上限';
              warnings.push(`物品${reward.subType}数量超过上限`);
            }
            break;
          case 'hero':
            // 检查英雄数量上限等
            break;
        }
        
        previewResults.push(previewResult);
      }
      
      const result = {
        characterId,
        previewResults,
        totalRewards: rewards.length,
        warnings
      };
      
      return { success: true, data: result, message: '奖励预览完成' };
    });
  }

  /**
   * 验证奖励合法性
   * 检查奖励配置是否合法，角色是否满足接收条件
   * 
   * @param payload 奖励验证参数
   * @returns 验证结果
   */
  @MessagePattern('orchestrator.reward.validate')
  async validateReward(@Payload() payload: {
    characterId: string;
    rewards: Array<{
      type: string;
      subType: string;
      amount: number;
    }>;
  }): Promise<XResponse<{
    characterId: string;
    isValid: boolean;
    validRewards: any[];
    invalidRewards: any[];
    errors: string[];
  }>> {
    return this.handleRequest(async () => {
      this.logger.log(`奖励验证: ${payload.characterId}`, { 
        rewardCount: payload.rewards.length 
      });
      
      const { characterId, rewards } = payload;
      const validRewards: any[] = [];
      const invalidRewards: any[] = [];
      const errors: string[] = [];
      
      // 简化版本：模拟奖励验证逻辑
      for (const reward of rewards) {
        let isValidReward = true;
        const validationErrors: string[] = [];
        
        // 基础验证
        if (!reward.type || !reward.subType || reward.amount <= 0) {
          isValidReward = false;
          validationErrors.push('奖励参数无效');
        }
        
        // 类型特定验证
        switch (reward.type) {
          case 'currency':
            if (!['cash', 'gold', 'energy', 'worldCoin', 'chip', 'integral'].includes(reward.subType)) {
              isValidReward = false;
              validationErrors.push('未知的货币类型');
            }
            if (reward.amount > 1000000) {
              isValidReward = false;
              validationErrors.push('货币数量超过单次发放上限');
            }
            break;
          case 'item':
            // 这里应该检查物品配置是否存在
            const itemConfigId = parseInt(reward.subType);
            if (isNaN(itemConfigId) || itemConfigId <= 0) {
              isValidReward = false;
              validationErrors.push('无效的物品配置ID');
            }
            break;
          case 'hero':
            // 这里应该检查英雄配置是否存在
            const heroConfigId = parseInt(reward.subType);
            if (isNaN(heroConfigId) || heroConfigId <= 0) {
              isValidReward = false;
              validationErrors.push('无效的英雄配置ID');
            }
            break;
          default:
            isValidReward = false;
            validationErrors.push('未知的奖励类型');
        }
        
        if (isValidReward) {
          validRewards.push(reward);
        } else {
          invalidRewards.push({
            ...reward,
            errors: validationErrors
          });
          errors.push(...validationErrors);
        }
      }
      
      const result = {
        characterId,
        isValid: invalidRewards.length === 0,
        validRewards,
        invalidRewards,
        errors
      };
      
      return { success: true, data: result, message: '奖励验证完成' };
    });
  }

  /**
   * 货币奖励发放
   * 专门用于货币类型奖励的便捷接口
   * 
   * @param payload 货币奖励参数
   * @returns 货币奖励发放结果
   */
  @MessagePattern('orchestrator.reward.currency')
  async currencyReward(@Payload() payload: {
    characterId: string;
    currencyType: string;
    amount: number;
    reason?: string;
  }): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`货币奖励发放: ${payload.characterId}`, {
        currencyType: payload.currencyType,
        amount: payload.amount,
        reason: payload.reason
      });
      
      const { characterId, currencyType, amount, reason } = payload;
      const result = await this.rewardOrchestratorService.singleReward(
        characterId,
        'currency',
        currencyType,
        amount,
        reason
      );
      
      return result;
    });
  }

  /**
   * 物品奖励发放
   * 专门用于物品类型奖励的便捷接口
   * 
   * @param payload 物品奖励参数
   * @returns 物品奖励发放结果
   */
  @MessagePattern('orchestrator.reward.item')
  async itemReward(@Payload() payload: {
    characterId: string;
    itemConfigId: number;
    quantity: number;
    reason?: string;
  }): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`物品奖励发放: ${payload.characterId}`, {
        itemConfigId: payload.itemConfigId,
        quantity: payload.quantity,
        reason: payload.reason
      });
      
      const { characterId, itemConfigId, quantity, reason } = payload;
      const result = await this.rewardOrchestratorService.singleReward(
        characterId,
        'item',
        itemConfigId.toString(),
        quantity,
        reason
      );
      
      return result;
    });
  }

  /**
   * 英雄奖励发放
   * 专门用于英雄类型奖励的便捷接口
   * 
   * @param payload 英雄奖励参数
   * @returns 英雄奖励发放结果
   */
  @MessagePattern('orchestrator.reward.hero')
  async heroReward(@Payload() payload: {
    characterId: string;
    heroConfigId: number;
    quantity: number;
    reason?: string;
  }): Promise<XResponse<any>> {
    return this.handleRequest(async () => {
      this.logger.log(`英雄奖励发放: ${payload.characterId}`, {
        heroConfigId: payload.heroConfigId,
        quantity: payload.quantity,
        reason: payload.reason
      });
      
      const { characterId, heroConfigId, quantity, reason } = payload;
      const result = await this.rewardOrchestratorService.singleReward(
        characterId,
        'hero',
        heroConfigId.toString(),
        quantity,
        reason
      );
      
      return result;
    });
  }
}
