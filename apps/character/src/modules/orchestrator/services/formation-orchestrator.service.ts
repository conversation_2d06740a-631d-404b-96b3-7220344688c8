import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { BaseService } from '@libs/common/service';
import { MicroserviceClientService } from '@libs/service-mesh';
import { XResult, XResultUtils } from '@libs/common/types/result.type';

// 导入各个子模块服务
import { FormationService } from '../../formation/formation.service';
import { HeroService } from '../../hero/services/hero.service';
import { InventoryService } from '../../inventory/inventory.service';
import { TacticService } from '../../tactic/tactic.service';

// 导入DTO
import {
  SmartFormationDto,
  SmartFormationResultDto
} from '../dto/orchestrator.dto';

/**
 * 阵容聚合服务 - 跨模块阵容管理业务编排
 * 
 * 🎯 核心职责：
 * - 智能布阵编排（英雄选择 + 位置优化 + 装备配置）
 * - 阵容战力优化建议
 * - 阵容与装备联动管理
 * - 阵容战术配置编排
 * 
 * 🔄 业务编排场景：
 * 1. 智能布阵：根据英雄属性和位置适应性自动布阵
 * 2. 装备优化：为阵容中的英雄自动配置最优装备
 * 3. 战术匹配：根据阵容特点推荐最适合的战术
 * 4. 阵容分析：分析阵容的优缺点并提供改进建议
 * 5. 多阵容管理：管理不同场景下的多套阵容配置
 * 
 * 🚀 性能优化：
 * - 并行处理英雄数据和装备数据
 * - 智能缓存布阵算法结果
 * - 增量更新阵容配置
 * - 批量处理装备变更
 * 
 * 🧠 智能算法：
 * - 位置适应性算法：根据英雄属性匹配最佳位置
 * - 战力最大化算法：在满足位置要求下最大化总战力
 * - 平衡性算法：保证攻防平衡的阵容配置
 * - 克制关系算法：考虑英雄间的克制关系
 */
@Injectable()
export class FormationOrchestratorService extends BaseService {
  constructor(
    @Inject(forwardRef(() => FormationService))
    private readonly formationService: FormationService,
    
    @Inject(forwardRef(() => HeroService))
    private readonly heroService: HeroService,
    
    @Inject(forwardRef(() => InventoryService))
    private readonly inventoryService: InventoryService,
    
    @Inject(forwardRef(() => TacticService))
    private readonly tacticService: TacticService,
    
    microserviceClient?: MicroserviceClientService,
  ) {
    super('FormationOrchestratorService', microserviceClient);
  }

  /**
   * 智能布阵
   * 综合考虑英雄属性、位置适应性、装备配置等因素进行最优布阵
   * 
   * 🔄 业务编排流程：
   * 1. 获取阵容和英雄数据
   * 2. 分析英雄属性和适应性
   * 3. 执行智能布阵算法
   * 4. 优化装备配置（可选）
   * 5. 计算战力变化
   * 6. 生成优化建议
   */
  async smartFormation(dto: SmartFormationDto): Promise<XResult<SmartFormationResultDto>> {
    return this.executeBusinessOperation(async () => {
      const { characterId, formationId, preferredHeroes, strategy = 'balanced', autoEquip = false } = dto;
      this.logger.log(`智能布阵: ${characterId}`, { formationId, strategy, autoEquip });

      // 1. 并行获取必要数据
      const [formationResult, heroesResult, equipmentResult] = await Promise.allSettled([
        this.formationService.getFormation(characterId, formationId),
        this.heroService.getCharacterHeroes(characterId),
        autoEquip ? this.inventoryService.getInventory(characterId) : Promise.resolve(null)
      ]);

      // 2. 验证数据获取结果
      if (formationResult.status === 'rejected' || XResultUtils.isFailure(formationResult.value)) {
        return XResultUtils.error('获取阵容数据失败', 'GET_FORMATION_FAILED');
      }

      if (heroesResult.status === 'rejected' || XResultUtils.isFailure(heroesResult.value)) {
        return XResultUtils.error('获取英雄数据失败', 'GET_HEROES_FAILED');
      }

      const formation = formationResult.value.data;
      const heroes = heroesResult.value.data || [];
      let availableEquipment: any[] = [];
      if (equipmentResult.status === 'fulfilled' && equipmentResult.value && XResultUtils.isSuccess(equipmentResult.value)) {
        // 从背包中提取可用装备
        availableEquipment = equipmentResult.value.data.items.filter(item =>
          item.type === 'equipment' && !item.isEquipped
        );
      }

      // 3. 记录原始战力
      const originalBattlePower = await this.calculateFormationBattlePower(formation);

      // 4. 执行智能布阵算法
      const smartFormationResult = this.executeSmartFormationAlgorithm(
        formation,
        heroes,
        preferredHeroes,
        strategy
      );

      if (XResultUtils.isFailure(smartFormationResult)) {
        return smartFormationResult;
      }

      const { optimizedFormation, positionMapping, unassignedHeroes } = smartFormationResult.data;

      // 5. 更新阵容配置
      const updateResult = await this.formationService.updateFormationPositions(
        characterId,
        formationId,
        positionMapping
      );

      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error('更新阵容配置失败', 'UPDATE_FORMATION_FAILED');
      }

      // 6. 自动装备优化（可选）
      let equipmentChanges: any[] = [];
      if (autoEquip && availableEquipment.length > 0) {
        const equipOptimizeResult = await this.optimizeEquipmentForFormation(
          characterId,
          optimizedFormation,
          availableEquipment
        );
        
        if (XResultUtils.isSuccess(equipOptimizeResult)) {
          equipmentChanges = equipOptimizeResult.data;
        }
      }

      // 7. 计算新战力
      const newBattlePower = await this.calculateFormationBattlePower(optimizedFormation);
      const battlePowerChange = newBattlePower - originalBattlePower;

      // 8. 生成优化建议
      const recommendations = this.generateOptimizationRecommendations(
        optimizedFormation,
        unassignedHeroes,
        strategy
      );

      const result: SmartFormationResultDto = {
        characterId,
        formationId,
        formation: optimizedFormation,
        positionMapping,
        unassignedHeroes,
        equipmentChanges,
        battlePowerChange,
        recommendations
      };

      this.logger.log(`智能布阵完成: ${characterId}`, {
        battlePowerChange,
        unassignedCount: unassignedHeroes.length,
        equipmentChanges: equipmentChanges.length,
        recommendations: recommendations.length
      });

      return XResultUtils.ok(result);
    }, { reason: 'smart_formation', metadata: { characterId: dto.characterId, formationId: dto.formationId } });
  }

  /**
   * 执行智能布阵算法
   * 根据策略和英雄属性进行最优位置分配
   */
  private executeSmartFormationAlgorithm(
    formation: any,
    heroes: any[],
    preferredHeroes?: string[],
    strategy: string = 'balanced'
  ): XResult<{
    optimizedFormation: any;
    positionMapping: any;
    unassignedHeroes: any[];
  }> {
    try {
      // 1. 获取阵型的位置配置
      const positions = this.getFormationPositions(formation);
      
      // 2. 筛选可用英雄
      let availableHeroes = [...heroes];
      
      // 优先使用指定英雄
      if (preferredHeroes && preferredHeroes.length > 0) {
        const preferred = heroes.filter(hero => preferredHeroes.includes(hero.heroId));
        const others = heroes.filter(hero => !preferredHeroes.includes(hero.heroId));
        availableHeroes = [...preferred, ...others];
      }

      // 3. 根据策略排序英雄
      availableHeroes = this.sortHeroesByStrategy(availableHeroes, strategy);

      // 4. 执行位置分配算法
      const positionMapping: any = {};
      const assignedHeroes: Set<string> = new Set();

      // 为每个位置分配最适合的英雄
      for (const position of positions) {
        const bestHero = this.findBestHeroForPosition(
          position,
          availableHeroes.filter(hero => !assignedHeroes.has(hero.heroId)),
          strategy
        );

        if (bestHero) {
          positionMapping[position.id] = bestHero.heroId;
          assignedHeroes.add(bestHero.heroId);
        }
      }

      // 5. 计算未分配的英雄
      const unassignedHeroes = availableHeroes.filter(hero => !assignedHeroes.has(hero.heroId));

      // 6. 更新阵容配置
      const optimizedFormation = {
        ...formation,
        positionToHerosObject: positionMapping
      };

      return XResultUtils.ok({
        optimizedFormation,
        positionMapping,
        unassignedHeroes
      });
    } catch (error) {
      this.logger.error('智能布阵算法执行失败', error);
      return XResultUtils.error('智能布阵算法执行失败', 'SMART_FORMATION_ALGORITHM_FAILED');
    }
  }

  /**
   * 获取阵型的位置配置
   * 基于阵型类型返回对应的位置配置
   */
  private getFormationPositions(formation: any): any[] {
    const formationType = formation.formationType || formation.type || '4-4-2';

    // 根据不同阵型返回对应的位置配置
    const formationConfigs = {
      '4-4-2': [
        { id: 'GK', name: '门将', type: 'goalkeeper', priority: 1, x: 50, y: 10 },
        { id: 'LB', name: '左后卫', type: 'defender', priority: 2, x: 20, y: 25 },
        { id: 'CB1', name: '中后卫1', type: 'defender', priority: 2, x: 40, y: 25 },
        { id: 'CB2', name: '中后卫2', type: 'defender', priority: 2, x: 60, y: 25 },
        { id: 'RB', name: '右后卫', type: 'defender', priority: 2, x: 80, y: 25 },
        { id: 'LM', name: '左中场', type: 'midfielder', priority: 3, x: 20, y: 50 },
        { id: 'CM1', name: '中场1', type: 'midfielder', priority: 3, x: 40, y: 50 },
        { id: 'CM2', name: '中场2', type: 'midfielder', priority: 3, x: 60, y: 50 },
        { id: 'RM', name: '右中场', type: 'midfielder', priority: 3, x: 80, y: 50 },
        { id: 'ST1', name: '前锋1', type: 'forward', priority: 4, x: 40, y: 75 },
        { id: 'ST2', name: '前锋2', type: 'forward', priority: 4, x: 60, y: 75 }
      ],
      '4-3-3': [
        { id: 'GK', name: '门将', type: 'goalkeeper', priority: 1, x: 50, y: 10 },
        { id: 'LB', name: '左后卫', type: 'defender', priority: 2, x: 20, y: 25 },
        { id: 'CB1', name: '中后卫1', type: 'defender', priority: 2, x: 40, y: 25 },
        { id: 'CB2', name: '中后卫2', type: 'defender', priority: 2, x: 60, y: 25 },
        { id: 'RB', name: '右后卫', type: 'defender', priority: 2, x: 80, y: 25 },
        { id: 'CDM', name: '后腰', type: 'midfielder', priority: 3, x: 50, y: 40 },
        { id: 'CM1', name: '中场1', type: 'midfielder', priority: 3, x: 35, y: 55 },
        { id: 'CM2', name: '中场2', type: 'midfielder', priority: 3, x: 65, y: 55 },
        { id: 'LW', name: '左边锋', type: 'forward', priority: 4, x: 20, y: 75 },
        { id: 'ST', name: '中锋', type: 'forward', priority: 4, x: 50, y: 80 },
        { id: 'RW', name: '右边锋', type: 'forward', priority: 4, x: 80, y: 75 }
      ],
      '5-4-1': [
        { id: 'GK', name: '门将', type: 'goalkeeper', priority: 1, x: 50, y: 10 },
        { id: 'LWB', name: '左翼卫', type: 'defender', priority: 2, x: 15, y: 30 },
        { id: 'CB1', name: '中后卫1', type: 'defender', priority: 2, x: 30, y: 25 },
        { id: 'CB2', name: '中后卫2', type: 'defender', priority: 2, x: 50, y: 25 },
        { id: 'CB3', name: '中后卫3', type: 'defender', priority: 2, x: 70, y: 25 },
        { id: 'RWB', name: '右翼卫', type: 'defender', priority: 2, x: 85, y: 30 },
        { id: 'LM', name: '左中场', type: 'midfielder', priority: 3, x: 25, y: 55 },
        { id: 'CM1', name: '中场1', type: 'midfielder', priority: 3, x: 40, y: 50 },
        { id: 'CM2', name: '中场2', type: 'midfielder', priority: 3, x: 60, y: 50 },
        { id: 'RM', name: '右中场', type: 'midfielder', priority: 3, x: 75, y: 55 },
        { id: 'ST', name: '前锋', type: 'forward', priority: 4, x: 50, y: 80 }
      ],
      '3-5-2': [
        { id: 'GK', name: '门将', type: 'goalkeeper', priority: 1, x: 50, y: 10 },
        { id: 'CB1', name: '中后卫1', type: 'defender', priority: 2, x: 30, y: 25 },
        { id: 'CB2', name: '中后卫2', type: 'defender', priority: 2, x: 50, y: 25 },
        { id: 'CB3', name: '中后卫3', type: 'defender', priority: 2, x: 70, y: 25 },
        { id: 'LWB', name: '左翼卫', type: 'midfielder', priority: 3, x: 15, y: 50 },
        { id: 'CM1', name: '中场1', type: 'midfielder', priority: 3, x: 35, y: 50 },
        { id: 'CM2', name: '中场2', type: 'midfielder', priority: 3, x: 50, y: 45 },
        { id: 'CM3', name: '中场3', type: 'midfielder', priority: 3, x: 65, y: 50 },
        { id: 'RWB', name: '右翼卫', type: 'midfielder', priority: 3, x: 85, y: 50 },
        { id: 'ST1', name: '前锋1', type: 'forward', priority: 4, x: 40, y: 75 },
        { id: 'ST2', name: '前锋2', type: 'forward', priority: 4, x: 60, y: 75 }
      ]
    };

    return formationConfigs[formationType] || formationConfigs['4-4-2'];
  }

  /**
   * 根据策略排序英雄
   */
  private sortHeroesByStrategy(heroes: any[], strategy: string): any[] {
    switch (strategy) {
      case 'attack':
        // 攻击策略：优先选择攻击力高的英雄
        return heroes.sort((a, b) => (b.attack || 0) - (a.attack || 0));
      case 'defense':
        // 防守策略：优先选择防守力高的英雄
        return heroes.sort((a, b) => (b.defense || 0) - (a.defense || 0));
      case 'balanced':
      default:
        // 平衡策略：按综合能力排序
        return heroes.sort((a, b) => {
          const aTotal = (a.attack || 0) + (a.defense || 0) + (a.speed || 0);
          const bTotal = (b.attack || 0) + (b.defense || 0) + (b.speed || 0);
          return bTotal - aTotal;
        });
    }
  }

  /**
   * 为位置找到最适合的英雄
   */
  private findBestHeroForPosition(position: any, availableHeroes: any[], strategy: string): any | null {
    if (availableHeroes.length === 0) return null;

    // 综合考虑英雄属性、位置偏好、经验、状态等因素
    let bestHero = null;
    let bestScore = -1;
    const candidates: Array<{ hero: any; score: number; reasons: string[] }> = [];

    // 为每个英雄计算适应性得分
    for (const hero of availableHeroes) {
      const score = this.calculateHeroPositionScore(hero, position, strategy);
      const reasons = this.getPositionMatchReasons(hero, position, strategy);

      candidates.push({ hero, score, reasons });

      if (score > bestScore) {
        bestScore = score;
        bestHero = hero;
      }
    }

    // 记录选择过程（用于调试和优化）
    this.logger.debug(`位置${position.id}选择结果`, {
      position: position.id,
      bestHero: bestHero?.heroId,
      bestScore,
      candidateCount: candidates.length,
      topCandidates: candidates
        .sort((a, b) => b.score - a.score)
        .slice(0, 3)
        .map(c => ({ heroId: c.hero.heroId, score: c.score, reasons: c.reasons }))
    });

    return bestHero;
  }

  /**
   * 获取位置匹配原因
   */
  private getPositionMatchReasons(hero: any, position: any, strategy: string): string[] {
    const reasons: string[] = [];

    // 基于属性匹配
    switch (position.type) {
      case 'goalkeeper':
        if ((hero.goalkeeping || 0) >= 80) reasons.push('门将技能优秀');
        if ((hero.defense || 0) >= 70) reasons.push('防守意识良好');
        break;
      case 'defender':
        if ((hero.defense || 0) >= 80) reasons.push('防守能力强');
        if ((hero.speed || 0) >= 70) reasons.push('速度适中');
        if ((hero.stamina || 0) >= 75) reasons.push('体能充沛');
        break;
      case 'midfielder':
        if ((hero.passing || 0) >= 75) reasons.push('传球精准');
        if ((hero.stamina || 0) >= 80) reasons.push('体能出色');
        const versatility = (hero.attack + hero.defense) / 2;
        if (versatility >= 70) reasons.push('攻防平衡');
        break;
      case 'forward':
        if ((hero.attack || 0) >= 80) reasons.push('攻击力强');
        if ((hero.shooting || 0) >= 75) reasons.push('射门精准');
        if ((hero.speed || 0) >= 80) reasons.push('速度优势');
        break;
    }

    // 基于位置偏好
    if (hero.preferredPositions?.includes(position.id)) {
      reasons.push('首选位置');
    } else if (hero.preferredPositions?.includes(position.type)) {
      reasons.push('适应位置类型');
    }

    // 基于策略匹配
    if (strategy === 'attack' && position.type === 'forward') {
      reasons.push('符合攻击策略');
    } else if (strategy === 'defense' && position.type === 'defender') {
      reasons.push('符合防守策略');
    }

    // 基于状态
    if ((hero.fitness || 100) >= 90) {
      reasons.push('状态良好');
    }

    if (reasons.length === 0) {
      reasons.push('基础适应性');
    }

    return reasons;
  }

  /**
   * 计算英雄在特定位置的适应性得分
   * 基于英雄属性、偏好位置、经验、体能等综合计算适应性
   */
  private calculateHeroPositionScore(hero: any, position: any, strategy: string): number {
    let score = 0;

    // 1. 基础属性匹配度
    const attributeScore = this.calculateAttributeMatchScore(hero, position);
    score += attributeScore;

    // 2. 位置偏好加成
    const preferenceScore = this.calculatePositionPreferenceScore(hero, position);
    score += preferenceScore;

    // 3. 经验加成
    const experienceScore = this.calculatePositionExperienceScore(hero, position);
    score += experienceScore;

    // 4. 体能状态影响
    const fitnessScore = this.calculateFitnessScore(hero);
    score *= fitnessScore;

    // 5. 策略匹配加成
    const strategyBonus = this.calculateStrategyBonus(hero, position, strategy);
    score *= strategyBonus;

    // 6. 年龄影响
    const ageMultiplier = this.calculateAgeMultiplier(hero, position);
    score *= ageMultiplier;

    // 7. 伤病影响
    const injuryPenalty = this.calculateInjuryPenalty(hero);
    score *= injuryPenalty;

    return Math.floor(score);
  }

  /**
   * 计算属性匹配得分
   */
  private calculateAttributeMatchScore(hero: any, position: any): number {
    const attributes = {
      attack: hero.attack || 0,
      defense: hero.defense || 0,
      speed: hero.speed || 0,
      goalkeeping: hero.goalkeeping || 0,
      passing: hero.passing || 0,
      shooting: hero.shooting || 0,
      stamina: hero.stamina || 0,
      accuracy: hero.accuracy || 0
    };

    let score = 0;

    switch (position.type) {
      case 'goalkeeper':
        score = attributes.goalkeeping * 3 + attributes.defense * 1.5 + attributes.accuracy * 1.2;
        break;
      case 'defender':
        score = attributes.defense * 2.5 + attributes.speed * 1.5 + attributes.stamina * 1.2 + attributes.passing * 0.8;
        break;
      case 'midfielder':
        score = attributes.passing * 2 + attributes.stamina * 1.8 + attributes.attack * 1.2 + attributes.defense * 1.2 + attributes.speed * 1;
        break;
      case 'forward':
        score = attributes.attack * 2.5 + attributes.shooting * 2.2 + attributes.speed * 1.8 + attributes.accuracy * 1.5;
        break;
    }

    return score;
  }

  /**
   * 计算位置偏好得分
   */
  private calculatePositionPreferenceScore(hero: any, position: any): number {
    if (!hero.preferredPositions || !Array.isArray(hero.preferredPositions)) {
      return 0;
    }

    if (hero.preferredPositions.includes(position.id)) {
      return 500; // 首选位置大幅加成
    } else if (hero.preferredPositions.includes(position.type)) {
      return 200; // 同类型位置中等加成
    }

    return 0;
  }

  /**
   * 计算位置经验得分
   */
  private calculatePositionExperienceScore(hero: any, position: any): number {
    if (!hero.positionExperience) return 0;

    const experience = hero.positionExperience[position.id] || hero.positionExperience[position.type] || 0;
    return Math.min(experience * 2, 300); // 经验加成，最高300分
  }

  /**
   * 计算体能得分
   */
  private calculateFitnessScore(hero: any): number {
    const fitness = hero.fitness || 100;

    if (fitness >= 90) return 1.1;      // 体能优秀，10%加成
    else if (fitness >= 70) return 1.0; // 体能良好，无影响
    else if (fitness >= 50) return 0.9; // 体能一般，10%减成
    else return 0.7;                    // 体能较差，30%减成
  }

  /**
   * 计算策略加成
   */
  private calculateStrategyBonus(hero: any, position: any, strategy: string): number {
    let bonus = 1.0;

    switch (strategy) {
      case 'attack':
        if (position.type === 'forward') bonus = 1.3;
        else if (position.type === 'midfielder') bonus = 1.1;
        else if (position.type === 'defender') bonus = 0.9;
        break;
      case 'defense':
        if (position.type === 'defender') bonus = 1.3;
        else if (position.type === 'goalkeeper') bonus = 1.2;
        else if (position.type === 'midfielder') bonus = 1.1;
        else if (position.type === 'forward') bonus = 0.8;
        break;
      case 'balanced':
        if (position.type === 'midfielder') bonus = 1.2;
        else bonus = 1.0;
        break;
    }

    return bonus;
  }

  /**
   * 计算年龄影响
   */
  private calculateAgeMultiplier(hero: any, position: any): number {
    const age = hero.age || 25;

    // 不同位置对年龄的敏感度不同
    if (position.type === 'goalkeeper') {
      // 门将黄金年龄更长
      if (age >= 28 && age <= 35) return 1.1;
      else if (age >= 20 && age <= 40) return 1.0;
      else return 0.9;
    } else if (position.type === 'defender') {
      // 后卫经验重要
      if (age >= 25 && age <= 32) return 1.1;
      else if (age >= 20 && age <= 35) return 1.0;
      else return 0.9;
    } else if (position.type === 'midfielder') {
      // 中场需要平衡
      if (age >= 23 && age <= 30) return 1.1;
      else if (age >= 20 && age <= 33) return 1.0;
      else return 0.9;
    } else if (position.type === 'forward') {
      // 前锋速度重要，年龄敏感
      if (age >= 22 && age <= 28) return 1.1;
      else if (age >= 20 && age <= 32) return 1.0;
      else return 0.8;
    }

    return 1.0;
  }

  /**
   * 计算伤病影响
   */
  private calculateInjuryPenalty(hero: any): number {
    if (hero.injuryStatus === 'injured') {
      return 0.3; // 受伤严重影响
    } else if (hero.injuryStatus === 'recovering') {
      return 0.7; // 恢复中中等影响
    } else if (hero.injuryRisk && hero.injuryRisk > 70) {
      return 0.9; // 高伤病风险轻微影响
    }

    return 1.0; // 健康状态无影响
  }

  /**
   * 优化阵容装备配置
   */
  private async optimizeEquipmentForFormation(
    characterId: string,
    formation: any,
    availableEquipment: any[]
  ): Promise<XResult<any[]>> {
    try {
      const equipmentChanges: any[] = [];
      
      // 为阵容中的英雄分配最优装备
      const heroIds = Object.values(formation.positionToHerosObject).filter(id => id);
      const equipmentPool = [...availableEquipment]; // 创建装备池副本

      // 按英雄重要性排序（门将 > 核心位置 > 其他）
      const sortedHeroIds = this.sortHeroesByImportance(heroIds as string[], formation);

      for (const heroId of sortedHeroIds) {
        // 获取英雄信息
        const hero = await this.getHeroInfo(heroId);
        if (!hero) continue;

        // 为英雄找到最适合的装备
        const bestEquipment = this.findBestEquipmentForHero(hero, equipmentPool);

        if (bestEquipment.length > 0) {
          // 从装备池中移除已分配的装备
          bestEquipment.forEach(equipment => {
            const index = equipmentPool.findIndex(e => e.itemId === equipment.itemId);
            if (index !== -1) {
              equipmentPool.splice(index, 1);
            }
          });

          equipmentChanges.push({
            heroId,
            heroName: hero.name || `英雄${heroId}`,
            position: this.getHeroPositionInFormation(heroId, formation),
            currentEquipment: hero.equipment || [],
            recommendedEquipment: bestEquipment,
            battlePowerIncrease: this.calculateEquipmentBattlePowerIncrease(hero, bestEquipment),
            reason: this.generateEquipmentChangeReason(hero, bestEquipment)
          });
        }
      }

      return XResultUtils.ok(equipmentChanges);
    } catch (error) {
      this.logger.error('装备优化失败', error);
      return XResultUtils.error('装备优化失败', 'EQUIPMENT_OPTIMIZATION_FAILED');
    }
  }

  /**
   * 按重要性排序英雄
   */
  private sortHeroesByImportance(heroIds: string[], formation: any): string[] {
    const positionImportance = {
      'GK': 1,    // 门将最重要
      'CB1': 2, 'CB2': 2, 'CB': 2,  // 中后卫
      'ST': 3, 'ST1': 3, 'ST2': 3,  // 前锋
      'CM': 4, 'CM1': 4, 'CM2': 4,  // 中场
      'LB': 5, 'RB': 5,              // 边后卫
      'LM': 6, 'RM': 6,              // 边中场
      'LF': 7, 'RF': 7               // 边锋
    };

    return heroIds.sort((a, b) => {
      const positionA = this.getHeroPositionInFormation(a, formation);
      const positionB = this.getHeroPositionInFormation(b, formation);

      const importanceA = positionImportance[positionA] || 10;
      const importanceB = positionImportance[positionB] || 10;

      return importanceA - importanceB;
    });
  }

  /**
   * 获取英雄在阵容中的位置
   */
  private getHeroPositionInFormation(heroId: string, formation: any): string {
    const positions = formation.positionToHerosObject || {};

    for (const [position, hId] of Object.entries(positions)) {
      if (hId === heroId) {
        return position;
      }
    }

    return 'UNKNOWN';
  }

  /**
   * 获取英雄信息
   */
  private async getHeroInfo(heroId: string): Promise<any> {
    try {
      // 这里应该调用HeroService获取英雄详细信息
      // 简化实现：返回基础英雄信息
      return {
        heroId,
        name: `英雄${heroId}`,
        level: 50,
        attack: 75,
        defense: 70,
        speed: 80,
        equipment: []
      };
    } catch (error) {
      this.logger.error(`获取英雄${heroId}信息失败`, error);
      return null;
    }
  }

  /**
   * 为英雄找到最适合的装备
   */
  private findBestEquipmentForHero(hero: any, availableEquipment: any[]): any[] {
    const bestEquipment: any[] = [];
    const equipmentSlots = ['boots', 'jersey', 'gloves', 'shin_guards']; // 装备槽位

    // 为每个装备槽位找到最适合的装备
    for (const slot of equipmentSlots) {
      const slotEquipment = availableEquipment.filter(eq => eq.type === slot);

      if (slotEquipment.length > 0) {
        // 根据英雄属性和装备属性匹配
        const bestForSlot = this.selectBestEquipmentForSlot(hero, slotEquipment, slot);
        if (bestForSlot) {
          bestEquipment.push(bestForSlot);
        }
      }
    }

    return bestEquipment;
  }

  /**
   * 为装备槽位选择最佳装备
   */
  private selectBestEquipmentForSlot(hero: any, slotEquipment: any[], slot: string): any | null {
    let bestEquipment = null;
    let bestScore = -1;

    for (const equipment of slotEquipment) {
      const score = this.calculateEquipmentScore(hero, equipment, slot);

      if (score > bestScore) {
        bestScore = score;
        bestEquipment = equipment;
      }
    }

    return bestEquipment;
  }

  /**
   * 计算装备得分
   */
  private calculateEquipmentScore(hero: any, equipment: any, slot: string): number {
    let score = 0;

    // 基础装备等级得分
    score += (equipment.level || 1) * 10;

    // 装备属性与英雄属性匹配度
    const heroPosition = this.getHeroPositionType(hero);

    switch (slot) {
      case 'boots':
        // 鞋子主要影响速度
        score += (equipment.speed || 0) * 2;
        if (heroPosition === 'forward' || heroPosition === 'midfielder') {
          score += (equipment.speed || 0) * 1; // 前锋和中场更需要速度
        }
        break;
      case 'jersey':
        // 球衣主要影响体力和防守
        score += (equipment.stamina || 0) * 1.5;
        score += (equipment.defense || 0) * 1;
        break;
      case 'gloves':
        // 手套主要影响精度（门将特别重要）
        score += (equipment.accuracy || 0) * 1.5;
        if (heroPosition === 'goalkeeper') {
          score += (equipment.accuracy || 0) * 2;
        }
        break;
      case 'shin_guards':
        // 护腿板主要影响防守
        score += (equipment.defense || 0) * 2;
        if (heroPosition === 'defender') {
          score += (equipment.defense || 0) * 1;
        }
        break;
    }

    // 装备品质加成
    const qualityBonus = this.getEquipmentQualityBonus(equipment.quality);
    score *= qualityBonus;

    return Math.floor(score);
  }

  /**
   * 获取英雄位置类型
   */
  private getHeroPositionType(hero: any): string {
    // 基于英雄属性判断最适合的位置类型
    const { attack = 0, defense = 0, goalkeeping = 0, speed = 0 } = hero;

    if (goalkeeping > Math.max(attack, defense)) {
      return 'goalkeeper';
    } else if (attack > defense * 1.2) {
      return 'forward';
    } else if (defense > attack * 1.2) {
      return 'defender';
    } else {
      return 'midfielder';
    }
  }

  /**
   * 获取装备品质加成
   */
  private getEquipmentQualityBonus(quality: string): number {
    const qualityBonuses = {
      'common': 1.0,
      'uncommon': 1.1,
      'rare': 1.3,
      'epic': 1.5,
      'legendary': 2.0
    };

    return qualityBonuses[quality] || 1.0;
  }

  /**
   * 计算装备战力提升
   */
  private calculateEquipmentBattlePowerIncrease(hero: any, equipment: any[]): number {
    let increase = 0;

    equipment.forEach(eq => {
      const baseIncrease = (eq.level || 1) * 200;
      const qualityMultiplier = this.getEquipmentQualityBonus(eq.quality);
      increase += baseIncrease * qualityMultiplier;
    });

    return Math.floor(increase);
  }

  /**
   * 生成装备更换原因
   */
  private generateEquipmentChangeReason(hero: any, equipment: any[]): string {
    const reasons = [];

    if (equipment.length > 0) {
      const avgLevel = equipment.reduce((sum, eq) => sum + (eq.level || 1), 0) / equipment.length;
      if (avgLevel > 5) {
        reasons.push('装备等级提升');
      }

      const hasHighQuality = equipment.some(eq => ['epic', 'legendary'].includes(eq.quality));
      if (hasHighQuality) {
        reasons.push('高品质装备');
      }

      reasons.push('属性匹配优化');
    }

    return reasons.length > 0 ? reasons.join('，') : '装备配置优化';
  }

  /**
   * 计算阵容战力
   */
  private async calculateFormationBattlePower(formation: any): Promise<number> {
    // 简化版本：基于阵容配置计算战力
    const baseAttack = formation.attack || 0;
    const baseDefense = formation.defend || 0;
    return baseAttack + baseDefense;
  }

  /**
   * 生成优化建议
   */
  private generateOptimizationRecommendations(
    formation: any,
    unassignedHeroes: any[],
    strategy: string
  ): string[] {
    const recommendations: string[] = [];

    // 基于未分配英雄生成建议
    if (unassignedHeroes.length > 0) {
      recommendations.push(`还有${unassignedHeroes.length}名英雄未上阵，可考虑替换表现较差的英雄`);
    }

    // 基于策略生成建议
    switch (strategy) {
      case 'attack':
        recommendations.push('当前采用攻击策略，建议加强中场组织和前场配合');
        break;
      case 'defense':
        recommendations.push('当前采用防守策略，建议注意防线密度和反击时机');
        break;
      case 'balanced':
        recommendations.push('当前采用平衡策略，建议根据对手特点调整战术重点');
        break;
    }

    return recommendations;
  }
}
