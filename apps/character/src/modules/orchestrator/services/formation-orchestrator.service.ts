import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { BaseService } from '@libs/common/service';
import { MicroserviceClientService } from '@libs/service-mesh';
import { XResult, XResultUtils } from '@libs/common/types/result.type';

// 导入各个子模块服务
import { FormationService } from '../../formation/formation.service';
import { HeroService } from '../../hero/services/hero.service';
import { InventoryService } from '../../inventory/inventory.service';
import { TacticService } from '../../tactic/tactic.service';

// 导入DTO
import {
  SmartFormationDto,
  SmartFormationResultDto
} from '../dto/orchestrator.dto';

/**
 * 阵容聚合服务 - 跨模块阵容管理业务编排
 * 
 * 🎯 核心职责：
 * - 智能布阵编排（英雄选择 + 位置优化 + 装备配置）
 * - 阵容战力优化建议
 * - 阵容与装备联动管理
 * - 阵容战术配置编排
 * 
 * 🔄 业务编排场景：
 * 1. 智能布阵：根据英雄属性和位置适应性自动布阵
 * 2. 装备优化：为阵容中的英雄自动配置最优装备
 * 3. 战术匹配：根据阵容特点推荐最适合的战术
 * 4. 阵容分析：分析阵容的优缺点并提供改进建议
 * 5. 多阵容管理：管理不同场景下的多套阵容配置
 * 
 * 🚀 性能优化：
 * - 并行处理英雄数据和装备数据
 * - 智能缓存布阵算法结果
 * - 增量更新阵容配置
 * - 批量处理装备变更
 * 
 * 🧠 智能算法：
 * - 位置适应性算法：根据英雄属性匹配最佳位置
 * - 战力最大化算法：在满足位置要求下最大化总战力
 * - 平衡性算法：保证攻防平衡的阵容配置
 * - 克制关系算法：考虑英雄间的克制关系
 */
@Injectable()
export class FormationOrchestratorService extends BaseService {
  constructor(
    @Inject(forwardRef(() => FormationService))
    private readonly formationService: FormationService,
    
    @Inject(forwardRef(() => HeroService))
    private readonly heroService: HeroService,
    
    @Inject(forwardRef(() => InventoryService))
    private readonly inventoryService: InventoryService,
    
    @Inject(forwardRef(() => TacticService))
    private readonly tacticService: TacticService,
    
    microserviceClient?: MicroserviceClientService,
  ) {
    super('FormationOrchestratorService', microserviceClient);
  }

  /**
   * 智能布阵
   * 综合考虑英雄属性、位置适应性、装备配置等因素进行最优布阵
   * 
   * 🔄 业务编排流程：
   * 1. 获取阵容和英雄数据
   * 2. 分析英雄属性和适应性
   * 3. 执行智能布阵算法
   * 4. 优化装备配置（可选）
   * 5. 计算战力变化
   * 6. 生成优化建议
   */
  async smartFormation(dto: SmartFormationDto): Promise<XResult<SmartFormationResultDto>> {
    return this.executeBusinessOperation(async () => {
      const { characterId, formationId, preferredHeroes, strategy = 'balanced', autoEquip = false } = dto;
      this.logger.log(`智能布阵: ${characterId}`, { formationId, strategy, autoEquip });

      // 1. 并行获取必要数据
      const [formationResult, heroesResult, equipmentResult] = await Promise.allSettled([
        this.formationService.getFormation(characterId, formationId),
        this.heroService.getCharacterHeroes(characterId),
        autoEquip ? this.inventoryService.getAvailableEquipment(characterId) : Promise.resolve(null)
      ]);

      // 2. 验证数据获取结果
      if (formationResult.status === 'rejected' || XResultUtils.isFailure(formationResult.value)) {
        return XResultUtils.error('获取阵容数据失败', 'GET_FORMATION_FAILED');
      }

      if (heroesResult.status === 'rejected' || XResultUtils.isFailure(heroesResult.value)) {
        return XResultUtils.error('获取英雄数据失败', 'GET_HEROES_FAILED');
      }

      const formation = formationResult.value.data;
      const heroes = heroesResult.value.data || [];
      const availableEquipment = equipmentResult.status === 'fulfilled' && equipmentResult.value ? equipmentResult.value.data : [];

      // 3. 记录原始战力
      const originalBattlePower = await this.calculateFormationBattlePower(formation);

      // 4. 执行智能布阵算法
      const smartFormationResult = this.executeSmartFormationAlgorithm(
        formation,
        heroes,
        preferredHeroes,
        strategy
      );

      if (XResultUtils.isFailure(smartFormationResult)) {
        return smartFormationResult;
      }

      const { optimizedFormation, positionMapping, unassignedHeroes } = smartFormationResult.data;

      // 5. 更新阵容配置
      const updateResult = await this.formationService.updateFormationPositions(
        characterId,
        formationId,
        positionMapping
      );

      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error('更新阵容配置失败', 'UPDATE_FORMATION_FAILED');
      }

      // 6. 自动装备优化（可选）
      let equipmentChanges: any[] = [];
      if (autoEquip && availableEquipment.length > 0) {
        const equipOptimizeResult = await this.optimizeEquipmentForFormation(
          characterId,
          optimizedFormation,
          availableEquipment
        );
        
        if (XResultUtils.isSuccess(equipOptimizeResult)) {
          equipmentChanges = equipOptimizeResult.data;
        }
      }

      // 7. 计算新战力
      const newBattlePower = await this.calculateFormationBattlePower(optimizedFormation);
      const battlePowerChange = newBattlePower - originalBattlePower;

      // 8. 生成优化建议
      const recommendations = this.generateOptimizationRecommendations(
        optimizedFormation,
        unassignedHeroes,
        strategy
      );

      const result: SmartFormationResultDto = {
        characterId,
        formationId,
        formation: optimizedFormation,
        positionMapping,
        unassignedHeroes,
        equipmentChanges,
        battlePowerChange,
        recommendations
      };

      this.logger.log(`智能布阵完成: ${characterId}`, {
        battlePowerChange,
        unassignedCount: unassignedHeroes.length,
        equipmentChanges: equipmentChanges.length,
        recommendations: recommendations.length
      });

      return XResultUtils.ok(result);
    }, { reason: 'smart_formation', metadata: { characterId: dto.characterId, formationId: dto.formationId } });
  }

  /**
   * 执行智能布阵算法
   * 根据策略和英雄属性进行最优位置分配
   */
  private executeSmartFormationAlgorithm(
    formation: any,
    heroes: any[],
    preferredHeroes?: string[],
    strategy: string = 'balanced'
  ): XResult<{
    optimizedFormation: any;
    positionMapping: any;
    unassignedHeroes: any[];
  }> {
    try {
      // 1. 获取阵型的位置配置
      const positions = this.getFormationPositions(formation);
      
      // 2. 筛选可用英雄
      let availableHeroes = [...heroes];
      
      // 优先使用指定英雄
      if (preferredHeroes && preferredHeroes.length > 0) {
        const preferred = heroes.filter(hero => preferredHeroes.includes(hero.heroId));
        const others = heroes.filter(hero => !preferredHeroes.includes(hero.heroId));
        availableHeroes = [...preferred, ...others];
      }

      // 3. 根据策略排序英雄
      availableHeroes = this.sortHeroesByStrategy(availableHeroes, strategy);

      // 4. 执行位置分配算法
      const positionMapping: any = {};
      const assignedHeroes: Set<string> = new Set();

      // 为每个位置分配最适合的英雄
      for (const position of positions) {
        const bestHero = this.findBestHeroForPosition(
          position,
          availableHeroes.filter(hero => !assignedHeroes.has(hero.heroId)),
          strategy
        );

        if (bestHero) {
          positionMapping[position.id] = bestHero.heroId;
          assignedHeroes.add(bestHero.heroId);
        }
      }

      // 5. 计算未分配的英雄
      const unassignedHeroes = availableHeroes.filter(hero => !assignedHeroes.has(hero.heroId));

      // 6. 更新阵容配置
      const optimizedFormation = {
        ...formation,
        positionToHerosObject: positionMapping
      };

      return XResultUtils.ok({
        optimizedFormation,
        positionMapping,
        unassignedHeroes
      });
    } catch (error) {
      this.logger.error('智能布阵算法执行失败', error);
      return XResultUtils.error('智能布阵算法执行失败', 'SMART_FORMATION_ALGORITHM_FAILED');
    }
  }

  /**
   * 获取阵型的位置配置
   */
  private getFormationPositions(formation: any): any[] {
    // 简化版本：返回标准11人制足球阵型位置
    return [
      { id: 'GK', name: '门将', type: 'goalkeeper', priority: 1 },
      { id: 'LB', name: '左后卫', type: 'defender', priority: 2 },
      { id: 'CB1', name: '中后卫1', type: 'defender', priority: 2 },
      { id: 'CB2', name: '中后卫2', type: 'defender', priority: 2 },
      { id: 'RB', name: '右后卫', type: 'defender', priority: 2 },
      { id: 'LM', name: '左中场', type: 'midfielder', priority: 3 },
      { id: 'CM1', name: '中场1', type: 'midfielder', priority: 3 },
      { id: 'CM2', name: '中场2', type: 'midfielder', priority: 3 },
      { id: 'RM', name: '右中场', type: 'midfielder', priority: 3 },
      { id: 'LF', name: '左前锋', type: 'forward', priority: 4 },
      { id: 'RF', name: '右前锋', type: 'forward', priority: 4 }
    ];
  }

  /**
   * 根据策略排序英雄
   */
  private sortHeroesByStrategy(heroes: any[], strategy: string): any[] {
    switch (strategy) {
      case 'attack':
        // 攻击策略：优先选择攻击力高的英雄
        return heroes.sort((a, b) => (b.attack || 0) - (a.attack || 0));
      case 'defense':
        // 防守策略：优先选择防守力高的英雄
        return heroes.sort((a, b) => (b.defense || 0) - (a.defense || 0));
      case 'balanced':
      default:
        // 平衡策略：按综合能力排序
        return heroes.sort((a, b) => {
          const aTotal = (a.attack || 0) + (a.defense || 0) + (a.speed || 0);
          const bTotal = (b.attack || 0) + (b.defense || 0) + (b.speed || 0);
          return bTotal - aTotal;
        });
    }
  }

  /**
   * 为位置找到最适合的英雄
   */
  private findBestHeroForPosition(position: any, availableHeroes: any[], strategy: string): any | null {
    if (availableHeroes.length === 0) return null;

    // 简化版本：根据位置类型和英雄属性匹配
    let bestHero = null;
    let bestScore = -1;

    for (const hero of availableHeroes) {
      const score = this.calculateHeroPositionScore(hero, position, strategy);
      if (score > bestScore) {
        bestScore = score;
        bestHero = hero;
      }
    }

    return bestHero;
  }

  /**
   * 计算英雄在特定位置的适应性得分
   */
  private calculateHeroPositionScore(hero: any, position: any, strategy: string): number {
    let score = 0;

    // 基础属性匹配
    switch (position.type) {
      case 'goalkeeper':
        score += (hero.goalkeeping || 0) * 2 + (hero.defense || 0);
        break;
      case 'defender':
        score += (hero.defense || 0) * 2 + (hero.speed || 0);
        break;
      case 'midfielder':
        score += (hero.passing || 0) + (hero.attack || 0) + (hero.defense || 0);
        break;
      case 'forward':
        score += (hero.attack || 0) * 2 + (hero.speed || 0);
        break;
    }

    // 策略加成
    if (strategy === 'attack' && position.type === 'forward') {
      score *= 1.2;
    } else if (strategy === 'defense' && position.type === 'defender') {
      score *= 1.2;
    }

    return score;
  }

  /**
   * 优化阵容装备配置
   */
  private async optimizeEquipmentForFormation(
    characterId: string,
    formation: any,
    availableEquipment: any[]
  ): Promise<XResult<any[]>> {
    try {
      const equipmentChanges: any[] = [];
      
      // 简化版本：为阵容中的英雄分配最优装备
      const heroIds = Object.values(formation.positionToHerosObject).filter(id => id);
      
      for (const heroId of heroIds) {
        // 为每个英雄找到最适合的装备
        const bestEquipment = this.findBestEquipmentForHero(heroId as string, availableEquipment);
        
        if (bestEquipment.length > 0) {
          equipmentChanges.push({
            heroId,
            equipment: bestEquipment
          });
        }
      }

      return XResultUtils.ok(equipmentChanges);
    } catch (error) {
      this.logger.error('装备优化失败', error);
      return XResultUtils.error('装备优化失败', 'EQUIPMENT_OPTIMIZATION_FAILED');
    }
  }

  /**
   * 为英雄找到最适合的装备
   */
  private findBestEquipmentForHero(heroId: string, availableEquipment: any[]): any[] {
    // 简化版本：返回前3件装备
    return availableEquipment.slice(0, 3);
  }

  /**
   * 计算阵容战力
   */
  private async calculateFormationBattlePower(formation: any): Promise<number> {
    // 简化版本：基于阵容配置计算战力
    const baseAttack = formation.attack || 0;
    const baseDefense = formation.defend || 0;
    return baseAttack + baseDefense;
  }

  /**
   * 生成优化建议
   */
  private generateOptimizationRecommendations(
    formation: any,
    unassignedHeroes: any[],
    strategy: string
  ): string[] {
    const recommendations: string[] = [];

    // 基于未分配英雄生成建议
    if (unassignedHeroes.length > 0) {
      recommendations.push(`还有${unassignedHeroes.length}名英雄未上阵，可考虑替换表现较差的英雄`);
    }

    // 基于策略生成建议
    switch (strategy) {
      case 'attack':
        recommendations.push('当前采用攻击策略，建议加强中场组织和前场配合');
        break;
      case 'defense':
        recommendations.push('当前采用防守策略，建议注意防线密度和反击时机');
        break;
      case 'balanced':
        recommendations.push('当前采用平衡策略，建议根据对手特点调整战术重点');
        break;
    }

    return recommendations;
  }
}
