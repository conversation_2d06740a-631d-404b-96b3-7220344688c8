import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { XResult, XResultUtils } from '@libs/common/types/result.type';
import { FormationService } from '../../formation/formation.service';
import { HeroService } from '../../hero/services/hero.service';
import { AutoSetupFormationPayloadDto } from '../dto/formation-orchestrator.dto';

@Injectable()
export class FormationOrchestratorService {
  constructor(
    @Inject(forwardRef(() => FormationService)) private readonly formationService: FormationService,
    @Inject(forwardRef(() => HeroService)) private readonly heroService: HeroService,
  ) {}

  async autoSetup(payload: AutoSetupFormationPayloadDto): Promise<XResult<any>> {
    const { characterId, formationId, candidateHeroIds = [] } = payload;

    let heroIds = candidateHeroIds;
    if (!heroIds || heroIds.length === 0) {
      // 复用HeroService.getHeroList以分页形式获取，默认拉取前100个
      const listResult = await this.heroService.getHeroList({ characterId, page: 1, limit: 100 } as any);
      if (XResultUtils.isFailure(listResult)) return listResult as XResult<any>;
      heroIds = (listResult.data.list || []).map((h: any) => h.heroId);
    }

    // 调用FormationService.autoFormation执行布阵
    const autoResult = await this.formationService.autoFormation(characterId, formationId, heroIds);
    if (XResultUtils.isFailure(autoResult)) return autoResult as XResult<any>;

    return XResultUtils.ok(autoResult.data);
  }
}
