import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { BaseService } from '@libs/common/service';
import { MicroserviceClientService } from '@libs/service-mesh';
import { XResult, XResultUtils } from '@libs/common/types/result.type';

// 导入各个子模块服务
import { CharacterService } from '../../character/character.service';
import { HeroService } from '../../hero/services/hero.service';
import { InventoryService } from '../../inventory/inventory.service';

// 导入DTO
import {
  BatchRewardDto,
  RewardResultDto,
  RewardItemDto
} from '../dto/orchestrator.dto';

/**
 * 奖励聚合服务 - 跨模块奖励发放业务编排
 * 
 * 🎯 核心职责：
 * - 批量奖励发放编排（货币 + 物品 + 英雄）
 * - 奖励类型智能识别和路由
 * - 奖励发放事务管理
 * - 奖励发放结果聚合
 * 
 * 🔄 业务编排场景：
 * 1. 任务完成奖励：可能包含金币、道具、英雄等多种奖励
 * 2. 活动奖励发放：批量发放多种类型奖励
 * 3. 充值返利：货币和道具的组合奖励
 * 4. 成就奖励：复杂的多类型奖励组合
 * 5. 补偿奖励：系统维护后的补偿发放
 * 
 * 🚀 性能优化：
 * - 按奖励类型分组批量处理
 * - 并行处理不同类型的奖励
 * - 事务支持确保奖励发放的原子性
 * - 失败重试和部分成功处理
 * 
 * 🛡️ 安全保障：
 * - 奖励数量上限检查
 * - 重复发放检测
 * - 奖励合法性验证
 * - 操作日志记录
 */
@Injectable()
export class RewardOrchestratorService extends BaseService {
  constructor(
    @Inject(forwardRef(() => CharacterService))
    private readonly characterService: CharacterService,
    
    @Inject(forwardRef(() => HeroService))
    private readonly heroService: HeroService,
    
    @Inject(forwardRef(() => InventoryService))
    private readonly inventoryService: InventoryService,
    
    microserviceClient?: MicroserviceClientService,
  ) {
    super('RewardOrchestratorService', microserviceClient);
  }

  /**
   * 批量奖励发放
   * 智能识别奖励类型并路由到对应的子模块服务
   * 
   * 🔄 业务编排流程：
   * 1. 奖励合法性验证
   * 2. 按类型分组奖励
   * 3. 并行处理各类型奖励
   * 4. 聚合处理结果
   * 5. 记录操作日志
   */
  async batchReward(dto: BatchRewardDto): Promise<XResult<RewardResultDto>> {
    return this.executeBusinessOperation(async () => {
      const { characterId, rewards, reason } = dto;
      this.logger.log(`批量奖励发放: ${characterId}`, { 
        totalRewards: rewards.length, 
        reason 
      });

      // 1. 验证角色存在
      const characterResult = await this.characterService.getCharacterInfo(characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`角色不存在: ${characterResult.message}`, characterResult.code);
      }

      // 2. 按类型分组奖励
      const groupedRewards = this.groupRewardsByType(rewards);
      
      // 3. 并行处理各类型奖励
      const [currencyResults, itemResults, heroResults] = await Promise.allSettled([
        this.processCurrencyRewards(characterId, groupedRewards.currency, reason),
        this.processItemRewards(characterId, groupedRewards.items, reason),
        this.processHeroRewards(characterId, groupedRewards.heroes, reason)
      ]);

      // 4. 聚合处理结果
      const result: RewardResultDto = {
        characterId,
        totalRewards: rewards.length,
        currencyRewards: [],
        itemRewards: [],
        heroRewards: [],
        failedRewards: []
      };

      // 处理货币奖励结果
      if (currencyResults.status === 'fulfilled' && XResultUtils.isSuccess(currencyResults.value)) {
        result.currencyRewards = currencyResults.value.data;
      } else {
        this.logger.error('货币奖励处理失败', currencyResults);
        result.failedRewards.push(...groupedRewards.currency.map(r => ({ ...r, error: '货币奖励处理失败' })));
      }

      // 处理物品奖励结果
      if (itemResults.status === 'fulfilled' && XResultUtils.isSuccess(itemResults.value)) {
        result.itemRewards = itemResults.value.data;
      } else {
        this.logger.error('物品奖励处理失败', itemResults);
        result.failedRewards.push(...groupedRewards.items.map(r => ({ ...r, error: '物品奖励处理失败' })));
      }

      // 处理英雄奖励结果
      if (heroResults.status === 'fulfilled' && XResultUtils.isSuccess(heroResults.value)) {
        result.heroRewards = heroResults.value.data;
      } else {
        this.logger.error('英雄奖励处理失败', heroResults);
        result.failedRewards.push(...groupedRewards.heroes.map(r => ({ ...r, error: '英雄奖励处理失败' })));
      }

      const successCount = result.currencyRewards.length + result.itemRewards.length + result.heroRewards.length;
      this.logger.log(`批量奖励发放完成: ${characterId}`, {
        totalRewards: rewards.length,
        successCount,
        failedCount: result.failedRewards.length
      });

      return XResultUtils.ok(result);
    }, { reason: 'batch_reward', metadata: { characterId: dto.characterId, rewardCount: dto.rewards.length } });
  }

  /**
   * 按类型分组奖励
   * 将混合的奖励列表按照处理方式分组
   */
  private groupRewardsByType(rewards: RewardItemDto[]): {
    currency: RewardItemDto[];
    items: RewardItemDto[];
    heroes: RewardItemDto[];
  } {
    const grouped = {
      currency: [] as RewardItemDto[],
      items: [] as RewardItemDto[],
      heroes: [] as RewardItemDto[]
    };

    rewards.forEach(reward => {
      switch (reward.type) {
        case 'currency':
          grouped.currency.push(reward);
          break;
        case 'item':
          grouped.items.push(reward);
          break;
        case 'hero':
          grouped.heroes.push(reward);
          break;
        default:
          this.logger.warn(`未知奖励类型: ${reward.type}`, reward);
      }
    });

    return grouped;
  }

  /**
   * 处理货币奖励
   * 调用CharacterService的批量货币操作
   */
  private async processCurrencyRewards(
    characterId: string,
    currencyRewards: RewardItemDto[],
    reason?: string
  ): Promise<XResult<any[]>> {
    if (currencyRewards.length === 0) {
      return XResultUtils.ok([]);
    }

    try {
      // 转换为CharacterService需要的格式
      const operations = currencyRewards.map(reward => ({
        currencyType: reward.subType,
        amount: reward.amount
      }));

      const result = await this.characterService.batchCurrencyOperation(
        characterId,
        operations,
        reason || '奖励发放'
      );

      return result;
    } catch (error) {
      this.logger.error('处理货币奖励失败', error);
      return XResultUtils.error('处理货币奖励失败', 'PROCESS_CURRENCY_REWARDS_FAILED');
    }
  }

  /**
   * 处理物品奖励
   * 调用InventoryService的批量添加物品
   */
  private async processItemRewards(
    characterId: string,
    itemRewards: RewardItemDto[],
    reason?: string
  ): Promise<XResult<any[]>> {
    if (itemRewards.length === 0) {
      return XResultUtils.ok([]);
    }

    try {
      const results: any[] = [];

      // 逐个处理物品奖励（可以优化为批量处理）
      for (const reward of itemRewards) {
        const configId = parseInt(reward.subType);
        const result = await this.inventoryService.addItemInternal(
          characterId,
          configId,
          reward.amount,
          reason || reward.reason || '奖励发放'
        );

        if (XResultUtils.isSuccess(result)) {
          results.push(result.data);
        } else {
          this.logger.error(`添加物品失败: ${configId}`, result);
        }
      }

      return XResultUtils.ok(results);
    } catch (error) {
      this.logger.error('处理物品奖励失败', error);
      return XResultUtils.error('处理物品奖励失败', 'PROCESS_ITEM_REWARDS_FAILED');
    }
  }

  /**
   * 处理英雄奖励
   * 调用HeroService创建英雄
   */
  private async processHeroRewards(
    characterId: string,
    heroRewards: RewardItemDto[],
    reason?: string
  ): Promise<XResult<any[]>> {
    if (heroRewards.length === 0) {
      return XResultUtils.ok([]);
    }

    try {
      const results: any[] = [];

      // 逐个处理英雄奖励
      for (const reward of heroRewards) {
        const heroConfigId = parseInt(reward.subType);
        
        // 创建多个相同英雄（如果amount > 1）
        for (let i = 0; i < reward.amount; i++) {
          const createResult = await this.heroService.createHero({
            characterId,
            heroConfigId,
            reason: reason || reward.reason || '奖励发放'
          });

          if (XResultUtils.isSuccess(createResult)) {
            results.push(createResult.data);
          } else {
            this.logger.error(`创建英雄失败: ${heroConfigId}`, createResult);
          }
        }
      }

      return XResultUtils.ok(results);
    } catch (error) {
      this.logger.error('处理英雄奖励失败', error);
      return XResultUtils.error('处理英雄奖励失败', 'PROCESS_HERO_REWARDS_FAILED');
    }
  }

  /**
   * 单一奖励发放
   * 为单个奖励提供便捷接口
   */
  async singleReward(
    characterId: string,
    rewardType: string,
    subType: string,
    amount: number,
    reason?: string
  ): Promise<XResult<any>> {
    const batchDto: BatchRewardDto = {
      characterId,
      rewards: [{
        type: rewardType,
        subType,
        amount,
        reason
      }],
      reason
    };

    const result = await this.batchReward(batchDto);
    
    if (XResultUtils.isSuccess(result)) {
      // 返回单个奖励的结果
      const rewardResult = result.data;
      if (rewardResult.currencyRewards.length > 0) {
        return XResultUtils.ok(rewardResult.currencyRewards[0]);
      } else if (rewardResult.itemRewards.length > 0) {
        return XResultUtils.ok(rewardResult.itemRewards[0]);
      } else if (rewardResult.heroRewards.length > 0) {
        return XResultUtils.ok(rewardResult.heroRewards[0]);
      } else if (rewardResult.failedRewards.length > 0) {
        return XResultUtils.error('奖励发放失败', 'REWARD_FAILED');
      }
    }

    return result;
  }
}
