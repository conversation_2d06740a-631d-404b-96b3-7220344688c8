import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { XResult, XResultUtils } from '@libs/common/types/result.type';
import { CharacterService } from '../../character/character.service';
import { InventoryService } from '../../inventory/inventory.service';
import { HeroService } from '../../hero/services/hero.service';
import { GrantRewardPayloadDto } from '../dto/character-orchestrator.dto';

@Injectable()
export class RewardOrchestratorService {
  constructor(
    @Inject(forwardRef(() => CharacterService)) private readonly characterService: CharacterService,
    @Inject(forwardRef(() => InventoryService)) private readonly inventoryService: InventoryService,
    @Inject(forwardRef(() => HeroService)) private readonly heroService: HeroService,
  ) {}

  // 统一发放奖励：复用现有服务，不新增业务逻辑，严格保持一致性
  async grant(payload: GrantRewardPayloadDto): Promise<XResult<any>> {
    const { characterId, currencyChanges = [], items = [], heroes = [], reason } = payload;

    // 1) 货币变更（如有）
    if (currencyChanges.length > 0) {
      const ops = currencyChanges.map(c => ({ currencyType: c.currencyType, amount: c.amount }));
      const currencyResult = await this.characterService.batchCurrencyOperation(characterId, ops, reason || 'reward_grant');
      if (XResultUtils.isFailure(currencyResult)) {
        return currencyResult as XResult<any>; // 直接透传错误
      }
    }

    // 2) 道具发放（如有） - 复用支持事务的batchAddItemsInternal
    if (items.length > 0) {
      const itemResult = await this.inventoryService.batchAddItemsInternal(
        characterId,
        items.map(i => ({ configId: i.configId, quantity: i.quantity })),
        reason || 'reward_grant_items'
      );
      if (XResultUtils.isFailure(itemResult)) {
        return itemResult as XResult<any>;
      }
    }

    // 3) 英雄发放（如有） - 逐个创建，沿用现有createHero实现
    const createdHeroes: string[] = [];
    if (heroes.length > 0) {
      for (const h of heroes) {
        const createResult = await this.heroService.createHero({
          characterId,
          resId: h.resId,
          name: h.name,
          position: h.position as any,
          quality: (h.quality as any) ?? 1,
          level: h.level ?? 1,
          nationality: undefined,
          club: undefined,
          obtainType: 1,
        } as any);

        if (XResultUtils.isFailure(createResult)) {
          return createResult as XResult<any>;
        }
        createdHeroes.push(createResult.data.heroId);
      }
    }

    return XResultUtils.ok({
      characterId,
      granted: {
        currencies: currencyChanges.length,
        items: items.length,
        heroes: createdHeroes,
      },
      reason: reason || 'reward_grant'
    });
  }
}
