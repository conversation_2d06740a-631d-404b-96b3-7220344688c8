import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { XResult, XResultUtils, TransactionResultManager } from '@libs/common/types/result.type';
import { CharacterService } from '../../character/character.service';
import { FormationService } from '../../formation/formation.service';
import { InventoryService } from '../../inventory/inventory.service';
import { HeroService } from '../../hero/services/hero.service';
import { TacticService } from '../../tactic/tactic.service';
import { InitializeCharacterPayloadDto, GetCompositeInfoPayloadDto, HeroGrant } from '../dto/character-orchestrator.dto';

@Injectable()
export class CharacterOrchestratorService {
  constructor(
    @Inject(forwardRef(() => CharacterService)) private readonly characterService: CharacterService,
    @Inject(forwardRef(() => FormationService)) private readonly formationService: FormationService,
    @Inject(forwardRef(() => InventoryService)) private readonly inventoryService: InventoryService,
    @Inject(forwardRef(() => HeroService)) private readonly heroService: HeroService,
    @Inject(forwardRef(() => TacticService)) private readonly tacticService: TacticService,
  ) {}

  // 角色初始化：严格复用现有各模块初始化方法，保证幂等
  async initialize(payload: InitializeCharacterPayloadDto): Promise<XResult<any>> {
    const { characterId, userId, serverId } = payload;

    // 1) 初始化角色基础数据（若存在则直接返回）
    const baseInit = await this.characterService.initializeBase(characterId, serverId, userId);
    if (XResultUtils.isFailure(baseInit)) return baseInit;

    // 2) 并行初始化阵容/背包/战术
    const [formationInit, inventoryInit, tacticInit] = await Promise.all([
      this.formationService.initCharacterFormations(characterId),
      this.inventoryService.initializeInventory(characterId),
      this.tacticService.initCharacterTactics(characterId),
    ]);

    if (XResultUtils.isFailure(formationInit)) return formationInit as XResult<any>;
    if (XResultUtils.isFailure(inventoryInit)) return inventoryInit as XResult<any>;
    if (XResultUtils.isFailure(tacticInit)) return tacticInit as XResult<any>;

    // 3) 可选：为新角色初始化默认球员（遵循现有获取方式，无默认则跳过）
    // 未发现统一的“默认球员初始化”方法，避免臆造逻辑：此处不创建球员。

    // 4) 汇总返回
    return XResultUtils.ok({
      character: baseInit.data && {
        characterId: baseInit.data.characterId,
        userId: baseInit.data.userId,
        serverId: baseInit.data.serverId,
        level: baseInit.data.level,
      },
      formationsInitialized: true,
      inventoryInitialized: true,
      tacticsInitialized: true,
    });
  }

  // 角色综合信息：复用各服务读取，编排层只做拼装
  async getCompositeInfo(payload: GetCompositeInfoPayloadDto): Promise<XResult<any>> {
    const { characterId } = payload;

    const [characterInfo, formations, inventorySummaries] = await Promise.all([
      this.characterService.getCharacterInfo(characterId),
      this.formationService.getCharacterFormations(characterId),
      this.inventoryService.getInventorySummaries(characterId),
    ]);

    if (XResultUtils.isFailure(characterInfo)) return characterInfo as XResult<any>;
    if (XResultUtils.isFailure(formations)) return formations as XResult<any>;
    if (XResultUtils.isFailure(inventorySummaries)) return inventorySummaries as XResult<any>;

    // 英雄列表使用已有分页接口需要DTO，编排层避免重造，保持轻量：仅返回阵容中引用的球员可延后由前端按需拉取
    return XResultUtils.ok({
      character: characterInfo.data,
      formations: formations.data,
      inventory: inventorySummaries.data,
    });
  }
}
