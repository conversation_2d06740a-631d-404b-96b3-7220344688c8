import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { BaseService } from '@libs/common/service';
import { MicroserviceClientService } from '@libs/service-mesh';
import { XResult, XResultUtils } from '@libs/common/types/result.type';

// 导入各个子模块服务
import { CharacterService } from '../../character/character.service';
import { FormationService } from '../../formation/formation.service';
import { HeroService } from '../../hero/services/hero.service';
import { InventoryService } from '../../inventory/inventory.service';
import { BeliefService } from '../../belief/belief.service';
import { TacticService } from '../../tactic/tactic.service';

// 导入DTO
import {
  CompleteCharacterInfoDto,
  InitializeCharacterDto,
  InitializeCharacterResultDto
} from '../dto/orchestrator.dto';

/**
 * 角色聚合服务 - 角色相关跨模块业务编排
 * 
 * 🎯 核心职责：
 * - 角色完整信息聚合（基础数据 + 阵容 + 英雄 + 背包 + 信念 + 战术）
 * - 角色初始化业务编排（并行初始化各子系统）
 * - 角色登录业务编排（数据完整性检查 + 补偿初始化）
 * - 角色数据同步编排（跨模块数据一致性保证）
 * 
 * 🚀 性能优化：
 * - 并行调用子模块服务，减少总响应时间
 * - 智能缓存聚合结果，避免重复计算
 * - 按需加载数据，支持部分数据获取
 * - 事务支持，确保初始化过程的原子性
 * 
 * 🔄 业务编排模式：
 * - 基础数据优先：先确保character基础数据存在
 * - 并行初始化：formation、inventory、tactic可并行初始化
 * - 依赖检查：hero依赖character，belief依赖character
 * - 补偿机制：失败时支持部分回滚和重试
 */
@Injectable()
export class CharacterOrchestratorService extends BaseService {
  constructor(
    @Inject(forwardRef(() => CharacterService))
    private readonly characterService: CharacterService,
    
    @Inject(forwardRef(() => FormationService))
    private readonly formationService: FormationService,
    
    @Inject(forwardRef(() => HeroService))
    private readonly heroService: HeroService,
    
    @Inject(forwardRef(() => InventoryService))
    private readonly inventoryService: InventoryService,
    
    @Inject(forwardRef(() => BeliefService))
    private readonly beliefService: BeliefService,
    
    @Inject(forwardRef(() => TacticService))
    private readonly tacticService: TacticService,
    
    microserviceClient?: MicroserviceClientService,
  ) {
    super('CharacterOrchestratorService', microserviceClient);
  }

  /**
   * 获取角色完整信息
   * 聚合所有子模块的角色相关数据，提供统一的角色信息视图
   * 
   * 🔄 业务编排流程：
   * 1. 获取角色基础信息（必需）
   * 2. 并行获取各子系统数据（可选）
   * 3. 聚合数据并计算衍生字段
   * 4. 返回完整的角色信息
   */
  async getCompleteCharacterInfo(
    characterId: string,
    options: {
      includeFormation?: boolean;
      includeHeroes?: boolean;
      includeInventory?: boolean;
      includeBelief?: boolean;
      includeTactics?: boolean;
      includeBattlePower?: boolean;
    } = {}
  ): Promise<XResult<CompleteCharacterInfoDto>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`获取角色完整信息: ${characterId}`, { options });

      // 1. 获取角色基础信息（必需）
      const characterResult = await this.characterService.getCharacterInfo(characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`获取角色基础信息失败: ${characterResult.message}`, characterResult.code);
      }

      const character = characterResult.data;
      const completeInfo: CompleteCharacterInfoDto = {
        // 复制基础角色信息
        ...character,
      };

      // 2. 并行获取各子系统数据（按需加载）
      const promises: Promise<any>[] = [];
      const dataKeys: string[] = [];

      if (options.includeFormation !== false) {
        promises.push(this.formationService.getCharacterFormations(characterId));
        dataKeys.push('formation');
      }

      if (options.includeHeroes !== false) {
        promises.push(this.heroService.getCharacterHeroes(characterId));
        dataKeys.push('heroes');
      }

      if (options.includeInventory !== false) {
        promises.push(this.inventoryService.getInventory(characterId));
        dataKeys.push('inventory');
      }

      if (options.includeBelief !== false) {
        promises.push(this.beliefService.getMyBelief({ characterId }));
        dataKeys.push('belief');
      }

      if (options.includeTactics !== false) {
        promises.push(this.tacticService.getCharacterTactics(characterId));
        dataKeys.push('tactics');
      }

      // 3. 等待所有并行请求完成
      const results = await Promise.allSettled(promises);

      // 4. 处理各子系统数据
      results.forEach((result, index) => {
        const dataKey = dataKeys[index];
        
        if (result.status === 'fulfilled' && XResultUtils.isSuccess(result.value)) {
          switch (dataKey) {
            case 'formation':
              completeInfo.currentFormation = result.value.data;
              break;
            case 'heroes':
              const heroes = result.value.data || [];
              completeInfo.heroCount = heroes.length;
              break;
            case 'inventory':
              completeInfo.inventorySummary = this.buildInventorySummary(result.value.data);
              break;
            case 'belief':
              completeInfo.beliefInfo = result.value.data;
              break;
            case 'tactics':
              completeInfo.tacticInfo = result.value.data;
              break;
          }
        } else {
          this.logger.warn(`获取${dataKey}数据失败`, {
            characterId,
            error: result.status === 'rejected' ? result.reason : 'Unknown error'
          });
        }
      });

      // 5. 计算总战力（如果需要）
      if (options.includeBattlePower !== false) {
        const battlePowerResult = await this.calculateTotalBattlePower(characterId);
        if (XResultUtils.isSuccess(battlePowerResult)) {
          completeInfo.totalBattlePower = battlePowerResult.data;
        }
      }

      this.logger.log(`角色完整信息获取成功: ${characterId}`, {
        heroCount: completeInfo.heroCount,
        totalBattlePower: completeInfo.totalBattlePower
      });

      return XResultUtils.ok(completeInfo);
    }, { reason: 'get_complete_character_info', metadata: { characterId, options } });
  }

  /**
   * 角色初始化业务编排
   * 确保角色在所有子系统中都有完整的数据结构
   * 
   * 🔄 业务编排流程：
   * 1. 初始化角色基础数据（优先级最高）
   * 2. 并行初始化独立子系统（formation、inventory、tactic）
   * 3. 初始化依赖子系统（hero依赖character）
   * 4. 验证初始化完整性
   * 5. 返回初始化结果摘要
   */
  async initializeCharacter(dto: InitializeCharacterDto): Promise<XResult<InitializeCharacterResultDto>> {
    return this.executeBusinessOperation(async () => {
      const { characterId, userId, serverId, forceReinit } = dto;
      this.logger.log(`角色初始化开始: ${characterId}`, { userId, serverId, forceReinit });

      const initSteps: string[] = [];
      let isNewCharacter = false;

      // 1. 初始化角色基础数据（必须优先完成）
      const characterResult = await this.characterService.initializeBase(characterId, serverId, userId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`角色基础数据初始化失败: ${characterResult.message}`, characterResult.code);
      }
      
      isNewCharacter = !characterResult.data.loginInfo?.loginTime;
      initSteps.push('character_base');

      // 2. 并行初始化独立子系统
      const [formationResult, inventoryResult, tacticResult] = await Promise.allSettled([
        this.formationService.initCharacterFormations(characterId),
        this.inventoryService.initializeInventory(characterId),
        this.tacticService.initCharacterTactics(characterId)
      ]);

      // 处理formation初始化结果
      let formations = null;
      if (formationResult.status === 'fulfilled' && XResultUtils.isSuccess(formationResult.value)) {
        formations = formationResult.value.data;
        initSteps.push('formations');
      } else {
        this.logger.error('阵容初始化失败', formationResult);
      }

      // 处理inventory初始化结果
      let inventory = null;
      if (inventoryResult.status === 'fulfilled' && XResultUtils.isSuccess(inventoryResult.value)) {
        inventory = inventoryResult.value.data;
        initSteps.push('inventory');
      } else {
        this.logger.error('背包初始化失败', inventoryResult);
      }

      // 处理tactic初始化结果
      let tactics = null;
      if (tacticResult.status === 'fulfilled' && XResultUtils.isSuccess(tacticResult.value)) {
        tactics = tacticResult.value.data;
        initSteps.push('tactics');
      } else {
        this.logger.error('战术初始化失败', tacticResult);
      }

      // 3. 如果是新角色，创建初始英雄和阵容
      if (isNewCharacter) {
        try {
          // 这里可以调用hero服务创建初始英雄
          // const initialHeroesResult = await this.heroService.createInitialHeroes(characterId);
          // if (XResultUtils.isSuccess(initialHeroesResult)) {
          //   initSteps.push('initial_heroes');
          // }
          
          initSteps.push('new_character_setup');
        } catch (error) {
          this.logger.warn('新角色设置失败', error);
        }
      }

      const result: InitializeCharacterResultDto = {
        character: characterResult.data,
        formations,
        inventory,
        tactics,
        isNewCharacter,
        initSteps
      };

      this.logger.log(`角色初始化完成: ${characterId}`, {
        isNewCharacter,
        initSteps: initSteps.length,
        completedSteps: initSteps
      });

      return XResultUtils.ok(result);
    }, { reason: 'initialize_character', metadata: { characterId: dto.characterId, userId: dto.userId } });
  }

  /**
   * 构建背包摘要信息
   * 提取背包的关键统计信息，避免返回完整的物品列表
   */
  private buildInventorySummary(inventory: any): any {
    if (!inventory) return null;

    return {
      totalItems: inventory.items?.length || 0,
      totalSlots: inventory.maxSlots || 0,
      usedSlots: inventory.usedSlots || 0,
      freeSlots: (inventory.maxSlots || 0) - (inventory.usedSlots || 0),
      // 可以添加更多摘要信息，如物品类型统计等
    };
  }

  /**
   * 计算总战力
   * 这里是简化版本，实际应该调用BattlePowerOrchestratorService
   */
  private async calculateTotalBattlePower(characterId: string): Promise<XResult<number>> {
    try {
      // 简化版本：返回模拟战力值
      // 实际应该聚合hero、formation、inventory、tactic等模块的战力贡献
      const mockBattlePower = 50000 + Math.floor(Math.random() * 100000);
      return XResultUtils.ok(mockBattlePower);
    } catch (error) {
      this.logger.error('计算总战力失败', error);
      return XResultUtils.error('计算总战力失败', 'CALCULATE_BATTLE_POWER_FAILED');
    }
  }
}
