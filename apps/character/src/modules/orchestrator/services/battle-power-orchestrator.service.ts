import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { BaseService } from '@libs/common/service';
import { MicroserviceClientService } from '@libs/service-mesh';
import { XResult, XResultUtils } from '@libs/common/types/result.type';

// 导入各个子模块服务
import { CharacterService } from '../../character/character.service';
import { FormationService } from '../../formation/formation.service';
import { HeroService } from '../../hero/services/hero.service';
import { InventoryService } from '../../inventory/inventory.service';
import { BeliefService } from '../../belief/belief.service';
import { TacticService } from '../../tactic/tactic.service';

// 导入DTO
import {
  BattlePowerCalculationDto,
  BattlePowerResultDto
} from '../dto/orchestrator.dto';

/**
 * 战力聚合服务 - 跨模块战力计算业务编排
 * 
 * 🎯 核心职责：
 * - 综合战力计算编排（英雄 + 阵型 + 装备 + 战术 + 信念）
 * - 战力构成分析和详情展示
 * - 战力变化追踪和对比
 * - 战力优化建议生成
 * 
 * 🔄 业务编排场景：
 * 1. 角色总战力计算：聚合所有影响因素的战力贡献
 * 2. 阵容战力计算：特定阵容的战力评估
 * 3. 装备影响分析：装备变更对战力的影响
 * 4. 战术效果评估：战术配置对战力的提升
 * 5. 信念加成计算：信念系统对战力的增强
 * 
 * 🚀 性能优化：
 * - 并行获取各模块数据
 * - 智能缓存战力计算结果
 * - 增量计算战力变化
 * - 批量处理多个角色战力
 * 
 * 📊 战力构成：
 * - 英雄基础战力：英雄等级、星级、属性
 * - 装备加成：装备属性、强化等级、套装效果
 * - 阵型加成：阵型配置、位置适应性
 * - 战术加成：战术等级、组合效果
 * - 信念加成：信念等级、技能效果
 */
@Injectable()
export class BattlePowerOrchestratorService extends BaseService {
  constructor(
    @Inject(forwardRef(() => CharacterService))
    private readonly characterService: CharacterService,
    
    @Inject(forwardRef(() => FormationService))
    private readonly formationService: FormationService,
    
    @Inject(forwardRef(() => HeroService))
    private readonly heroService: HeroService,
    
    @Inject(forwardRef(() => InventoryService))
    private readonly inventoryService: InventoryService,
    
    @Inject(forwardRef(() => BeliefService))
    private readonly beliefService: BeliefService,
    
    @Inject(forwardRef(() => TacticService))
    private readonly tacticService: TacticService,
    
    microserviceClient?: MicroserviceClientService,
  ) {
    super('BattlePowerOrchestratorService', microserviceClient);
  }

  /**
   * 综合战力计算
   * 聚合所有影响战力的因素，计算角色或阵容的总战力
   * 
   * 🔄 业务编排流程：
   * 1. 获取角色基础信息
   * 2. 并行获取各模块数据
   * 3. 计算各部分战力贡献
   * 4. 聚合总战力和详细构成
   * 5. 生成优化建议
   */
  async calculateBattlePower(dto: BattlePowerCalculationDto): Promise<XResult<BattlePowerResultDto>> {
    return this.executeBusinessOperation(async () => {
      const { characterId, formationId, includeEquipment = true, includeTactics = true, includeBelief = true } = dto;
      this.logger.log(`计算综合战力: ${characterId}`, { formationId, includeEquipment, includeTactics, includeBelief });

      // 1. 验证角色存在
      const characterResult = await this.characterService.getCharacterInfo(characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`角色不存在: ${characterResult.message}`, characterResult.code);
      }

      // 2. 并行获取各模块数据
      const dataPromises = [
        this.getHeroData(characterId, formationId),
        this.getFormationData(characterId, formationId),
        includeEquipment ? this.getEquipmentData(characterId) : Promise.resolve(null),
        includeTactics ? this.getTacticData(characterId) : Promise.resolve(null),
        includeBelief ? this.getBeliefData(characterId) : Promise.resolve(null)
      ];

      const [heroData, formationData, equipmentData, tacticData, beliefData] = await Promise.allSettled(dataPromises);

      // 3. 计算各部分战力贡献
      const battlePowerComponents = {
        heroBattlePower: 0,
        equipmentBonus: 0,
        tacticBonus: 0,
        beliefBonus: 0,
        formationBonus: 0
      };

      const details = {
        heroDetails: [],
        equipmentDetails: [],
        tacticDetails: [],
        beliefDetails: null
      };

      // 处理英雄战力
      if (heroData.status === 'fulfilled' && heroData.value) {
        const heroPowerResult = this.calculateHeroBattlePower(heroData.value);
        battlePowerComponents.heroBattlePower = heroPowerResult.totalPower;
        details.heroDetails = heroPowerResult.details;
      }

      // 处理阵型加成
      if (formationData.status === 'fulfilled' && formationData.value) {
        battlePowerComponents.formationBonus = this.calculateFormationBonus(formationData.value);
      }

      // 处理装备加成
      if (equipmentData.status === 'fulfilled' && equipmentData.value) {
        const equipmentPowerResult = this.calculateEquipmentBonus(equipmentData.value);
        battlePowerComponents.equipmentBonus = equipmentPowerResult.totalBonus;
        details.equipmentDetails = equipmentPowerResult.details;
      }

      // 处理战术加成
      if (tacticData.status === 'fulfilled' && tacticData.value) {
        const tacticPowerResult = this.calculateTacticBonus(tacticData.value);
        battlePowerComponents.tacticBonus = tacticPowerResult.totalBonus;
        details.tacticDetails = tacticPowerResult.details;
      }

      // 处理信念加成
      if (beliefData.status === 'fulfilled' && beliefData.value) {
        const beliefPowerResult = this.calculateBeliefBonus(beliefData.value);
        battlePowerComponents.beliefBonus = beliefPowerResult.totalBonus;
        details.beliefDetails = beliefPowerResult.details;
      }

      // 4. 计算总战力
      const totalBattlePower = Object.values(battlePowerComponents).reduce((sum, power) => sum + power, 0);

      const result: BattlePowerResultDto = {
        characterId,
        formationId: formationId || 'current',
        totalBattlePower,
        ...battlePowerComponents,
        ...details
      };

      this.logger.log(`战力计算完成: ${characterId}`, {
        totalBattlePower,
        heroPower: battlePowerComponents.heroBattlePower,
        equipmentBonus: battlePowerComponents.equipmentBonus,
        tacticBonus: battlePowerComponents.tacticBonus,
        beliefBonus: battlePowerComponents.beliefBonus,
        formationBonus: battlePowerComponents.formationBonus
      });

      return XResultUtils.ok(result);
    }, { reason: 'calculate_battle_power', metadata: { characterId: dto.characterId, formationId: dto.formationId } });
  }

  /**
   * 获取英雄数据
   */
  private async getHeroData(characterId: string, formationId?: string): Promise<any> {
    try {
      if (formationId) {
        // 获取特定阵容的英雄
        const formationResult = await this.formationService.getFormation(characterId, formationId);
        if (XResultUtils.isSuccess(formationResult) && formationResult.data) {
          // 从阵容中提取英雄ID，然后获取英雄详情
          const heroIds = this.extractHeroIdsFromFormation(formationResult.data);
          return this.heroService.getHeroesByIds(characterId, heroIds);
        }
      }
      
      // 获取所有英雄
      return this.heroService.getCharacterHeroes(characterId);
    } catch (error) {
      this.logger.error('获取英雄数据失败', error);
      return null;
    }
  }

  /**
   * 获取阵容数据
   */
  private async getFormationData(characterId: string, formationId?: string): Promise<any> {
    try {
      if (formationId) {
        return this.formationService.getFormation(characterId, formationId);
      } else {
        return this.formationService.getCurrentFormation(characterId);
      }
    } catch (error) {
      this.logger.error('获取阵容数据失败', error);
      return null;
    }
  }

  /**
   * 获取装备数据
   */
  private async getEquipmentData(characterId: string): Promise<any> {
    try {
      return this.inventoryService.getEquippedItems(characterId);
    } catch (error) {
      this.logger.error('获取装备数据失败', error);
      return null;
    }
  }

  /**
   * 获取战术数据
   */
  private async getTacticData(characterId: string): Promise<any> {
    try {
      return this.tacticService.getCharacterTactics(characterId);
    } catch (error) {
      this.logger.error('获取战术数据失败', error);
      return null;
    }
  }

  /**
   * 获取信念数据
   */
  private async getBeliefData(characterId: string): Promise<any> {
    try {
      return this.beliefService.getMyBelief({ characterId });
    } catch (error) {
      this.logger.error('获取信念数据失败', error);
      return null;
    }
  }

  /**
   * 计算英雄战力
   */
  private calculateHeroBattlePower(heroData: any): { totalPower: number; details: any[] } {
    // 简化版本：基于英雄等级和星级计算战力
    const heroes = heroData.data || [];
    let totalPower = 0;
    const details: any[] = [];

    heroes.forEach((hero: any) => {
      const heroPower = (hero.level || 1) * 1000 + (hero.star || 1) * 5000;
      totalPower += heroPower;
      details.push({
        heroId: hero.heroId,
        name: hero.name,
        level: hero.level,
        star: hero.star,
        battlePower: heroPower
      });
    });

    return { totalPower, details };
  }

  /**
   * 计算阵型加成
   */
  private calculateFormationBonus(formationData: any): number {
    // 简化版本：基于阵型配置计算加成
    const formation = formationData.data;
    if (!formation) return 0;

    // 基础阵型加成
    return Math.floor(formation.attack * 0.1 + formation.defend * 0.1);
  }

  /**
   * 计算装备加成
   */
  private calculateEquipmentBonus(equipmentData: any): { totalBonus: number; details: any[] } {
    // 简化版本：基于装备数量和等级计算加成
    const equipment = equipmentData.data || [];
    let totalBonus = 0;
    const details: any[] = [];

    equipment.forEach((item: any) => {
      const itemBonus = (item.level || 1) * 500;
      totalBonus += itemBonus;
      details.push({
        itemId: item.itemId,
        name: item.name,
        level: item.level,
        bonus: itemBonus
      });
    });

    return { totalBonus, details };
  }

  /**
   * 计算战术加成
   */
  private calculateTacticBonus(tacticData: any): { totalBonus: number; details: any[] } {
    // 简化版本：基于战术等级计算加成
    const tactics = tacticData.data?.tactics || [];
    let totalBonus = 0;
    const details: any[] = [];

    tactics.forEach((tactic: any) => {
      const tacticBonus = (tactic.level || 1) * 300;
      totalBonus += tacticBonus;
      details.push({
        tacticKey: tactic.tacticKey,
        level: tactic.level,
        bonus: tacticBonus
      });
    });

    return { totalBonus, details };
  }

  /**
   * 计算信念加成
   */
  private calculateBeliefBonus(beliefData: any): { totalBonus: number; details: any } {
    // 简化版本：基于信念等级计算加成
    const belief = beliefData.data;
    if (!belief) return { totalBonus: 0, details: null };

    const beliefBonus = (belief.level || 1) * 1000;
    return {
      totalBonus: beliefBonus,
      details: {
        beliefId: belief.beliefId,
        level: belief.level,
        bonus: beliefBonus
      }
    };
  }

  /**
   * 从阵容中提取英雄ID
   */
  private extractHeroIdsFromFormation(formation: any): string[] {
    // 简化版本：从阵容配置中提取英雄ID
    const heroIds: string[] = [];
    
    if (formation.positionToHerosObject) {
      Object.values(formation.positionToHerosObject).forEach((heroId: any) => {
        if (heroId && typeof heroId === 'string') {
          heroIds.push(heroId);
        }
      });
    }

    return heroIds;
  }
}
