import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { BaseService } from '@libs/common/service';
import { MicroserviceClientService } from '@libs/service-mesh';
import { XResult, XResultUtils } from '@libs/common/types/result.type';

// 导入各个子模块服务
import { CharacterService } from '../../character/character.service';
import { FormationService } from '../../formation/formation.service';
import { HeroService } from '../../hero/services/hero.service';
import { InventoryService } from '../../inventory/inventory.service';
import { BeliefService } from '../../belief/belief.service';
import { TacticService } from '../../tactic/tactic.service';

// 导入DTO
import {
  BattlePowerCalculationDto,
  BattlePowerResultDto
} from '../dto/orchestrator.dto';

/**
 * 战力聚合服务 - 跨模块战力计算业务编排
 * 
 * 🎯 核心职责：
 * - 综合战力计算编排（英雄 + 阵型 + 装备 + 战术 + 信念）
 * - 战力构成分析和详情展示
 * - 战力变化追踪和对比
 * - 战力优化建议生成
 * 
 * 🔄 业务编排场景：
 * 1. 角色总战力计算：聚合所有影响因素的战力贡献
 * 2. 阵容战力计算：特定阵容的战力评估
 * 3. 装备影响分析：装备变更对战力的影响
 * 4. 战术效果评估：战术配置对战力的提升
 * 5. 信念加成计算：信念系统对战力的增强
 * 
 * 🚀 性能优化：
 * - 并行获取各模块数据
 * - 智能缓存战力计算结果
 * - 增量计算战力变化
 * - 批量处理多个角色战力
 * 
 * 📊 战力构成：
 * - 英雄基础战力：英雄等级、星级、属性
 * - 装备加成：装备属性、强化等级、套装效果
 * - 阵型加成：阵型配置、位置适应性
 * - 战术加成：战术等级、组合效果
 * - 信念加成：信念等级、技能效果
 */
@Injectable()
export class BattlePowerOrchestratorService extends BaseService {
  constructor(
    @Inject(forwardRef(() => CharacterService))
    private readonly characterService: CharacterService,
    
    @Inject(forwardRef(() => FormationService))
    private readonly formationService: FormationService,
    
    @Inject(forwardRef(() => HeroService))
    private readonly heroService: HeroService,
    
    @Inject(forwardRef(() => InventoryService))
    private readonly inventoryService: InventoryService,
    
    @Inject(forwardRef(() => BeliefService))
    private readonly beliefService: BeliefService,
    
    @Inject(forwardRef(() => TacticService))
    private readonly tacticService: TacticService,
    
    microserviceClient?: MicroserviceClientService,
  ) {
    super('BattlePowerOrchestratorService', microserviceClient);
  }

  /**
   * 综合战力计算
   * 聚合所有影响战力的因素，计算角色或阵容的总战力
   * 
   * 🔄 业务编排流程：
   * 1. 获取角色基础信息
   * 2. 并行获取各模块数据
   * 3. 计算各部分战力贡献
   * 4. 聚合总战力和详细构成
   * 5. 生成优化建议
   */
  async calculateBattlePower(dto: BattlePowerCalculationDto): Promise<XResult<BattlePowerResultDto>> {
    return this.executeBusinessOperation(async () => {
      const { characterId, formationId, includeEquipment = true, includeTactics = true, includeBelief = true } = dto;
      this.logger.log(`计算综合战力: ${characterId}`, { formationId, includeEquipment, includeTactics, includeBelief });

      // 1. 验证角色存在
      const characterResult = await this.characterService.getCharacterInfo(characterId);
      if (XResultUtils.isFailure(characterResult)) {
        return XResultUtils.error(`角色不存在: ${characterResult.message}`, characterResult.code);
      }

      // 2. 并行获取各模块数据
      const dataPromises = [
        this.getHeroData(characterId, formationId),
        this.getFormationData(characterId, formationId),
        includeEquipment ? this.getEquipmentData(characterId) : Promise.resolve(null),
        includeTactics ? this.getTacticData(characterId) : Promise.resolve(null),
        includeBelief ? this.getBeliefData(characterId) : Promise.resolve(null)
      ];

      const [heroData, formationData, equipmentData, tacticData, beliefData] = await Promise.allSettled(dataPromises);

      // 3. 计算各部分战力贡献
      const battlePowerComponents = {
        heroBattlePower: 0,
        equipmentBonus: 0,
        tacticBonus: 0,
        beliefBonus: 0,
        formationBonus: 0
      };

      const details = {
        heroDetails: [],
        equipmentDetails: [],
        tacticDetails: [],
        beliefDetails: null
      };

      // 处理英雄战力
      if (heroData.status === 'fulfilled' && heroData.value) {
        const heroPowerResult = this.calculateHeroBattlePower(heroData.value);
        battlePowerComponents.heroBattlePower = heroPowerResult.totalPower;
        details.heroDetails = heroPowerResult.details;
      }

      // 处理阵型加成
      if (formationData.status === 'fulfilled' && formationData.value) {
        battlePowerComponents.formationBonus = this.calculateFormationBonus(formationData.value);
      }

      // 处理装备加成
      if (equipmentData.status === 'fulfilled' && equipmentData.value) {
        const equipmentPowerResult = this.calculateEquipmentBonus(equipmentData.value);
        battlePowerComponents.equipmentBonus = equipmentPowerResult.totalBonus;
        details.equipmentDetails = equipmentPowerResult.details;
      }

      // 处理战术加成
      if (tacticData.status === 'fulfilled' && tacticData.value) {
        const tacticPowerResult = this.calculateTacticBonus(tacticData.value);
        battlePowerComponents.tacticBonus = tacticPowerResult.totalBonus;
        details.tacticDetails = tacticPowerResult.details;
      }

      // 处理信念加成
      if (beliefData.status === 'fulfilled' && beliefData.value) {
        const beliefPowerResult = this.calculateBeliefBonus(beliefData.value);
        battlePowerComponents.beliefBonus = beliefPowerResult.totalBonus;
        details.beliefDetails = beliefPowerResult.details;
      }

      // 4. 计算总战力
      const totalBattlePower = Object.values(battlePowerComponents).reduce((sum, power) => sum + power, 0);

      const result: BattlePowerResultDto = {
        characterId,
        formationId: formationId || 'current',
        totalBattlePower,
        ...battlePowerComponents,
        ...details
      };

      this.logger.log(`战力计算完成: ${characterId}`, {
        totalBattlePower,
        heroPower: battlePowerComponents.heroBattlePower,
        equipmentBonus: battlePowerComponents.equipmentBonus,
        tacticBonus: battlePowerComponents.tacticBonus,
        beliefBonus: battlePowerComponents.beliefBonus,
        formationBonus: battlePowerComponents.formationBonus
      });

      return XResultUtils.ok(result);
    }, { reason: 'calculate_battle_power', metadata: { characterId: dto.characterId, formationId: dto.formationId } });
  }

  /**
   * 获取英雄数据
   */
  private async getHeroData(characterId: string, formationId?: string): Promise<any> {
    try {
      if (formationId) {
        // 获取特定阵容的英雄
        const formationResult = await this.formationService.getFormations(characterId);
        if (XResultUtils.isSuccess(formationResult) && formationResult.data) {
          // 从阵容中提取英雄ID，然后获取英雄详情
          const heroIds = this.extractHeroIdsFromFormation(formationResult.data);
          // 获取所有英雄，然后过滤出指定的英雄
          const allHeroesResult = await this.heroService.getCharacterHeroes(characterId);
          if (XResultUtils.isSuccess(allHeroesResult)) {
            const filteredHeroes = allHeroesResult.data.filter(hero =>
              heroIds.includes(hero.heroId)
            );
            return { success: true, data: filteredHeroes };
          }
          return allHeroesResult;
        }
      }
      
      // 获取所有英雄
      return this.heroService.getCharacterHeroes(characterId);
    } catch (error) {
      this.logger.error('获取英雄数据失败', error);
      return null;
    }
  }

  /**
   * 获取阵容数据
   */
  private async getFormationData(characterId: string, formationId?: string): Promise<any> {
    try {
      if (formationId) {
        return this.formationService.getFormation(characterId, formationId);
      } else {
        return this.formationService.getCurrentFormation(characterId);
      }
    } catch (error) {
      this.logger.error('获取阵容数据失败', error);
      return null;
    }
  }

  /**
   * 获取装备数据
   * 从背包中获取已装备的物品，包括装备属性和强化等级
   */
  private async getEquipmentData(characterId: string): Promise<any> {
    try {
      const inventoryResult = await this.inventoryService.getInventory(characterId);
      if (XResultUtils.isSuccess(inventoryResult)) {
        // 过滤出装备类型的物品
        const equippedItems = inventoryResult.data.items.filter(item =>
          item.type === 'equipment' && item.isEquipped
        );

        // 增强装备数据，添加计算所需的属性
        const enhancedEquipment = equippedItems.map(item => ({
          ...item,
          // 计算装备的实际属性加成
          actualAttack: (item.attack || 0) * (1 + (item.enhanceLevel || 0) * 0.1),
          actualDefense: (item.defense || 0) * (1 + (item.enhanceLevel || 0) * 0.1),
          actualSpeed: (item.speed || 0) * (1 + (item.enhanceLevel || 0) * 0.1),
          // 添加套装信息
          setBonus: this.calculateItemSetBonus(item, equippedItems),
          // 添加品质系数
          qualityMultiplier: this.getQualityMultiplier(item.quality || 'common')
        }));

        return { success: true, data: enhancedEquipment };
      }
      return null;
    } catch (error) {
      this.logger.error('获取装备数据失败', error);
      return null;
    }
  }

  /**
   * 计算物品套装加成
   */
  private calculateItemSetBonus(item: any, allEquipment: any[]): number {
    if (!item.setName) return 0;

    const setItems = allEquipment.filter(eq => eq.setName === item.setName);
    const setCount = setItems.length;

    // 根据套装件数计算加成
    if (setCount >= 6) return 0.3;      // 6件套装30%加成
    else if (setCount >= 4) return 0.2; // 4件套装20%加成
    else if (setCount >= 2) return 0.1; // 2件套装10%加成

    return 0;
  }

  /**
   * 获取战术数据
   */
  private async getTacticData(characterId: string): Promise<any> {
    try {
      return this.tacticService.getCharacterTactics(characterId);
    } catch (error) {
      this.logger.error('获取战术数据失败', error);
      return null;
    }
  }

  /**
   * 获取信念数据
   */
  private async getBeliefData(characterId: string): Promise<any> {
    try {
      return this.beliefService.getMyBelief({ characterId });
    } catch (error) {
      this.logger.error('获取信念数据失败', error);
      return null;
    }
  }

  /**
   * 计算英雄战力
   * 基于英雄的等级、星级、属性、技能等综合计算战力
   */
  private calculateHeroBattlePower(heroData: any): { totalPower: number; details: any[] } {
    const heroes = heroData.data || [];
    let totalPower = 0;
    const details: any[] = [];

    heroes.forEach((hero: any) => {
      // 基础属性战力
      const baseAttributes = {
        attack: hero.attack || 0,
        defense: hero.defense || 0,
        speed: hero.speed || 0,
        goalkeeping: hero.goalkeeping || 0,
        passing: hero.passing || 0,
        shooting: hero.shooting || 0
      };

      const attributesPower = Object.values(baseAttributes).reduce((sum, attr) => sum + attr, 0) * 10;

      // 等级战力（每级100战力）
      const levelPower = (hero.level || 1) * 100;

      // 星级战力（每星2000战力）
      const starPower = (hero.star || 1) * 2000;

      // 经验战力（基于当前经验）
      const expPower = Math.floor((hero.exp || 0) / 100);

      // 位置适应性加成（基于英雄在当前位置的适应度）
      const positionBonus = this.calculatePositionBonus(hero);

      // 技能战力（基于英雄技能等级）
      const skillPower = this.calculateSkillPower(hero);

      // 装备战力（如果英雄装备了物品）
      const equipmentPower = this.calculateHeroEquipmentPower(hero);

      // 总战力计算
      const heroPower = attributesPower + levelPower + starPower + expPower + positionBonus + skillPower + equipmentPower;

      totalPower += heroPower;
      details.push({
        heroId: hero.heroId,
        name: hero.name,
        level: hero.level,
        star: hero.star,
        attributes: baseAttributes,
        battlePower: heroPower,
        breakdown: {
          attributes: attributesPower,
          level: levelPower,
          star: starPower,
          experience: expPower,
          position: positionBonus,
          skills: skillPower,
          equipment: equipmentPower
        }
      });
    });

    return { totalPower, details };
  }

  /**
   * 计算位置适应性加成
   */
  private calculatePositionBonus(hero: any): number {
    if (!hero.position || !hero.preferredPositions) return 0;

    const isPreferredPosition = hero.preferredPositions.includes(hero.position);
    return isPreferredPosition ? hero.level * 50 : 0;
  }

  /**
   * 计算技能战力
   */
  private calculateSkillPower(hero: any): number {
    if (!hero.skills || !Array.isArray(hero.skills)) return 0;

    return hero.skills.reduce((total, skill) => {
      const skillLevel = skill.level || 1;
      const skillPower = skillLevel * 300; // 每级技能300战力
      return total + skillPower;
    }, 0);
  }

  /**
   * 计算英雄装备战力
   */
  private calculateHeroEquipmentPower(hero: any): number {
    if (!hero.equipment || !Array.isArray(hero.equipment)) return 0;

    return hero.equipment.reduce((total, equipment) => {
      const equipLevel = equipment.level || 1;
      const equipPower = equipLevel * 400; // 每级装备400战力
      return total + equipPower;
    }, 0);
  }

  /**
   * 计算阵型加成
   * 基于阵型配置、英雄位置匹配度、阵型等级等计算加成
   */
  private calculateFormationBonus(formationData: any): number {
    const formation = formationData.data;
    if (!formation) return 0;

    // 1. 基础阵型属性加成
    const baseAttackBonus = (formation.attack || 0) * 0.15;
    const baseDefendBonus = (formation.defend || 0) * 0.15;

    // 2. 阵型等级加成
    const formationLevel = formation.level || 1;
    const levelBonus = formationLevel * 200;

    // 3. 阵型完整度加成（所有位置都有英雄）
    const positionCount = Object.keys(formation.positionToHerosObject || {}).length;
    const maxPositions = 11; // 标准足球阵型11个位置
    const completenessBonus = (positionCount / maxPositions) * 1000;

    // 4. 阵型平衡性加成（攻防平衡）
    const attackDefendRatio = Math.min(formation.attack, formation.defend) / Math.max(formation.attack, formation.defend, 1);
    const balanceBonus = attackDefendRatio * 500;

    // 5. 阵型熟练度加成（使用次数越多，熟练度越高）
    const proficiencyBonus = Math.min((formation.usageCount || 0) * 10, 1000);

    // 6. 特殊阵型加成（某些阵型有特殊效果）
    const specialBonus = this.calculateSpecialFormationBonus(formation);

    const totalBonus = baseAttackBonus + baseDefendBonus + levelBonus + completenessBonus + balanceBonus + proficiencyBonus + specialBonus;

    return Math.floor(totalBonus);
  }

  /**
   * 计算特殊阵型加成
   */
  private calculateSpecialFormationBonus(formation: any): number {
    const formationType = formation.formationType || formation.type;

    // 根据不同阵型给予特殊加成
    const specialBonuses = {
      '4-4-2': 300,    // 经典阵型，平衡加成
      '4-3-3': 400,    // 攻击阵型，攻击加成
      '5-4-1': 350,    // 防守阵型，防守加成
      '3-5-2': 380,    // 中场控制阵型
      '4-2-3-1': 420,  // 现代阵型，全面加成
      '3-4-3': 390,    // 攻击阵型变种
      '5-3-2': 320     // 防守反击阵型
    };

    return specialBonuses[formationType] || 0;
  }

  /**
   * 计算装备加成
   * 基于装备类型、等级、品质、强化等级、套装效果等计算加成
   */
  private calculateEquipmentBonus(equipmentData: any): { totalBonus: number; details: any[] } {
    const equipment = equipmentData.data || [];
    let totalBonus = 0;
    const details: any[] = [];
    const setEquipment = new Map<string, any[]>(); // 用于计算套装效果

    equipment.forEach((item: any) => {
      // 1. 基础装备属性加成
      const baseAttributes = {
        attack: item.attack || 0,
        defense: item.defense || 0,
        speed: item.speed || 0,
        accuracy: item.accuracy || 0,
        stamina: item.stamina || 0
      };
      const baseBonus = Object.values(baseAttributes).reduce((sum, attr) => sum + attr, 0) * 5;

      // 2. 装备等级加成
      const level = item.level || 1;
      const levelBonus = level * 300;

      // 3. 装备品质加成
      const qualityMultiplier = this.getQualityMultiplier(item.quality || 'common');
      const qualityBonus = baseBonus * qualityMultiplier;

      // 4. 强化等级加成
      const enhanceLevel = item.enhanceLevel || 0;
      const enhanceBonus = enhanceLevel * 200;

      // 5. 装备类型特殊加成
      const typeBonus = this.getEquipmentTypeBonus(item.type, item);

      // 收集套装装备
      if (item.setName) {
        if (!setEquipment.has(item.setName)) {
          setEquipment.set(item.setName, []);
        }
        setEquipment.get(item.setName)!.push(item);
      }

      const itemTotalBonus = baseBonus + levelBonus + qualityBonus + enhanceBonus + typeBonus;
      totalBonus += itemTotalBonus;

      details.push({
        itemId: item.itemId,
        name: item.name,
        type: item.type,
        level: level,
        quality: item.quality,
        enhanceLevel: enhanceLevel,
        setName: item.setName,
        bonus: itemTotalBonus,
        breakdown: {
          base: baseBonus,
          level: levelBonus,
          quality: qualityBonus,
          enhance: enhanceBonus,
          type: typeBonus
        }
      });
    });

    // 6. 计算套装效果加成
    const setBonus = this.calculateSetBonus(setEquipment);
    totalBonus += setBonus.totalBonus;

    // 添加套装效果到详情中
    if (setBonus.details.length > 0) {
      details.push(...setBonus.details);
    }

    return { totalBonus: Math.floor(totalBonus), details };
  }

  /**
   * 获取品质倍数
   */
  private getQualityMultiplier(quality: string): number {
    const qualityMultipliers = {
      'common': 1.0,      // 普通
      'uncommon': 1.2,    // 不常见
      'rare': 1.5,        // 稀有
      'epic': 2.0,        // 史诗
      'legendary': 3.0,   // 传说
      'mythic': 4.0       // 神话
    };

    return qualityMultipliers[quality] || 1.0;
  }

  /**
   * 获取装备类型特殊加成
   */
  private getEquipmentTypeBonus(type: string, item: any): number {
    const typeBonuses = {
      'boots': (item.speed || 0) * 2,        // 鞋子增加速度加成
      'gloves': (item.accuracy || 0) * 2,    // 手套增加精度加成
      'jersey': (item.stamina || 0) * 2,     // 球衣增加体力加成
      'shin_guards': (item.defense || 0) * 2, // 护腿板增加防守加成
      'ball': (item.attack || 0) * 2         // 足球增加攻击加成
    };

    return typeBonuses[type] || 0;
  }

  /**
   * 计算套装效果加成
   */
  private calculateSetBonus(setEquipment: Map<string, any[]>): { totalBonus: number; details: any[] } {
    let totalBonus = 0;
    const details: any[] = [];

    setEquipment.forEach((items, setName) => {
      const setCount = items.length;
      let setBonus = 0;
      let setEffect = '';

      // 根据套装件数给予不同加成
      if (setCount >= 2) {
        setBonus += 500; // 2件套装加成
        setEffect = '2件套装：基础属性+10%';
      }
      if (setCount >= 4) {
        setBonus += 1000; // 4件套装加成
        setEffect += ', 4件套装：战力+1000';
      }
      if (setCount >= 6) {
        setBonus += 2000; // 6件套装加成
        setEffect += ', 6件套装：战力+2000';
      }

      if (setBonus > 0) {
        totalBonus += setBonus;
        details.push({
          type: 'set_bonus',
          setName: setName,
          setCount: setCount,
          bonus: setBonus,
          effect: setEffect
        });
      }
    });

    return { totalBonus, details };
  }

  /**
   * 计算战术加成
   * 基于战术等级、战术组合效果、战术与阵型匹配度等计算加成
   */
  private calculateTacticBonus(tacticData: any): { totalBonus: number; details: any[] } {
    const tactics = tacticData.data?.tactics || [];
    let totalBonus = 0;
    const details: any[] = [];
    const tacticTypes = new Set<string>();

    tactics.forEach((tactic: any) => {
      // 1. 基础战术等级加成
      const level = tactic.level || 1;
      const baseLevelBonus = level * 400;

      // 2. 战术类型加成
      const tacticType = tactic.tacticType || 'balanced';
      tacticTypes.add(tacticType);
      const typeBonus = this.getTacticTypeBonus(tacticType, level);

      // 3. 战术熟练度加成
      const proficiency = tactic.proficiency || 0;
      const proficiencyBonus = Math.floor(proficiency / 10) * 50;

      // 4. 战术效果加成（基于战术的具体效果）
      const effectBonus = this.calculateTacticEffectBonus(tactic);

      const tacticTotalBonus = baseLevelBonus + typeBonus + proficiencyBonus + effectBonus;
      totalBonus += tacticTotalBonus;

      details.push({
        tacticKey: tactic.tacticKey,
        tacticName: tactic.tacticName || tactic.tacticKey,
        tacticType: tacticType,
        level: level,
        proficiency: proficiency,
        bonus: tacticTotalBonus,
        breakdown: {
          level: baseLevelBonus,
          type: typeBonus,
          proficiency: proficiencyBonus,
          effect: effectBonus
        }
      });
    });

    // 5. 战术组合加成（不同类型战术的组合效果）
    const combinationBonus = this.calculateTacticCombinationBonus(tacticTypes);
    totalBonus += combinationBonus;

    if (combinationBonus > 0) {
      details.push({
        type: 'combination_bonus',
        tacticTypes: Array.from(tacticTypes),
        bonus: combinationBonus,
        description: '战术组合加成'
      });
    }

    return { totalBonus: Math.floor(totalBonus), details };
  }

  /**
   * 获取战术类型加成
   */
  private getTacticTypeBonus(tacticType: string, level: number): number {
    const typeBonuses = {
      'attack': level * 150,      // 攻击型战术
      'defense': level * 120,     // 防守型战术
      'balanced': level * 100,    // 平衡型战术
      'counter': level * 180,     // 反击型战术
      'possession': level * 130,  // 控球型战术
      'pressing': level * 160     // 逼抢型战术
    };

    return typeBonuses[tacticType] || level * 100;
  }

  /**
   * 计算战术效果加成
   */
  private calculateTacticEffectBonus(tactic: any): number {
    let effectBonus = 0;

    // 基于战术的具体效果属性计算加成
    if (tactic.attackBonus) effectBonus += tactic.attackBonus * 10;
    if (tactic.defenseBonus) effectBonus += tactic.defenseBonus * 10;
    if (tactic.speedBonus) effectBonus += tactic.speedBonus * 8;
    if (tactic.accuracyBonus) effectBonus += tactic.accuracyBonus * 12;
    if (tactic.staminaBonus) effectBonus += tactic.staminaBonus * 6;

    return effectBonus;
  }

  /**
   * 计算战术组合加成
   */
  private calculateTacticCombinationBonus(tacticTypes: Set<string>): number {
    const typeCount = tacticTypes.size;

    // 根据战术类型多样性给予组合加成
    if (typeCount >= 3) {
      return 1500; // 3种或以上不同类型战术
    } else if (typeCount >= 2) {
      return 800;  // 2种不同类型战术
    }

    return 0;
  }

  /**
   * 计算信念加成
   * 基于信念等级、信念技能、信念类型、贡献度等计算加成
   */
  private calculateBeliefBonus(beliefData: any): { totalBonus: number; details: any } {
    const belief = beliefData.data;
    if (!belief) return { totalBonus: 0, details: null };

    // 1. 基础信念等级加成
    const level = belief.level || 1;
    const baseLevelBonus = level * 800;

    // 2. 信念类型加成
    const beliefType = belief.beliefType || 'neutral';
    const typeBonus = this.getBeliefTypeBonus(beliefType, level);

    // 3. 个人贡献度加成
    const contribution = belief.contribution || 0;
    const contributionBonus = Math.floor(contribution / 100) * 50;

    // 4. 信念技能加成
    const skillBonus = this.calculateBeliefSkillBonus(belief.skills || []);

    // 5. 信念活跃度加成
    const activityBonus = this.calculateBeliefActivityBonus(belief);

    const totalBonus = baseLevelBonus + typeBonus + contributionBonus + skillBonus + activityBonus;

    return {
      totalBonus: Math.floor(totalBonus),
      details: {
        beliefId: belief.beliefId,
        beliefName: belief.beliefName || '未知信念',
        beliefType: beliefType,
        level: level,
        contribution: contribution,
        bonus: totalBonus,
        breakdown: {
          level: baseLevelBonus,
          type: typeBonus,
          contribution: contributionBonus,
          skills: skillBonus,
          activity: activityBonus
        }
      }
    };
  }

  /**
   * 获取信念类型加成
   */
  private getBeliefTypeBonus(beliefType: string, level: number): number {
    const typeBonuses = {
      'offensive': level * 200,    // 攻击型信念
      'defensive': level * 180,    // 防守型信念
      'balanced': level * 150,     // 平衡型信念
      'technical': level * 220,    // 技术型信念
      'physical': level * 190,     // 体能型信念
      'mental': level * 170        // 精神型信念
    };

    return typeBonuses[beliefType] || level * 150;
  }

  /**
   * 计算信念技能加成
   */
  private calculateBeliefSkillBonus(skills: any[]): number {
    return skills.reduce((total, skill) => {
      const skillLevel = skill.level || 1;
      const skillBonus = skillLevel * 300;
      return total + skillBonus;
    }, 0);
  }

  /**
   * 计算信念活跃度加成
   */
  private calculateBeliefActivityBonus(belief: any): number {
    const lastActiveTime = belief.lastActiveTime;
    if (!lastActiveTime) return 0;

    const now = new Date().getTime();
    const timeDiff = now - new Date(lastActiveTime).getTime();
    const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));

    // 最近活跃的信念获得加成，超过7天开始衰减
    if (daysDiff <= 1) {
      return 500; // 今天活跃
    } else if (daysDiff <= 3) {
      return 300; // 3天内活跃
    } else if (daysDiff <= 7) {
      return 100; // 一周内活跃
    }

    return 0; // 超过一周未活跃
  }

  /**
   * 从阵容中提取英雄ID
   * 解析阵容配置，提取所有位置上的英雄ID，并进行数据验证
   */
  private extractHeroIdsFromFormation(formation: any): string[] {
    const heroIds: string[] = [];

    if (formation.positionToHerosObject) {
      Object.entries(formation.positionToHerosObject).forEach(([position, heroId]: [string, any]) => {
        if (heroId && typeof heroId === 'string') {
          // 验证英雄ID格式
          if (this.isValidHeroId(heroId)) {
            heroIds.push(heroId);
          } else {
            this.logger.warn(`阵容${formation.formationId}中位置${position}的英雄ID格式无效: ${heroId}`);
          }
        }
      });
    }

    // 去重处理（防止同一英雄在多个位置）
    const uniqueHeroIds = [...new Set(heroIds)];

    if (uniqueHeroIds.length !== heroIds.length) {
      this.logger.warn(`阵容${formation.formationId}中发现重复的英雄配置`, {
        totalPositions: heroIds.length,
        uniqueHeroes: uniqueHeroIds.length
      });
    }

    return uniqueHeroIds;
  }

  /**
   * 验证英雄ID格式
   */
  private isValidHeroId(heroId: string): boolean {
    // 英雄ID应该是非空字符串，且符合一定格式
    return heroId.length > 0 && /^[a-zA-Z0-9_-]+$/.test(heroId);
  }
}
