import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { XResult, XResultUtils } from '@libs/common/types/result.type';
import { FormationService } from '../../formation/formation.service';
import { CharacterService } from '../../character/character.service';
import { CalculatePowerPayloadDto } from '../dto/power-orchestrator.dto';

@Injectable()
export class PowerOrchestratorService {
  constructor(
    @Inject(forwardRef(() => FormationService)) private readonly formationService: FormationService,
    @Inject(forwardRef(() => CharacterService)) private readonly characterService: CharacterService,
  ) {}

  async calculate(payload: CalculatePowerPayloadDto): Promise<XResult<any>> {
    const { characterId, formationId } = payload;

    // 读取阵容集合
    const formationsResult = await this.formationService.getCharacterFormations(characterId);
    if (XResultUtils.isFailure(formationsResult)) return formationsResult as XResult<any>;
    const formations = formationsResult.data;
    if (!formations) {
      return XResultUtils.error('角色阵容数据不存在', 'FORMATIONS_NOT_FOUND');
    }

    // 选择目标阵容
    const targetFormationId = formationId || formations.currTeamFormationId;
    const formation = formations.teamFormations.find(f => f.uid === targetFormationId);
    if (!formation) {
      return XResultUtils.error(`阵容不存在: ${targetFormationId}`, 'FORMATION_NOT_FOUND');
    }

    // 重算阵容属性
    const recalc = await this.formationService.recalculateFormationAttributes(formation as any, characterId);
    if (XResultUtils.isFailure(recalc)) return recalc as XResult<any>;

    // 返回关键指标（沿用TeamFormation字段）
    return XResultUtils.ok({
      formationId: formation.uid,
      attack: formation.attack,
      defend: formation.defend,
      actualStrength: formation.actualStrength,
    });
  }
}
