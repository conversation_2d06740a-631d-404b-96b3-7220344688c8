import { IsString, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON>y, IsN<PERSON>ber, IsUUID } from 'class-validator';
import { Type } from 'class-transformer';

export class InitializeCharacterPayloadDto {
  @IsString()
  characterId: string;

  @IsString()
  userId: string;

  @IsString()
  serverId: string;

  @IsOptional()
  @IsString()
  requestId?: string;
}

export class GetCompositeInfoPayloadDto {
  @IsString()
  characterId: string;
}

export class GrantRewardPayloadDto {
  @IsString()
  characterId: string;

  @IsOptional()
  @IsString()
  requestId?: string;

  @IsOptional()
  @Type(() => CurrencyChange)
  @IsArray()
  currencyChanges?: CurrencyChange[];

  @IsOptional()
  @Type(() => ItemGrant)
  @IsArray()
  items?: ItemGrant[];

  @IsOptional()
  @Type(() => HeroGrant)
  @IsArray()
  heroes?: HeroGrant[];

  @IsOptional()
  @IsString()
  reason?: string;
}

export class Curren<PERSON><PERSON>hang<PERSON> {
  @IsString()
  currencyType: string; // 'cash' | 'gold' | ...

  @IsNumber()
  amount: number; // 正为加 负为减
}

export class ItemGrant {
  @IsNumber()
  configId: number;

  @IsNumber()
  quantity: number;
}

export class HeroGrant {
  @IsNumber()
  resId: number;

  @IsString()
  position: string;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsNumber()
  quality?: number;

  @IsOptional()
  @IsNumber()
  level?: number;
}
