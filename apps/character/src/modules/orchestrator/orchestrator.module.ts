import { Module, forwardRef } from '@nestjs/common';
import { CharacterModule } from '../character/character.module';
import { FormationModule } from '../formation/formation.module';
import { InventoryModule } from '../inventory/inventory.module';
import { HeroModule } from '../hero/hero.module';
import { TacticModule } from '../tactic/tactic.module';

import { CharacterOrchestratorService } from './services/character-orchestrator.service';
import { RewardOrchestratorService } from './services/reward-orchestrator.service';
import { PowerOrchestratorService } from './services/power-orchestrator.service';
import { FormationOrchestratorService } from './services/formation-orchestrator.service';

import { OrchestratorCharacterController } from './controllers/orchestrator.character.controller';
import { OrchestratorRewardController } from './controllers/orchestrator.reward.controller';
import { OrchestratorPowerController } from './controllers/orchestrator.power.controller';
import { OrchestratorFormationController } from './controllers/orchestrator.formation.controller';

@Module({
  imports: [
    forwardRef(() => CharacterModule),
    forwardRef(() => FormationModule),
    forwardRef(() => InventoryModule),
    forwardRef(() => HeroModule),
    forwardRef(() => TacticModule),
  ],
  controllers: [
    OrchestratorCharacterController,
    OrchestratorRewardController,
    OrchestratorPowerController,
    OrchestratorFormationController,
  ],
  providers: [
    CharacterOrchestratorService,
    RewardOrchestratorService,
    PowerOrchestratorService,
    FormationOrchestratorService,
  ],
  exports: [
    CharacterOrchestratorService,
    RewardOrchestratorService,
    PowerOrchestratorService,
    FormationOrchestratorService,
  ],
})
export class OrchestratorModule {}
