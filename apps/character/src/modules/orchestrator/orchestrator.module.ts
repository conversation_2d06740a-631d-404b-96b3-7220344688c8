import { Module, forwardRef } from '@nestjs/common';
import { CharacterOrchestratorService } from './services/character-orchestrator.service';
import { RewardOrchestratorService } from './services/reward-orchestrator.service';
import { BattlePowerOrchestratorService } from './services/battle-power-orchestrator.service';
import { FormationOrchestratorService } from './services/formation-orchestrator.service';
import { CharacterOrchestratorController } from './controllers/character-orchestrator.controller';
import { RewardOrchestratorController } from './controllers/reward-orchestrator.controller';
import { BattlePowerOrchestratorController } from './controllers/battle-power-orchestrator.controller';
import { FormationOrchestratorController } from './controllers/formation-orchestrator.controller';

// 导入各个子模块
import { CharacterModule } from '../character/character.module';
import { FormationModule } from '../formation/formation.module';
import { HeroModule } from '../hero/hero.module';
import { InventoryModule } from '../inventory/inventory.module';
import { BeliefModule } from '../belief/belief.module';
import { TacticModule } from '../tactic/tactic.module';

/**
 * 聚合层模块 - Character服务业务编排中心
 * 
 * 🎯 核心职责：
 * - 编排跨模块业务流程
 * - 提供复合业务服务接口
 * - 处理模块间依赖协调
 * - 统一业务逻辑入口
 * 
 * 🏗️ 架构原则：
 * - 子模块专注核心职责：各模块只处理自身核心领域的基本业务逻辑和数据持久化
 * - 聚合层负责编排：处理跨模块业务流、复合业务服务、模块间依赖协调
 * - 依赖方向控制：子模块之间避免直接调用，客户端通过聚合层接口达到业务效果
 * 
 * 🔄 业务编排场景：
 * 1. 角色初始化/信息获取：聚合character（基础数据）、formation（阵容）、hero（英雄数据）、inventory（装备道具）
 * 2. 奖励发放：编排inventory（发放道具）、character（发放货币）、hero（发放英雄）
 * 3. 战力计算：依赖hero（英雄属性）、formation（阵容加成）、inventory（装备加成）、tactic（战术增益）
 * 4. 布阵操作：涉及formation（阵容结构）、hero（英雄状态）、inventory（装备配置）
 * 5. 信念系统增强：belief对hero、formation、tactic的增强效果
 * 
 * 🚀 性能优化：
 * - 并行调用子模块服务
 * - 智能缓存复合数据
 * - 批量操作减少调用次数
 * - 事务支持确保数据一致性
 */
@Module({
  imports: [
    // 使用forwardRef避免循环依赖
    forwardRef(() => CharacterModule),
    forwardRef(() => FormationModule),
    forwardRef(() => HeroModule),
    forwardRef(() => InventoryModule),
    forwardRef(() => BeliefModule),
    forwardRef(() => TacticModule),
  ],
  controllers: [
    CharacterOrchestratorController,
    RewardOrchestratorController,
    BattlePowerOrchestratorController,
    FormationOrchestratorController,
  ],
  providers: [
    CharacterOrchestratorService,
    RewardOrchestratorService,
    BattlePowerOrchestratorService,
    FormationOrchestratorService,
  ],
  exports: [
    CharacterOrchestratorService,
    RewardOrchestratorService,
    BattlePowerOrchestratorService,
    FormationOrchestratorService,
  ],
})
export class OrchestratorModule {}
