import { <PERSON>du<PERSON>, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

// Schema导入
import {Hero, HeroSchema, HeroSkill} from '@character/common/schemas/hero.schema';

// Repository导入
import { HeroRepository } from '@character/common/repositories/hero.repository';

// Controllers导入
import { HeroController } from './controllers/hero.controller';
import { HeroCultivationController } from './controllers/hero-cultivation.controller';
import { HeroTrainingController } from './controllers/hero-training.controller';
import { HeroCareerController } from './controllers/hero-career.controller';
import { HeroSkillController } from './controllers/hero-skill.controller';
import { HeroScoutController } from './controllers/hero-scout.controller';

// Services导入
import { HeroService } from './services/hero.service';
import { HeroCultivationService } from './services/hero-cultivation.service';
import { HeroTrainingService } from './services/hero-training.service';
import { HeroCareerService } from './services/hero-career.service';
import { HeroSkillService } from './services/hero-skill.service';
import { HeroScoutService } from './services/hero-scout.service';

// 其他模块导入
import { CharacterModule } from '../character/character.module';
import { InventoryModule } from '../inventory/inventory.module';
import { FormationModule } from '../formation/formation.module';
import { Skill, SkillSchema} from "@character/common/schemas/skill.schema";
import { SkillRepository } from "@character/common/repositories/skill.repository";

/**
 * 英雄统一模块
 * 采用扁平化架构：单一模块文件 + 多个Controller/Service
 * 
 * 🎯 模块职责：
 * - 英雄基础管理（创建、查询、更新、删除）
 * - 英雄养成系统（升星、突破、进化）
 * - 英雄训练系统（特训、替换、状态管理）
 * - 英雄生涯管理（续约、退役、状态提升）
 * - 英雄技能系统（学习、升级、激活）
 * - 球探系统（探索、签约、包管理）
 * 
 * 🏗️ 架构优势：
 * - 扁平化结构：减少模块间复杂依赖
 * - 统一管理：所有英雄相关功能集中管理
 * - 职责清晰：每个Controller/Service专注特定业务
 * - 易于维护：单一模块配置，降低维护成本
 */
@Module({
  imports: [
    // MongoDB Schema注册
    MongooseModule.forFeature([
      { name: Hero.name, schema: HeroSchema },
      { name: Skill.name, schema: SkillSchema },
    ]),
    
    // 依赖的其他模块
    forwardRef(() => CharacterModule),
    forwardRef(() => InventoryModule),
    forwardRef(() => FormationModule),
  ],
  
  controllers: [
    HeroController,              // 英雄基础管理
    HeroCultivationController,   // 英雄养成
    HeroTrainingController,      // 英雄训练
    HeroCareerController,        // 英雄生涯
    HeroSkillController,         // 英雄技能
    HeroScoutController,         // 球探系统
  ],
  
  providers: [
    // Repository
    HeroRepository,
    SkillRepository,
    
    // Services
    HeroService,                 // 英雄基础服务
    HeroCultivationService,      // 英雄养成服务
    HeroTrainingService,         // 英雄训练服务
    HeroCareerService,           // 英雄生涯服务
    HeroSkillService,            // 英雄技能服务
    HeroScoutService,            // 球探服务
  ],
  
  exports: [
    HeroRepository,
    SkillRepository,
    HeroService,                 // 供其他模块使用的核心服务
    HeroCultivationService,
    HeroTrainingService,
    HeroCareerService,
    HeroSkillService,
    HeroScoutService,
  ],
})
export class HeroModule {}
