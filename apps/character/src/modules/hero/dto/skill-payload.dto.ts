/**
 * Skill模块的Payload DTO定义
 * 
 * 为skill.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，直接包含字段而不嵌套dto
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsEnum, IsArray, Min, Max, Length } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';
import { SkillPosition } from "@character/common/types";

// 技能重置类型枚举
export enum SkillResetType {
  ALL = 'all',           // 重置所有
  LEVEL = 'level',       // 重置等级
  ACTIVATION = 'activation' // 重置激活状态
}

// 批量操作类型枚举
export enum SkillBatchOperation {
  ACTIVATE = 'activate',     // 激活
  DEACTIVATE = 'deactivate', // 取消激活
  UPGRADE = 'upgrade',       // 升级
  DELETE = 'delete'          // 删除
}

// ==================== 1. 技能配置相关 ====================

/**
 * 获取技能配置Payload DTO
 * @MessagePattern('skill.getConfig')
 */
export class GetConfigPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '技能ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '技能ID必须是数字' })
  @Min(1, { message: '技能ID不能小于1' })
  @Max(999999, { message: '技能ID不能大于999999' })
  skillId: number;
}

/**
 * 获取技能配置列表Payload DTO
 * @MessagePattern('skill.getConfigList')
 * 扩展GetSkillConfigListDto，合并BasePayloadDto内容
 */
export class GetConfigListPayloadDto extends BasePayloadDto {
  @ApiPropertyOptional({ description: '页码（可选）', example: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '页码必须是数字' })
  @Min(1, { message: '页码不能小于1' })
  page?: number;

  @ApiPropertyOptional({ description: '每页数量（可选）', example: 20 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '每页数量必须是数字' })
  @Min(1, { message: '每页数量不能小于1' })
  @Max(100, { message: '每页数量不能大于100' })
  limit?: number;

  @ApiPropertyOptional({ description: '技能类型（可选）', example: 'active', enum: ['active', 'passive', 'special'] })
  @Expose()
  @IsOptional()
  @IsString({ message: '技能类型必须是字符串' })
  @Length(1, 20, { message: '技能类型长度必须在1-20个字符之间' })
  type?: string;

  @ApiPropertyOptional({ description: '适用位置（可选）', enum: SkillPosition })
  @Expose()
  @IsOptional()
  @IsEnum(SkillPosition, { message: '适用位置必须是有效的枚举值' })
  position?: SkillPosition;
}

/**
 * 根据位置获取技能配置Payload DTO
 * @MessagePattern('skill.getConfigByPosition')
 */
export class GetConfigByPositionPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '技能位置', enum: SkillPosition, example: SkillPosition.ALL })
  @Expose()
  @IsEnum(SkillPosition, { message: '技能位置必须是有效的枚举值' })
  position: SkillPosition;

  @ApiPropertyOptional({ description: '限制数量（可选）', example: 10 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '限制数量必须是数字' })
  @Min(1, { message: '限制数量不能小于1' })
  @Max(100, { message: '限制数量不能大于100' })
  limit?: number;
}

// ==================== 2. 技能学习和升级相关 ====================

/**
 * 学习技能Payload DTO
 * @MessagePattern('skill.learn')
 * 扩展LearnSkillDto，合并BasePayloadDto内容
 */
export class LearnPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiProperty({ description: '技能配置ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '技能配置ID必须是数字' })
  @Min(1, { message: '技能配置ID不能小于1' })
  @Max(999999, { message: '技能配置ID不能大于999999' })
  configId: number;

  @ApiPropertyOptional({ description: '学习方式（可选）', example: 'gold', enum: ['gold', 'item', 'free'] })
  @Expose()
  @IsOptional()
  @IsString({ message: '学习方式必须是字符串' })
  @Length(1, 20, { message: '学习方式长度必须在1-20个字符之间' })
  learnMethod?: string;

  @ApiPropertyOptional({ description: '使用的道具ID（可选）', example: 2001 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '道具ID必须是数字' })
  @Min(1, { message: '道具ID不能小于1' })
  @Max(999999, { message: '道具ID不能大于999999' })
  itemId?: number;

  @ApiPropertyOptional({ description: '获得来源' })
  @Expose()
  @IsOptional()
  @IsString({ message: '获得来源必须是字符串' })
  @Length(1, 50, { message: '获得来源长度必须在1-50个字符之间' })
  obtainSource?: string;

  @ApiPropertyOptional({ description: '获得费用' })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '获得费用必须是数字' })
  @Min(0, { message: '获得费用不能小于0' })
  obtainCost?: number;
}

/**
 * 升级技能Payload DTO
 * @MessagePattern('skill.upgrade')
 * 扩展UpgradeSkillDto，合并BasePayloadDto内容
 */
export class UpgradePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '技能ID', example: 'skill_12345' })
  @Expose()
  @IsString({ message: '技能ID必须是字符串' })
  @Length(1, 50, { message: '技能ID长度必须在1-50个字符之间' })
  skillId: string;

  @ApiPropertyOptional({ description: '目标等级（可选，默认为1）', example: 1 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '目标等级必须是数字' })
  @Min(1, { message: '目标等级不能小于1' })
  @Max(10, { message: '目标等级不能大于10' })
  targetLevel?: number;

  @ApiPropertyOptional({ description: '升级方式（可选）', example: 'gold', enum: ['gold', 'item', 'exp'] })
  @Expose()
  @IsOptional()
  @IsString({ message: '升级方式必须是字符串' })
  @Length(1, 20, { message: '升级方式长度必须在1-20个字符之间' })
  upgradeMethod?: string;

  @ApiPropertyOptional({ description: '使用的道具ID（可选）', example: 2001 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '道具ID必须是数字' })
  @Min(1, { message: '道具ID不能小于1' })
  @Max(999999, { message: '道具ID不能大于999999' })
  itemId?: number;
}

// ==================== 3. 技能激活和管理相关 ====================

/**
 * 激活技能Payload DTO
 * @MessagePattern('skill.activate')
 * 扩展ActivateSkillDto，合并BasePayloadDto内容
 */
export class ActivatePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '技能ID', example: 'skill_12345' })
  @Expose()
  @IsString({ message: '技能ID必须是字符串' })
  @Length(1, 50, { message: '技能ID长度必须在1-50个字符之间' })
  skillId: string;

  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiPropertyOptional({ description: '技能槽位置（可选）', example: 0 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '技能槽位置必须是数字' })
  @Min(0, { message: '技能槽位置不能小于0' })
  @Max(10, { message: '技能槽位置不能大于10' })
  slotPosition?: number;
}

/**
 * 取消激活技能Payload DTO
 * @MessagePattern('skill.deactivate')
 */
export class DeactivatePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '技能ID', example: 'skill_12345' })
  @Expose()
  @IsString({ message: '技能ID必须是字符串' })
  @Length(1, 50, { message: '技能ID长度必须在1-50个字符之间' })
  skillId: string;

  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;
}

// ==================== 4. 技能查询相关 ====================

/**
 * 获取技能列表Payload DTO
 * @MessagePattern('skill.getList')
 * 扩展GetSkillListDto，合并BasePayloadDto内容
 */
export class GetListPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiPropertyOptional({ description: '激活状态（可选）', example: 'active', enum: ['active', 'inactive', 'all'] })
  @Expose()
  @IsOptional()
  @IsString({ message: '激活状态必须是字符串' })
  @Length(1, 20, { message: '激活状态长度必须在1-20个字符之间' })
  activeStatus?: string;

  @ApiPropertyOptional({ description: '是否只显示已装备（可选）', example: false })
  @Expose()
  @IsOptional()
  equippedOnly?: boolean;
}

/**
 * 获取已激活技能Payload DTO
 * @MessagePattern('skill.getActive')
 */
export class GetActivePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;
}

/**
 * 获取技能统计Payload DTO
 * @MessagePattern('skill.getStats')
 */
export class GetStatsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;
}

// ==================== 5. 技能重置和批量操作相关 ====================

/**
 * 重置技能Payload DTO
 * @MessagePattern('skill.reset')
 */
export class ResetPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiProperty({ description: '重置类型', enum: SkillResetType, example: SkillResetType.ALL })
  @Expose()
  @IsEnum(SkillResetType, { message: '重置类型必须是有效的枚举值' })
  resetType: SkillResetType;
}

/**
 * 批量操作技能Payload DTO
 * @MessagePattern('skill.batchOperation')
 */
export class BatchOperationPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiProperty({ description: '操作类型', enum: SkillBatchOperation, example: SkillBatchOperation.ACTIVATE })
  @Expose()
  @IsEnum(SkillBatchOperation, { message: '操作类型必须是有效的枚举值' })
  operation: SkillBatchOperation;

  @ApiProperty({ description: '技能ID列表', type: [String], example: ['skill_001', 'skill_002'] })
  @Expose()
  @IsArray({ message: '技能ID列表必须是数组' })
  @IsString({ each: true, message: '技能ID必须是字符串' })
  @Length(1, 50, { each: true, message: '技能ID长度必须在1-50个字符之间' })
  skillIds: string[];
}

// ==================== 6. 技能使用相关 ====================

/**
 * 使用技能Payload DTO
 * @MessagePattern('skill.use')
 */
export class UsePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '技能ID', example: 'skill_12345' })
  @Expose()
  @IsString({ message: '技能ID必须是字符串' })
  @Length(1, 50, { message: '技能ID长度必须在1-50个字符之间' })
  skillId: string;

  @ApiProperty({ description: '球员ID', example: 'hero_12345' })
  @Expose()
  @IsString({ message: '球员ID必须是字符串' })
  @Length(1, 50, { message: '球员ID长度必须在1-50个字符之间' })
  heroId: string;

  @ApiPropertyOptional({ description: '目标ID（可选）', example: 'hero_67890' })
  @Expose()
  @IsOptional()
  @IsString({ message: '目标ID必须是字符串' })
  @Length(1, 50, { message: '目标ID长度必须在1-50个字符之间' })
  targetId?: string;
}
