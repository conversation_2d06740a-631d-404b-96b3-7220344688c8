import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { CharacterRepository } from '@character/common/repositories/character.repository';
import { XResult, XResultUtils } from '@libs/common/types/result.type';
import { GAME_CONSTANTS } from '@character/common/constants/game.constants';
import { PushEventsHelper } from '@libs/common/push';

/**
 * 体力恢复定时任务服务
 * 
 * 基于old项目的体力恢复机制：
 * 1. 每5分钟恢复1点体力
 * 2. 体力上限100点（可配置）
 * 3. 体力满时停止恢复
 * 4. 推送体力恢复满通知
 */
@Injectable()
export class EnergyRecoveryService {
  private readonly logger = new Logger(EnergyRecoveryService.name);

  constructor(
    private readonly characterRepository: CharacterRepository,
    private readonly pushEventsHelper: PushEventsHelper
  ) {}

  /**
   * 每5分钟执行一次体力恢复
   * 基于old项目的恢复间隔配置
   */
  @Cron('0 */5 * * * *') // 每5分钟执行一次
  async processEnergyRecovery(): Promise<void> {
    this.logger.log('开始处理体力恢复定时任务');
    
    try {
      const result = await this.recoverAllCharactersEnergy();
      if (XResultUtils.isSuccess(result)) {
        this.logger.log(`体力恢复处理完成: ${result.data?.processedCount || 0}个角色`);
      } else {
        this.logger.error(`体力恢复处理失败: ${result.message}`);
      }
    } catch (error) {
      this.logger.error('体力恢复定时任务异常:', error);
    }
  }

  /**
   * 处理所有角色的体力恢复
   * 基于old项目的getEnergyInfo逻辑
   */
  async recoverAllCharactersEnergy(): Promise<XResult<{ processedCount: number; recoveredCount: number }>> {
    // 获取所有体力未满的角色
    const charactersResult = await this.characterRepository.findCharactersWithIncompleteEnergy();
    if (XResultUtils.isFailure(charactersResult)) {
      return XResultUtils.error(`获取体力未满角色列表失败: ${charactersResult.message}`, charactersResult.code);
    }

    const characters = charactersResult.data || [];
    if (characters.length === 0) {
      return XResultUtils.ok({ processedCount: 0, recoveredCount: 0 });
    }

    const currentTime = Date.now();
    const recoveryUpdates = [];
    const notificationPromises = [];

    for (const character of characters) {
      try {
        const recoveryResult = this.calculateEnergyRecovery(character, currentTime);
        
        if (recoveryResult.shouldRecover) {
          recoveryUpdates.push({
            characterId: character.characterId,
            newEnergy: recoveryResult.newEnergy,
            newRecoverTime: recoveryResult.newRecoverTime,
            isEnergyFull: recoveryResult.isEnergyFull,
            recoveredAmount: recoveryResult.recoveredAmount
          });

          // 如果体力恢复满，准备推送通知
          if (recoveryResult.isEnergyFull && !character.energyInfo?.isEnergyFull) {
            notificationPromises.push(
              this.notifyEnergyFull(character.characterId, character.userId)
            );
          }
        }
      } catch (error) {
        this.logger.warn(`计算角色体力恢复失败: ${character.characterId}`, error);
      }
    }

    // 批量更新体力
    let successCount = 0;
    if (recoveryUpdates.length > 0) {
      const batchUpdateResult = await this.batchUpdateEnergyRecovery(recoveryUpdates);
      if (XResultUtils.isSuccess(batchUpdateResult)) {
        successCount = batchUpdateResult.data?.successCount || 0;
      }
    }

    // 发送体力恢复满通知
    if (notificationPromises.length > 0) {
      await Promise.allSettled(notificationPromises);
    }

    return XResultUtils.ok({
      processedCount: characters.length,
      recoveredCount: successCount
    });
  }

  /**
   * 计算单个角色的体力恢复
   * 基于old项目的体力恢复算法
   */
  private calculateEnergyRecovery(character: any, currentTime: number): {
    shouldRecover: boolean;
    newEnergy: number;
    newRecoverTime: number;
    isEnergyFull: boolean;
    recoveredAmount: number;
  } {
    const maxEnergy = GAME_CONSTANTS.CHARACTER.MAX_ENERGY;
    const recoverInterval = GAME_CONSTANTS.CHARACTER.ENERGY_RECOVER_INTERVAL; // 5分钟
    const recoverAmount = GAME_CONSTANTS.CHARACTER.ENERGY_RECOVER_AMOUNT; // 1点

    // 如果体力已满，无需恢复
    if (character.energy >= maxEnergy) {
      return {
        shouldRecover: false,
        newEnergy: character.energy,
        newRecoverTime: character.energyInfo?.energyRecoverTime || currentTime,
        isEnergyFull: true,
        recoveredAmount: 0
      };
    }

    const lastRecoverTime = character.energyInfo?.energyRecoverTime || currentTime;
    const timeDiff = currentTime - lastRecoverTime;

    // 计算可以恢复的次数
    const recoveryTimes = Math.floor(timeDiff / recoverInterval);
    
    if (recoveryTimes <= 0) {
      return {
        shouldRecover: false,
        newEnergy: character.energy,
        newRecoverTime: lastRecoverTime,
        isEnergyFull: false,
        recoveredAmount: 0
      };
    }

    // 计算恢复后的体力值
    const recoveredAmount = recoveryTimes * recoverAmount;
    const newEnergy = Math.min(maxEnergy, character.energy + recoveredAmount);
    const actualRecovered = newEnergy - character.energy;
    
    // 计算新的恢复时间
    const newRecoverTime = lastRecoverTime + (recoveryTimes * recoverInterval);
    const isEnergyFull = newEnergy >= maxEnergy;

    return {
      shouldRecover: actualRecovered > 0,
      newEnergy,
      newRecoverTime: isEnergyFull ? currentTime : newRecoverTime,
      isEnergyFull,
      recoveredAmount: actualRecovered
    };
  }

  /**
   * 批量更新体力恢复
   */
  private async batchUpdateEnergyRecovery(updates: any[]): Promise<XResult<{ successCount: number }>> {
    let successCount = 0;
    
    for (const update of updates) {
      try {
        const updateResult = await this.characterRepository.update(update.characterId, {
          energy: update.newEnergy,
          'energyInfo.energyRecoverTime': update.newRecoverTime,
          'energyInfo.isEnergyFull': update.isEnergyFull ? 0 : 1
        });

        if (XResultUtils.isSuccess(updateResult)) {
          successCount++;
          this.logger.debug(`体力恢复成功: ${update.characterId}, 恢复${update.recoveredAmount}点, 当前${update.newEnergy}点`);
        }
      } catch (error) {
        this.logger.warn(`更新角色体力失败: ${update.characterId}`, error);
      }
    }

    return XResultUtils.ok({ successCount });
  }

  /**
   * 推送体力恢复满通知
   */
  private async notifyEnergyFull(characterId: string, userId: string): Promise<void> {
    try {
      await this.pushEventsHelper.notifyEnergyFull(userId, {
        characterId,
        energy: GAME_CONSTANTS.CHARACTER.MAX_ENERGY
      });
    } catch (error) {
      this.logger.warn(`推送体力恢复满通知失败: ${characterId}`, error);
    }
  }

  /**
   * 手动触发体力恢复（用于测试或特殊情况）
   */
  async manualEnergyRecovery(characterId: string): Promise<XResult<any>> {
    const characterResult = await this.characterRepository.findById(characterId);
    if (XResultUtils.isFailure(characterResult)) {
      return XResultUtils.error(`角色不存在: ${characterId}`, 'CHARACTER_NOT_FOUND');
    }

    const character = characterResult.data;
    const currentTime = Date.now();
    const recoveryResult = this.calculateEnergyRecovery(character, currentTime);

    if (!recoveryResult.shouldRecover) {
      return XResultUtils.ok({
        message: '无需恢复体力',
        currentEnergy: character.energy,
        maxEnergy: GAME_CONSTANTS.CHARACTER.MAX_ENERGY
      });
    }

    const updateResult = await this.characterRepository.update(characterId, {
      energy: recoveryResult.newEnergy,
      'energyInfo.energyRecoverTime': recoveryResult.newRecoverTime,
      'energyInfo.isEnergyFull': recoveryResult.isEnergyFull ? 0 : 1
    });

    if (XResultUtils.isFailure(updateResult)) {
      return XResultUtils.error(`更新体力失败: ${updateResult.message}`, updateResult.code);
    }

    // 推送体力恢复满通知
    if (recoveryResult.isEnergyFull) {
      await this.notifyEnergyFull(characterId, character.userId);
    }

    return XResultUtils.ok({
      message: '体力恢复成功',
      oldEnergy: character.energy,
      newEnergy: recoveryResult.newEnergy,
      recoveredAmount: recoveryResult.recoveredAmount,
      isEnergyFull: recoveryResult.isEnergyFull
    });
  }
}
