请分析 `character` 服务下的子模块核心职责，并基于实际业务耦合性设计聚合层架构。具体模块路径如下：
apps\character\src\modules\
- belief       # 信念系统（如角色天赋、特性系统）
- character    # 角色核心数据管理
- formation    # 阵容编排模块
- hero         # 英雄资产管理系统
- inventory    # 道具与货币库存管理
- tactic       # 战术策略模块

实际业务中存在以下跨模块调用场景：
1. 角色初始化/信息获取：需聚合 character（基础数据）、formation（阵容）、hero（英雄数据）、inventory（装备道具）
2. 奖励发放：可能调用 inventory（发放道具）、character（发放货币）、hero（发放英雄）
3. 战力计算：依赖 hero（英雄属性）、formation（阵容加成）、inventory（装备加成）、tactic（战术增益）
4. 布阵操作：涉及 formation（阵容结构）、hero（英雄状态）、inventory（装备配置）
5. 其他潜在业务：如信念系统(belief)对英雄、阵容、战术的增强效果
6. 通过模块之间的依赖深入分析其他潜在业务

请遵循以下架构原则：
1. 子模块专注核心职责：各模块只处理自身核心领域的基本业务逻辑和数据持久化
2. 新建聚合模块：创建 `orchestrator` 模块负责：
    - 编排跨模块业务流
    - 提供复合业务服务（如获取完整角色信息、综合战力计算）
    - 处理模块间依赖协调
3. 依赖方向：子模块之间尽量避免直接调用，客户端直接访问聚合层接口达到业务效果

请基于现有模块的真实业务逻辑进行完整无遗漏的实施，严格遵循以下要求：
保持业务逻辑一致性：所有实现必须基于当前系统的真实业务规则和流程，不得虚构或假设逻辑。如存在未明确描述的业务细节，需先行确认或沿用既有模式。
最大化代码复用：优先直接复用已存在的业务代码（包括函数、类、组件、工具方法等），仅在没有现有实现或现有实现无法满足需求时才编写新代码。复用时应确保上下文兼容性，避免引入冗余或冲突。
完整覆盖需求：确保实现覆盖所有显性和隐性需求，包括边界条件、异常处理、日志记录及性能要求，不得遗漏任何已有规范或约束。
输出要求：提供清晰、可执行的实现方案或代码，并标注复用部分与新开发部分的来源及依据。如涉及修改现有代码，需说明变更原因及影响范围。

