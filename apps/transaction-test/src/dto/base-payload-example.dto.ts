/**
 * BasePayloadDto使用示例
 * 
 * 展示如何使用BasePayloadDto基类创建具体的payload DTO
 */

import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, ValidateNested, Min, Max, Length, IsBoolean } from 'class-validator';
import { Expose, Type } from 'class-transformer';
import { BasePayloadDto, IdDtoPayloadDto, DoubleIdDtoPayloadDto, DtoPayloadDto } from '../../../../libs/common/src/dto/base-payload.dto';

// ==================== 业务DTO定义 ====================

/**
 * 角色更新DTO
 */
export class UpdateCharacterDto {
  @ApiProperty({ description: '角色名称', example: 'UpdatedHero', required: false })
  @Expose()
  @IsOptional()
  @IsString({ message: '角色名称必须是字符串' })
  @Length(2, 20, { message: '角色名称长度必须在2-20个字符之间' })
  name?: string;

  @ApiProperty({ description: '角色等级', example: 30, required: false })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '角色等级必须是数字' })
  @Min(1, { message: '角色等级不能小于1' })
  @Max(100, { message: '角色等级不能大于100' })
  level?: number;

  @ApiProperty({ description: '角色经验值', example: 5000, required: false })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '经验值必须是数字' })
  @Min(0, { message: '经验值不能为负数' })
  experience?: number;
}

/**
 * 角色创建DTO
 */
export class CreateCharacterDto {
  @ApiProperty({ description: '角色名称', example: 'NewHero' })
  @Expose()
  @IsString({ message: '角色名称必须是字符串' })
  @Length(2, 20, { message: '角色名称长度必须在2-20个字符之间' })
  name: string;

  @ApiProperty({ description: '角色职业', example: 'warrior' })
  @Expose()
  @IsString({ message: '角色职业必须是字符串' })
  @Length(2, 20, { message: '角色职业长度必须在2-20个字符之间' })
  class: string;

  @ApiProperty({ description: '初始属性点', example: 10 })
  @Expose()
  @IsNumber({}, { message: '属性点必须是数字' })
  @Min(1, { message: '属性点不能小于1' })
  @Max(50, { message: '属性点不能大于50' })
  attributePoints: number;
}

/**
 * 装备升级DTO
 */
export class UpgradeEquipmentDto {
  @ApiProperty({ description: '目标等级', example: 5 })
  @Expose()
  @IsNumber({}, { message: '目标等级必须是数字' })
  @Min(1, { message: '目标等级不能小于1' })
  @Max(20, { message: '目标等级不能大于20' })
  targetLevel: number;

  @ApiProperty({ description: '是否使用保护石', example: true, required: false })
  @Expose()
  @IsOptional()
  useProtectionStone?: boolean;
}

// ==================== Payload DTO定义 ====================

/**
 * 角色更新Payload DTO
 * 使用IdDtoPayloadDto基类
 * 
 * payload结构: { characterId: string, updateDto: UpdateCharacterDto, serverId?: string, injectedContext?: InjectedContext }
 */
export class UpdateCharacterPayloadDto extends IdDtoPayloadDto<UpdateCharacterDto> {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  id: string; // 重写基类的抽象字段，提供具体的验证

  @ApiProperty({ description: '角色更新数据', type: UpdateCharacterDto })
  @Expose()
  @ValidateNested({ message: '角色更新数据格式不正确' })
  @Type(() => UpdateCharacterDto)
  dto: UpdateCharacterDto; // 重写基类的抽象字段，指定具体类型
  
  // 为了更清晰，我们给dto字段一个别名
  get updateDto(): UpdateCharacterDto {
    return this.dto;
  }
}

/**
 * 角色创建Payload DTO
 * 使用DtoPayloadDto基类
 * 
 * payload结构: { createDto: CreateCharacterDto, serverId?: string, injectedContext?: InjectedContext }
 */
export class CreateCharacterPayloadDto extends DtoPayloadDto<CreateCharacterDto> {
  @ApiProperty({ description: '角色创建数据', type: CreateCharacterDto })
  @Expose()
  @ValidateNested({ message: '角色创建数据格式不正确' })
  @Type(() => CreateCharacterDto)
  dto: CreateCharacterDto; // 重写基类的抽象字段，指定具体类型
  
  // 为了更清晰，我们给dto字段一个别名
  get createDto(): CreateCharacterDto {
    return this.dto;
  }
}

/**
 * 装备升级Payload DTO
 * 使用DoubleIdDtoPayloadDto基类
 * 
 * payload结构: { characterId: string, equipmentId: string, upgradeDto: UpgradeEquipmentDto, serverId?: string, injectedContext?: InjectedContext }
 */
export class UpgradeEquipmentPayloadDto extends DoubleIdDtoPayloadDto<UpgradeEquipmentDto> {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  @Length(1, 50, { message: '角色ID长度必须在1-50个字符之间' })
  id1: string; // 重写基类的抽象字段

  @ApiProperty({ description: '装备ID', example: 'equip_67890' })
  @Expose()
  @IsString({ message: '装备ID必须是字符串' })
  @Length(1, 50, { message: '装备ID长度必须在1-50个字符之间' })
  id2: string; // 重写基类的抽象字段

  // 为了更清晰，我们给id2字段一个别名
  get equipmentId(): string {
    return this.id2;
  }

  @ApiProperty({ description: '装备升级数据', type: UpgradeEquipmentDto })
  @Expose()
  @ValidateNested({ message: '装备升级数据格式不正确' })
  @Type(() => UpgradeEquipmentDto)
  dto: UpgradeEquipmentDto; // 重写基类的抽象字段，指定具体类型
  
  // 为了更清晰，我们给dto字段一个别名
  get upgradeDto(): UpgradeEquipmentDto {
    return this.dto;
  }
}

/**
 * 自定义字段Payload DTO
 * 直接继承BasePayloadDto，添加自定义字段
 * 
 * payload结构: { gameId: string, roomId: string, maxPlayers: number, isPrivate: boolean, serverId?: string, injectedContext?: InjectedContext }
 */
export class CustomFieldsPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '游戏ID', example: 'game_123' })
  @Expose()
  @IsString({ message: '游戏ID必须是字符串' })
  @Length(1, 50, { message: '游戏ID长度必须在1-50个字符之间' })
  gameId: string;

  @ApiProperty({ description: '房间ID', example: 'room_456' })
  @Expose()
  @IsString({ message: '房间ID必须是字符串' })
  @Length(1, 50, { message: '房间ID长度必须在1-50个字符之间' })
  roomId: string;

  @ApiProperty({ description: '最大玩家数', example: 8 })
  @Expose()
  @IsNumber({}, { message: '最大玩家数必须是数字' })
  @Min(1, { message: '最大玩家数不能小于1' })
  @Max(100, { message: '最大玩家数不能大于100' })
  maxPlayers: number;

  @ApiProperty({ description: '是否私有房间', example: false })
  @Expose()
  @IsBoolean({ message: '是否私有房间必须是布尔值' })
  isPrivate: boolean;
}
