/**
 * 完善的参数验证测试脚本 - TCP版本
 * 
 * 🎯 测试目标：
 * - 测试transaction-test服务的@MessagePattern接口
 * - 验证@UsePipes装饰器的参数验证功能
 * - 测试不同验证模式的行为差异
 * - 验证复杂嵌套对象的验证逻辑
 * 
 * 📋 测试场景：
 * 1. validation.test.basic - 基础参数验证测试
 * 2. validation.test.strict - 严格参数验证测试
 * 3. validation.test.lenient - 宽松参数验证测试
 * 4. validation.test.none - 无验证测试
 * 5. 各种错误场景测试
 */

const net = require('net');

/**
 * TCP微服务客户端
 * 基于NestJS TCP传输协议实现
 */
class TCPMicroserviceClient {
  constructor(host = '127.0.0.1', port = 3011) {
    this.host = host;
    this.port = port;
    this.socket = null;
    this.connected = false;
    this.messageId = 0;
    this.pendingRequests = new Map();
  }

  async connect() {
    return new Promise((resolve, reject) => {
      this.socket = new net.Socket();
      
      this.socket.connect(this.port, this.host, () => {
        this.connected = true;
        console.log(`✅ 连接到微服务: ${this.host}:${this.port}`);
        this.setupDataHandler();
        resolve();
      });

      this.socket.on('error', (error) => {
        console.error(`❌ 连接失败: ${error.message}`);
        this.connected = false;
        reject(error);
      });

      this.socket.on('close', () => {
        console.log('🔌 TCP连接已关闭');
        this.connected = false;
      });
    });
  }

  setupDataHandler() {
    let buffer = Buffer.alloc(0);

    this.socket.on('data', (chunk) => {
      buffer = Buffer.concat([buffer, chunk]);

      while (buffer.length >= 4) {
        const messageLength = buffer.readUInt32BE(0);
        
        if (buffer.length >= 4 + messageLength) {
          const messageData = buffer.slice(4, 4 + messageLength);
          buffer = buffer.slice(4 + messageLength);
          
          try {
            const response = JSON.parse(messageData.toString());
            this.handleResponse(response);
          } catch (error) {
            console.error('解析响应失败:', error.message);
          }
        } else {
          break;
        }
      }
    });
  }

  handleResponse(response) {
    const { id } = response;
    if (id && this.pendingRequests.has(id)) {
      const { resolve, reject } = this.pendingRequests.get(id);
      this.pendingRequests.delete(id);
      
      if (response.err) {
        reject(new Error(response.err));
      } else {
        resolve(response.response || response);
      }
    }
  }

  async call(pattern, data, timeout = 10000) {
    if (!this.connected) {
      throw new Error('客户端未连接');
    }

    return new Promise((resolve, reject) => {
      const id = `msg_${++this.messageId}_${Date.now()}`;
      
      const message = {
        pattern,
        data,
        id
      };

      const messageStr = JSON.stringify(message);
      const messageBuffer = Buffer.from(messageStr);
      const lengthBuffer = Buffer.allocUnsafe(4);
      lengthBuffer.writeUInt32BE(messageBuffer.length, 0);
      
      const fullMessage = Buffer.concat([lengthBuffer, messageBuffer]);

      // 存储待处理的请求
      this.pendingRequests.set(id, { resolve, reject });

      // 设置超时
      const timeoutId = setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error(`请求超时 (${timeout}ms)`));
        }
      }, timeout);

      // 发送消息
      this.socket.write(fullMessage, (error) => {
        if (error) {
          clearTimeout(timeoutId);
          this.pendingRequests.delete(id);
          reject(error);
        }
      });
    });
  }

  async disconnect() {
    if (this.socket) {
      this.socket.end();
      this.connected = false;
      console.log('🔌 断开微服务连接');
    }
  }
}

class ValidationTester {
  constructor() {
    this.client = new TCPMicroserviceClient('127.0.0.1', 3011);
    
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0,
      details: []
    };
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🧪 开始参数验证测试套件');
    console.log('=' .repeat(60));

    try {
      await this.client.connect();
      console.log('✅ 微服务客户端连接成功');

      // 运行各种测试场景
      await this.testBasicValidation();
      await this.testStrictValidation();
      await this.testLenientValidation();
      await this.testNoValidation();
      await this.testValidationErrors();

      // 输出测试结果
      this.printTestSummary();

    } catch (error) {
      console.error('❌ 测试套件执行失败:', error.message);
    } finally {
      await this.client.disconnect();
    }
  }

  /**
   * 测试基础参数验证
   */
  async testBasicValidation() {
    console.log('\n📋 测试1: 基础参数验证（标准模式）');
    console.log('-'.repeat(40));

    const validPayload = {
      username: 'testUser123',
      email: '<EMAIL>',
      age: 25,
      balance: 1000.50,
      level: 'gold',
      tags: ['vip', 'active'],
      preferences: {
        language: 'zh-CN',
        timezone: 'Asia/Shanghai',
        notifications: true
      },
      notes: '这是一个测试用户',
      isActive: true
    };

    await this.runTest(
      'basic-validation-success',
      'validation.test.basic',
      validPayload,
      (result) => {
        return result && 
               result.data && 
               result.data.validatedData &&
               result.data.validatedData.username === validPayload.username &&
               result.data.validatedData.preferences.language === validPayload.preferences.language;
      }
    );
  }

  /**
   * 测试严格参数验证
   */
  async testStrictValidation() {
    console.log('\n📋 测试2: 严格参数验证（严格模式）');
    console.log('-'.repeat(40));

    const validPayload = {
      username: 'strictUser',
      email: '<EMAIL>',
      age: 30,
      balance: 2000.00,
      level: 'platinum',
      tags: ['premium'],
      preferences: {
        language: 'en-US',
        timezone: 'America/New_York',
        notifications: false
      }
    };

    await this.runTest(
      'strict-validation-success',
      'validation.test.strict',
      validPayload,
      (result) => {
        return result && 
               result.data && 
               result.data.validationMode === 'strict' &&
               result.data.validatedData.username === validPayload.username;
      }
    );
  }

  /**
   * 测试宽松参数验证
   */
  async testLenientValidation() {
    console.log('\n📋 测试3: 宽松参数验证（兼容模式）');
    console.log('-'.repeat(40));

    const payloadWithExtraFields = {
      username: 'lenientUser',
      email: '<EMAIL>',
      age: 28,
      balance: 1500.75,
      level: 'silver',
      tags: ['normal'],
      preferences: {
        language: 'ja-JP',
        timezone: 'Asia/Tokyo',
        notifications: true
      },
      // 额外字段（在严格模式下会被拒绝）
      extraField1: 'should be allowed',
      extraField2: 12345,
      extraNested: {
        someProperty: 'value'
      }
    };

    await this.runTest(
      'lenient-validation-success',
      'validation.test.lenient',
      payloadWithExtraFields,
      (result) => {
        return result && 
               result.data && 
               result.data.validationMode === 'lenient' &&
               result.data.receivedData.extraField1 === 'should be allowed';
      }
    );
  }

  /**
   * 测试无验证模式
   */
  async testNoValidation() {
    console.log('\n📋 测试4: 无验证模式（对比基准）');
    console.log('-'.repeat(40));

    const arbitraryPayload = {
      anything: 'goes',
      numbers: [1, 2, 3],
      nested: {
        deeply: {
          nested: 'value'
        }
      },
      nullValue: null,
      undefinedValue: undefined
    };

    await this.runTest(
      'no-validation-success',
      'validation.test.none',
      arbitraryPayload,
      (result) => {
        return result && 
               result.data && 
               result.data.rawData.anything === 'goes';
      }
    );
  }

  /**
   * 测试各种验证错误场景
   */
  async testValidationErrors() {
    console.log('\n📋 测试5: 验证错误场景');
    console.log('-'.repeat(40));

    // 测试场景1：缺少必需字段
    await this.runErrorTest(
      'missing-required-fields',
      'validation.test.basic',
      {
        username: 'test',
        // 缺少 email, age, balance, level, tags, preferences
      },
      'VALIDATION_FAILED'
    );

    // 测试场景2：字段类型错误
    await this.runErrorTest(
      'invalid-field-types',
      'validation.test.basic',
      {
        username: 123, // 应该是字符串
        email: 'invalid-email', // 无效邮箱格式
        age: 'twenty-five', // 应该是数字
        balance: -100, // 不能为负数
        level: 'invalid-level', // 无效枚举值
        tags: 'not-an-array', // 应该是数组
        preferences: 'not-an-object' // 应该是对象
      },
      'VALIDATION_FAILED'
    );
  }

  /**
   * 运行单个测试
   */
  async runTest(testName, pattern, payload, validator) {
    this.testResults.total++;
    
    try {
      console.log(`🔍 执行测试: ${testName}`);
      console.log(`📤 发送数据:`, JSON.stringify(payload, null, 2));
      
      const result = await this.client.call(pattern, payload);
      
      console.log(`📥 接收结果:`, JSON.stringify(result, null, 2));
      
      if (validator(result)) {
        console.log(`✅ 测试通过: ${testName}`);
        this.testResults.passed++;
        this.testResults.details.push({ name: testName, status: 'PASSED', result });
      } else {
        console.log(`❌ 测试失败: ${testName} - 验证器返回false`);
        this.testResults.failed++;
        this.testResults.details.push({ name: testName, status: 'FAILED', result, reason: 'Validator returned false' });
      }
      
    } catch (error) {
      console.log(`❌ 测试异常: ${testName} - ${error.message}`);
      this.testResults.failed++;
      this.testResults.details.push({ name: testName, status: 'ERROR', error: error.message });
    }
  }

  /**
   * 运行错误测试（期望失败）
   */
  async runErrorTest(testName, pattern, payload, expectedErrorCode) {
    this.testResults.total++;
    
    try {
      console.log(`🔍 执行错误测试: ${testName}`);
      console.log(`📤 发送无效数据:`, JSON.stringify(payload, null, 2));
      
      const result = await this.client.call(pattern, payload);
      
      // 如果没有抛出异常，说明测试失败了
      console.log(`❌ 错误测试失败: ${testName} - 期望验证失败但却成功了`);
      console.log(`📥 意外的成功结果:`, JSON.stringify(result, null, 2));
      this.testResults.failed++;
      this.testResults.details.push({ 
        name: testName, 
        status: 'FAILED', 
        reason: 'Expected validation error but got success',
        result 
      });
      
    } catch (error) {
      console.log(`📥 捕获到期望的错误:`, error.message);
      
      if (error.message.includes(expectedErrorCode)) {
        console.log(`✅ 错误测试通过: ${testName}`);
        this.testResults.passed++;
        this.testResults.details.push({ name: testName, status: 'PASSED', error: error.message });
      } else {
        console.log(`❌ 错误测试失败: ${testName} - 错误代码不匹配`);
        console.log(`期望: ${expectedErrorCode}, 实际: ${error.message}`);
        this.testResults.failed++;
        this.testResults.details.push({ 
          name: testName, 
          status: 'FAILED', 
          reason: 'Error code mismatch',
          expected: expectedErrorCode,
          actual: error.message 
        });
      }
    }
  }

  /**
   * 打印测试结果摘要
   */
  printTestSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 测试结果摘要');
    console.log('='.repeat(60));
    console.log(`总测试数: ${this.testResults.total}`);
    console.log(`通过: ${this.testResults.passed} ✅`);
    console.log(`失败: ${this.testResults.failed} ❌`);
    console.log(`成功率: ${((this.testResults.passed / this.testResults.total) * 100).toFixed(1)}%`);
    
    if (this.testResults.failed > 0) {
      console.log('\n❌ 失败的测试详情:');
      this.testResults.details
        .filter(detail => detail.status !== 'PASSED')
        .forEach(detail => {
          console.log(`  - ${detail.name}: ${detail.reason || detail.error}`);
        });
    }
    
    console.log('\n🎉 参数验证测试套件执行完成！');
  }
}

// 主执行逻辑
async function main() {
  const args = process.argv.slice(2);
  const testType = args[0];
  
  const tester = new ValidationTester();
  
  if (testType) {
    console.log(`🎯 运行特定测试: ${testType}`);
    // 这里可以扩展支持运行特定测试
    switch (testType) {
      case 'basic':
        await tester.client.connect();
        await tester.testBasicValidation();
        await tester.client.disconnect();
        break;
      case 'strict':
        await tester.client.connect();
        await tester.testStrictValidation();
        await tester.client.disconnect();
        break;
      case 'errors':
        await tester.client.connect();
        await tester.testValidationErrors();
        await tester.client.disconnect();
        break;
      default:
        console.log('❌ 未知的测试类型，运行完整测试套件');
        await tester.runAllTests();
    }
  } else {
    await tester.runAllTests();
  }
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

// 启动测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { ValidationTester, TCPMicroserviceClient };
