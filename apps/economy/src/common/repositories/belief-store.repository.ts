import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { XResult, XResultUtils } from '@libs/common/types/result.type';
import { RepositoryResultWrapper } from '@libs/common/repository';
import { BeliefStore, BeliefStoreDocument, ItemPricing, PurchaseRecord } from '../schemas/belief-store.schema';

/**
 * 信仰商店数据访问层
 * 基于old项目的信仰商店数据管理
 * 
 * 🎯 核心功能：
 * - 商店状态管理
 * - 商品定价管理
 * - 购买记录管理
 * - 统计数据查询
 * - 阶段切换管理
 * 
 * 🔧 数据操作：
 * - 商店信息CRUD
 * - 定价记录管理
 * - 购买历史查询
 * - 统计数据聚合
 * - 缓存数据同步
 * 
 * old项目对应：
 * - beliefService.js中的数据操作
 * - FaithStore数据模型
 * - 商店状态持久化
 */
@Injectable()
export class BeliefStoreRepository extends BaseRepository<BeliefStoreDocument> {
  constructor(
    @InjectModel(BeliefStore.name) 
    private readonly beliefStoreModel: Model<BeliefStoreDocument>,
  ) {
    super(beliefStoreModel, 'BeliefStoreRepository');
  }

  /**
   * 获取当前商店状态
   */
  async getCurrentStore(): Promise<XResult<BeliefStoreDocument | null>> {
    return RepositoryResultWrapper.wrapNullable(async () => {
      return await this.beliefStoreModel
        .findOne()
        .sort({ phase: -1 })
        .exec();
    });
  }

  /**
   * 更新商品定价
   */
  async updateItemPricing(itemId: number, pricingList: ItemPricing[]): Promise<XResult<BeliefStoreDocument | null>> {
    return RepositoryResultWrapper.wrapNullable(async () => {
      const store = await this.beliefStoreModel.findOne().sort({ phase: -1 }).exec();
      if (!store) {
        throw new Error('商店不存在');
      }

      // 更新商品定价
      let item = store.itemList.find(item => item.itemId === itemId);
      if (!item) {
        item = { itemId, num: 0, refreshTime: 0, itemList: [] };
        store.itemList.push(item);
      }
      item.itemList = pricingList;
      return await store.save();
    });
  }

  /**
   * 添加定价记录
   */
  async addPricingRecord(itemId: number, beliefId: number, priceList: ItemPricing[]): Promise<XResult<void>> {
    return RepositoryResultWrapper.wrap(async () => {
      const store = await this.beliefStoreModel.findOne().sort({ phase: -1 }).exec();
      if (!store) {
        throw new Error('商店不存在');
      }

      // 添加定价记录
      store.pricingInfo.push({
        itemId,
        beliefId,
        priceList,
        time: Date.now()
      });
      await store.save();
    });
  }

  /**
   * 添加购买记录
   */
  async addPurchaseRecord(
    playerId: string,
    beliefId: number,
    itemId: number,
    quantity: number,
    beliefNum: number
  ): Promise<XResult<void>> {
    return RepositoryResultWrapper.wrap(async () => {
      const store = await this.beliefStoreModel.findOne().sort({ phase: -1 }).exec();
      if (!store) {
        throw new Error('商店不存在');
      }

      // 添加购买记录
      store.buyInfo.push({
        playerId,
        beliefId,
        itemId,
        num: quantity,
        beliefNum,
        time: Date.now()
      });

      // 更新玩家购买统计
      if (!store.buyUidMap.has(playerId)) {
        store.buyUidMap.set(playerId, []);
      }
      const playerPurchases = store.buyUidMap.get(playerId)!;
      const existingPurchase = playerPurchases.find(p => p.itemId === itemId);
      if (existingPurchase) {
        existingPurchase.num += quantity;
      } else {
        playerPurchases.push({ itemId, num: quantity });
      }
      store.buyNum = store.buyUidMap.size;
      await store.save();
    });
  }

  /**
   * 更新商品购买统计
   */
  async updateItemPurchaseCount(itemId: number, quantity: number): Promise<XResult<BeliefStoreDocument | null>> {
    return RepositoryResultWrapper.wrapNullable(async () => {
      return await this.beliefStoreModel.findOneAndUpdate(
        { 'itemList.itemId': itemId },
        { 
          $inc: { 'itemList.$.num': quantity },
          $set: { lastUpdateTime: Date.now() }
        },
        { new: true }
      ).exec();
    });
  }

  /**
   * 切换商店阶段
   */
  async switchPhase(targetPhase?: 1 | 2, duration?: number): Promise<XResult<BeliefStoreDocument | null>> {
    return RepositoryResultWrapper.wrapNullable(async () => {
      const store = await this.beliefStoreModel.findOne().sort({ phase: -1 }).exec();
      if (!store) {
        throw new Error('商店不存在');
      }

      // 切换阶段
      if (targetPhase) {
        store.shopPeriod = targetPhase;
      } else {
        store.shopPeriod = store.shopPeriod === 1 ? 2 : 1;
      }
      store.phaseStartTime = Date.now();
      if (store.shopPeriod === 1) {
        store.phase += 1;
      }

      if (duration) {
        store.phaseDuration = duration;
      }

      return await store.save();
    });
  }

  /**
   * 获取玩家购买历史
   */
  async getPlayerPurchaseHistory(
    playerId: string,
    page: number = 1,
    limit: number = 20,
    startTime?: number,
    endTime?: number
  ): Promise<XResult<{
    records: PurchaseRecord[];
    total: number;
    page: number;
    totalPages: number;
  }>> {
    return RepositoryResultWrapper.wrap(async () => {
      const matchConditions: any = { 'buyInfo.playerId': playerId };
      
      if (startTime || endTime) {
        matchConditions['buyInfo.time'] = {};
        if (startTime) matchConditions['buyInfo.time'].$gte = startTime;
        if (endTime) matchConditions['buyInfo.time'].$lte = endTime;
      }

      const pipeline: any[] = [
        { $match: matchConditions },
        { $unwind: '$buyInfo' },
        { $match: { 'buyInfo.playerId': playerId } },
        { $sort: { 'buyInfo.time': -1 } },
        {
          $facet: {
            records: [
              { $skip: (page - 1) * limit },
              { $limit: limit },
              { $replaceRoot: { newRoot: '$buyInfo' } }
            ],
            totalCount: [
              { $count: 'count' }
            ]
          }
        }
      ];

      const result = await this.beliefStoreModel.aggregate(pipeline).exec();
      const data = result[0];
      const records = data.records || [];
      const total = data.totalCount[0]?.count || 0;

      return {
        records,
        total,
        page,
        totalPages: Math.ceil(total / limit)
      };
    });
  }

  /**
   * 获取商店统计信息
   */
  async getStoreStats(): Promise<XResult<{
    totalSales: number;
    totalRevenue: number;
    topItems: Array<{ itemId: number; quantity: number; revenue: number }>;
    buyerCount: number;
    averageOrderValue: number;
  }>> {
    return RepositoryResultWrapper.wrap(async () => {
      const pipeline = [
        { $unwind: '$buyInfo' },
        {
          $group: {
            _id: null,
            totalSales: { $sum: '$buyInfo.num' },
            totalRevenue: { $sum: '$buyInfo.beliefNum' },
            uniqueBuyers: { $addToSet: '$buyInfo.playerId' },
            itemStats: {
              $push: {
                itemId: '$buyInfo.itemId',
                quantity: '$buyInfo.num',
                revenue: '$buyInfo.beliefNum'
              }
            }
          }
        },
        {
          $project: {
            totalSales: 1,
            totalRevenue: 1,
            buyerCount: { $size: '$uniqueBuyers' },
            averageOrderValue: {
              $cond: {
                if: { $gt: ['$totalSales', 0] },
                then: { $divide: ['$totalRevenue', '$totalSales'] },
                else: 0
              }
            },
            itemStats: 1
          }
        }
      ];

      const result = await this.beliefStoreModel.aggregate(pipeline).exec();
      const stats = result[0] || {
        totalSales: 0,
        totalRevenue: 0,
        buyerCount: 0,
        averageOrderValue: 0,
        itemStats: []
      };

      // 计算热门商品排行
      const itemStatsMap = new Map();
      stats.itemStats.forEach((item: any) => {
        if (!itemStatsMap.has(item.itemId)) {
          itemStatsMap.set(item.itemId, { itemId: item.itemId, quantity: 0, revenue: 0 });
        }
        const existing = itemStatsMap.get(item.itemId);
        existing.quantity += item.quantity;
        existing.revenue += item.revenue;
      });

      const topItems = Array.from(itemStatsMap.values())
        .sort((a, b) => b.quantity - a.quantity)
        .slice(0, 10);

      return {
        totalSales: stats.totalSales,
        totalRevenue: stats.totalRevenue,
        topItems,
        buyerCount: stats.buyerCount,
        averageOrderValue: Math.round(stats.averageOrderValue * 100) / 100
      };
    });
  }

  /**
   * 获取定价历史
   */
  async getPricingHistory(
    itemId?: number,
    beliefId?: number,
    limit: number = 50
  ): Promise<XResult<any[]>> {
    return RepositoryResultWrapper.wrap(async () => {
      const matchConditions: any = {};
      if (itemId) matchConditions['pricingInfo.itemId'] = itemId;
      if (beliefId) matchConditions['pricingInfo.beliefId'] = beliefId;

      const pipeline: any[] = [
        { $unwind: '$pricingInfo' },
        { $match: matchConditions },
        { $sort: { 'pricingInfo.time': -1 } },
        { $limit: limit },
        { $replaceRoot: { newRoot: '$pricingInfo' } }
      ];

      return await this.beliefStoreModel.aggregate(pipeline).exec();
    });
  }

  /**
   * 清理过期数据
   */
  async cleanExpiredData(daysToKeep: number = 30): Promise<XResult<number>> {
    return RepositoryResultWrapper.wrap(async () => {
      const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);
      
      const result = await this.beliefStoreModel.updateMany(
        {},
        {
          $pull: {
            buyInfo: { time: { $lt: cutoffTime } },
            pricingInfo: { time: { $lt: cutoffTime } }
          }
        }
      ).exec();

      return result.modifiedCount;
    });
  }

  /**
   * 重置商店数据（新期数开始）
   */
  async resetStoreForNewPhase(): Promise<XResult<BeliefStoreDocument | null>> {
    return RepositoryResultWrapper.wrapNullable(async () => {
      const store = await this.beliefStoreModel.findOne().sort({ phase: -1 }).exec();
      if (!store) {
        throw new Error('商店不存在');
      }

      // 重置商店数据
      store.phase += 1;
      store.shopPeriod = 1; // 重新开始定价阶段
      store.itemList = []; // 清空商品列表
      store.buyUidMap.clear(); // 清空购买统计
      store.buyNum = 0;
      store.phaseStartTime = Date.now();
      store.lastUpdateTime = Date.now();

      return await store.save();
    });
  }

  /**
   * 获取商品定价信息
   */
  async getItemPricing(itemId: number): Promise<XResult<ItemPricing[]>> {
    return RepositoryResultWrapper.wrap(async () => {
      const store = await this.beliefStoreModel.findOne().sort({ phase: -1 }).exec();
      if (!store) {
        throw new Error('商店不存在');
      }

      const item = store.itemList.find(item => item.itemId === itemId);
      return item ? item.itemList : [];
    });
  }

  /**
   * 检查玩家购买限制
   */
  async checkPlayerPurchaseLimit(playerId: string, itemId: number): Promise<XResult<{
    purchased: number;
    canPurchase: boolean;
    limit: number;
  }>> {
    return RepositoryResultWrapper.wrap(async () => {
      const store = await this.beliefStoreModel.findOne().sort({ phase: -1 }).exec();
      if (!store) {
        throw new Error('商店不存在');
      }

      const playerPurchases = store.buyUidMap.get(playerId) || [];
      const itemPurchase = playerPurchases.find(p => p.itemId === itemId);
      const purchased = itemPurchase ? itemPurchase.num : 0;

      // TODO: 从配置中获取限购数量
      const limit = 10; // 临时硬编码
      const canPurchase = purchased < limit;

      return {
        purchased,
        canPurchase,
        limit
      };
    });
  }
}
