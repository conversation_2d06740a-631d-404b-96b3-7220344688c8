import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

// 商品定价信息子文档
@Schema({ _id: false })
export class ItemPricing {
  @Prop({ required: true })
  selfId: number; // 设置定价的信仰ID

  @Prop({ required: true })
  beliefId: number; // 目标信仰ID

  @Prop({ required: true, enum: [50, 200, 300] })
  price: number; // 价格档位：50%、200%、300%
}

// 商品信息子文档
@Schema({ _id: false })
export class StoreItem {
  @Prop({ required: true })
  itemId: number; // 商品ID

  @Prop({ default: 0 })
  num: number; // 全服购买数量

  @Prop({ default: 0 })
  refreshTime: number; // 刷新时间

  @Prop({ type: [ItemPricing], default: [] })
  itemList: ItemPricing[]; // 定价列表
}

// 定价记录子文档
@Schema({ _id: false })
export class PricingRecord {
  @Prop({ required: true })
  itemId: number; // 商品ID

  @Prop({ required: true })
  beliefId: number; // 设置定价的信仰ID

  @Prop({ type: [ItemPricing], required: true })
  priceList: ItemPricing[]; // 定价列表

  @Prop({ default: () => Date.now() })
  time: number; // 定价时间
}

// 购买记录子文档
@Schema({ _id: false })
export class PurchaseRecord {
  @Prop({ required: true })
  playerId: string; // 玩家ID

  @Prop({ required: true })
  beliefId: number; // 玩家信仰ID

  @Prop({ required: true })
  itemId: number; // 商品ID

  @Prop({ required: true })
  num: number; // 购买数量

  @Prop({ required: true })
  beliefNum: number; // 消耗信仰积分

  @Prop({ default: () => Date.now() })
  time: number; // 购买时间
}

// 玩家购买统计子文档
@Schema({ _id: false })
export class PlayerPurchase {
  @Prop({ required: true })
  itemId: number; // 商品ID

  @Prop({ default: 0 })
  num: number; // 购买数量
}

@Schema({ collection: 'belief_stores', timestamps: true })
export class BeliefStore extends Document {
  @Prop({ default: 1, enum: [1, 2] })
  shopPeriod: number; // 商店状态：1-定价阶段，2-开售阶段

  @Prop({ default: 0 })
  phase: number; // 第几期

  @Prop({ type: [StoreItem], default: [] })
  itemList: StoreItem[]; // 商品列表

  @Prop({ type: [PricingRecord], default: [] })
  pricingInfo: PricingRecord[]; // 定价记录

  @Prop({ type: [PurchaseRecord], default: [] })
  buyInfo: PurchaseRecord[]; // 购买记录

  @Prop({ type: Map, of: [PlayerPurchase], default: new Map() })
  buyUidMap: Map<string, PlayerPurchase[]>; // 玩家购买统计

  @Prop({ default: 0 })
  buyNum: number; // 购买人数

  @Prop({ default: () => Date.now() })
  lastUpdateTime: number; // 最后更新时间

  @Prop({ default: () => Date.now() })
  phaseStartTime: number; // 当前阶段开始时间

  @Prop({ default: 0 })
  phaseDuration: number; // 阶段持续时间（毫秒）
}

export type BeliefStoreDocument = BeliefStore & Document;
export const BeliefStoreSchema = SchemaFactory.createForClass(BeliefStore);

// 创建索引
BeliefStoreSchema.index({ phase: 1 });
BeliefStoreSchema.index({ shopPeriod: 1 });
BeliefStoreSchema.index({ 'buyInfo.playerId': 1 });
BeliefStoreSchema.index({ 'buyInfo.beliefId': 1 });
BeliefStoreSchema.index({ 'buyInfo.time': -1 });

// 添加实例方法
BeliefStoreSchema.methods.addPricingRecord = function(itemId: number, beliefId: number, priceList: ItemPricing[]) {
  this.pricingInfo.push({
    itemId,
    beliefId,
    priceList,
    time: Date.now()
  } as PricingRecord);

  // 保持最多1000条记录
  if (this.pricingInfo.length > 1000) {
    this.pricingInfo = this.pricingInfo.slice(-1000);
  }
};

BeliefStoreSchema.methods.addPurchaseRecord = function(
  playerId: string, 
  beliefId: number, 
  itemId: number, 
  num: number, 
  beliefNum: number
) {
  this.buyInfo.push({
    playerId,
    beliefId,
    itemId,
    num,
    beliefNum,
    time: Date.now()
  } as PurchaseRecord);

  // 更新玩家购买统计
  if (!this.buyUidMap.has(playerId)) {
    this.buyUidMap.set(playerId, []);
  }

  const playerPurchases = this.buyUidMap.get(playerId)!;
  const existingPurchase = playerPurchases.find(p => p.itemId === itemId);
  
  if (existingPurchase) {
    existingPurchase.num += num;
  } else {
    playerPurchases.push({ itemId, num } as PlayerPurchase);
  }

  // 更新购买人数
  this.buyNum = this.buyUidMap.size;

  // 保持最多10000条购买记录
  if (this.buyInfo.length > 10000) {
    this.buyInfo = this.buyInfo.slice(-10000);
  }
};

BeliefStoreSchema.methods.switchPhase = function() {
  this.shopPeriod = this.shopPeriod === 1 ? 2 : 1;
  this.phaseStartTime = Date.now();
  
  if (this.shopPeriod === 1) {
    // 切换到定价阶段，增加期数
    this.phase += 1;
  }
};

BeliefStoreSchema.methods.getItemPricing = function(itemId: number): ItemPricing[] {
  const item = this.itemList.find((item: StoreItem) => item.itemId === itemId);
  return item ? item.itemList : [];
};

BeliefStoreSchema.methods.updateItemPricing = function(itemId: number, pricingList: ItemPricing[]) {
  let item = this.itemList.find((item: StoreItem) => item.itemId === itemId);
  
  if (!item) {
    item = {
      itemId,
      num: 0,
      refreshTime: 0,
      itemList: []
    } as StoreItem;
    this.itemList.push(item);
  }

  item.itemList = pricingList;
};

// 设置toJSON选项
BeliefStoreSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    delete ret._id;
    delete ret.__v;
    // 将Map转换为Object
    if (ret.buyUidMap instanceof Map) {
      ret.buyUidMap = Object.fromEntries(ret.buyUidMap);
    }
    return ret;
  },
});
