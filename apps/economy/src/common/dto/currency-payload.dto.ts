/**
 * Currency模块的Payload DTO定义
 * 
 * 为currency.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, Length, Min } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 货币转换相关 ====================

/**
 * 货币转换Payload DTO
 * @MessagePattern('currency.convert')
 * 基于真实接口结构：{ fromCurrency: string; toCurrency: string; amount: number; injectedContext?: InjectedContext }
 */
export class ConvertCurrencyPayloadDto extends BasePayloadDto {
  @ApiProperty({ 
    description: '源货币类型', 
    example: 'gold',
    enum: ['gold', 'diamond', 'coin', 'token'],
    enumName: 'CurrencyType'
  })
  @Expose()
  @IsString({ message: '源货币类型必须是字符串' })
  @Length(1, 20, { message: '源货币类型长度必须在1-20个字符之间' })
  fromCurrency: string;

  @ApiProperty({ 
    description: '目标货币类型', 
    example: 'diamond',
    enum: ['gold', 'diamond', 'coin', 'token'],
    enumName: 'CurrencyType'
  })
  @Expose()
  @IsString({ message: '目标货币类型必须是字符串' })
  @Length(1, 20, { message: '目标货币类型长度必须在1-20个字符之间' })
  toCurrency: string;

  @ApiProperty({ description: '转换数量', example: 1000, minimum: 1 })
  @Expose()
  @IsNumber({}, { message: '转换数量必须是数字' })
  @Min(1, { message: '转换数量不能小于1' })
  amount: number;
}

// ==================== 2. 汇率查询相关 ====================

/**
 * 获取汇率Payload DTO
 * @MessagePattern('currency.getRate')
 * 基于真实接口结构：{ fromCurrency: string; toCurrency: string; injectedContext?: InjectedContext }
 */
export class GetExchangeRatePayloadDto extends BasePayloadDto {
  @ApiProperty({ 
    description: '源货币类型', 
    example: 'gold',
    enum: ['gold', 'diamond', 'coin', 'token'],
    enumName: 'CurrencyType'
  })
  @Expose()
  @IsString({ message: '源货币类型必须是字符串' })
  @Length(1, 20, { message: '源货币类型长度必须在1-20个字符之间' })
  fromCurrency: string;

  @ApiProperty({ 
    description: '目标货币类型', 
    example: 'diamond',
    enum: ['gold', 'diamond', 'coin', 'token'],
    enumName: 'CurrencyType'
  })
  @Expose()
  @IsString({ message: '目标货币类型必须是字符串' })
  @Length(1, 20, { message: '目标货币类型长度必须在1-20个字符之间' })
  toCurrency: string;
}
