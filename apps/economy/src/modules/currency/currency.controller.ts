import { <PERSON>, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { CurrencyService } from './currency.service';

import { XResponse } from '@libs/common/types/result.type';

import {
  ConvertCurrencyPayloadDto,
  GetExchangeRatePayloadDto
} from "@economy/common/dto/currency-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';

@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class CurrencyController extends BaseController {
  constructor(private readonly currencyService: CurrencyService) {
    super('CurrencyController');
  }

  /**
   * 货币转换
   */
  @MessagePattern('currency.convert')
  async convertCurrency(@Payload() payload: ConvertCurrencyPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`货币转换: ${payload.amount} ${payload.fromCurrency} -> ${payload.toCurrency}`);
    const result = await this.currencyService.convertCurrency(
      payload.fromCurrency, 
      payload.toCurrency, 
      payload.amount
    );
    return this.toSuccessResponse(result);
  }

  /**
   * 获取汇率
   */
  @MessagePattern('currency.getRate')
  async getExchangeRate(@Payload() payload: GetExchangeRatePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取汇率: ${payload.fromCurrency} -> ${payload.toCurrency}`);
    const rate = await this.currencyService.getExchangeRate(payload.fromCurrency, payload.toCurrency);
    return this.toSuccessResponse(rate);
  }
}
