/**
 * 货币系统服务 - 已适配Result模式
 *
 * 核心功能：
 * - 货币转换和汇率计算
 * - 多币种支持
 * - 汇率管理
 * - 货币交易记录
 * - 实时汇率获取
 *
 * Result模式适配：
 * - 继承BaseService基类，获得统一的业务操作框架
 * - 所有public方法返回XResult<T>类型
 * - 使用executeBusinessOperation包装关键业务操作
 * - 标准化的错误处理和微服务调用
 * - 移除所有try-catch异常处理
 */

import { Injectable, Logger } from '@nestjs/common';
import { XResult, XResultUtils, RepositoryResultWrapper } from '@libs/common/types/result.type';
import { BaseService } from '@libs/common/service/base-service';

@Injectable()
export class CurrencyService extends BaseService {

  constructor(
  ) {
    // 调用BaseService构造函数，传入服务名称和微服务客户端
    super('CurrencyService');
  }

  /**
   * 货币转换
   * 已适配Result模式：直接返回XResult，保持原有简单逻辑
   */
  async convertCurrency(fromCurrency: string, toCurrency: string, amount: number): Promise<XResult<any>> {
    this.logger.log(`货币转换: ${amount} ${fromCurrency} -> ${toCurrency}`);

    // 获取汇率
    const exchangeRateResult = await this.getExchangeRate(fromCurrency, toCurrency);
    if (XResultUtils.isFailure(exchangeRateResult)) {
      return XResultUtils.error(`获取汇率失败: ${exchangeRateResult.message}`, exchangeRateResult.code);
    }

    const exchangeRate = exchangeRateResult.data;

    // TODO: 实现货币转换逻辑
    return XResultUtils.ok({
      fromCurrency,
      toCurrency,
      originalAmount: amount,
      convertedAmount: amount * exchangeRate, // 简化处理
      exchangeRate,
    });
  }

  /**
   * 获取汇率
   * 已适配Result模式：直接返回XResult，保持原有简单逻辑
   */
  async getExchangeRate(fromCurrency: string, toCurrency: string): Promise<XResult<number>> {
    this.logger.log(`获取汇率: ${fromCurrency} -> ${toCurrency}`);
    // TODO: 实现汇率获取逻辑
    return XResultUtils.ok(1.0);
  }
}
