/**
 * 兑换大厅服务 - 已适配Result模式
 * 基于old项目exchangeHall.js业务逻辑迁移
 *
 * 核心功能：
 * - 兑换大厅信息管理
 * - 物品合成和分解
 * - 兑换大厅刷新
 * - 物品兑换操作
 *
 * Result模式适配：
 * - 继承BaseService基类，获得统一的业务操作框架
 * - 所有public方法返回XResult<T>类型
 * - 使用executeBusinessOperation包装关键业务操作
 * - 标准化的错误处理和微服务调用
 * - 移除所有try-catch异常处理
 */

import { Injectable, Logger } from '@nestjs/common';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';
import { ExchangeRepository } from '@economy/common/repositories/exchange.repository';
import { GameConfigFacade } from '@libs/game-config';
import { MicroserviceClientService } from '@libs/service-mesh';
import { MICROSERVICE_NAMES } from '@libs/shared/constants';
import { XResult, XResultUtils, RepositoryResultWrapper } from '@libs/common/types/result.type';
import { BaseService } from '@libs/common/service/base-service';

@Injectable()
export class ExchangeService extends BaseService {

  constructor(
    private readonly exchangeRepository: ExchangeRepository,
    private readonly gameConfig: GameConfigFacade,
    microserviceClient: MicroserviceClientService, // 传递给BaseService
  ) {
    // 调用BaseService构造函数，传入服务名称和微服务客户端
    super('ExchangeService', microserviceClient);
  }

  /**
   * 获取兑换大厅信息
   * 对应old项目中的getExchangeHallInfo方法
   * 已适配Result模式：直接返回XResult，无需executeBusinessOperation包装
   */
  async getExchangeHallInfo(uid: string, serverId: string): Promise<XResult<any>> {
    const exchangeResult = await this.exchangeRepository.getOrCreateExchange(uid, serverId);
    if (XResultUtils.isFailure(exchangeResult)) {
      return XResultUtils.error(`获取兑换信息失败: ${exchangeResult.message}`, exchangeResult.code);
    }

    const exchange = exchangeResult.data;

    // 检查是否需要刷新
    if (exchange.needsDailyRefresh()) {
      // 生成新的兑换列表
      const newExchangeListResult = await this.generateExchangeList(1);
      if (XResultUtils.isFailure(newExchangeListResult)) {
        return XResultUtils.error(`生成兑换列表失败: ${newExchangeListResult.message}`, newExchangeListResult.code);
      }

      exchange.exchangeList = newExchangeListResult.data;
      exchange.reExchangeHallTime = Date.now();

      // 重置刷新次数
      exchange.exchangeNumList = [
        { type: 1, num: 1 },
        { type: 2, num: 1 },
        { type: 3, num: 1 },
      ];

      // 这里使用RepositoryResultWrapper.wrap是正确的，因为exchange.save()是mongoose原生方法
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await exchange.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        this.logger.warn(`保存兑换信息失败: ${saveResult.message}`);
      }
    }

    // 获取玩家碎片数量
    const chipCountResult = await this.getPlayerChipCount(uid);
    if (XResultUtils.isFailure(chipCountResult)) {
      return XResultUtils.error(`获取玩家碎片数量失败: ${chipCountResult.message}`, chipCountResult.code);
    }

    const chipCount = chipCountResult.data;

    return XResultUtils.ok({
      uid,
      chip: chipCount,
      itemList: exchange.exchangeList,
      exchangeNumList: exchange.exchangeNumList,
      totalExchanges: exchange.totalExchanges,
      totalChipSpent: exchange.totalChipSpent,
    });
  }

  /**
   * 合成物品
   * 对应old项目中的compoundItem方法
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async compoundItem(uid: string, serverId: string, resId: number, num: number): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      if (!resId || num < 1) {
        return XResultUtils.failure(ErrorMessages[ErrorCode.INVALID_PARAMETER], ErrorCode.INVALID_PARAMETER, {
          resId,
          num
        });
      }

      const exchangeResult = await this.exchangeRepository.getOrCreateExchange(uid, serverId);
      if (XResultUtils.isFailure(exchangeResult)) {
        return XResultUtils.error(`获取兑换信息失败: ${exchangeResult.message}`, exchangeResult.code);
      }

      const exchange = exchangeResult.data;

      // 检查玩家是否有足够的物品
      const itemCountResult = await this.getPlayerItemCount(uid, resId);
      if (XResultUtils.isFailure(itemCountResult)) {
        return XResultUtils.error(`获取玩家物品数量失败: ${itemCountResult.message}`, itemCountResult.code);
      }

      const itemCount = itemCountResult.data;

      // 获取物品配置
      const itemConfigResult = await this.getItemConfig(resId);
      if (XResultUtils.isFailure(itemConfigResult)) {
        return XResultUtils.error(`获取物品配置失败: ${itemConfigResult.message}`, itemConfigResult.code);
      }

      const itemConfig = itemConfigResult.data;
      if (!itemConfig || !itemConfig.IsUpgrade) {
        return XResultUtils.failure(ErrorMessages[ErrorCode.ITEM_CONFIG_NOT_FOUND], ErrorCode.ITEM_CONFIG_NOT_FOUND, {
          itemId: resId,
          canUpgrade: itemConfig?.IsUpgrade || false
        });
      }

      const needNum = itemConfig.UpgradeNum * num;
      if (itemCount < needNum) {
        return XResultUtils.failure(ErrorMessages[ErrorCode.ITEM_NOT_ENOUGH], ErrorCode.ITEM_NOT_ENOUGH, {
          required: needNum,
          available: itemCount,
          itemId: resId
        });
      }

      // 扣除材料物品
      const deductResult = await this.deductPlayerItem(uid, resId, needNum);
      if (XResultUtils.isFailure(deductResult)) {
        return XResultUtils.error(`扣除物品失败: ${deductResult.message}`, deductResult.code);
      }

      // 给予合成结果
      const resultItemId = itemConfig.UpgradeId;
      const addResult = await this.addPlayerItem(uid, resultItemId, num);
      if (XResultUtils.isFailure(addResult)) {
        return XResultUtils.error(`添加物品失败: ${addResult.message}`, addResult.code);
      }

      // 记录合成历史
      exchange.addCompoundRecord(resId, num, resultItemId, num);
      // 这里使用RepositoryResultWrapper.wrap是正确的，因为exchange.save()是mongoose原生方法
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await exchange.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        this.logger.warn(`保存兑换信息失败: ${saveResult.message}`);
      }

      this.logger.log(`物品合成成功: ${uid}, 物品: ${resId}, 数量: ${num}, 结果: ${resultItemId}`);

      return XResultUtils.ok({
        success: true,
        resId,
        num,
        resultItemId,
        resultNum: num,
        operationTime: Date.now(),
      });
    }, {
      reason: 'compound_item',
      metadata: { uid, serverId, resId, num }
    });
  }

  /**
   * 分解物品
   * 对应old项目中的decomposeItem方法
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async decomposeItem(uid: string, serverId: string, resId: number, num: number): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      if (!resId || num < 1) {
        return XResultUtils.failure(ErrorMessages[ErrorCode.INVALID_PARAMETER], ErrorCode.INVALID_PARAMETER, {
          resId,
          num
        });
      }

      const exchangeResult = await this.exchangeRepository.getOrCreateExchange(uid, serverId);
      if (XResultUtils.isFailure(exchangeResult)) {
        return XResultUtils.error(`获取兑换信息失败: ${exchangeResult.message}`, exchangeResult.code);
      }

      const exchange = exchangeResult.data;

      // 检查玩家是否有足够的物品
      const itemCountResult = await this.getPlayerItemCount(uid, resId);
      if (XResultUtils.isFailure(itemCountResult)) {
        return XResultUtils.error(`获取玩家物品数量失败: ${itemCountResult.message}`, itemCountResult.code);
      }

      const itemCount = itemCountResult.data;
      if (itemCount < num) {
        return XResultUtils.failure(ErrorMessages[ErrorCode.ITEM_NOT_ENOUGH], ErrorCode.ITEM_NOT_ENOUGH, {
          required: num,
          available: itemCount,
          itemId: resId
        });
      }

      // 获取物品配置
      const itemConfigResult = await this.getItemConfig(resId);
      if (XResultUtils.isFailure(itemConfigResult)) {
        return XResultUtils.error(`获取物品配置失败: ${itemConfigResult.message}`, itemConfigResult.code);
      }

      const itemConfig = itemConfigResult.data;
      if (!itemConfig) {
        return XResultUtils.failure(ErrorMessages[ErrorCode.ITEM_CONFIG_NOT_FOUND], ErrorCode.ITEM_CONFIG_NOT_FOUND, {
          itemId: resId
        });
      }

      const chipNum = itemConfig.Get * num;

      // 扣除物品
      const deductResult = await this.deductPlayerItem(uid, resId, num);
      if (XResultUtils.isFailure(deductResult)) {
        return XResultUtils.error(`扣除物品失败: ${deductResult.message}`, deductResult.code);
      }

      // 给予碎片
      const addChipResult = await this.addPlayerChip(uid, chipNum);
      if (XResultUtils.isFailure(addChipResult)) {
        return XResultUtils.error(`添加碎片失败: ${addChipResult.message}`, addChipResult.code);
      }

      // 记录分解历史
      exchange.addDecomposeRecord(resId, num, chipNum);
      // 这里使用RepositoryResultWrapper.wrap是正确的，因为exchange.save()是mongoose原生方法
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await exchange.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        this.logger.warn(`保存兑换信息失败: ${saveResult.message}`);
      }

      this.logger.log(`物品分解成功: ${uid}, 物品: ${resId}, 数量: ${num}, 获得碎片: ${chipNum}`);

      return XResultUtils.ok({
        success: true,
        resId,
        num,
        chipGained: chipNum,
        operationTime: Date.now(),
      });
    }, {
      reason: 'decompose_item',
      metadata: { uid, serverId, resId, num }
    });
  }

  /**
   * 刷新兑换大厅
   * 对应old项目中的flushExchangeHall方法
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async flushExchangeHall(uid: string, serverId: string, type: number, teamId?: number): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const exchangeResult = await this.exchangeRepository.getOrCreateExchange(uid, serverId);
      if (XResultUtils.isFailure(exchangeResult)) {
        return XResultUtils.error(`获取兑换信息失败: ${exchangeResult.message}`, exchangeResult.code);
      }

      const exchange = exchangeResult.data;

      // 获取刷新配置
      const refreshConfigResult = await this.getRefreshConfig(type, exchange.getRefreshNumByType(type));
      if (XResultUtils.isFailure(refreshConfigResult)) {
        return XResultUtils.error(`获取刷新配置失败: ${refreshConfigResult.message}`, refreshConfigResult.code);
      }

      const refreshConfig = refreshConfigResult.data;
      if (!refreshConfig) {
        return XResultUtils.failure(ErrorMessages[ErrorCode.REFRESH_CONFIG_NOT_FOUND], ErrorCode.REFRESH_CONFIG_NOT_FOUND, {
          type,
          refreshNum: exchange.getRefreshNumByType(type)
        });
      }

      // 检查玩家资源是否足够
      const chipCost = refreshConfig.Num1;
      const goldCost = refreshConfig.Num2;

      const chipCountResult = await this.getPlayerChipCount(uid);
      if (XResultUtils.isFailure(chipCountResult)) {
        return XResultUtils.error(`获取玩家碎片数量失败: ${chipCountResult.message}`, chipCountResult.code);
      }

      const goldCountResult = await this.getPlayerGoldCount(uid);
      if (XResultUtils.isFailure(goldCountResult)) {
        return XResultUtils.error(`获取玩家金币数量失败: ${goldCountResult.message}`, goldCountResult.code);
      }

      const chipCount = chipCountResult.data;
      const goldCount = goldCountResult.data;

      if (chipCount < chipCost) {
        return XResultUtils.failure(ErrorMessages[ErrorCode.CHIP_NOT_ENOUGH], ErrorCode.CHIP_NOT_ENOUGH, {
          required: chipCost,
          current: chipCount,
          deficit: chipCost - chipCount
        });
      }

      if (goldCount < goldCost) {
        return XResultUtils.failure(ErrorMessages[ErrorCode.GOLD_NOT_ENOUGH], ErrorCode.GOLD_NOT_ENOUGH, {
          required: goldCost,
          current: goldCount,
          deficit: goldCost - goldCount
        });
      }

      // 扣除资源
      const deductChipResult = await this.deductPlayerChip(uid, chipCost);
      if (XResultUtils.isFailure(deductChipResult)) {
        return XResultUtils.error(`扣除碎片失败: ${deductChipResult.message}`, deductChipResult.code);
      }

      const deductGoldResult = await this.deductPlayerGold(uid, goldCost);
      if (XResultUtils.isFailure(deductGoldResult)) {
        return XResultUtils.error(`扣除金币失败: ${deductGoldResult.message}`, deductGoldResult.code);
      }

      // 生成新的兑换列表
      const newExchangeListResult = await this.generateExchangeList(type, teamId);
      if (XResultUtils.isFailure(newExchangeListResult)) {
        return XResultUtils.error(`生成兑换列表失败: ${newExchangeListResult.message}`, newExchangeListResult.code);
      }

      const newExchangeList = newExchangeListResult.data;
      exchange.exchangeList = newExchangeList;

      // 增加刷新次数
      const newRefreshNum = exchange.addRefreshNumByType(type);

      // 这里使用RepositoryResultWrapper.wrap是正确的，因为exchange.save()是mongoose原生方法
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await exchange.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        this.logger.warn(`保存兑换信息失败: ${saveResult.message}`);
      }

      this.logger.log(`兑换大厅刷新成功: ${uid}, 类型: ${type}, 刷新次数: ${newRefreshNum}`);

      return XResultUtils.ok({
        success: true,
        type,
        itemList: newExchangeList,
        refreshNum: newRefreshNum,
        chipCost,
        goldCost,
        operationTime: Date.now(),
      });
    }, {
      reason: 'flush_exchange_hall',
      metadata: { uid, serverId, type, teamId }
    });
  }

  /**
   * 兑换物品
   * 对应old项目中的exchangeItem方法
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async exchangeItem(uid: string, serverId: string, id: number): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      if (id < 1) {
        return XResultUtils.failure(ErrorMessages[ErrorCode.INVALID_PARAMETER], ErrorCode.INVALID_PARAMETER, {
          id
        });
      }

      const exchangeResult = await this.exchangeRepository.getOrCreateExchange(uid, serverId);
      if (XResultUtils.isFailure(exchangeResult)) {
        return XResultUtils.error(`获取兑换信息失败: ${exchangeResult.message}`, exchangeResult.code);
      }

      const exchange = exchangeResult.data;

      // 获取兑换配置
      const exchangeConfigResult = await this.getExchangeConfig(id);
      if (XResultUtils.isFailure(exchangeConfigResult)) {
        return XResultUtils.error(`获取兑换配置失败: ${exchangeConfigResult.message}`, exchangeConfigResult.code);
      }

      const exchangeConfig = exchangeConfigResult.data;
      if (!exchangeConfig) {
        return XResultUtils.failure(ErrorMessages[ErrorCode.EXCHANGE_CONFIG_NOT_FOUND], ErrorCode.EXCHANGE_CONFIG_NOT_FOUND, {
          exchangeId: id
        });
      }

      // 检查是否已购买
      if (exchange.checkItemIsBuy(id)) {
        return XResultUtils.failure(ErrorMessages[ErrorCode.ITEM_ALREADY_BOUGHT], ErrorCode.ITEM_ALREADY_BOUGHT, {
          exchangeId: id
        });
      }

      // 检查碎片是否足够
      const chipCountResult = await this.getPlayerChipCount(uid);
      if (XResultUtils.isFailure(chipCountResult)) {
        return XResultUtils.error(`获取玩家碎片数量失败: ${chipCountResult.message}`, chipCountResult.code);
      }

      const chipCount = chipCountResult.data;
      if (chipCount < exchangeConfig.ShowPrice) {
        return XResultUtils.failure(ErrorMessages[ErrorCode.CHIP_NOT_ENOUGH], ErrorCode.CHIP_NOT_ENOUGH, {
          required: exchangeConfig.ShowPrice,
          current: chipCount,
          deficit: exchangeConfig.ShowPrice - chipCount
        });
      }

      // 扣除碎片
      const deductChipResult = await this.deductPlayerChip(uid, exchangeConfig.ShowPrice);
      if (XResultUtils.isFailure(deductChipResult)) {
        return XResultUtils.error(`扣除碎片失败: ${deductChipResult.message}`, deductChipResult.code);
      }

      // 给予物品
      const addItemResult = await this.addPlayerItem(uid, exchangeConfig.ItemId, exchangeConfig.Num);
      if (XResultUtils.isFailure(addItemResult)) {
        return XResultUtils.error(`添加物品失败: ${addItemResult.message}`, addItemResult.code);
      }

      // 标记为已购买
      exchange.markItemAsBought(id);
      exchange.totalChipSpent += exchangeConfig.ShowPrice;

      // 这里使用RepositoryResultWrapper.wrap是正确的，因为exchange.save()是mongoose原生方法
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await exchange.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        this.logger.warn(`保存兑换信息失败: ${saveResult.message}`);
      }

      this.logger.log(`兑换物品成功: ${uid}, 兑换ID: ${id}, 物品: ${exchangeConfig.ItemId}, 数量: ${exchangeConfig.Num}`);

      return XResultUtils.ok({
        success: true,
        exchangeId: id,
        itemId: exchangeConfig.ItemId,
        itemNum: exchangeConfig.Num,
        chipCost: exchangeConfig.ShowPrice,
        operationTime: Date.now(),
      });
    }, {
      reason: 'exchange_item',
      metadata: { uid, serverId, id }
    });
  }

  // ==================== 私有方法 ====================

  /**
   * 生成兑换列表
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async generateExchangeList(type: number, teamId?: number): Promise<XResult<any[]>> {
    // 基于old项目: 权重随机算法生成兑换商品

    // 1. 获取兑换配置表数据
    const exchangeConfigsResult = await this.getExchangeConfigsByType(type, teamId);
    if (XResultUtils.isFailure(exchangeConfigsResult)) {
      this.logger.error(`获取兑换配置失败: ${exchangeConfigsResult.message}`);
      // 返回默认数据
      return XResultUtils.ok([
        { id: 1001, itemId: 2001, price: 100, isBuy: 0, awardGroup: type, rate: 100, rewardTeam: 1 },
        { id: 1002, itemId: 2002, price: 200, isBuy: 0, awardGroup: type, rate: 80, rewardTeam: 1 },
        { id: 1003, itemId: 2003, price: 300, isBuy: 0, awardGroup: type, rate: 60, rewardTeam: 1 },
      ]);
    }

    const exchangeConfigs = exchangeConfigsResult.data;
    if (!exchangeConfigs || exchangeConfigs.length === 0) {
      this.logger.warn(`没有找到类型${type}的兑换配置`);
      return XResultUtils.ok([]);
    }

    // 2. 计算总权重
    const totalWeight = exchangeConfigs.reduce((sum, config) => sum + (config.rate || 100), 0);

    // 3. 生成兑换列表（基于old项目：每次刷新生成6个物品）
    const exchangeList = [];
    const maxItems = 6;
    const usedItems = new Set<number>();

    for (let i = 0; i < maxItems && exchangeList.length < exchangeConfigs.length; i++) {
      // 4. 权重随机选择
      const selectedConfig = this.selectByWeight(exchangeConfigs, totalWeight, usedItems);
      if (selectedConfig) {
        exchangeList.push({
          id: selectedConfig.id,
          itemId: selectedConfig.itemId,
          price: selectedConfig.price || 100,
          isBuy: 0,
          awardGroup: selectedConfig.awardGroup || type,
          rate: selectedConfig.rate || 100,
          rewardTeam: selectedConfig.rewardTeam || 1,
        });
        usedItems.add(selectedConfig.id);
      }
    }

    this.logger.debug(`生成兑换列表: 类型${type}, 数量${exchangeList.length}`);
    return XResultUtils.ok(exchangeList);
  }

  /**
   * 根据类型获取兑换配置
   * 基于old项目: Exchange配置表查询
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async getExchangeConfigsByType(type: number, teamId?: number): Promise<XResult<any[]>> {
    // TODO: 从配置表获取兑换配置
    // const configs = await this.gameConfig.exchange?.getByType(type);
    // return XResultUtils.ok(configs.filter(config => !teamId || config.rewardTeam === teamId));

    // 暂时返回模拟数据
    return XResultUtils.ok([
      { id: 1001, itemId: 2001, price: 100, rate: 100, awardGroup: type, rewardTeam: teamId || 1 },
      { id: 1002, itemId: 2002, price: 200, rate: 80, awardGroup: type, rewardTeam: teamId || 1 },
      { id: 1003, itemId: 2003, price: 300, rate: 60, awardGroup: type, rewardTeam: teamId || 1 },
      { id: 1004, itemId: 2004, price: 400, rate: 40, awardGroup: type, rewardTeam: teamId || 1 },
      { id: 1005, itemId: 2005, price: 500, rate: 20, awardGroup: type, rewardTeam: teamId || 1 },
      { id: 1006, itemId: 2006, price: 600, rate: 10, awardGroup: type, rewardTeam: teamId || 1 },
    ]);
  }

  /**
   * 权重随机选择
   * 基于old项目: 权重随机算法
   */
  private selectByWeight(configs: any[], totalWeight: number, usedItems: Set<number>): any {
    // 过滤已使用的物品
    const availableConfigs = configs.filter(config => !usedItems.has(config.id));
    if (availableConfigs.length === 0) {
      return null;
    }

    // 重新计算可用配置的总权重
    const availableTotalWeight = availableConfigs.reduce((sum, config) => sum + (config.rate || 100), 0);

    // 生成随机数
    let randomWeight = Math.random() * availableTotalWeight;

    // 根据权重选择
    for (const config of availableConfigs) {
      randomWeight -= (config.rate || 100);
      if (randomWeight <= 0) {
        return config;
      }
    }

    // 如果没有选中，返回第一个可用的
    return availableConfigs[0];
  }

  /**
   * 获取玩家碎片数量
   * 基于old项目: player.chip字段
   * 已适配Result模式：使用callMicroservice方法，返回XResult类型
   */
  private async getPlayerChipCount(uid: string): Promise<XResult<number>> {
    // 调用Character服务获取玩家碎片数量（基于old项目player.chip字段）
    const result = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'character.getInfo',
      { characterId: uid }
    );

    if (XResultUtils.isFailure(result)) {
      return XResultUtils.error(`获取玩家信息失败: ${result.message}`, result.code);
    }

    const playerData = result.data;
    if (!playerData) {
      this.logger.warn(`获取玩家碎片数量失败，玩家不存在: ${uid}`);
      return XResultUtils.ok(0);
    }

    return XResultUtils.ok(playerData.chip || 0);
  }

  /**
   * 获取玩家物品数量
   * 基于old项目: bag.getItemNum方法
   * 已适配Result模式：使用callMicroservice方法，返回XResult类型
   */
  private async getPlayerItemCount(uid: string, resId: number): Promise<XResult<number>> {
    // 调用Character服务获取玩家物品数量（基于old项目bag.getItemNumByResID）
    const result = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'item.getItemQuantityByConfigId',
      { characterId: uid, configId: resId }
    );

    if (XResultUtils.isFailure(result)) {
      return XResultUtils.error(`获取玩家物品数量失败: ${result.message}`, result.code);
    }

    return XResultUtils.ok(result.data?.quantity || 0);
  }

  /**
   * 获取玩家金币数量
   * 基于old项目: player.gold字段
   * 已适配Result模式：使用callMicroservice方法，返回XResult类型
   */
  private async getPlayerGoldCount(uid: string): Promise<XResult<number>> {
    // 调用Character服务获取玩家金币数量（基于old项目player.gold字段）
    const result = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'character.getInfo',
      { characterId: uid }
    );

    if (XResultUtils.isFailure(result)) {
      return XResultUtils.error(`获取玩家信息失败: ${result.message}`, result.code);
    }

    const playerData = result.data;
    return XResultUtils.ok(playerData?.gold || 0);
  }

  /**
   * 获取物品配置
   * 基于old项目: dataApi.allData.data["Item"][resId]
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async getItemConfig(resId: number): Promise<XResult<any>> {
    // 从Item配置表获取物品配置（基于old项目Item.json）
    const config = await this.gameConfig.item.get(resId);
    if (!config) {
      this.logger.warn(`物品配置不存在: ${resId}`);
      return XResultUtils.ok(null);
    }

    // 转换为old项目兼容的格式
    const itemConfig = {
      id: config.id,
      IsUpgrade: config.isUpgrade || 0,        // 是否可升级 0不可 1可以
      UpgradeNum: config.upgradeNumber || 1,   // 升级需要的数量
      UpgradeId: config.upgradeId || 0,        // 升级后的物品ID
      Get: config.get || 0,                    // 分解获得的碎片数量
      name: config.name || `物品${resId}`,
      type: config.type || 0,
    };

    this.logger.debug(`获取物品配置成功: ${resId}`, itemConfig);
    return XResultUtils.ok(itemConfig);
  }

  /**
   * 获取刷新配置
   * 基于old项目: dataApi.allData.data["ExchangeControl"][type]
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async getRefreshConfig(type: number, refreshNum: number): Promise<XResult<any>> {
    // 从ExchangeControl配置表获取刷新配置（基于old项目ExchangeControl.json）
    const configs = await this.gameConfig.exchangeControl.getAll();
    if (!configs || configs.length === 0) {
      this.logger.warn('ExchangeControl配置表为空');
      return XResultUtils.ok({
        Num1: 50 * refreshNum,  // 默认碎片消耗
        Num2: 100 * refreshNum, // 默认金币消耗
      });
    }

    // 查找对应类型的刷新配置（基于old项目ExchangeControl结构）
    const refreshConfig = configs.find(config => config.id === type);
    if (!refreshConfig) {
      this.logger.warn(`刷新配置不存在: 类型${type}`);
      return XResultUtils.ok({
        Num1: 50 * refreshNum,  // 默认碎片消耗
        Num2: 100 * refreshNum, // 默认金币消耗
      });
    }

    return XResultUtils.ok({
      Num1: (refreshConfig.num1 || 50) * refreshNum,  // 碎片消耗（Type1对应的数量）
      Num2: (refreshConfig.num2 || 100) * refreshNum, // 金币消耗（Type2对应的数量）
    });
  }

  /**
   * 获取兑换配置
   * 基于old项目: dataApi.allData.data["ExchangeShop"][id]
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async getExchangeConfig(id: number): Promise<XResult<any>> {
    // 从ExchangeShop配置表获取兑换配置（基于old项目ExchangeShop.json）
    const config = await this.gameConfig.exchangeShop.get(id);
    if (!config) {
      this.logger.warn(`兑换配置不存在: ${id}`);
      return XResultUtils.ok(null);
    }

    // 转换为old项目兼容的格式
    const exchangeConfig = {
      id: config.id,
      ShowPrice: config.showPrice || 100,      // 显示价格（碎片消耗）
      ItemId: config.itemId || 0,              // 物品ID
      Num: config.num || 1,                    // 物品数量
      TotalPrice: config.totalPrice || 0,      // 总价格
      MoneyType: config.moneyType || 1,        // 货币类型
      Rate: config.rate || 100,                // 权重
      AwardGroup: config.awardGroup || 1,      // 奖励组
      Club: config.club || 0,                  // 俱乐部限制
      RewardTeam: config.rewardTeam || 1,      // 奖励队伍
    };

    this.logger.debug(`获取兑换配置成功: ${id}`, exchangeConfig);
    return XResultUtils.ok(exchangeConfig);
  }

  /**
   * 扣除玩家物品
   * 基于old项目: bag.delItem方法
   * 已适配Result模式：使用callMicroservice方法，返回XResult类型
   */
  private async deductPlayerItem(uid: string, resId: number, num: number): Promise<XResult<void>> {
    // 调用Character服务扣除玩家物品（基于old项目bag.delItem）
    const result = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'item.removeItem',
      { characterId: uid, configId: resId, quantity: num, reason: 'exchange' }
    );

    if (XResultUtils.isFailure(result)) {
      return XResultUtils.error(`扣除物品失败: ${result.message}`, result.code);
    }

    this.logger.log(`扣除玩家物品成功: ${uid}, 物品: ${resId}, 数量: ${num}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 给予玩家物品
   * 基于old项目: bag.addItem方法
   * 已适配Result模式：使用callMicroservice方法，返回XResult类型
   */
  private async addPlayerItem(uid: string, resId: number, num: number): Promise<XResult<void>> {
    // 调用Character服务给予玩家物品（基于old项目bag.addItem）
    const result = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'item.addItem',
      { characterId: uid, resId: resId, num: num }
    );

    if (XResultUtils.isFailure(result)) {
      return XResultUtils.error(`添加物品失败: ${result.message}`, result.code);
    }

    this.logger.log(`给予玩家物品成功: ${uid}, 物品: ${resId}, 数量: ${num}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 给予玩家碎片
   * 已适配Result模式：返回XResult类型
   */
  private async addPlayerChip(uid: string, num: number): Promise<XResult<void>> {
    // TODO: 调用Character服务给予玩家碎片
    // const result = await this.callMicroservice(
    //   'character.addChip',
    //   { characterId: uid, amount: num, reason: 'exchange' }
    // );
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`给予碎片失败: ${result.message}`, result.code);
    // }

    this.logger.log(`给予玩家碎片: ${uid}, 数量: ${num}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 扣除玩家碎片
   * 已适配Result模式：返回XResult类型
   */
  private async deductPlayerChip(uid: string, num: number): Promise<XResult<void>> {
    // TODO: 调用Character服务扣除玩家碎片
    // const result = await this.callMicroservice(
    //   'character.deductChip',
    //   { characterId: uid, amount: num, reason: 'exchange' }
    // );
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`扣除碎片失败: ${result.message}`, result.code);
    // }

    this.logger.log(`扣除玩家碎片: ${uid}, 数量: ${num}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 扣除玩家金币
   * 已适配Result模式：返回XResult类型
   */
  private async deductPlayerGold(uid: string, num: number): Promise<XResult<void>> {
    // TODO: 调用Character服务扣除玩家金币
    // const result = await this.callMicroservice(
    //   'character.deductGold',
    //   { characterId: uid, amount: num, reason: 'exchange' }
    // );
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`扣除金币失败: ${result.message}`, result.code);
    // }

    this.logger.log(`扣除玩家金币: ${uid}, 数量: ${num}`);
    return XResultUtils.ok(undefined);
  }
}
