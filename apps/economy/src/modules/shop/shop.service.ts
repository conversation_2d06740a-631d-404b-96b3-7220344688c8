/**
 * 商店系统服务 - 已适配Result模式
 * 基于old项目Store实体的完整业务逻辑迁移
 *
 * 核心功能：
 * - 商店信息管理和刷新
 * - 商品购买和限购控制
 * - 月卡购买和奖励领取
 * - 购买历史和统计
 * - 批量商店刷新
 *
 * Result模式适配：
 * - 继承BaseService基类，获得统一的业务操作框架
 * - 所有public方法返回XResult<T>类型
 * - 使用executeBusinessOperation包装关键业务操作
 * - 标准化的错误处理和微服务调用
 * - 移除所有try-catch异常处理和多余的success字段
 */

import { Injectable, Logger } from '@nestjs/common';
import { ShopDocument, ShopType, RefreshCycle } from '@economy/common/schemas/shop.schema';
import { ShopRepository } from '@economy/common/repositories/shop.repository';
import {
  PurchaseGoodsDto,
  RefreshShopDto,
  ClaimMonthCardDto,
  BuyMonthCardDto,
  ShopInfoDto,
  GetShopListDto,
  GetPurchaseHistoryDto,
  ShopStatsDto
} from '@economy/common/dto/shop.dto';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';
import { GameConfigFacade } from '@libs/game-config';
import { MicroserviceClientService } from '@libs/service-mesh';
import { MICROSERVICE_NAMES } from '@libs/shared/constants';
import { XResult, XResultUtils, RepositoryResultWrapper } from '@libs/common/types/result.type';
import { BaseService } from '@libs/common/service/base-service';
import { BuyMonthCardPayloadDto, PurchaseGoodsPayloadDto, RefreshShopPayloadDto } from '@economy/common/dto/shop-payload.dto';

@Injectable()
export class ShopService extends BaseService {

  constructor(
    private readonly shopRepository: ShopRepository,
    private readonly gameConfig: GameConfigFacade,
    microserviceClient: MicroserviceClientService, // 传递给BaseService
  ) {
    // 调用BaseService构造函数，传入服务名称和微服务客户端
    super('ShopService', microserviceClient);
  }

  /**
   * 获取商店信息
   * 已适配Result模式：直接返回XResult，无需executeBusinessOperation包装
   */
  async getShopInfo(characterId: string,shopType: ShopType): Promise<XResult<ShopInfoDto>> {
    const shopResult = await this.shopRepository.getOrCreateShop(characterId, shopType);
    if (XResultUtils.isFailure(shopResult)) {
      return XResultUtils.error(`获取商店信息失败: ${shopResult.message}`, shopResult.code);
    }

    const shop = shopResult.data;
    return XResultUtils.ok(this.toShopInfoDto(shop));
  }

  /**
   * 购买商品（基于old项目Store实体的购买逻辑）
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async purchaseGoods(dto: PurchaseGoodsPayloadDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const shopResult = await this.shopRepository.getOrCreateShop(dto.characterId, dto.shopType);
      if (XResultUtils.isFailure(shopResult)) {
        return XResultUtils.error(`获取商店信息失败: ${shopResult.message}`, shopResult.code);
      }

      const shop = shopResult.data;

      // 1. 检查商品配置是否存在（基于old项目getConfigInfo）
      const goodsConfigResult = await this.getShopConfig(dto.goodsId);
      if (XResultUtils.isFailure(goodsConfigResult)) {
        return XResultUtils.error(`获取商品配置失败: ${goodsConfigResult.message}`, goodsConfigResult.code);
      }

      const goodsConfig = goodsConfigResult.data;
      if (!goodsConfig) {
        return XResultUtils.error(`商品配置不存在: ${dto.goodsId}`, 'CONFIG_NOT_FOUND');
      }

      // 2. 检查是否在规定时间内（基于old项目checkIsInBuyTime）
      const timeCheckResult = this.checkIsInBuyTime(goodsConfig);
      if (timeCheckResult !== 0) {
        return XResultUtils.error('商品不在销售时间内', 'TIME_LIMIT_EXCEEDED');
      }

      // 3. 检查VIP或个人等级是否达标（基于old项目checkBuyLimitType）
      const limitCheckResult = await this.checkBuyLimitType(dto.characterId, goodsConfig);
      if (XResultUtils.isFailure(limitCheckResult)) {
        return XResultUtils.error(`检查购买限制失败: ${limitCheckResult.message}`, limitCheckResult.code);
      }

      // checkBuyLimitType 现在在失败时直接返回 error，成功时返回 ok(0)
      // 所以这里不需要额外检查

      // 4. 检查购买数量是否超过购买限制（基于old项目checkBuyCustomer）
      const customerCheckResult = this.checkBuyCustomer(shop, goodsConfig, dto.goodsId, dto.quantity);
      if (customerCheckResult !== 0) {
        return XResultUtils.error('超过购买限制', 'PURCHASE_LIMIT_EXCEEDED');
      }

      // 5. 检查玩家货币是否足够并扣钱（基于old项目checkIsHaveMoney）
      const moneyCheckResult = await this.checkIsHaveMoney(dto.characterId, goodsConfig, dto.quantity);
      if (XResultUtils.isFailure(moneyCheckResult)) {
        return XResultUtils.error(`检查货币失败: ${moneyCheckResult.message}`, moneyCheckResult.code);
      }

      // checkIsHaveMoney 现在在失败时直接返回 error，成功时返回 ok(0)
      // 所以这里不需要额外检查

      // 6. 计算费用
      const totalCost = this.calculateCost(goodsConfig, dto.quantity);

      // 7. 添加购买数量（基于old项目addBuyCount）
      this.addBuyCount(shop, goodsConfig, dto.goodsId, dto.quantity);

      // 8. 发货（基于old项目sendItem方法）
      const sendResult = await this.sendItem(dto.characterId, goodsConfig, dto.quantity);
      if (XResultUtils.isFailure(sendResult)) {
        return XResultUtils.error(`发货失败: ${sendResult.message}`, sendResult.code);
      }

      const sendData = sendResult.data;
      if (sendData !== 0) {
        return XResultUtils.error('发货失败', 'DELIVERY_FAILED');
      }

      // 9. 添加购买记录
      shop.addPurchaseRecord(
        dto.goodsId,
        dto.quantity,
        totalCost,
        goodsConfig.priceType === 1 ? 'cash' : 'gold'
      );

      // 这里使用RepositoryResultWrapper.wrap是正确的，因为shop.save()是mongoose原生方法
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await shop.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存商店状态失败: ${saveResult.message}`, saveResult.code);
      }

      const updatedShop = saveResult.data;

      this.logger.log(`商品购买成功: ${dto.characterId}, 商品: ${dto.goodsId}, 数量: ${dto.quantity}, 费用: ${totalCost}`);

      // 10. 触发任务（基于old项目逻辑）
      const triggerResult = await this.triggerPurchaseTask(dto.characterId, dto.quantity);
      if (XResultUtils.isFailure(triggerResult)) {
        this.logger.warn(`触发购买任务失败: ${triggerResult.message}`);
      }

      return XResultUtils.ok({
        totalCost,
        currency: goodsConfig.priceType === 1 ? 'cash' : 'gold',
        goodsId: dto.goodsId,
        quantity: dto.quantity,
        resId: goodsConfig.resId,
        shop: this.toShopInfoDto(updatedShop),
      });
    }, {
      reason: 'purchase_goods',
      metadata: { characterId: dto.characterId, serverId: dto.serverId, goodsId: dto.goodsId, quantity: dto.quantity }
    });
  }

  /**
   * 刷新商店
   * 已适配Result模式：直接返回XResult，无需executeBusinessOperation包装
   */
  async refreshShop(dto: RefreshShopPayloadDto): Promise<XResult<ShopInfoDto>> {
    const shopResult = await this.shopRepository.getOrCreateShop(dto.characterId, dto.shopType);
    if (XResultUtils.isFailure(shopResult)) {
      return XResultUtils.error(`获取商店信息失败: ${shopResult.message}`, shopResult.code);
    }

    const shop = shopResult.data;

    if (dto.forceRefresh || this.needsRefresh(shop, dto.cycle || RefreshCycle.DAILY)) {
      shop.refreshLimits(dto.cycle || RefreshCycle.DAILY);

      // 这里使用RepositoryResultWrapper.wrap是正确的，因为shop.save()是mongoose原生方法
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await shop.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存商店状态失败: ${saveResult.message}`, saveResult.code);
      }

      this.logger.log(`商店刷新成功: ${dto.characterId}, 类型: ${dto.shopType}`);
    }

    return XResultUtils.ok(this.toShopInfoDto(shop));
  }

  /**
   * 购买月卡（基于old项目MonthCard实体）
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async buyMonthCard(dto: BuyMonthCardPayloadDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const shopResult = await this.shopRepository.getOrCreateShop(dto.characterId, ShopType.NORMAL);
      if (XResultUtils.isFailure(shopResult)) {
        return XResultUtils.error(`获取商店信息失败: ${shopResult.message}`, shopResult.code);
      }

      const shop = shopResult.data;

      // 1. 检查支付状态（基于old项目支付验证逻辑）
      const paymentResult = await this.checkPaymentStatus(dto.characterId, dto.orderId);
      if (XResultUtils.isFailure(paymentResult)) {
        return XResultUtils.error(`检查支付状态失败: ${paymentResult.message}`, paymentResult.code);
      }

      const payment = paymentResult.data;
      if (!payment.success) {
        return XResultUtils.error('支付验证失败', ErrorCode.PAYMENT_VERIFICATION_FAILED);
      }

      // 2. 检查月卡配置（基于old项目MonthCard配置表）
      const monthCardConfigResult = await this.getMonthCardConfig(dto.cardType);
      if (XResultUtils.isFailure(monthCardConfigResult)) {
        return XResultUtils.error(`获取月卡配置失败: ${monthCardConfigResult.message}`, monthCardConfigResult.code);
      }

      const monthCardConfig = monthCardConfigResult.data;
      if (!monthCardConfig) {
        return XResultUtils.error(`月卡配置不存在: ${dto.cardType}`, ErrorCode.CONFIG_NOT_FOUND);
      }

      // 3. 验证购买天数与配置是否匹配
      if (dto.days !== monthCardConfig.days) {
        return XResultUtils.error('购买天数与配置不匹配', ErrorCode.INVALID_PARAMETER);
      }

      // 4. 检查是否已购买月卡（防重复购买）
      if (shop.isBuy === 1 && shop.cardTime > 0) {
        // 如果已有月卡，则延长时间
        shop.cardTime += dto.days;
        shop.allDay += dto.days;
      } else {
        // 首次购买月卡
        shop.isBuy = 1;
        shop.allDay = dto.days;
        shop.cardTime = dto.days;
        shop.isGetReward = 0; // 重置奖励领取状态
      }

      const now = Date.now();
      shop.buyMonthCardTime = now;
      shop.buyMonthNum += 1;
      shop.refreshTime = now;

      // 这里使用RepositoryResultWrapper.wrap是正确的，因为shop.save()是mongoose原生方法
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await shop.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存商店状态失败: ${saveResult.message}`, saveResult.code);
      }

      const updatedShop = saveResult.data;

      this.logger.log(`月卡购买成功: ${dto.characterId}, 类型: ${dto.cardType}, 天数: ${dto.days}, 总天数: ${shop.cardTime}`);

      // 5. 触发月卡购买相关任务
      const triggerResult = await this.triggerMonthCardTasks(dto.characterId, dto.cardType);
      if (XResultUtils.isFailure(triggerResult)) {
        this.logger.warn(`触发月卡任务失败: ${triggerResult.message}`);
      }

      return XResultUtils.ok({
        cardType: dto.cardType,
        days: dto.days,
        totalDays: shop.cardTime,
        config: monthCardConfig,
        shop: this.toShopInfoDto(updatedShop),
      });
    }, {
      reason: 'buy_month_card',
      metadata: { characterId: dto.characterId, serverId: dto.serverId, cardType: dto.cardType, days: dto.days }
    });
  }

  /**
   * 领取月卡奖励（基于old项目MonthCard实体）
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async claimMonthCardReward(claimDto: ClaimMonthCardDto): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const shopResult = await this.shopRepository.findShopByCharacterAndType(claimDto.characterId, ShopType.NORMAL);
      if (XResultUtils.isFailure(shopResult)) {
        return XResultUtils.error(`查找商店失败: ${shopResult.message}`, shopResult.code);
      }

      const shop = shopResult.data;
      if (!shop) {
        return XResultUtils.error(ErrorMessages[ErrorCode.SHOP_NOT_FOUND], ErrorCode.SHOP_NOT_FOUND);
      }

      if (!shop.canClaimMonthCardReward) {
        return XResultUtils.error(ErrorMessages[ErrorCode.MONTH_CARD_REWARD_CLAIMED], ErrorCode.MONTH_CARD_REWARD_CLAIMED);
      }

      const reward = shop.claimMonthCardReward();
      shop.cardTime = Math.max(0, shop.cardTime - 1); // 减少剩余天数

      // 这里使用RepositoryResultWrapper.wrap是正确的，因为shop.save()是mongoose原生方法
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await shop.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存商店状态失败: ${saveResult.message}`, saveResult.code);
      }

      const updatedShop = saveResult.data;

      this.logger.log(`月卡奖励领取成功: ${claimDto.characterId}`);

      return XResultUtils.ok({
        reward,
        remainingDays: shop.cardTime,
        shop: this.toShopInfoDto(updatedShop),
      });
    }, {
      reason: 'claim_month_card_reward',
      metadata: { characterId: claimDto.characterId }
    });
  }

  /**
   * 获取商店列表
   * 已适配Result模式：直接返回XResult，无需executeBusinessOperation包装
   */
  async getShopList(query: GetShopListDto): Promise<XResult<ShopInfoDto[]>> {
    const shopsResult = await this.shopRepository.findShopsByCharacterId(query);
    if (XResultUtils.isFailure(shopsResult)) {
      return XResultUtils.error(`获取商店列表失败: ${shopsResult.message}`, shopsResult.code);
    }

    const shops = shopsResult.data;
    return XResultUtils.ok(shops.map(shop => this.toShopInfoDto(shop)));
  }

  /**
   * 获取购买历史
   * 已适配Result模式：直接返回XResult，无需executeBusinessOperation包装
   */
  async getPurchaseHistory(query: GetPurchaseHistoryDto): Promise<XResult<any>> {
    const historyResult = await this.shopRepository.getPurchaseHistory(query);
    if (XResultUtils.isFailure(historyResult)) {
      return XResultUtils.error(`获取购买历史失败: ${historyResult.message}`, historyResult.code);
    }

    return historyResult;
  }

  /**
   * 获取商店统计
   * 已适配Result模式：直接返回XResult，无需executeBusinessOperation包装
   */
  async getShopStats(query: ShopStatsDto): Promise<XResult<any>> {
    const statsResult = await this.shopRepository.getShopStats(query.characterId, query.days, query.shopType);
    if (XResultUtils.isFailure(statsResult)) {
      return XResultUtils.error(`获取商店统计失败: ${statsResult.message}`, statsResult.code);
    }

    return statsResult;
  }

  /**
   * 批量刷新商店（定时任务用）
   * 已适配Result模式：直接返回XResult，无需executeBusinessOperation包装
   */
  async batchRefreshShops(cycle: RefreshCycle): Promise<XResult<any>> {
    const shopsResult = await this.shopRepository.findShopsNeedingRefresh(cycle);
    if (XResultUtils.isFailure(shopsResult)) {
      return XResultUtils.error(`查找需要刷新的商店失败: ${shopsResult.message}`, shopsResult.code);
    }

    const shopsNeedingRefresh = shopsResult.data;

    if (shopsNeedingRefresh.length === 0) {
      return XResultUtils.ok({ refreshed: 0 });
    }

    const shopIds = shopsNeedingRefresh.map(shop => shop.shopId);
    const batchResult = await this.shopRepository.batchRefreshShops(shopIds, cycle);
    if (XResultUtils.isFailure(batchResult)) {
      return XResultUtils.error(`批量刷新商店失败: ${batchResult.message}`, batchResult.code);
    }

    this.logger.log(`批量刷新商店完成: ${shopsNeedingRefresh.length}个商店, 周期: ${cycle}`);

    return XResultUtils.ok({
      refreshed: shopsNeedingRefresh.length,
      cycle,
      result: batchResult.data,
    });
  }

  // ==================== 私有方法 ====================

  /**
   * 计算购买费用
   */
  private calculateCost(goodsId: number, quantity: number, useDiscount?: boolean): number {
    // TODO: 根据商品配置计算实际费用
    // 这里简化处理
    const basePrice = 100; // 基础价格
    let totalCost = basePrice * quantity;
    
    if (useDiscount) {
      totalCost = Math.floor(totalCost * 0.9); // 9折
    }
    
    return totalCost;
  }

  /**
   * 更新限购记录
   * 已适配Result模式：返回XResult类型
   */
  private async updateLimitRecord(shop: ShopDocument, goodsId: number, quantity: number, cycle: RefreshCycle): Promise<XResult<void>> {
    let limitRecords = shop.everyDayBuy; // 默认每日限购

    switch (cycle) {
      case RefreshCycle.WEEKLY:
        limitRecords = shop.weeklyBuy;
        break;
      case RefreshCycle.MONTHLY:
        limitRecords = shop.monthlyBuy;
        break;
      case RefreshCycle.SEASON:
        limitRecords = shop.seasonBuy;
        break;
    }

    let record = limitRecords.find(r => r.goodsId === goodsId);
    if (!record) {
      // 从配置获取限购数量（基于old项目Shop配置表的Num字段）
      const limitCountResult = await this.getLimitCountFromConfig(goodsId, cycle);
      if (XResultUtils.isFailure(limitCountResult)) {
        return XResultUtils.error(`获取限购配置失败: ${limitCountResult.message}`, limitCountResult.code);
      }

      const limitCount = limitCountResult.data;
      record = {
        goodsId,
        purchasedCount: 0,
        limitCount: limitCount,
        resetTime: Date.now(),
      };
      limitRecords.push(record);
    }

    record.purchasedCount += quantity;
    return XResultUtils.ok(undefined);
  }

  /**
   * 检查是否需要刷新
   * 基于old项目: Store.prototype.getStoreInfo中的时间检查逻辑
   */
  private needsRefresh(shop: ShopDocument, cycle: RefreshCycle): boolean {
    // 基于old项目：使用时间工具检查是否需要刷新
    switch (cycle) {
      case RefreshCycle.DAILY:
        // 基于old项目：!timeUtils.isToday(this.everyDayReTime)
        return !this.isToday(shop.everyDayReTime || 0);

      case RefreshCycle.WEEKLY:
        // 基于old项目：!timeUtils.isSameWeek(this.weeklyReTime)
        return !this.isSameWeek(shop.weeklyReTime || 0);

      case RefreshCycle.MONTHLY:
        // 基于old项目：!timeUtils.isSameMonth(this.monthlyReTime)
        return !this.isSameMonth(shop.monthlyReTime || 0);

      case RefreshCycle.SEASON:
        // 季节刷新：90天
        const seasonInterval = 90 * 24 * 60 * 60 * 1000;
        return (Date.now() - (shop.seasonReTime || 0)) >= seasonInterval;

      default:
        return false;
    }
  }

  /**
   * 转换为商店信息DTO
   */
  private toShopInfoDto(shop: ShopDocument): ShopInfoDto {
    return {
      shopId: shop.shopId,
      characterId: shop.characterId,
      shopType: shop.shopType,
      everyDayReTime: shop.everyDayReTime,
      weeklyReTime: shop.weeklyReTime,
      monthlyReTime: shop.monthlyReTime,
      seasonReTime: shop.seasonReTime,
      everyDayBuy: shop.everyDayBuy,
      weeklyBuy: shop.weeklyBuy,
      monthlyBuy: shop.monthlyBuy,
      seasonBuy: shop.seasonBuy,
      purchaseHistory: shop.purchaseHistory,
      totalSpent: shop.totalSpent,
      totalPurchases: shop.totalPurchases,
      lastPurchaseTime: shop.lastPurchaseTime,
      needsDailyRefresh: shop.needsDailyRefresh,
      needsWeeklyRefresh: shop.needsWeeklyRefresh,
      needsMonthlyRefresh: shop.needsMonthlyRefresh,
      isMonthCardActive: shop.isMonthCardActive,
      canClaimMonthCardReward: shop.canClaimMonthCardReward,
      cardTime: shop.cardTime,
      isGetReward: shop.isGetReward,
    };
  }

  /**
   * 发放购买奖励
   * 基于old项目: Store.prototype.sendItem方法
   *
   * 实现逻辑：
   * 1. 获取商品配置
   * 2. 根据商品类型发放不同奖励
   * 3. 处理背包满的情况（转邮件）
   * 4. 触发相关任务
   */
  private async sendPurchaseRewards(characterId: string, goodsId: number, quantity: number): Promise<XResult<any[]>> {
    // 1. 获取商品配置
    const shopConfigResult = await this.getShopConfig(goodsId);
    if (XResultUtils.isFailure(shopConfigResult)) {
      return XResultUtils.error(`获取商品配置失败: ${shopConfigResult.message}`, shopConfigResult.code);
    }

    const shopConfig = shopConfigResult.data;
    if (!shopConfig) {
      this.logger.error(`商品配置不存在: ${goodsId}`);
      return XResultUtils.ok([]);
    }

    const rewards = [];

    // 2. 根据商品类型发放奖励（基于old项目逻辑）
    if (shopConfig.itemType !== 1) {
      // 普通物品
      const rewardResult = await this.sendItemReward(characterId, shopConfig.parameters, quantity);
      if (XResultUtils.isSuccess(rewardResult) && rewardResult.data) {
        rewards.push(rewardResult.data);
      }
    } else {
      // 球员类型
      const heroRewardResult = await this.sendHeroReward(characterId, shopConfig.parameters, quantity);
      if (XResultUtils.isSuccess(heroRewardResult) && heroRewardResult.data) {
        rewards.push(heroRewardResult.data);
      }
    }

    // 3. 触发购买任务（基于old项目：TARGET_TYPE.NINE）
    const triggerResult = await this.triggerPurchaseTask(characterId, quantity);
    if (XResultUtils.isFailure(triggerResult)) {
      this.logger.warn(`触发购买任务失败: ${triggerResult.message}`);
    }

    this.logger.log(`购买奖励发放完成: ${characterId}, 商品: ${goodsId}, 奖励数量: ${rewards.length}`);
    return XResultUtils.ok(rewards);
  }

  /**
   * 发放物品奖励
   * 基于old项目: player.bag.addItem逻辑
   * 已适配Result模式：使用callMicroservice方法，返回XResult类型
   */
  private async sendItemReward(characterId: string, itemId: number, quantity: number): Promise<XResult<any>> {
    // TODO: 调用Character服务添加物品
    // const result = await this.callMicroservice(
    //   'inventory.addItem',
    //   { characterId, itemId, quantity, source: 'shop_purchase' }
    // );

    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`添加物品失败: ${result.message}`, result.code);
    // }

    // if (result.data?.code === 'BAG_FULL') {
    //   // 背包满了，转邮件发送（基于old项目逻辑）
    //   const mailResult = await this.sendRewardByMail(characterId, itemId, quantity, 'shop_purchase');
    //   if (XResultUtils.isFailure(mailResult)) {
    //     return XResultUtils.error(`邮件发送失败: ${mailResult.message}`, mailResult.code);
    //   }
    //   return XResultUtils.ok({ type: 'item', itemId, quantity, deliveryMethod: 'mail' });
    // }

    return XResultUtils.ok({ type: 'item', itemId, quantity, deliveryMethod: 'bag' });
  }

  /**
   * 发放球员奖励
   * 基于old项目: 球员购买和相同球员检查逻辑
   * 已适配Result模式：使用callMicroservice方法，返回XResult类型
   */
  private async sendHeroReward(characterId: string, heroResId: number, quantity: number): Promise<XResult<any>> {
    // TODO: 调用Hero服务检查相同球员并创建
    // const result = await this.callMicroservice(
    //   'hero.createFromPurchase',
    //   { characterId, heroResId, quantity, source: 'shop_purchase' }
    // );

    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`创建球员失败: ${result.message}`, result.code);
    // }

    // 基于old项目：如果已有相同球员，会转换为球星卡
    return XResultUtils.ok({ type: 'hero', heroResId, quantity, deliveryMethod: 'direct' });
  }

  /**
   * 获取商品配置
   * 基于old项目: dataApi.allData.data["Shop"][Id]
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async getShopConfig(goodsId: number): Promise<XResult<any>> {
    // 从Shop配置表获取商品配置（基于old项目Shop.json）
    const config = await this.gameConfig.shop.get(goodsId);
    if (!config) {
      this.logger.warn(`商品配置不存在: ${goodsId}`);
      return XResultUtils.ok(null);
    }

    // 转换为old项目兼容的格式（基于old项目Store.prototype.getConfigInfo）
    const shopConfig = {
      id: config.id,                           // 物品序号
      resId: config.parameters,                // 物品id（对应old项目Parameters字段）
      itemType: config.itemType,               // 物品类型 0：物品 1：球员
      priceType: config.priceType,             // 价格类型 1：现金 2：金币
      isDiscount: config.yNDisCount || 0,      // 是否折扣 0不是 1是
      price: config.price,                     // 物品原价
      discountPrice: config.discountPrice || config.price, // 折后价格
      customer: config.customer || 0,          // 是否限购 0不限购 1限购
      customerNum: config.num || 1,            // 限购数量
      purchase: config.purchase || 0,          // 限购类型 0:无 1：个人限购 2：全服限购
      refreshType: config.refreshType || 0,    // 刷新类型 0：不刷新 1：每日刷新 2：每周刷新 3：每月刷新
      isLimitedTime: config.isLimitedTime || 0, // 是否限时 0不限时 1限时
      startTime: config.startTime ? new Date(config.startTime).getTime() : 0,   // 开始时间
      endingTime: config.endingTime ? new Date(config.endingTime).getTime() : 0, // 结束时间
      limitType: config.limitType || 0,        // 限制类型 0：无 1：VIP 2：等级
      limitParameter: config.limitParameter || 0, // 限制类型等级
    };

    this.logger.debug(`获取商品配置成功: ${goodsId}`, shopConfig);
    return XResultUtils.ok(shopConfig);
  }

  /**
   * 触发购买任务
   * 基于old项目: tasks.triggerTask(TARGET_TYPE.NINE)
   * 已适配Result模式：使用callMicroservice方法，返回XResult类型
   */
  private async triggerPurchaseTask(characterId: string, quantity: number): Promise<XResult<void>> {
    // TODO: 调用Activity服务触发购买任务
    // for (let i = 0; i < quantity; i++) {
    //   const result = await this.callMicroservice(
    //     'task.triggerTask',
    //     { characterId, triggerType: 9, arg1: 1 } // TARGET_TYPE.NINE = 9
    //   );
    //   if (XResultUtils.isFailure(result)) {
    //     return XResultUtils.error(`触发购买任务失败: ${result.message}`, result.code);
    //   }
    // }

    this.logger.debug(`触发购买任务: ${characterId}, 数量: ${quantity}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 检查是否为今天
   * 基于old项目: timeUtils.isToday
   */
  private isToday(timestamp: number): boolean {
    const now = new Date();
    const target = new Date(timestamp);

    return now.getFullYear() === target.getFullYear() &&
           now.getMonth() === target.getMonth() &&
           now.getDate() === target.getDate();
  }

  /**
   * 检查是否为同一周
   * 基于old项目: timeUtils.isSameWeek
   */
  private isSameWeek(timestamp: number): boolean {
    const now = new Date();
    const target = new Date(timestamp);

    // 获取周一作为一周的开始
    const getMonday = (date: Date) => {
      const day = date.getDay();
      const diff = date.getDate() - day + (day === 0 ? -6 : 1);
      return new Date(date.setDate(diff));
    };

    const nowMonday = getMonday(new Date(now));
    const targetMonday = getMonday(new Date(target));

    return nowMonday.getTime() === targetMonday.getTime();
  }

  /**
   * 检查是否为同一月
   * 基于old项目: timeUtils.isSameMonth
   */
  private isSameMonth(timestamp: number): boolean {
    const now = new Date();
    const target = new Date(timestamp);

    return now.getFullYear() === target.getFullYear() &&
           now.getMonth() === target.getMonth();
  }

  /**
   * 检查是否在规定时间内
   * 基于old项目: Store.prototype.checkIsInBuyTime
   */
  private checkIsInBuyTime(config: any): number {
    if (config.isLimitedTime === 1) {
      const time = Date.now(); // 当前时间
      if (config.startTime === 0 || config.endingTime === 0) {
        return -1; // TIME_FAIL
      }
      if (time < config.startTime || time > config.endingTime) {
        return -1; // TIME_FAIL
      }
    }
    return 0; // OK
  }

  /**
   * 检查VIP或个人等级是否达标
   * 基于old项目: Store.prototype.checkBuyLimitType
   * 已适配Result模式：使用callMicroservice方法，返回XResult类型
   */
  private async checkBuyLimitType(characterId: string, config: any): Promise<XResult<number>> {
    if (config.limitType === 1) {
      // VIP等级检查
      const characterInfoResult = await this.callMicroservice(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.getInfo',
        { characterId }
      );

      if (XResultUtils.isFailure(characterInfoResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterInfoResult.message}`, characterInfoResult.code);
      }

      const characterInfo = characterInfoResult.data;
      if (characterInfo) {
        const vipLevel = characterInfo.vip || 0;
        if (vipLevel < config.limitParameter) {
          return XResultUtils.error('VIP等级不足', 'LEVEL_REQUIREMENT_NOT_MET');
        }
      }
    } else if (config.limitType === 2) {
      // 玩家等级检查
      const characterInfoResult = await this.callMicroservice(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.getInfo',
        { characterId }
      );

      if (XResultUtils.isFailure(characterInfoResult)) {
        return XResultUtils.error(`获取角色信息失败: ${characterInfoResult.message}`, characterInfoResult.code);
      }

      const characterInfo = characterInfoResult.data;
      if (characterInfo) {
        const level = characterInfo.level || 0;
        if (level < config.limitParameter) {
          return XResultUtils.error('玩家等级不足', 'LEVEL_REQUIREMENT_NOT_MET');
        }
      }
    }

    return XResultUtils.ok(0); // OK
  }

  /**
   * 检查购买数量是否超过购买限制
   * 基于old项目: Store.prototype.checkBuyCustomer
   */
  private checkBuyCustomer(shop: ShopDocument, config: any, goodsId: number, num: number): number {
    // 检查是否限购
    if (config.purchase === 1) {
      if (num > config.customerNum) {
        return -1; // RANGE_FAIL
      }

      // 根据刷新类型去检测数量 1每日 2每周 3每月
      if (config.refreshType === 1) {
        const everyDayBuy = shop.everyDayBuy;
        for (const buyRecord of everyDayBuy) {
          if (goodsId === buyRecord.goodsId) {
            const count = num + buyRecord.purchasedCount;
            if (count > config.customerNum) {
              return -1; // RANGE_FAIL
            }
          }
        }
      } else if (config.refreshType === 2) {
        const weeklyBuy = shop.weeklyBuy;
        for (const buyRecord of weeklyBuy) {
          if (goodsId === buyRecord.goodsId) {
            const count = num + buyRecord.purchasedCount;
            if (count > config.customerNum) {
              return -1; // RANGE_FAIL
            }
          }
        }
      } else if (config.refreshType === 3) {
        const monthlyBuy = shop.monthlyBuy;
        for (const buyRecord of monthlyBuy) {
          if (goodsId === buyRecord.goodsId) {
            const count = num + buyRecord.purchasedCount;
            if (count > config.customerNum) {
              return -1; // RANGE_FAIL
            }
          }
        }
      }
    }
    return 0; // OK
  }

  /**
   * 检查是否足够的钱，并扣钱
   * 基于old项目: Store.prototype.checkIsHaveMoney
   * 已适配Result模式：使用callMicroservice方法，返回XResult类型
   */
  private async checkIsHaveMoney(characterId: string, config: any, num: number): Promise<XResult<number>> {
    let totalCost: number;
    let currencyType: 'cash' | 'gold' | 'energy' | 'worldCoin' | 'chip' | 'integral';

    // 计算总费用（基于old项目逻辑）
    if (config.isDiscount === 1) {
      totalCost = config.discountPrice * num;
    } else {
      totalCost = config.price * num;
    }

    // 确定货币类型（基于old项目priceType字段）
    if (config.priceType === 1) {
      currencyType = 'cash'; // 现金（欧元）
    } else {
      currencyType = 'gold'; // 金币
    }

    // 1. 先获取角色信息检查余额（基于old项目checkResourceIsEnough）
    const characterInfoResult = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'character.getInfo',
      { characterId }
    );

    if (XResultUtils.isFailure(characterInfoResult)) {
      return XResultUtils.error(`获取角色信息失败: ${characterInfoResult.message}`, characterInfoResult.code);
    }

    const characterInfo = characterInfoResult.data;
    if (!characterInfo) {
      this.logger.error(`获取角色信息失败: ${characterId}`);
      return XResultUtils.error('获取角色信息失败', 'CHARACTER_NOT_FOUND');
    }

    const currentBalance = characterInfo[currencyType] || 0;
    if (currentBalance < totalCost) {
      const errorMessage = config.priceType === 1 ? '现金不足' : '金币不足';
      this.logger.warn(`货币不足: ${characterId}, 需要: ${totalCost}, 当前: ${currentBalance}, 类型: ${currencyType}`);
      return XResultUtils.error(errorMessage, 'ECONOMY_INSUFFICIENT_CURRENCY');
    }

    // 2. 扣除货币（基于old项目deductMoney）
    const deductResult = await this.callMicroservice(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      'character.currency.subtract',
      {
        characterId,
        currencyDto: {
          currencyType,
          amount: totalCost,
          reason: 'shop_purchase'
        }
      }
    );

    if (XResultUtils.isFailure(deductResult)) {
      this.logger.error(`扣除货币失败: ${characterId}, 类型: ${currencyType}, 数量: ${totalCost}, 错误: ${deductResult.message}`);
      return XResultUtils.error(`扣除货币失败: ${deductResult.message}`, 'ECONOMY_INSUFFICIENT_CURRENCY');
    }

    this.logger.log(`货币扣除成功: ${characterId}, 类型: ${currencyType}, 数量: ${totalCost}`);
    return XResultUtils.ok(0); // OK
  }

  /**
   * 添加购买数量
   * 基于old项目: Store.prototype.addBuyCount
   */
  private addBuyCount(shop: ShopDocument, config: any, goodsId: number, num: number): void {
    if (config.purchase === 1) {
      // 根据刷新类型添加购买记录
      if (config.refreshType === 1) {
        // 每日刷新
        let found = false;
        for (const buyRecord of shop.everyDayBuy) {
          if (goodsId === buyRecord.goodsId) {
            buyRecord.purchasedCount += num;
            found = true;
            break;
          }
        }
        if (!found) {
          shop.everyDayBuy.push({
            goodsId,
            purchasedCount: num,
            limitCount: config.customerNum,
            resetTime: Date.now(),
          });
        }
      } else if (config.refreshType === 2) {
        // 每周刷新
        let found = false;
        for (const buyRecord of shop.weeklyBuy) {
          if (goodsId === buyRecord.goodsId) {
            buyRecord.purchasedCount += num;
            found = true;
            break;
          }
        }
        if (!found) {
          shop.weeklyBuy.push({
            goodsId,
            purchasedCount: num,
            limitCount: config.customerNum,
            resetTime: Date.now(),
          });
        }
      } else if (config.refreshType === 3) {
        // 每月刷新
        let found = false;
        for (const buyRecord of shop.monthlyBuy) {
          if (goodsId === buyRecord.goodsId) {
            buyRecord.purchasedCount += num;
            found = true;
            break;
          }
        }
        if (!found) {
          shop.monthlyBuy.push({
            goodsId,
            purchasedCount: num,
            limitCount: config.customerNum,
            resetTime: Date.now(),
          });
        }
      }
    }
  }

  /**
   * 发货
   * 基于old项目: Store.prototype.sendItem
   * 已适配Result模式：使用callMicroservice方法，返回XResult类型
   */
  private async sendItem(characterId: string, config: any, num: number): Promise<XResult<number>> {
    if (config.itemType === 0) {
      // 物品类型：调用Character服务添加物品（基于old项目bag.addItem）
      const addItemResult = await this.callMicroservice(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'item.addItem',
        {
          characterId,
          resId: config.resId,
          num: num
        }
      );

      if (XResultUtils.isFailure(addItemResult)) {
        this.logger.error(`添加物品失败: ${config.resId}, 数量: ${num}, 错误: ${addItemResult.message}`);
        // 如果背包满了，发送邮件（基于old项目逻辑）
        if (addItemResult.code === 'BAG_FULL') {
          const mailResult = await this.sendItemByMail(characterId, config.resId, num, 'shop_purchase');
          if (XResultUtils.isFailure(mailResult)) {
            return XResultUtils.error(`邮件发送失败: ${mailResult.message}`, mailResult.code);
          }
          this.logger.log(`背包已满，物品已发送邮件: ${characterId}, 物品: ${config.resId}, 数量: ${num}`);
          return XResultUtils.ok(0); // 邮件发送成功也算发货成功
        }
        // 其他添加物品失败的情况
        return XResultUtils.error(`添加物品失败: ${addItemResult.message}`, 'DELIVERY_FAILED');
      }
    } else if (config.itemType === 1) {
      // 球员类型：调用Hero服务添加球员（基于old项目heros.addHero）
      const addHeroResult = await this.callMicroservice(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'hero.create',
        {
          characterId,
          resId: config.resId,
          serverId: 'server_001', // TODO: 从参数获取
          source: 'shop_purchase'
        }
      );

      if (XResultUtils.isFailure(addHeroResult)) {
        // 检查是否已有相同球员（基于old项目checkHaveSameHero）
        const heroCardResult = await this.checkHaveSameHero(config.resId);
        if (XResultUtils.isFailure(heroCardResult)) {
          return XResultUtils.error(`检查相同球员失败: ${heroCardResult.message}`, heroCardResult.code);
        }

        const heroCard = heroCardResult.data;
        if (heroCard !== 0) {
          // 已有相同球员，转换为球星卡（基于old项目逻辑）
          const addCardResult = await this.callMicroservice(
            MICROSERVICE_NAMES.CHARACTER_SERVICE,
            'item.addItem',
            {
              characterId,
              resId: heroCard,
              num: num
            }
          );

          if (XResultUtils.isFailure(addCardResult)) {
            this.logger.error(`添加球星卡失败: ${heroCard}, 数量: ${num}, 错误: ${addCardResult.message}`);
            return XResultUtils.error(`添加球星卡失败: ${addCardResult.message}`, 'DELIVERY_FAILED');
          }

          this.logger.log(`球员已存在，转换为球星卡: ${characterId}, 球员: ${config.resId}, 球星卡: ${heroCard}`);
        } else {
          this.logger.error(`添加球员失败: ${config.resId}, 数量: ${num}, 错误: ${addHeroResult.message}`);
          return XResultUtils.error(`添加球员失败: ${addHeroResult.message}`, 'DELIVERY_FAILED');
        }
      }
    }

    this.logger.log(`发货成功: 类型${config.itemType}, ID${config.resId}, 数量${num}`);
    return XResultUtils.ok(0); // OK
  }

  /**
   * 获取月卡配置
   * 基于old项目: dataApi.allData.data["ActiveParam"] GroupId=5
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async getMonthCardConfig(cardType: number): Promise<XResult<any>> {
    // 从ActiveParam配置表获取月卡配置（基于old项目逻辑）
    const activeParams = await this.gameConfig.activeParam.getAll();
    if (!activeParams || activeParams.length === 0) {
      this.logger.warn('ActiveParam配置表为空');
      return XResultUtils.ok(null);
    }

    // 查找GroupId为5的月卡配置（基于old项目MonthCard.prototype.getConfig）
    const monthCardConfig = activeParams.find(config => config.groupId === 5);
    if (!monthCardConfig) {
      this.logger.warn('月卡配置不存在（GroupId=5）');
      return XResultUtils.ok(null);
    }

    return XResultUtils.ok({
      id: 5, // GroupId
      days: 30, // 固定30天
      price: monthCardConfig.parameter1 || 0, // 费用
      priceType: 1, // 1-现金
      rewardType1: monthCardConfig.rewardType1,
      reward1: monthCardConfig.reward1,
      num1: monthCardConfig.number1,
      rewardType2: monthCardConfig.rewardType2,
      reward2: monthCardConfig.reward2,
      num2: monthCardConfig.number2,
      description: '月卡',
    });
  }

  /**
   * 检查支付状态
   * 基于old项目支付验证逻辑
   * 已适配Result模式：返回XResult类型，移除try-catch和success字段
   */
  private async checkPaymentStatus(characterId: string, orderId: string): Promise<XResult<any>> {
    // TODO: 实现真实的支付验证逻辑
    // 这里应该调用支付服务验证订单状态
    // const result = await this.callMicroservice(
    //   'payment.verifyOrder',
    //   { characterId, orderId }
    // );
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`支付验证失败: ${result.message}`, result.code);
    // }

    // 暂时返回成功（开发阶段）
    this.logger.debug(`检查支付状态: ${characterId}, 订单: ${orderId}`);
    return XResultUtils.ok({ success: true });
  }

  /**
   * 触发月卡购买相关任务
   * 已适配Result模式：使用callMicroservice方法，返回XResult类型
   */
  private async triggerMonthCardTasks(characterId: string, cardType: number): Promise<XResult<void>> {
    // TODO: 调用Activity服务触发月卡购买任务
    // const result = await this.callMicroservice(
    //   'task.triggerMonthCardTask',
    //   { characterId, cardType }
    // );
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`触发月卡任务失败: ${result.message}`, result.code);
    // }

    this.logger.debug(`触发月卡购买任务: ${characterId}, 卡类型: ${cardType}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 从配置获取限购数量
   * 基于old项目Shop配置表的Num字段
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async getLimitCountFromConfig(goodsId: number, cycle: RefreshCycle): Promise<XResult<number>> {
    const configResult = await this.getShopConfig(goodsId);
    if (XResultUtils.isFailure(configResult)) {
      return XResultUtils.error(`获取商品配置失败: ${configResult.message}`, configResult.code);
    }

    const config = configResult.data;
    if (!config) {
      return XResultUtils.ok(10); // 默认限购数量
    }

    return XResultUtils.ok(config.customerNum || 10);
  }

  /**
   * 检查是否有相同球员
   * 基于old项目: Store.prototype.checkHaveSameHero
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async checkHaveSameHero(resId: number): Promise<XResult<number>> {
    // 从Hero配置表获取对应的球星卡ID（footballer已重命名为hero）
    const heroConfig = await this.gameConfig.hero.get(resId);
    if (!heroConfig) {
      this.logger.warn(`球员配置不存在: ${resId}`);
      return XResultUtils.ok(0);
    }

    return XResultUtils.ok(heroConfig.itemId || 0); // 返回对应的球星卡ID
  }

  /**
   * 通过邮件发送物品
   * 基于old项目: 背包满时发送邮件逻辑
   * 已适配Result模式：使用callMicroservice方法，返回XResult类型
   */
  private async sendItemByMail(characterId: string, resId: number, num: number, reason: string): Promise<XResult<void>> {
    // TODO: 调用邮件服务发送物品
    // const mailResult = await this.callMicroservice(
    //   'mail.sendSystemMail',
    //   {
    //     characterId,
    //     title: '商店购买奖励',
    //     content: '您购买的物品已通过邮件发送',
    //     attachments: [{
    //       type: 'item',
    //       resId: resId,
    //       quantity: num
    //     }],
    //     reason
    //   }
    // );
    // if (XResultUtils.isFailure(mailResult)) {
    //   return XResultUtils.error(`邮件发送失败: ${mailResult.message}`, mailResult.code);
    // }

    this.logger.log(`邮件发送成功: ${characterId}, 物品: ${resId}, 数量: ${num}`);
    return XResultUtils.ok(undefined);
  }

}
