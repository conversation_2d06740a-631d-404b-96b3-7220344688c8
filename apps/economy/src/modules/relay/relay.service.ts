/**
 * 联赛转播服务 - 已适配Result模式
 * 基于old项目relay.js业务逻辑迁移
 *
 * 核心功能：
 * - 联赛转播购买和管理
 * - 积分系统和商城兑换
 * - 奖励领取和发放
 * - 转播状态查询
 *
 * Result模式适配：
 * - 继承BaseService基类，获得统一的业务操作框架
 * - 所有public方法返回XResult<T>类型
 * - 使用executeBusinessOperation包装关键业务操作
 * - 标准化的错误处理和微服务调用
 * - 移除所有try-catch异常处理和多余的success字段
 */

import { Injectable, Logger } from '@nestjs/common';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';
import { RelayRepository } from '@economy/common/repositories/relay.repository';
import { XResult, XResultUtils, RepositoryResultWrapper } from '@libs/common/types/result.type';
import { BaseService } from '@libs/common/service/base-service';
import { MicroserviceClientService } from '@libs/service-mesh';

@Injectable()
export class RelayService extends BaseService {

  constructor(
    private readonly relayRepository: RelayRepository,
    microserviceClient: MicroserviceClientService, // 传递给BaseService
  ) {
    // 调用BaseService构造函数，传入服务名称和微服务客户端
    super('RelayService', microserviceClient);
  }

  /**
   * 获取联赛转播信息
   * 已适配Result模式：直接返回XResult，无需executeBusinessOperation包装
   */
  async getRelayInfo(uid: string, serverId: string): Promise<XResult<any>> {
    const relayResult = await this.relayRepository.getOrCreateRelay(uid, serverId);
    if (XResultUtils.isFailure(relayResult)) {
      return XResultUtils.error(`获取联赛转播信息失败: ${relayResult.message}`, relayResult.code);
    }

    const relay = relayResult.data;

    return XResultUtils.ok({
      uid,
      isJoin: relay.isJoin,
      integral: relay.integral,
      totalIntegral: relay.totalIntegral,
      joinTime: relay.joinTime,
      expireTime: relay.expireTime,
      isExpired: relay.isExpired(),
      awardList: relay.awardList,
      availableAwards: relay.getAvailableAwards(),
      totalAwardsReceived: relay.totalAwardsReceived,
      totalConvertCount: relay.totalConvertCount,
      totalIntegralSpent: relay.totalIntegralSpent,
      seasonId: relay.seasonId,
      weekId: relay.weekId,
    });
  }

  /**
   * 购买联赛转播
   * 对应old项目中的buyRelay方法
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async buyRelay(uid: string, serverId: string): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const relayResult = await this.relayRepository.getOrCreateRelay(uid, serverId);
      if (XResultUtils.isFailure(relayResult)) {
        return XResultUtils.error(`获取联赛转播信息失败: ${relayResult.message}`, relayResult.code);
      }

      const relay = relayResult.data;

      // 检查是否已加入
      if (relay.isJoin === 1) {
        return XResultUtils.error(ErrorMessages[ErrorCode.RELAY_ALREADY_JOINED], 'RELAY_ALREADY_JOINED');
      }

      // 检查是否过期
      if (relay.isExpired()) {
        return XResultUtils.error(ErrorMessages[ErrorCode.RELAY_EXPIRED], 'RELAY_EXPIRED');
      }

      // 检查玩家资源是否足够（钻石等）
      const costResult = await this.getRelayCost();
      if (XResultUtils.isFailure(costResult)) {
        return XResultUtils.error(`获取转播费用失败: ${costResult.message}`, costResult.code);
      }

      const cost = costResult.data;

      const playerDiamondResult = await this.getPlayerDiamond(uid);
      if (XResultUtils.isFailure(playerDiamondResult)) {
        return XResultUtils.error(`获取玩家钻石失败: ${playerDiamondResult.message}`, playerDiamondResult.code);
      }

      const playerDiamond = playerDiamondResult.data;

      if (playerDiamond < cost) {
        return XResultUtils.error(ErrorMessages[ErrorCode.DIAMOND_NOT_ENOUGH], 'DIAMOND_NOT_ENOUGH');
      }

      // 扣除钻石
      const deductResult = await this.deductPlayerDiamond(uid, cost);
      if (XResultUtils.isFailure(deductResult)) {
        return XResultUtils.error(`扣除钻石失败: ${deductResult.message}`, deductResult.code);
      }

      // 加入转播
      relay.isJoin = 1;
      relay.joinTime = Date.now();

      // 设置过期时间（7天）
      relay.expireTime = Date.now() + (7 * 24 * 60 * 60 * 1000);

      // 初始化奖励列表
      const awardListResult = await this.initRelayAwards();
      if (XResultUtils.isFailure(awardListResult)) {
        return XResultUtils.error(`初始化奖励列表失败: ${awardListResult.message}`, awardListResult.code);
      }

      relay.awardList = awardListResult.data;

      // 这里使用RepositoryResultWrapper.wrap是正确的，因为relay.save()是mongoose原生方法
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await relay.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存转播状态失败: ${saveResult.message}`, saveResult.code);
      }

      this.logger.log(`购买联赛转播成功: ${uid}, 费用: ${cost}`);

      return XResultUtils.ok({
        cost,
        joinTime: relay.joinTime,
        expireTime: relay.expireTime,
        awardList: relay.awardList,
        operationTime: Date.now(),
      });
    }, {
      reason: 'buy_relay',
      metadata: { uid, serverId }
    });
  }

  /**
   * 商城兑换
   * 对应old项目中的convertibility方法
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async convertibility(uid: string, serverId: string, id: number): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      if (id < 1) {
        return XResultUtils.error(ErrorMessages[ErrorCode.INVALID_PARAMETER], 'INVALID_PARAMETER');
      }

      const relayResult = await this.relayRepository.getOrCreateRelay(uid, serverId);
      if (XResultUtils.isFailure(relayResult)) {
        return XResultUtils.error(`获取联赛转播信息失败: ${relayResult.message}`, relayResult.code);
      }

      const relay = relayResult.data;

      // 检查是否已加入转播
      if (relay.isJoin === 0) {
        return XResultUtils.error(ErrorMessages[ErrorCode.RELAY_NOT_JOINED], 'RELAY_NOT_JOINED');
      }

      // 检查是否过期
      if (relay.isExpired()) {
        return XResultUtils.error(ErrorMessages[ErrorCode.RELAY_EXPIRED], 'RELAY_EXPIRED');
      }

      // 获取兑换配置
      const convertConfigResult = await this.getConvertConfig(id);
      if (XResultUtils.isFailure(convertConfigResult)) {
        return XResultUtils.error(`获取兑换配置失败: ${convertConfigResult.message}`, convertConfigResult.code);
      }

      const convertConfig = convertConfigResult.data;
      if (!convertConfig) {
        return XResultUtils.error(ErrorMessages[ErrorCode.EXCHANGE_CONFIG_NOT_FOUND], 'EXCHANGE_CONFIG_NOT_FOUND');
      }

      // 检查积分是否足够
      if (!relay.canConvertItem(convertConfig.ItemId, convertConfig.Price)) {
        return XResultUtils.error(ErrorMessages[ErrorCode.INTEGRAL_NOT_ENOUGH], 'INTEGRAL_NOT_ENOUGH');
      }

      // 给予物品
      const addItemResult = await this.addPlayerItem(uid, convertConfig.ItemId, convertConfig.Num);
      if (XResultUtils.isFailure(addItemResult)) {
        return XResultUtils.error(`给予物品失败: ${addItemResult.message}`, addItemResult.code);
      }

      // 记录兑换
      relay.addConvertRecord(id, convertConfig.ItemId, convertConfig.Price, convertConfig.Num);

      // 这里使用RepositoryResultWrapper.wrap是正确的，因为relay.save()是mongoose原生方法
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await relay.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存转播状态失败: ${saveResult.message}`, saveResult.code);
      }

      this.logger.log(`商城兑换成功: ${uid}, 兑换ID: ${id}, 物品: ${convertConfig.ItemId}, 数量: ${convertConfig.Num}, 积分: ${convertConfig.Price}`);

      return XResultUtils.ok({
        convertId: id,
        itemId: convertConfig.ItemId,
        itemNum: convertConfig.Num,
        integralCost: convertConfig.Price,
        remainingIntegral: relay.integral,
        operationTime: Date.now(),
      });
    }, {
      reason: 'convertibility',
      metadata: { uid, serverId, id }
    });
  }

  /**
   * 领取奖励
   * 对应old项目中的receiveAward方法
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async receiveAward(uid: string, serverId: string, awardId: number): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      if (awardId < 1) {
        return XResultUtils.error(ErrorMessages[ErrorCode.INVALID_PARAMETER], 'INVALID_PARAMETER');
      }

      const relayResult = await this.relayRepository.getOrCreateRelay(uid, serverId);
      if (XResultUtils.isFailure(relayResult)) {
        return XResultUtils.error(`获取联赛转播信息失败: ${relayResult.message}`, relayResult.code);
      }

      const relay = relayResult.data;

      // 检查是否已加入转播
      if (relay.isJoin === 0) {
        return XResultUtils.error(ErrorMessages[ErrorCode.RELAY_NOT_JOINED], 'RELAY_NOT_JOINED');
      }

      // 检查奖励是否可领取
      if (!relay.canReceiveAward(awardId)) {
        return XResultUtils.error(ErrorMessages[ErrorCode.AWARD_NOT_AVAILABLE], 'AWARD_NOT_AVAILABLE');
      }

      const award = relay.awardList.find(a => a.id === awardId);
      if (!award) {
        return XResultUtils.error(ErrorMessages[ErrorCode.AWARD_NOT_FOUND], 'AWARD_NOT_FOUND');
      }

      // 发放奖励
      const giveRewardsResult = await this.giveRewards(uid, award.rewards);
      if (XResultUtils.isFailure(giveRewardsResult)) {
        return XResultUtils.error(`发放奖励失败: ${giveRewardsResult.message}`, giveRewardsResult.code);
      }

      // 标记为已领取
      const success = relay.receiveAward(awardId);
      if (!success) {
        return XResultUtils.error(ErrorMessages[ErrorCode.AWARD_RECEIVE_FAILED], 'AWARD_RECEIVE_FAILED');
      }

      // 这里使用RepositoryResultWrapper.wrap是正确的，因为relay.save()是mongoose原生方法
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await relay.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存转播状态失败: ${saveResult.message}`, saveResult.code);
      }

      this.logger.log(`领取转播奖励成功: ${uid}, 奖励ID: ${awardId}`);

      return XResultUtils.ok({
        awardId,
        rewards: award.rewards,
        operationTime: Date.now(),
      });
    }, {
      reason: 'receive_award',
      metadata: { uid, serverId, awardId }
    });
  }

  /**
   * 一键领取所有奖励
   * 对应old项目中的receiveAllAward方法
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async receiveAllAward(uid: string, serverId: string): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      const relayResult = await this.relayRepository.getOrCreateRelay(uid, serverId);
      if (XResultUtils.isFailure(relayResult)) {
        return XResultUtils.error(`获取联赛转播信息失败: ${relayResult.message}`, relayResult.code);
      }

      const relay = relayResult.data;

      // 检查是否已加入转播
      if (relay.isJoin === 0) {
        return XResultUtils.error(ErrorMessages[ErrorCode.RELAY_NOT_JOINED], 'RELAY_NOT_JOINED');
      }

      const availableAwards = relay.getAvailableAwards();
      if (availableAwards.length === 0) {
        return XResultUtils.error(ErrorMessages[ErrorCode.NO_AWARDS_AVAILABLE], 'NO_AWARDS_AVAILABLE');
      }

      const receivedAwards = [];
      let totalRewards = [];

      for (const award of availableAwards) {
        // 发放奖励
        const giveRewardsResult = await this.giveRewards(uid, award.rewards);
        if (XResultUtils.isFailure(giveRewardsResult)) {
          this.logger.warn(`发放奖励失败: ${giveRewardsResult.message}, 奖励ID: ${award.id}`);
          continue; // 继续处理其他奖励
        }

        // 标记为已领取
        relay.receiveAward(award.id);

        receivedAwards.push({
          awardId: award.id,
          rewards: award.rewards,
        });

        totalRewards = totalRewards.concat(award.rewards);
      }

      // 这里使用RepositoryResultWrapper.wrap是正确的，因为relay.save()是mongoose原生方法
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await relay.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存转播状态失败: ${saveResult.message}`, saveResult.code);
      }

      this.logger.log(`一键领取转播奖励成功: ${uid}, 领取数量: ${receivedAwards.length}`);

      return XResultUtils.ok({
        receivedCount: receivedAwards.length,
        receivedAwards,
        totalRewards,
        operationTime: Date.now(),
      });
    }, {
      reason: 'receive_all_award',
      metadata: { uid, serverId }
    });
  }

  /**
   * 添加积分
   * 用于游戏内其他系统调用
   * 已适配Result模式：直接返回XResult，无需executeBusinessOperation包装
   */
  async addIntegral(uid: string, serverId: string, amount: number): Promise<XResult<any>> {
    const relayResult = await this.relayRepository.getOrCreateRelay(uid, serverId);
    if (XResultUtils.isFailure(relayResult)) {
      return XResultUtils.error(`获取联赛转播信息失败: ${relayResult.message}`, relayResult.code);
    }

    const relay = relayResult.data;

    // 只有加入转播的玩家才能获得积分
    if (relay.isJoin === 0 || relay.isExpired()) {
      return XResultUtils.error('未加入转播或已过期', 'RELAY_NOT_JOINED_OR_EXPIRED');
    }

    relay.addIntegral(amount);

    // 这里使用RepositoryResultWrapper.wrap是正确的，因为relay.save()是mongoose原生方法
    const saveResult = await RepositoryResultWrapper.wrap(async () => {
      return await relay.save();
    });
    if (XResultUtils.isFailure(saveResult)) {
      return XResultUtils.error(`保存转播状态失败: ${saveResult.message}`, saveResult.code);
    }

    this.logger.log(`添加转播积分: ${uid}, 数量: ${amount}, 总积分: ${relay.integral}`);

    return XResultUtils.ok({
      addedIntegral: amount,
      totalIntegral: relay.integral,
      operationTime: Date.now(),
    });
  }

  // ==================== 私有方法 ====================

  /**
   * 获取转播费用
   * 已适配Result模式：返回XResult类型
   */
  private async getRelayCost(): Promise<XResult<number>> {
    // TODO: 从配置表获取
    return XResultUtils.ok(100); // 默认100钻石
  }

  /**
   * 获取玩家钻石数量
   * 已适配Result模式：使用callMicroservice方法，返回XResult类型
   */
  private async getPlayerDiamond(uid: string): Promise<XResult<number>> {
    // TODO: 调用Character服务获取玩家钻石数量
    // const result = await this.callMicroservice(
    //   'character.getCurrency',
    //   { characterId: uid, currencyType: 'diamond' }
    // );
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`获取玩家钻石失败: ${result.message}`, result.code);
    // }
    // return XResultUtils.ok(result.data.amount);

    return XResultUtils.ok(1000); // 模拟数据
  }

  /**
   * 扣除玩家钻石
   * 已适配Result模式：使用callMicroservice方法，返回XResult类型
   */
  private async deductPlayerDiamond(uid: string, amount: number): Promise<XResult<void>> {
    // TODO: 调用Character服务扣除玩家钻石
    // const result = await this.callMicroservice(
    //   'character.currency.subtract',
    //   {
    //     characterId: uid,
    //     currencyDto: {
    //       currencyType: 'diamond',
    //       amount: amount,
    //       reason: 'relay_purchase'
    //     }
    //   }
    // );
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`扣除玩家钻石失败: ${result.message}`, result.code);
    // }

    this.logger.log(`扣除玩家钻石: ${uid}, 数量: ${amount}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 给予玩家物品
   * 已适配Result模式：使用callMicroservice方法，返回XResult类型
   */
  private async addPlayerItem(uid: string, itemId: number, num: number): Promise<XResult<void>> {
    // TODO: 调用Character服务给予玩家物品
    // const result = await this.callMicroservice(
    //   'item.addItem',
    //   { characterId: uid, resId: itemId, num: num, source: 'relay_exchange' }
    // );
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`给予玩家物品失败: ${result.message}`, result.code);
    // }

    this.logger.log(`给予玩家物品: ${uid}, 物品: ${itemId}, 数量: ${num}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 发放奖励
   * 已适配Result模式：使用callMicroservice方法，返回XResult类型
   */
  private async giveRewards(uid: string, rewards: any): Promise<XResult<void>> {
    // TODO: 根据奖励类型发放不同奖励
    // if (rewards.itemType === 'ITEM') {
    //   const result = await this.callMicroservice(
    //     'item.addItem',
    //     { characterId: uid, resId: rewards.resId, num: rewards.num, source: 'relay_reward' }
    //   );
    //   if (XResultUtils.isFailure(result)) {
    //     return XResultUtils.error(`发放物品奖励失败: ${result.message}`, result.code);
    //   }
    // } else if (rewards.itemType === 'CURRENCY') {
    //   const result = await this.callMicroservice(
    //     'character.currency.add',
    //     {
    //       characterId: uid,
    //       currencyDto: {
    //         currencyType: rewards.currencyType,
    //         amount: rewards.amount,
    //         reason: 'relay_reward'
    //       }
    //     }
    //   );
    //   if (XResultUtils.isFailure(result)) {
    //     return XResultUtils.error(`发放货币奖励失败: ${result.message}`, result.code);
    //   }
    // }

    this.logger.log(`发放转播奖励: ${uid}, 奖励: ${JSON.stringify(rewards)}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 初始化转播奖励
   * 已适配Result模式：返回XResult类型
   */
  private async initRelayAwards(): Promise<XResult<any[]>> {
    // TODO: 从配置表获取转播奖励配置
    return XResultUtils.ok([
      { id: 1, type: 1, integral: 100, isReceive: 0, rewards: { itemType: 'ITEM', resId: 3001, num: 1 }, receiveTime: 0 },
      { id: 2, type: 1, integral: 300, isReceive: 0, rewards: { itemType: 'ITEM', resId: 3002, num: 1 }, receiveTime: 0 },
      { id: 3, type: 1, integral: 500, isReceive: 0, rewards: { itemType: 'ITEM', resId: 3003, num: 1 }, receiveTime: 0 },
    ]);
  }

  /**
   * 获取兑换配置
   * 已适配Result模式：返回XResult类型
   */
  private async getConvertConfig(id: number): Promise<XResult<any>> {
    // TODO: 从配置表获取兑换配置
    return XResultUtils.ok({
      ItemId: 4001,
      Num: 1,
      Price: 50,
    });
  }
}
