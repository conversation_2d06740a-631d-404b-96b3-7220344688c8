import { Injectable, Logger } from '@nestjs/common';
import { BaseService } from '@libs/common/service/base-service';
import { XResult, XResultUtils } from '@libs/common/types/result.type';
import { BeliefStoreRepository } from '../../common/repositories/belief-store.repository';
import { GameConfigFacade } from '@libs/game-config';
import { MicroserviceClientService } from '@libs/service-mesh';

/**
 * 信仰商店业务逻辑层
 * 基于old项目的完整信仰商店系统实现
 * 
 * 🎯 核心功能：
 * - 信仰商店定价系统（董事长定价）
 * - 信仰商店购买系统
 * - 商品刷新和限购管理
 * - 定价阶段和开售阶段切换
 * - 信仰商品折扣机制
 * 
 * 🔧 业务规则：
 * - 董事长可为3个不同信仰设置价格档位（50%、200%、300%）
 * - 本信仰商品享受半价优惠（除非董事长特别设置）
 * - 支持个人限购和全服限购
 * - 信仰等级限制商品购买
 * - 双阶段循环：定价→开售→定价...
 * 
 * old项目对应：
 * - beliefService.js中的商店相关方法
 * - FaithStore.json配置表
 * - 定价和购买的完整业务流程
 */
@Injectable()
export class BeliefStoreService extends BaseService {
  protected readonly microserviceClient: MicroserviceClientService;

  constructor(
    private readonly beliefStoreRepository: BeliefStoreRepository,
    private readonly gameConfig: GameConfigFacade,
    microserviceClient: MicroserviceClientService,
  ) {
    super('BeliefStoreService');
    this.microserviceClient = microserviceClient;
  }

  /**
   * 获取信仰商店信息
   */
  async getBeliefStoreInfo(dto: { characterId?: string; serverId?: string }): Promise<XResult<any>> {
    this.logger.log(`获取信仰商店信息: ${dto.characterId}`);

    try {
      // 1. 获取商店状态
      const storeResult = await this.beliefStoreRepository.getCurrentStore();
      if (XResultUtils.isFailure(storeResult)) {
        return XResultUtils.error(`获取商店状态失败: ${storeResult.message}`, storeResult.code);
      }

      const store = storeResult.data;
      if (!store) {
        // 初始化商店
        const initResult = await this.initializeBeliefStore();
        if (XResultUtils.isFailure(initResult)) {
          return XResultUtils.error(`初始化商店失败: ${initResult.message}`, initResult.code);
        }
        return await this.getBeliefStoreInfo(dto);
      }

      // 2. 获取玩家信仰信息
      const beliefResult = await this.microserviceClient.call('character', 'belief.getMyBelief', {
        characterId: dto.characterId,
        serverId: dto.serverId
      });

      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取玩家信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const playerBelief = beliefResult.data;

      // 3. 计算商品价格（考虑玩家信仰折扣）
      const itemsWithPrices = await this.calculateItemPrices(store.itemList, playerBelief?.beliefId);

      // 4. 计算阶段剩余时间
      const phaseTimeLeft = this.calculatePhaseTimeLeft(store);

      return XResultUtils.ok({
        phase: store.phase,
        shopPeriod: store.shopPeriod,
        phaseTimeLeft,
        itemList: itemsWithPrices,
        buyNum: store.buyNum,
        playerBelief: playerBelief ? {
          beliefId: playerBelief.beliefId,
          beliefName: playerBelief.name,
          beliefNum: playerBelief.myContribution || 0
        } : null
      });

    } catch (error) {
      this.logger.error(`获取信仰商店信息失败: ${error.message}`, error.stack);
      return XResultUtils.error(`获取信仰商店信息失败: ${error.message}`, 'GET_STORE_INFO_ERROR');
    }
  }

  /**
   * 董事长设置商品定价
   */
  async setBeliefStorePrice(dto: {
    characterId?: string;
    serverId?: string;
    itemId?: number;
    priceList?: Array<{ beliefId: number; price: 50 | 200 | 300 }>;
  }): Promise<XResult<any>> {
    this.logger.log(`设置商品定价: ${dto.characterId}, 商品${dto.itemId}`);

    try {
      // 1. 验证玩家是否为董事长
      const beliefResult = await this.microserviceClient.call('character', 'belief.getMyBelief', {
        characterId: dto.characterId,
        serverId: dto.serverId
      });

      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取玩家信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const playerBelief = beliefResult.data;
      if (!playerBelief || playerBelief.myPosition !== 1) {
        return XResultUtils.error('只有董事长可以设置商品定价', 'PERMISSION_DENIED');
      }

      // 2. 验证商店是否处于定价阶段
      const storeResult = await this.beliefStoreRepository.getCurrentStore();
      if (XResultUtils.isFailure(storeResult)) {
        return XResultUtils.error(`获取商店状态失败: ${storeResult.message}`, storeResult.code);
      }

      const store = storeResult.data;
      if (!store || store.shopPeriod !== 1) {
        return XResultUtils.error('当前不是定价阶段，无法设置价格', 'INVALID_PHASE');
      }

      // 3. 验证定价规则
      const validationResult = this.validatePricing(dto.priceList);
      if (XResultUtils.isFailure(validationResult)) {
        return validationResult;
      }

      // 4. 转换定价数据格式
      const pricingList = dto.priceList.map(p => ({
        selfId: playerBelief.beliefId,
        beliefId: p.beliefId,
        price: p.price
      }));

      // 5. 更新商品定价
      const updateResult = await this.beliefStoreRepository.updateItemPricing(dto.itemId, pricingList);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新商品定价失败: ${updateResult.message}`, updateResult.code);
      }

      // 6. 记录定价历史
      await this.beliefStoreRepository.addPricingRecord(dto.itemId, playerBelief.beliefId, pricingList);

      return XResultUtils.ok({
        itemId: dto.itemId,
        priceList: dto.priceList,
        setTime: Date.now()
      });

    } catch (error) {
      this.logger.error(`设置商品定价失败: ${error.message}`, error.stack);
      return XResultUtils.error(`设置商品定价失败: ${error.message}`, 'SET_PRICE_ERROR');
    }
  }

  /**
   * 购买信仰商店商品
   */
  async buyBeliefStoreItem(dto: {
    characterId?: string;
    serverId?: string;
    itemId?: number;
    quantity?: number;
    beliefId?: number;
  }): Promise<XResult<any>> {
    this.logger.log(`购买信仰商店商品: ${dto.characterId}, 商品${dto.itemId}, 数量${dto.quantity}`);

    try {
      // 1. 验证商店是否处于开售阶段
      const storeResult = await this.beliefStoreRepository.getCurrentStore();
      if (XResultUtils.isFailure(storeResult)) {
        return XResultUtils.error(`获取商店状态失败: ${storeResult.message}`, storeResult.code);
      }

      const store = storeResult.data;
      if (!store || store.shopPeriod !== 2) {
        return XResultUtils.error('当前不是开售阶段，无法购买商品', 'INVALID_PHASE');
      }

      // 2. 获取玩家信仰信息
      const beliefResult = await this.microserviceClient.call('character', 'belief.getMyBelief', {
        characterId: dto.characterId,
        serverId: dto.serverId
      });

      if (XResultUtils.isFailure(beliefResult)) {
        return XResultUtils.error(`获取玩家信仰信息失败: ${beliefResult.message}`, beliefResult.code);
      }

      const playerBelief = beliefResult.data;
      if (!playerBelief) {
        return XResultUtils.error('玩家未加入任何信仰，无法购买商品', 'NO_BELIEF');
      }

      // 3. 验证购买条件
      const validationResult = await this.validatePurchase(dto, store, playerBelief);
      if (XResultUtils.isFailure(validationResult)) {
        return validationResult;
      }

      // 4. 计算购买价格
      const priceResult = await this.calculatePurchasePrice(dto, store, playerBelief);
      if (XResultUtils.isFailure(priceResult)) {
        return priceResult;
      }

      const { totalCost, discount } = priceResult.data;

      // 5. 扣除信仰积分
      const deductResult = await this.microserviceClient.call('character', 'character.deductCurrency', {
        characterId: dto.characterId,
        serverId: dto.serverId,
        currencyType: 'beliefNum',
        amount: totalCost,
        reason: 'belief_store_purchase'
      });

      if (XResultUtils.isFailure(deductResult)) {
        return XResultUtils.error(`扣除信仰积分失败: ${deductResult.message}`, deductResult.code);
      }

      // 6. 发放商品
      const itemResult = await this.microserviceClient.call('character', 'inventory.addItem', {
        characterId: dto.characterId,
        serverId: dto.serverId,
        itemId: dto.itemId,
        quantity: dto.quantity,
        reason: 'belief_store_purchase'
      });

      if (XResultUtils.isFailure(itemResult)) {
        // 购买失败，退还信仰积分
        await this.microserviceClient.call('character', 'character.addCurrency', {
          characterId: dto.characterId,
          serverId: dto.serverId,
          currencyType: 'beliefNum',
          amount: totalCost,
          reason: 'belief_store_refund'
        });
        return XResultUtils.error(`发放商品失败: ${itemResult.message}`, itemResult.code);
      }

      // 7. 更新购买记录
      await this.beliefStoreRepository.addPurchaseRecord(
        dto.characterId,
        playerBelief.beliefId,
        dto.itemId,
        dto.quantity,
        totalCost
      );

      // 8. 更新商品购买统计
      await this.beliefStoreRepository.updateItemPurchaseCount(dto.itemId, dto.quantity);

      return XResultUtils.ok({
        itemId: dto.itemId,
        quantity: dto.quantity,
        totalCost,
        discount,
        beliefNumLeft: playerBelief.myContribution - totalCost
      });

    } catch (error) {
      this.logger.error(`购买信仰商店商品失败: ${error.message}`, error.stack);
      return XResultUtils.error(`购买信仰商店商品失败: ${error.message}`, 'BUY_ITEM_ERROR');
    }
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 初始化信仰商店
   */
  private async initializeBeliefStore(): Promise<XResult<any>> {
    try {
      const storeData = {
        shopPeriod: 1, // 从定价阶段开始
        phase: 1,
        itemList: [],
        pricingInfo: [],
        buyInfo: [],
        buyUidMap: new Map(),
        buyNum: 0,
        lastUpdateTime: Date.now(),
        phaseStartTime: Date.now(),
        phaseDuration: 24 * 60 * 60 * 1000 // 24小时
      };

      return await this.beliefStoreRepository.createOne(storeData);
    } catch (error) {
      return XResultUtils.error(`初始化信仰商店失败: ${error.message}`, 'INIT_STORE_ERROR');
    }
  }

  /**
   * 计算商品价格（考虑信仰折扣）
   * 基于old项目: BeliefShopPurchase中的价格计算逻辑
   */
  private async calculateItemPrices(itemList: any[], playerBeliefId?: number): Promise<any[]> {
    const faithStoreConfig = await this.gameConfig.faithStore.getAll();

    return await Promise.all(itemList.map(async item => {
      const config = faithStoreConfig.find(c => c.parameters === item.itemId);
      if (!config) {
        return { ...item, finalPrice: 0, canPurchase: false };
      }

      let finalPrice = config.price;
      let discount = 0;
      let isSet = false;

      // 1. 检查董事长定价
      const pricing = item.itemList.find((p: any) => p.beliefId === playerBeliefId);
      if (pricing) {
        finalPrice = Math.floor(config.price * pricing.price / 100);
        discount = 100 - pricing.price;
        isSet = true;
      }

      // 2. 本信仰商品半价优惠（如果没有董事长特别设置）
      if (await this.isBeliefItem(item.itemId, playerBeliefId) && !isSet) {
        finalPrice = Math.floor(finalPrice / 2);
        discount = 50;
      }

      return {
        ...item,
        basePrice: config.price,
        finalPrice,
        discount,
        canPurchase: true,
        beliefLevel: config.lv || 0,
        customer: config.customer || 0,
        purchase: config.purchase || 0
      };
    }));
  }

  /**
   * 判断是否为信仰商品
   * 基于old项目: isBeliefItem方法
   */
  private async isBeliefItem(itemId: number, beliefId?: number): Promise<boolean> {
    if (!beliefId) return false;

    // 根据PageSign判断商品所属信仰
    // PageSign 10-25 对应信仰ID 1-16
    const faithStoreConfig = await this.gameConfig.faithStore.get(itemId);
    if (!faithStoreConfig) return false;

    const itemBeliefId = faithStoreConfig.pageSign - 9;
    return itemBeliefId === beliefId && itemBeliefId > 0 && itemBeliefId <= 16;
  }

  /**
   * 计算阶段剩余时间
   */
  private calculatePhaseTimeLeft(store: any): number {
    const elapsed = Date.now() - store.phaseStartTime;
    const remaining = store.phaseDuration - elapsed;
    return Math.max(0, remaining);
  }

  /**
   * 验证定价规则
   */
  private validatePricing(priceList: Array<{ beliefId: number; price: 50 | 200 | 300 }>): XResult<void> {
    // 1. 检查是否设置了3个不同的信仰
    if (priceList.length !== 3) {
      return XResultUtils.error('必须为3个不同的信仰设置价格', 'INVALID_PRICE_COUNT');
    }

    // 2. 检查信仰ID是否重复
    const beliefIds = priceList.map(p => p.beliefId);
    const uniqueBeliefIds = new Set(beliefIds);
    if (uniqueBeliefIds.size !== 3) {
      return XResultUtils.error('不能为同一个信仰设置多个价格', 'DUPLICATE_BELIEF');
    }

    // 3. 检查价格是否重复
    const prices = priceList.map(p => p.price);
    const uniquePrices = new Set(prices);
    if (uniquePrices.size !== 3) {
      return XResultUtils.error('不能设置重复的价格档位', 'DUPLICATE_PRICE');
    }

    // 4. 检查价格档位是否有效
    const validPrices = [50, 200, 300];
    for (const price of prices) {
      if (!validPrices.includes(price)) {
        return XResultUtils.error('价格档位只能是50%、200%、300%', 'INVALID_PRICE_TIER');
      }
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 验证购买条件
   * 基于old项目: BeliefShopPurchase中的验证逻辑
   */
  private async validatePurchase(dto: any, store: any, playerBelief: any): Promise<XResult<void>> {
    try {
      // 1. 检查商品配置是否存在
      const faithStoreConfig = await this.gameConfig.faithStore.get(dto.itemId);
      if (!faithStoreConfig) {
        return XResultUtils.error('商品不存在', 'ITEM_NOT_FOUND');
      }

      // 2. 检查是否在开售阶段（公共商品除外）
      const isPub = faithStoreConfig.pageSign === 16; // PageSign=16为公共商品
      if (store.shopPeriod !== 2 && !isPub) {
        return XResultUtils.error('未到开售阶段', 'NOT_SALE_PHASE');
      }

      // 3. 检查信仰等级限制
      let requiredBeliefLevel = faithStoreConfig.lv || 0;
      let targetBeliefId = dto.beliefId;

      // 如果是特定信仰商品，检查该信仰的等级
      if (faithStoreConfig.pageSign >= 10 && faithStoreConfig.pageSign <= 25) {
        targetBeliefId = faithStoreConfig.pageSign - 9;
      }

      // 获取目标信仰信息
      const targetBeliefResult = await this.microserviceClient.call('character', 'belief.getInfo', {
        beliefId: targetBeliefId,
        serverId: dto.serverId
      });

      if (XResultUtils.isFailure(targetBeliefResult)) {
        return XResultUtils.error('获取信仰信息失败', 'GET_BELIEF_INFO_ERROR');
      }

      const targetBelief = targetBeliefResult.data;
      if (requiredBeliefLevel > (targetBelief?.level || 0)) {
        return XResultUtils.error('信仰等级不足', 'BELIEF_LEVEL_INSUFFICIENT');
      }

      // 4. 检查全服限购
      const storeItem = store.itemList.find((item: any) => item.itemId === dto.itemId);
      const globalPurchased = storeItem?.num || 0;
      const globalLimit = faithStoreConfig.customer || 0;

      if (globalLimit > 0 && globalPurchased + dto.quantity > globalLimit) {
        return XResultUtils.error('全服限购数量不足', 'GLOBAL_PURCHASE_LIMIT_EXCEEDED');
      }

      // 5. 检查个人限购
      const playerPurchases = store.buyUidMap.get(dto.characterId) || [];
      const playerPurchased = playerPurchases.find((p: any) => p.itemId === dto.itemId)?.num || 0;
      const personalLimit = faithStoreConfig.purchase || 0;

      if (personalLimit > 0 && playerPurchased + dto.quantity > personalLimit) {
        return XResultUtils.error('个人限购数量不足', 'PERSONAL_PURCHASE_LIMIT_EXCEEDED');
      }

      return XResultUtils.ok(undefined);

    } catch (error) {
      return XResultUtils.error(`验证购买条件失败: ${error.message}`, 'VALIDATE_PURCHASE_ERROR');
    }
  }

  /**
   * 计算购买价格
   * 基于old项目: BeliefShopPurchase中的价格计算逻辑
   */
  private async calculatePurchasePrice(dto: any, store: any, playerBelief: any): Promise<XResult<{ totalCost: number; discount: number }>> {
    try {
      // 1. 获取商品配置
      const faithStoreConfig = await this.gameConfig.faithStore.get(dto.itemId);
      if (!faithStoreConfig) {
        return XResultUtils.error('商品配置不存在', 'ITEM_CONFIG_NOT_FOUND');
      }

      let unitPrice = faithStoreConfig.price;
      let discount = 0;
      let isSet = false;

      // 2. 检查董事长定价
      const storeItem = store.itemList.find((item: any) => item.itemId === dto.itemId);
      if (storeItem && storeItem.itemList.length > 0) {
        const pricing = storeItem.itemList.find((p: any) => p.beliefId === dto.beliefId);
        if (pricing) {
          unitPrice = Math.floor(faithStoreConfig.price * pricing.price / 100);
          discount = 100 - pricing.price;
          isSet = true;
        }
      }

      // 3. 本信仰商品半价优惠（如果没有董事长特别设置）
      if (await this.isBeliefItem(dto.itemId, playerBelief?.beliefId) && !isSet) {
        unitPrice = Math.floor(unitPrice / 2);
        discount = 50;
      }

      // 4. 计算总价
      const totalCost = unitPrice * dto.quantity;

      return XResultUtils.ok({
        totalCost,
        discount,
        unitPrice,
        basePrice: faithStoreConfig.price
      });

    } catch (error) {
      return XResultUtils.error(`计算购买价格失败: ${error.message}`, 'CALCULATE_PRICE_ERROR');
    }
  }

  /**
   * 获取信仰商店商品列表
   */
  async getBeliefStoreItemList(dto: { characterId?: string; serverId?: string }): Promise<XResult<any>> {
    this.logger.log(`获取信仰商店商品列表: ${dto.characterId}`);

    try {
      // 1. 获取商店状态
      const storeResult = await this.beliefStoreRepository.getCurrentStore();
      if (XResultUtils.isFailure(storeResult)) {
        return XResultUtils.error(`获取商店状态失败: ${storeResult.message}`, storeResult.code);
      }

      const store = storeResult.data;
      if (!store) {
        return XResultUtils.ok({ items: [], phase: 0, shopPeriod: 1 });
      }

      // 2. 获取玩家信仰信息
      const beliefResult = await this.microserviceClient.call('character', 'belief.getMyBelief', {
        characterId: dto.characterId,
        serverId: dto.serverId
      });

      const playerBelief: any = XResultUtils.isSuccess(beliefResult) ? beliefResult.data : null;

      // 3. 获取所有信仰商店配置
      const faithStoreConfigs = await this.gameConfig.faithStore.getAll();

      // 4. 构建商品列表
      const items = await Promise.all(
        faithStoreConfigs.map(async (config) => {
          const storeItem = store.itemList.find((item: any) => item.itemId === config.parameters);
          const playerPurchases = store.buyUidMap.get(dto.characterId) || [];
          const playerPurchased = playerPurchases.find((p: any) => p.itemId === config.parameters)?.num || 0;

          // 计算价格
          let finalPrice = config.price;
          let discount = 0;
          let isSet = false;

          // 检查董事长定价
          if (storeItem && storeItem.itemList.length > 0) {
            const pricing = storeItem.itemList.find((p: any) => p.beliefId === playerBelief?.beliefId);
            if (pricing) {
              finalPrice = Math.floor(config.price * pricing.price / 100);
              discount = 100 - pricing.price;
              isSet = true;
            }
          }

          // 本信仰商品半价优惠
          if (await this.isBeliefItem(config.parameters, playerBelief?.beliefId) && !isSet) {
            finalPrice = Math.floor(finalPrice / 2);
            discount = 50;
          }

          return {
            itemId: config.parameters,
            basePrice: config.price,
            finalPrice,
            discount,
            beliefLevel: config.lv || 0,
            globalPurchased: storeItem?.num || 0,
            globalLimit: config.customer || 0,
            playerPurchased,
            personalLimit: config.purchase || 0,
            canPurchase: this.canPurchaseItem(config, store, playerBelief, playerPurchased),
            pageSign: config.pageSign,
            itemType: config.itemType
          };
        })
      );

      return XResultUtils.ok({
        items,
        phase: store.phase,
        shopPeriod: store.shopPeriod,
        phaseTimeLeft: this.calculatePhaseTimeLeft(store)
      });

    } catch (error) {
      this.logger.error(`获取信仰商店商品列表失败: ${error.message}`, error.stack);
      return XResultUtils.error(`获取信仰商店商品列表失败: ${error.message}`, 'GET_ITEM_LIST_ERROR');
    }
  }

  /**
   * 获取信仰商店购买历史
   */
  async getBeliefStoreBuyHistory(dto: {
    characterId?: string;
    serverId?: string;
    page?: number;
    limit?: number;
    startTime?: number;
    endTime?: number;
  }): Promise<XResult<any>> {
    // TODO: 实现购买历史获取逻辑
    return XResultUtils.ok({ records: [], total: 0, page: 1, totalPages: 0 });
  }

  /**
   * 获取信仰商店统计信息
   */
  async getBeliefStoreStats(dto: { characterId?: string; serverId?: string }): Promise<XResult<any>> {
    // TODO: 实现统计信息获取逻辑
    return XResultUtils.ok({});
  }

  /**
   * 刷新信仰商店商品
   */
  async refreshBeliefStore(dto: {
    serverId?: string;
    refreshType?: 'daily' | 'weekly' | 'monthly' | 'phase';
  }): Promise<XResult<any>> {
    // TODO: 实现商店刷新逻辑
    return XResultUtils.ok({});
  }

  /**
   * 切换商店阶段
   */
  async switchBeliefStorePhase(dto: {
    serverId?: string;
    targetPhase?: 1 | 2;
    duration?: number;
  }): Promise<XResult<any>> {
    // TODO: 实现阶段切换逻辑
    return XResultUtils.ok({});
  }

  /**
   * 预览商品购买
   */
  async previewBeliefStoreBuy(dto: {
    characterId?: string;
    serverId?: string;
    itemId?: number;
    quantity?: number;
    beliefId?: number;
  }): Promise<XResult<any>> {
    // TODO: 实现购买预览逻辑
    return XResultUtils.ok({});
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 检查是否可以购买商品
   */
  private canPurchaseItem(config: any, store: any, playerBelief: any, playerPurchased: number): boolean {
    // 1. 检查阶段
    const isPub = config.pageSign === 16;
    if (store.shopPeriod !== 2 && !isPub) {
      return false;
    }

    // 2. 检查信仰等级
    let requiredLevel = config.lv || 0;
    let targetBeliefLevel = 0;

    if (config.pageSign >= 10 && config.pageSign <= 25) {
      // 特定信仰商品，需要检查该信仰等级
      // TODO: 这里需要获取对应信仰的等级
      targetBeliefLevel = 1; // 临时设置
    } else {
      targetBeliefLevel = playerBelief?.level || 0;
    }

    if (requiredLevel > targetBeliefLevel) {
      return false;
    }

    // 3. 检查限购
    const globalLimit = config.customer || 0;
    const personalLimit = config.purchase || 0;
    const storeItem = store.itemList.find((item: any) => item.itemId === config.parameters);
    const globalPurchased = storeItem?.num || 0;

    if (globalLimit > 0 && globalPurchased >= globalLimit) {
      return false;
    }

    if (personalLimit > 0 && playerPurchased >= personalLimit) {
      return false;
    }

    return true;
  }
}
