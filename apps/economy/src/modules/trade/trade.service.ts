/**
 * 交易系统服务 - 已适配Result模式
 *
 * 核心功能：
 * - 玩家间物品交易
 * - 交易状态管理
 * - 交易物品验证
 * - 交易转移执行
 * - 交易通知系统
 *
 * Result模式适配：
 * - 继承BaseService基类，获得统一的业务操作框架
 * - 所有public方法返回XResult<T>类型
 * - 使用executeBusinessOperation包装关键业务操作
 * - 标准化的错误处理和微服务调用
 * - 移除所有try-catch异常处理
 */

import { Injectable, Logger } from '@nestjs/common';
import { XResult, XResultUtils, RepositoryResultWrapper } from '@libs/common/types/result.type';
import { BaseService } from '@libs/common/service/base-service';
import { MicroserviceClientService } from '@libs/service-mesh';

@Injectable()
export class TradeService extends BaseService {

  constructor(
    microserviceClient: MicroserviceClientService, // 传递给BaseService
  ) {
    // 调用BaseService构造函数，传入服务名称和微服务客户端
    super('TradeService', microserviceClient);
  }

  /**
   * 创建交易
   * 基于old项目: 玩家间物品交易逻辑
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   *
   * 实现逻辑：
   * 1. 验证交易参数
   * 2. 检查交易双方状态
   * 3. 验证交易物品
   * 4. 创建交易记录
   * 5. 通知交易对方
   */
  async createTrade(tradeData: any): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log('创建交易', tradeData);

      // 1. 验证交易参数
      const validationResult = await this.validateTradeData(tradeData);
      if (XResultUtils.isFailure(validationResult)) {
        return XResultUtils.error(`验证交易参数失败: ${validationResult.message}`, validationResult.code);
      }

      if (!validationResult.data.valid) {
        return XResultUtils.failure(validationResult.data.error, 'INVALID_PARAMS');
      }

      // 2. 检查交易双方状态
      const statusCheckResult = await this.checkTradersStatus(tradeData.fromPlayerId, tradeData.toPlayerId);
      if (XResultUtils.isFailure(statusCheckResult)) {
        return XResultUtils.error(`检查交易双方状态失败: ${statusCheckResult.message}`, statusCheckResult.code);
      }

      const statusCheck = statusCheckResult.data;
      if (!statusCheck.canTrade) {
        return XResultUtils.failure(statusCheck.reason, 'INVALID_TRADER_STATUS');
      }

      // 3. 验证交易物品和货币
      const itemsCheckResult = await this.validateTradeItems(tradeData);
      if (XResultUtils.isFailure(itemsCheckResult)) {
        return XResultUtils.error(`验证交易物品失败: ${itemsCheckResult.message}`, itemsCheckResult.code);
      }

      const itemsCheck = itemsCheckResult.data;
      if (!itemsCheck.valid) {
        return XResultUtils.failure(itemsCheck.error, 'INVALID_TRADE_ITEMS');
      }

      // 4. 创建交易记录
      const tradeRecordResult = await this.createTradeRecord(tradeData);
      if (XResultUtils.isFailure(tradeRecordResult)) {
        return XResultUtils.error(`创建交易记录失败: ${tradeRecordResult.message}`, tradeRecordResult.code);
      }

      const tradeRecord = tradeRecordResult.data;
      if (!tradeRecord.success) {
        return XResultUtils.failure('创建交易记录失败', 'CREATE_TRADE_FAILED');
      }

      // 5. 锁定交易物品（防止重复交易）
      const lockResult = await this.lockTradeItems(tradeData.fromPlayerId, tradeData.fromItems);
      if (XResultUtils.isFailure(lockResult)) {
        this.logger.warn(`锁定交易物品失败: ${lockResult.message}`);
      }

      // 6. 通知交易对方
      const notifyResult = await this.notifyTradeTarget(tradeData.toPlayerId, tradeRecord.tradeId);
      if (XResultUtils.isFailure(notifyResult)) {
        this.logger.warn(`通知交易对方失败: ${notifyResult.message}`);
      }

      this.logger.log(`交易创建成功: ${tradeRecord.tradeId}`);
      return XResultUtils.ok({
        success: true,
        tradeId: tradeRecord.tradeId,
        status: 'pending',
        createdAt: Date.now(),
        expiresAt: Date.now() + (30 * 60 * 1000), // 30分钟过期
        fromPlayer: tradeData.fromPlayerId,
        toPlayer: tradeData.toPlayerId,
        fromItems: tradeData.fromItems,
        toItems: tradeData.toItems,
        fromCurrency: tradeData.fromCurrency,
        toCurrency: tradeData.toCurrency,
      });
    }, {
      reason: 'create_trade',
      metadata: { fromPlayer: tradeData.fromPlayerId, toPlayer: tradeData.toPlayerId }
    });
  }

  /**
   * 确认交易
   * 基于old项目: 交易确认和物品转移逻辑
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   *
   * 实现逻辑：
   * 1. 查询交易记录
   * 2. 验证交易状态
   * 3. 检查交易是否过期
   * 4. 验证双方物品状态
   * 5. 执行物品转移
   * 6. 更新交易状态
   */
  async confirmTrade(tradeId: string): Promise<XResult<any>> {
    return this.executeBusinessOperation(async () => {
      this.logger.log(`确认交易: ${tradeId}`);

      // 1. 查询交易记录
      const tradeRecordResult = await this.findTradeRecord(tradeId);
      if (XResultUtils.isFailure(tradeRecordResult)) {
        return XResultUtils.error(`查询交易记录失败: ${tradeRecordResult.message}`, tradeRecordResult.code);
      }

      const tradeRecord = tradeRecordResult.data;
      if (!tradeRecord) {
        return XResultUtils.failure('交易记录不存在', 'TRADE_NOT_FOUND');
      }

      // 2. 验证交易状态
      if (tradeRecord.status !== 'pending') {
        return XResultUtils.failure(`交易状态无效: ${tradeRecord.status}`, 'INVALID_TRADE_STATUS', {
          currentStatus: tradeRecord.status,
          expectedStatus: 'pending'
        });
      }

      // 3. 检查交易是否过期
      if (Date.now() > tradeRecord.expiresAt) {
        const expireResult = await this.expireTrade(tradeId);
        if (XResultUtils.isFailure(expireResult)) {
          this.logger.warn(`处理交易过期失败: ${expireResult.message}`);
        }
        return XResultUtils.failure('交易已过期', 'TRADE_EXPIRED', {
          expiresAt: tradeRecord.expiresAt,
          currentTime: Date.now()
        });
      }

      // 4. 再次验证双方物品状态（防止物品被其他操作消耗）
      const finalItemsCheckResult = await this.validateTradeItemsBeforeConfirm(tradeRecord);
      if (XResultUtils.isFailure(finalItemsCheckResult)) {
        return XResultUtils.error(`验证交易物品失败: ${finalItemsCheckResult.message}`, finalItemsCheckResult.code);
      }

      const finalItemsCheck = finalItemsCheckResult.data;
      if (!finalItemsCheck.valid) {
        return XResultUtils.failure(finalItemsCheck.error, 'ITEMS_CHANGED');
      }

      // 5. 执行物品和货币转移（原子操作）
      const transferResult = await this.executeTradeTransfer(tradeRecord);
      if (XResultUtils.isFailure(transferResult)) {
        // 发生异常时尝试回滚
        const rollbackResult = await this.rollbackTrade(tradeId);
        if (XResultUtils.isFailure(rollbackResult)) {
          this.logger.error(`交易回滚失败: ${rollbackResult.message}`);
        }
        return XResultUtils.error(`交易执行失败: ${transferResult.message}`, transferResult.code);
      }

      const transfer = transferResult.data;
      if (!transfer.success) {
        return XResultUtils.failure('交易执行失败', 'TRANSFER_FAILED', {
          details: transfer.error
        });
      }

      // 6. 更新交易状态为已完成
      const updateStatusResult = await this.updateTradeStatus(tradeId, 'completed');
      if (XResultUtils.isFailure(updateStatusResult)) {
        this.logger.warn(`更新交易状态失败: ${updateStatusResult.message}`);
      }

      // 7. 解锁交易物品
      const unlockResult = await this.unlockTradeItems(tradeRecord.fromPlayerId, tradeRecord.fromItems);
      if (XResultUtils.isFailure(unlockResult)) {
        this.logger.warn(`解锁交易物品失败: ${unlockResult.message}`);
      }

      // 8. 记录交易日志
      const logResult = await this.logTradeCompletion(tradeRecord);
      if (XResultUtils.isFailure(logResult)) {
        this.logger.warn(`记录交易日志失败: ${logResult.message}`);
      }

      // 9. 通知双方交易完成
      const notifyResult = await this.notifyTradeCompletion(tradeRecord);
      if (XResultUtils.isFailure(notifyResult)) {
        this.logger.warn(`通知交易完成失败: ${notifyResult.message}`);
      }

      this.logger.log(`交易确认成功: ${tradeId}`);
      return XResultUtils.ok({
        success: true,
        tradeId,
        status: 'completed',
        completedAt: Date.now(),
        fromPlayer: tradeRecord.fromPlayerId,
        toPlayer: tradeRecord.toPlayerId,
        transferDetails: transfer.details,
      });
    }, {
      reason: 'confirm_trade',
      metadata: { tradeId }
    });
  }

  /**
   * 验证交易数据
   * 基于old项目: 交易参数验证逻辑
   * 已适配Result模式：返回XResult类型
   */
  private async validateTradeData(tradeData: any): Promise<XResult<{ valid: boolean; error?: string }>> {
    // 检查必要参数
    if (!tradeData.fromPlayerId) {
      return XResultUtils.ok({ valid: false, error: '发起方玩家ID不能为空' });
    }

    if (!tradeData.toPlayerId) {
      return XResultUtils.ok({ valid: false, error: '接收方玩家ID不能为空' });
    }

    if (tradeData.fromPlayerId === tradeData.toPlayerId) {
      return XResultUtils.ok({ valid: false, error: '不能与自己交易' });
    }

    // 检查交易物品
    if (!tradeData.fromItems && !tradeData.fromCurrency) {
      return XResultUtils.ok({ valid: false, error: '发起方必须提供交易物品或货币' });
    }

    if (!tradeData.toItems && !tradeData.toCurrency) {
      return XResultUtils.ok({ valid: false, error: '接收方必须提供交易物品或货币' });
    }

    return XResultUtils.ok({ valid: true });
  }

  /**
   * 检查交易双方状态
   * 基于old项目: 玩家状态检查逻辑
   * 已适配Result模式：返回XResult类型，使用callMicroservice调用其他服务
   */
  private async checkTradersStatus(fromPlayerId: string, toPlayerId: string): Promise<XResult<{ canTrade: boolean; reason?: string }>> {
    // TODO: 调用Character服务检查玩家状态
    // const fromPlayerStatusResult = await this.callMicroservice(
    //   'character.checkTradeStatus',
    //   { playerId: fromPlayerId }
    // );
    // if (XResultUtils.isFailure(fromPlayerStatusResult)) {
    //   return XResultUtils.error(`检查发起方状态失败: ${fromPlayerStatusResult.message}`, fromPlayerStatusResult.code);
    // }

    // const toPlayerStatusResult = await this.callMicroservice(
    //   'character.checkTradeStatus',
    //   { playerId: toPlayerId }
    // );
    // if (XResultUtils.isFailure(toPlayerStatusResult)) {
    //   return XResultUtils.error(`检查接收方状态失败: ${toPlayerStatusResult.message}`, toPlayerStatusResult.code);
    // }

    // 检查玩家是否在线、是否被封禁、是否在交易冷却期等
    // const fromPlayerStatus = fromPlayerStatusResult.data;
    // if (!fromPlayerStatus.canTrade) {
    //   return XResultUtils.ok({ canTrade: false, reason: `发起方无法交易: ${fromPlayerStatus.reason}` });
    // }

    // const toPlayerStatus = toPlayerStatusResult.data;
    // if (!toPlayerStatus.canTrade) {
    //   return XResultUtils.ok({ canTrade: false, reason: `接收方无法交易: ${toPlayerStatus.reason}` });
    // }

    // 暂时返回true
    return XResultUtils.ok({ canTrade: true });
  }

  /**
   * 验证交易物品
   * 基于old项目: 物品存在性和数量验证逻辑
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async validateTradeItems(tradeData: any): Promise<XResult<{ valid: boolean; error?: string }>> {
    // 验证发起方物品
    if (tradeData.fromItems && tradeData.fromItems.length > 0) {
      for (const item of tradeData.fromItems) {
        const hasItemResult = await this.checkPlayerHasItem(tradeData.fromPlayerId, item.itemId, item.quantity);
        if (XResultUtils.isFailure(hasItemResult)) {
          return XResultUtils.error(`检查发起方物品失败: ${hasItemResult.message}`, hasItemResult.code);
        }
        if (!hasItemResult.data) {
          return XResultUtils.ok({ valid: false, error: `发起方物品不足: ${item.itemId} x${item.quantity}` });
        }
      }
    }

    // 验证发起方货币
    if (tradeData.fromCurrency) {
      const hasCurrencyResult = await this.checkPlayerHasCurrency(
        tradeData.fromPlayerId,
        tradeData.fromCurrency.type,
        tradeData.fromCurrency.amount
      );
      if (XResultUtils.isFailure(hasCurrencyResult)) {
        return XResultUtils.error(`检查发起方货币失败: ${hasCurrencyResult.message}`, hasCurrencyResult.code);
      }
      if (!hasCurrencyResult.data) {
        return XResultUtils.ok({ valid: false, error: `发起方货币不足: ${tradeData.fromCurrency.type} ${tradeData.fromCurrency.amount}` });
      }
    }

    // 验证接收方物品
    if (tradeData.toItems && tradeData.toItems.length > 0) {
      for (const item of tradeData.toItems) {
        const hasItemResult = await this.checkPlayerHasItem(tradeData.toPlayerId, item.itemId, item.quantity);
        if (XResultUtils.isFailure(hasItemResult)) {
          return XResultUtils.error(`检查接收方物品失败: ${hasItemResult.message}`, hasItemResult.code);
        }
        if (!hasItemResult.data) {
          return XResultUtils.ok({ valid: false, error: `接收方物品不足: ${item.itemId} x${item.quantity}` });
        }
      }
    }

    // 验证接收方货币
    if (tradeData.toCurrency) {
      const hasCurrencyResult = await this.checkPlayerHasCurrency(
        tradeData.toPlayerId,
        tradeData.toCurrency.type,
        tradeData.toCurrency.amount
      );
      if (XResultUtils.isFailure(hasCurrencyResult)) {
        return XResultUtils.error(`检查接收方货币失败: ${hasCurrencyResult.message}`, hasCurrencyResult.code);
      }
      if (!hasCurrencyResult.data) {
        return XResultUtils.ok({ valid: false, error: `接收方货币不足: ${tradeData.toCurrency.type} ${tradeData.toCurrency.amount}` });
      }
    }

    return XResultUtils.ok({ valid: true });
  }

  /**
   * 创建交易记录
   * 基于old项目: 交易记录存储逻辑
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async createTradeRecord(tradeData: any): Promise<XResult<{ success: boolean; tradeId?: string }>> {
    const tradeId = this.generateTradeId();

    const tradeRecord = {
      tradeId,
      fromPlayerId: tradeData.fromPlayerId,
      toPlayerId: tradeData.toPlayerId,
      fromItems: tradeData.fromItems || [],
      toItems: tradeData.toItems || [],
      fromCurrency: tradeData.fromCurrency || null,
      toCurrency: tradeData.toCurrency || null,
      status: 'pending',
      createdAt: Date.now(),
      expiresAt: Date.now() + (30 * 60 * 1000), // 30分钟过期
      message: tradeData.message || '',
    };

    // TODO: 保存到数据库
    // const saveResult = await RepositoryResultWrapper.wrap(async () => {
    //   return await this.tradeRepository.create(tradeRecord);
    // });
    // if (XResultUtils.isFailure(saveResult)) {
    //   return XResultUtils.error(`保存交易记录失败: ${saveResult.message}`, saveResult.code);
    // }

    return XResultUtils.ok({ success: true, tradeId });
  }

  /**
   * 生成交易ID
   * 基于old项目: 唯一ID生成逻辑
   */
  private generateTradeId(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `trade_${timestamp}_${random}`;
  }

  /**
   * 执行交易转移
   * 基于old项目: 物品和货币转移的原子操作
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async executeTradeTransfer(tradeRecord: any): Promise<XResult<{ success: boolean; details?: any; error?: string }>> {
    const transferDetails = {
      fromPlayerTransfers: [],
      toPlayerTransfers: [],
    };

    // 1. 转移发起方的物品给接收方
    if (tradeRecord.fromItems && tradeRecord.fromItems.length > 0) {
      for (const item of tradeRecord.fromItems) {
        // 从发起方扣除物品
        const removeResult = await this.removePlayerItem(tradeRecord.fromPlayerId, item.itemId, item.quantity);
        if (XResultUtils.isFailure(removeResult)) {
          return XResultUtils.error(`扣除发起方物品失败: ${removeResult.message}`, removeResult.code);
        }

        // 给接收方添加物品
        const addResult = await this.addPlayerItem(tradeRecord.toPlayerId, item.itemId, item.quantity);
        if (XResultUtils.isFailure(addResult)) {
          return XResultUtils.error(`添加接收方物品失败: ${addResult.message}`, addResult.code);
        }

        transferDetails.fromPlayerTransfers.push({
          type: 'item',
          action: 'give',
          itemId: item.itemId,
          quantity: item.quantity,
        });
      }
    }

    // 2. 转移发起方的货币给接收方
    if (tradeRecord.fromCurrency) {
      const removeCurrencyResult = await this.removePlayerCurrency(
        tradeRecord.fromPlayerId,
        tradeRecord.fromCurrency.type,
        tradeRecord.fromCurrency.amount
      );
      if (XResultUtils.isFailure(removeCurrencyResult)) {
        return XResultUtils.error(`扣除发起方货币失败: ${removeCurrencyResult.message}`, removeCurrencyResult.code);
      }

      const addCurrencyResult = await this.addPlayerCurrency(
        tradeRecord.toPlayerId,
        tradeRecord.fromCurrency.type,
        tradeRecord.fromCurrency.amount
      );
      if (XResultUtils.isFailure(addCurrencyResult)) {
        return XResultUtils.error(`添加接收方货币失败: ${addCurrencyResult.message}`, addCurrencyResult.code);
      }

      transferDetails.fromPlayerTransfers.push({
        type: 'currency',
        action: 'give',
        currencyType: tradeRecord.fromCurrency.type,
        amount: tradeRecord.fromCurrency.amount,
      });
    }

    // 3. 转移接收方的物品给发起方
    if (tradeRecord.toItems && tradeRecord.toItems.length > 0) {
      for (const item of tradeRecord.toItems) {
        // 从接收方扣除物品
        const removeResult = await this.removePlayerItem(tradeRecord.toPlayerId, item.itemId, item.quantity);
        if (XResultUtils.isFailure(removeResult)) {
          return XResultUtils.error(`扣除接收方物品失败: ${removeResult.message}`, removeResult.code);
        }

        // 给发起方添加物品
        const addResult = await this.addPlayerItem(tradeRecord.fromPlayerId, item.itemId, item.quantity);
        if (XResultUtils.isFailure(addResult)) {
          return XResultUtils.error(`添加发起方物品失败: ${addResult.message}`, addResult.code);
        }

        transferDetails.toPlayerTransfers.push({
          type: 'item',
          action: 'give',
          itemId: item.itemId,
          quantity: item.quantity,
        });
      }
    }

    // 4. 转移接收方的货币给发起方
    if (tradeRecord.toCurrency) {
      const removeCurrencyResult = await this.removePlayerCurrency(
        tradeRecord.toPlayerId,
        tradeRecord.toCurrency.type,
        tradeRecord.toCurrency.amount
      );
      if (XResultUtils.isFailure(removeCurrencyResult)) {
        return XResultUtils.error(`扣除接收方货币失败: ${removeCurrencyResult.message}`, removeCurrencyResult.code);
      }

      const addCurrencyResult = await this.addPlayerCurrency(
        tradeRecord.fromPlayerId,
        tradeRecord.toCurrency.type,
        tradeRecord.toCurrency.amount
      );
      if (XResultUtils.isFailure(addCurrencyResult)) {
        return XResultUtils.error(`添加发起方货币失败: ${addCurrencyResult.message}`, addCurrencyResult.code);
      }

      transferDetails.toPlayerTransfers.push({
        type: 'currency',
        action: 'give',
        currencyType: tradeRecord.toCurrency.type,
        amount: tradeRecord.toCurrency.amount,
      });
    }

    return XResultUtils.ok({ success: true, details: transferDetails });
  }

  /**
   * 检查玩家是否拥有指定物品
   * 基于old项目: 背包物品检查逻辑
   * 已适配Result模式：返回XResult类型，使用callMicroservice调用其他服务
   */
  private async checkPlayerHasItem(playerId: string, itemId: number, quantity: number): Promise<XResult<boolean>> {
    // TODO: 调用Character服务检查物品
    // const result = await this.callMicroservice(
    //   'inventory.checkItem',
    //   { playerId, itemId, quantity }
    // );
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`检查玩家物品失败: ${result.message}`, result.code);
    // }
    // return XResultUtils.ok(result.data.hasItem);

    // 暂时返回true
    return XResultUtils.ok(true);
  }

  /**
   * 检查玩家是否拥有指定货币
   * 基于old项目: 货币检查逻辑
   * 已适配Result模式：返回XResult类型，使用callMicroservice调用其他服务
   */
  private async checkPlayerHasCurrency(playerId: string, currencyType: string, amount: number): Promise<XResult<boolean>> {
    // TODO: 调用Character服务检查货币
    // const result = await this.callMicroservice(
    //   'character.checkCurrency',
    //   { playerId, currencyType, amount }
    // );
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`检查玩家货币失败: ${result.message}`, result.code);
    // }
    // return XResultUtils.ok(result.data.hasCurrency);

    // 暂时返回true
    return XResultUtils.ok(true);
  }

  /**
   * 查找交易记录
   * 基于old项目: 交易记录查询逻辑
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async findTradeRecord(tradeId: string): Promise<XResult<any>> {
    // TODO: 从数据库查询交易记录
    // const tradeResult = await RepositoryResultWrapper.wrap(async () => {
    //   return await this.tradeRepository.findById(tradeId);
    // });
    // if (XResultUtils.isFailure(tradeResult)) {
    //   return XResultUtils.error(`查询交易记录失败: ${tradeResult.message}`, tradeResult.code);
    // }
    // return XResultUtils.ok(tradeResult.data);

    // 暂时返回模拟数据
    return XResultUtils.ok({
      tradeId,
      fromPlayerId: 'player1',
      toPlayerId: 'player2',
      fromItems: [],
      toItems: [],
      fromCurrency: null,
      toCurrency: null,
      status: 'pending',
      createdAt: Date.now() - 60000,
      expiresAt: Date.now() + 1800000,
    });
  }

  /**
   * 添加玩家物品
   * 基于old项目: 背包物品添加逻辑
   * 已适配Result模式：返回XResult类型，使用callMicroservice调用其他服务
   */
  private async addPlayerItem(playerId: string, itemId: number, quantity: number): Promise<XResult<void>> {
    // TODO: 调用Character服务添加物品
    // const result = await this.callMicroservice(
    //   'inventory.addItem',
    //   { playerId, itemId, quantity, source: 'trade' }
    // );
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`添加玩家物品失败: ${result.message}`, result.code);
    // }

    this.logger.debug(`添加玩家物品: ${playerId}, 物品: ${itemId}, 数量: ${quantity}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 移除玩家物品
   * 基于old项目: 背包物品扣除逻辑
   * 已适配Result模式：返回XResult类型，使用callMicroservice调用其他服务
   */
  private async removePlayerItem(playerId: string, itemId: number, quantity: number): Promise<XResult<void>> {
    // TODO: 调用Character服务扣除物品
    // const result = await this.callMicroservice(
    //   'inventory.removeItem',
    //   { playerId, itemId, quantity, reason: 'trade' }
    // );
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`扣除玩家物品失败: ${result.message}`, result.code);
    // }

    this.logger.debug(`扣除玩家物品: ${playerId}, 物品: ${itemId}, 数量: ${quantity}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 添加玩家货币
   * 基于old项目: 货币添加逻辑
   * 已适配Result模式：返回XResult类型，使用callMicroservice调用其他服务
   */
  private async addPlayerCurrency(playerId: string, currencyType: string, amount: number): Promise<XResult<void>> {
    // TODO: 调用Character服务添加货币
    // const result = await this.callMicroservice(
    //   'character.addCurrency',
    //   { playerId, currencyType, amount, reason: 'trade' }
    // );
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`添加玩家货币失败: ${result.message}`, result.code);
    // }

    this.logger.debug(`添加玩家货币: ${playerId}, 类型: ${currencyType}, 数量: ${amount}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 移除玩家货币
   * 基于old项目: 货币扣除逻辑
   * 已适配Result模式：返回XResult类型，使用callMicroservice调用其他服务
   */
  private async removePlayerCurrency(playerId: string, currencyType: string, amount: number): Promise<XResult<void>> {
    // TODO: 调用Character服务扣除货币
    // const result = await this.callMicroservice(
    //   'character.removeCurrency',
    //   { playerId, currencyType, amount, reason: 'trade' }
    // );
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`扣除玩家货币失败: ${result.message}`, result.code);
    // }

    this.logger.debug(`扣除玩家货币: ${playerId}, 类型: ${currencyType}, 数量: ${amount}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 锁定交易物品
   * 基于old项目: 物品锁定逻辑
   * 已适配Result模式：返回XResult类型，使用callMicroservice调用其他服务
   */
  private async lockTradeItems(playerId: string, items: any[]): Promise<XResult<void>> {
    // TODO: 调用Character服务锁定物品
    // const result = await this.callMicroservice(
    //   'inventory.lockItems',
    //   { playerId, items, reason: 'trade' }
    // );
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`锁定交易物品失败: ${result.message}`, result.code);
    // }

    this.logger.debug(`锁定交易物品: ${playerId}, 物品数量: ${items.length}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 解锁交易物品
   * 基于old项目: 物品解锁逻辑
   * 已适配Result模式：返回XResult类型，使用callMicroservice调用其他服务
   */
  private async unlockTradeItems(playerId: string, items: any[]): Promise<XResult<void>> {
    // TODO: 调用Character服务解锁物品
    // const result = await this.callMicroservice(
    //   'inventory.unlockItems',
    //   { playerId, items, reason: 'trade' }
    // );
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`解锁交易物品失败: ${result.message}`, result.code);
    // }

    this.logger.debug(`解锁交易物品: ${playerId}, 物品数量: ${items.length}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 通知交易目标
   * 基于old项目: 交易通知逻辑
   * 已适配Result模式：返回XResult类型，使用callMicroservice调用其他服务
   */
  private async notifyTradeTarget(playerId: string, tradeId: string): Promise<XResult<void>> {
    // TODO: 调用通知服务发送交易通知
    // const result = await this.callMicroservice(
    //   'notification.sendTradeNotification',
    //   { playerId, tradeId, type: 'trade_request' }
    // );
    // if (XResultUtils.isFailure(result)) {
    //   return XResultUtils.error(`通知交易目标失败: ${result.message}`, result.code);
    // }

    this.logger.debug(`通知交易目标: ${playerId}, 交易ID: ${tradeId}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 更新交易状态
   * 基于old项目: 交易状态更新逻辑
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async updateTradeStatus(tradeId: string, status: string): Promise<XResult<void>> {
    // TODO: 更新数据库交易状态
    // const updateResult = await RepositoryResultWrapper.wrap(async () => {
    //   return await this.tradeRepository.updateStatus(tradeId, status);
    // });
    // if (XResultUtils.isFailure(updateResult)) {
    //   return XResultUtils.error(`更新交易状态失败: ${updateResult.message}`, updateResult.code);
    // }

    this.logger.debug(`更新交易状态: ${tradeId} -> ${status}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 验证交易物品状态（确认前）
   * 基于old项目: 交易确认前的最终验证
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async validateTradeItemsBeforeConfirm(tradeRecord: any): Promise<XResult<{ valid: boolean; error?: string }>> {
    // 再次验证所有物品和货币状态
    return await this.validateTradeItems(tradeRecord);
  }

  /**
   * 交易过期处理
   * 基于old项目: 交易过期清理逻辑
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async expireTrade(tradeId: string): Promise<XResult<void>> {
    const updateResult = await this.updateTradeStatus(tradeId, 'expired');
    if (XResultUtils.isFailure(updateResult)) {
      return XResultUtils.error(`更新交易过期状态失败: ${updateResult.message}`, updateResult.code);
    }

    // TODO: 解锁相关物品
    this.logger.log(`交易已过期: ${tradeId}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 交易回滚
   * 基于old项目: 交易异常回滚逻辑
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async rollbackTrade(tradeId: string): Promise<XResult<void>> {
    const updateResult = await this.updateTradeStatus(tradeId, 'failed');
    if (XResultUtils.isFailure(updateResult)) {
      return XResultUtils.error(`更新交易回滚状态失败: ${updateResult.message}`, updateResult.code);
    }

    // TODO: 回滚所有操作
    this.logger.log(`交易已回滚: ${tradeId}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 记录交易完成日志
   * 基于old项目: 交易日志记录逻辑
   * 已适配Result模式：返回XResult类型，移除try-catch
   */
  private async logTradeCompletion(tradeRecord: any): Promise<XResult<void>> {
    // TODO: 记录交易完成日志到数据库或日志系统
    // const logResult = await RepositoryResultWrapper.wrap(async () => {
    //   return await this.tradeLogRepository.create({
    //     tradeId: tradeRecord.tradeId,
    //     fromPlayer: tradeRecord.fromPlayerId,
    //     toPlayer: tradeRecord.toPlayerId,
    //     completedAt: Date.now(),
    //     transferDetails: tradeRecord.transferDetails
    //   });
    // });
    // if (XResultUtils.isFailure(logResult)) {
    //   return XResultUtils.error(`记录交易日志失败: ${logResult.message}`, logResult.code);
    // }

    this.logger.log(`交易完成日志: ${tradeRecord.tradeId}`);
    return XResultUtils.ok(undefined);
  }

  /**
   * 通知交易完成
   * 基于old项目: 交易完成通知逻辑
   * 已适配Result模式：返回XResult类型，使用callMicroservice调用其他服务
   */
  private async notifyTradeCompletion(tradeRecord: any): Promise<XResult<void>> {
    // TODO: 通知双方交易完成
    // const fromPlayerNotifyResult = await this.callMicroservice(
    //   'notification.sendTradeNotification',
    //   {
    //     playerId: tradeRecord.fromPlayerId,
    //     tradeId: tradeRecord.tradeId,
    //     type: 'trade_completed',
    //     message: '交易已完成'
    //   }
    // );
    // if (XResultUtils.isFailure(fromPlayerNotifyResult)) {
    //   this.logger.warn(`通知发起方交易完成失败: ${fromPlayerNotifyResult.message}`);
    // }

    // const toPlayerNotifyResult = await this.callMicroservice(
    //   'notification.sendTradeNotification',
    //   {
    //     playerId: tradeRecord.toPlayerId,
    //     tradeId: tradeRecord.tradeId,
    //     type: 'trade_completed',
    //     message: '交易已完成'
    //   }
    // );
    // if (XResultUtils.isFailure(toPlayerNotifyResult)) {
    //   this.logger.warn(`通知接收方交易完成失败: ${toPlayerNotifyResult.message}`);
    // }

    this.logger.log(`通知交易完成: ${tradeRecord.tradeId}`);
    return XResultUtils.ok(undefined);
  }
}
