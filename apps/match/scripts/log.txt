{"id": "test_1758265501562_664", "type": "response", "service": "gateway", "action": "response", "payload": {"success": true, "data": {"success": true, "data": {"characterId": "char_server_001_92d9401c_vvzumy59q", "currencyType": "cash", "amount": 1000000, "newBalance": 14010000, "reason": "quick_test_setup"}, "code": "SUCCESS", "timestamp": 1758265501674}, "requestId": "test_1758265501562_664", "duration": 85, "service": "character", "action": "character.currency.add"}, "timestamp": 1758265501683}