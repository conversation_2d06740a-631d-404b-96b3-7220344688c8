import { Controller, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { TrophyService } from './trophy.service';
import {
  GetTrophyCopyDataResponseDto,
  PveTrophyBattleResultResponseDto,
  BuyTrophyTimesResponseDto
} from '../../common/dto/trophy.dto';
import { Cacheable, CacheEvict } from '@libs/redis';

import { XResponse } from '@libs/common/types/result.type';

import {
  BuyTrophyTimesPayloadDto,
  GetBattleHistoryPayloadDto,
  GetStatisticsPayloadDto,
  GetTrophyCopyDataPayloadDto,
  PveTrophyBattlePayloadDto,
  ResetDailyTimesPayloadDto
} from "@match/common/dto/trophy-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';

/**
 * 杯赛系统控制器
 * 严格基于old项目trophyCopy.js的接口设计
 * 
 * 核心接口：
 * - trophy.getTrophyCopyData: 获取杯赛副本数据
 * - trophy.pveTrophyBattle: PVE杯赛战斗
 * - trophy.buyTrophyTimes: 购买杯赛次数
 */
@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class TrophyController extends BaseController {
  constructor(private readonly trophyService: TrophyService) {
    super('TrophyController');
  }

  /**
   * 获取杯赛副本数据
   * 基于old项目的getTrophyCopyData接口
   */
  @MessagePattern('trophy.getTrophyCopyData')
  @Cacheable({
    key: 'trophy:data:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300 // 5分钟缓存
  })
  async getTrophyCopyData(@Payload() payload: GetTrophyCopyDataPayloadDto): Promise<XResponse<GetTrophyCopyDataResponseDto>> {
    this.logger.log(`获取杯赛副本数据: ${payload.characterId}`);
    
    const result = await this.trophyService.getTrophyCopyData(payload);
    return this.fromResult(result);
  }

  /**
   * PVE杯赛战斗
   * 基于old项目的pveTrophyCopyBattle接口
   */
  @MessagePattern('trophy.pveTrophyBattle')
  @CacheEvict({
    key: 'trophy:data:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async pveTrophyBattle(@Payload() payload: PveTrophyBattlePayloadDto): Promise<XResponse<PveTrophyBattleResultResponseDto>> {
    this.logger.log(`PVE杯赛战斗: ${payload.characterId}, 杯赛${payload.trophyId}, 副本${payload.teamCopyId}`);
    
    const result = await this.trophyService.pveTrophyBattle(payload);
    return this.fromResult(result);
  }

  /**
   * 购买杯赛次数
   * 基于old项目的buyTrophyTimes接口
   */
  @MessagePattern('trophy.buyTrophyTimes')
  @CacheEvict({
    key: 'trophy:data:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async buyTrophyTimes(@Payload() payload: BuyTrophyTimesPayloadDto): Promise<XResponse<BuyTrophyTimesResponseDto>> {
    this.logger.log(`购买杯赛次数: ${payload.characterId}, 杯赛${payload.trophyId}, 次数${payload.num}`);
    
    const result = await this.trophyService.buyTrophyTimes(payload);
    return this.fromResult(result);
  }

  /**
   * 获取杯赛统计信息（管理接口）
   * TODO: 需要实现
   */
  @MessagePattern('trophy.getStatistics')
  async getStatistics(@Payload() payload: GetStatisticsPayloadDto): Promise<XResponse<any>> {
    this.logger.log('获取杯赛统计信息');
    
    return this.toSuccessResponse({});
  }

  /**
   * 重置每日杯赛次数（管理接口）
   * 修复：添加真正的重置逻辑和正确的返回格式
   */
  @MessagePattern('trophy.resetDailyTimes')
  async resetDailyTimes(@Payload() payload: ResetDailyTimesPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`重置每日杯赛次数: ${payload.characterId || 'all'}`);

    const result = await this.trophyService.resetDailyTimes(payload.characterId);
    return this.fromResult(result);
  }

  /**
   * 获取杯赛战斗历史（管理接口）
   * TODO: 需要实现
   */
  @MessagePattern('trophy.getBattleHistory')
  async getBattleHistory(@Payload() payload: GetBattleHistoryPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取杯赛战斗历史: ${payload.characterId}`);
    
    return this.toSuccessResponse({});
  }
}
