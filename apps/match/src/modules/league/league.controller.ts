import { Controller, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { LeagueService } from './league.service';
import {

  GetLeagueCopyDataResponseDto,
  PVEBattleResultResponseDto
} from '../../common/dto/league.dto';
import { Cacheable, CacheEvict } from '@libs/redis';

import { XResponse, XResultUtils } from '@libs/common/types/result.type';

import {
  BuyLeagueTimesPayloadDto,
  GetLeagueCopyDataPayloadDto,
  GetStatisticsPayloadDto,
  PveBattlePayloadDto,
  TakeLeagueRewardPayloadDto
} from "@match/common/dto/league-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';
/**
 * 联赛系统控制器
 * 严格基于old项目leagueCopy.js的接口设计
 * 
 * 核心接口：
 * - league.getLeagueCopyData: 获取联赛副本数据
 * - league.pveBattle: PVE联赛战斗
 * - league.takeLeagueReward: 领取联赛奖励
 */
@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class LeagueController extends BaseController {
  constructor(private readonly leagueService: LeagueService) {
    super('LeagueController');
  }

  /**
   * 获取联赛副本数据
   * 基于old项目的getLeagueCopyData接口
   */
  @MessagePattern('league.getLeagueCopyData')
  @Cacheable({
    key: 'league:data:#{payload.characterId}:#{payload.type || "all"}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300 // 5分钟缓存
  })
  async getLeagueCopyData(@Payload() payload: GetLeagueCopyDataPayloadDto): Promise<XResponse<GetLeagueCopyDataResponseDto>> {
    this.logger.log(`获取联赛副本数据: ${payload.characterId}, 类型: ${payload.type || 'all'}`);

    const result = await this.leagueService.getLeagueCopyData(payload);
    
    if (XResultUtils.isFailure(result)) {
      this.logger.log(`获取联赛副本数据失败: ${result.message}`, result.code);
    }
    const leagueCopyData = result.data;
    this.logger.log(`联赛数据获取成功: ${payload.characterId}, 下一联赛: ${leagueCopyData.nextLeagueId}, 下一副本: ${leagueCopyData.nextTeamCopyId}`);

    return this.fromResult(result);
  }

  /**
   * PVE联赛战斗
   * 基于old项目的PVELeagueCopyBattle接口
   */
  @MessagePattern('league.pveBattle')
  @CacheEvict({
    key: 'league:data:#{payload.characterId}:*',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async pveBattle(@Payload() payload: PveBattlePayloadDto): Promise<XResponse<PVEBattleResultResponseDto>> {
    this.logger.log(`PVE联赛战斗: ${payload.characterId}, 联赛${payload.leagueId}, 副本${payload.teamCopyId}`);
    
    const result = await this.leagueService.pveBattle(payload);
    
    if (XResultUtils.isFailure(result)) {
      this.logger.log(`PVE联赛战斗异常: ${result.message}`, result.code);
    }
    const battleResult = result.data;
    this.logger.log(`PVE战斗成功: ${payload.characterId}, 获得奖励: ${battleResult.rewards?.length || 0}个`);
    
    return this.fromResult(result);
  }

  /**
   * 领取联赛奖励
   * 基于old项目的联赛奖励发放逻辑
   */
  @MessagePattern('league.takeLeagueReward')
  @CacheEvict({
    key: 'league:data:#{payload.characterId}:*',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async takeLeagueReward(@Payload() payload: TakeLeagueRewardPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`领取联赛奖励: ${payload.characterId}, 联赛${payload.leagueId}`);
    
    const result = await this.leagueService.takeLeagueReward(payload);
    return this.fromResult(result);
  }

  /**
   * 购买联赛次数
   * 基于old项目的购买次数逻辑
   */
  @MessagePattern('league.buyLeagueTimes')
  @CacheEvict({
    key: 'league:data:#{payload.characterId}:*',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async buyLeagueTimes(@Payload() payload: BuyLeagueTimesPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`购买联赛次数: ${payload.characterId}, 次数: ${payload.times}`);

    const result = await this.leagueService.buyLeagueTimes(payload);
    return this.fromResult(result);
  }

  /**
   * 获取联赛统计信息（管理接口）
   * TODO: 未实现
   */
  @MessagePattern('league.getStatistics')
  async getStatistics(@Payload() payload: GetStatisticsPayloadDto): Promise<XResponse<any>> {
    this.logger.log('获取联赛统计信息');

    return this.toSuccessResponse({});
  }
}
