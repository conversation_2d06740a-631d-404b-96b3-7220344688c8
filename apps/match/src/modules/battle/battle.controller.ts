import { Controller, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { BattleService } from './battle.service';
import {
  BattleResultResponseDto,
  GetBattleReplayResponseDto
} from '../../common/dto/battle.dto';
import { Cacheable, CacheEvict } from '@libs/redis';

import { XResponse, XResultUtils } from '@libs/common/types/result.type';

import {
  CleanExpiredRoomsPayloadDto,
  DeleteBattleRoomPayloadDto,
  GetBattleReplayPayloadDto,
  GetStatisticsPayloadDto,
  PveBattlePayloadDto,
  PvpBattlePayloadDto
} from "@match/common/dto/battle-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';
/**
 * 战斗系统控制器
 * 严格基于old项目battleService.js的接口设计
 * 
 * 核心接口：
 * - battle.pveBattle: PVE战斗计算
 * - battle.pvpBattle: PVP战斗计算
 * - battle.getBattleReplay: 获取战斗回放
 * - battle.deleteBattleRoom: 删除战斗房间
 */
@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class BattleController extends BaseController {
  constructor(private readonly battleService: BattleService) {
    super('BattleController');
  }

  /**
   * PVE战斗
   * 基于old项目的initPveBattle接口
   */
  @MessagePattern('battle.pveBattle')
  async pveBattle(@Payload() payload: PveBattlePayloadDto): Promise<XResponse<BattleResultResponseDto>> {
    this.logger.log(`PVE战斗请求: ${payload.characterId}, 类型: ${payload.battleType}`);
    
    const result = await this.battleService.pveBattle(payload);
    const battleResult = result.data;

    if (XResultUtils.isFailure(result)) {
      this.logger.log(`PVE战斗异常: ${result.message}`, result.code);
    } else {
      this.logger.log(`PVE战斗完成: ${payload.characterId}, 比分: ${battleResult.homeScore}:${battleResult.awayScore}`);
    }
    
    return this.fromResult(result);
  }

  /**
   * PVP战斗
   * 基于old项目的pvpMatchBattle接口
   */
  @MessagePattern('battle.pvpBattle')
  async pvpBattle(@Payload() payload: PvpBattlePayloadDto): Promise<XResponse<BattleResultResponseDto>> {
    this.logger.log(`PVP战斗请求: ${payload.homeCharacterId} vs ${payload.awayCharacterId}`);
    
    const result = await this.battleService.pvpBattle(payload);
    const battleResult = result.data;
    if (XResultUtils.isFailure(result)) {
      this.logger.log(`PVP战斗异常: ${result.message}`, result.code);
    } else {
      this.logger.log(`PVP战斗完成: ${payload.homeCharacterId} vs ${payload.awayCharacterId}, 比分: ${battleResult.homeScore}:${battleResult.awayScore}`);
    }
    
    return this.fromResult(result);
  }

  /**
   * 获取战斗回放
   * 基于old项目的战斗回放功能
   */
  @MessagePattern('battle.getBattleReplay')
  @Cacheable({
    key: 'battle:replay:#{payload.roomId}',
    dataType: 'global',
    ttl: 3600 // 1小时缓存
  })
  async getBattleReplay(@Payload() payload: GetBattleReplayPayloadDto): Promise<XResponse<GetBattleReplayResponseDto>> {
    this.logger.log(`获取战斗回放: ${payload.roomId}`);
    
    const result = await this.battleService.getBattleReplay(payload);
    return this.fromResult(result);
  }

  /**
   * 删除战斗房间
   * 基于old项目的deleteBattleRoom接口
   */
  @MessagePattern('battle.deleteBattleRoom')
  @CacheEvict({
    key: 'battle:replay:#{payload.roomId}',
    dataType: 'global'
  })
  async deleteBattleRoom(@Payload() payload: DeleteBattleRoomPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`删除战斗房间: ${payload.roomId}`);
    
    const result = await this.battleService.deleteBattleRoom(payload.roomId);
    return this.fromResult(result);
  }

  /**
   * 获取战斗统计信息（管理接口）
   */
  @MessagePattern('battle.getStatistics')
  async getStatistics(@Payload() payload: GetStatisticsPayloadDto): Promise<XResponse<any>> {
    this.logger.log('获取战斗统计信息');
    
    const result = await this.battleService.getBattleStatistics();
    return this.fromResult(result);
  }

  /**
   * 清理过期房间（管理接口）
   */
  @MessagePattern('battle.cleanExpiredRooms')
  async cleanExpiredRooms(@Payload() payload: CleanExpiredRoomsPayloadDto): Promise<XResponse<any>> {
    this.logger.log('清理过期战斗房间');
    
    const result = await this.battleService.cleanExpiredRooms();
    return this.fromResult(result);
  }
}
