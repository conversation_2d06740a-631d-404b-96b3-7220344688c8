import { Controller, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { BusinessService } from './business.service';
import { 

  GetBusinessMatchInfoResponseDto,
  BusinessSearchResponseDto,
  BusinessMatchResponseDto
} from '../../common/dto/business.dto';
import { Cacheable, CacheEvict } from '@libs/redis';

import { XResponse } from '@libs/common/types/result.type';

import {
  BusinessMatchPayloadDto,
  BusinessSearchPayloadDto,
  BuyBusinessMatchPayloadDto,
  GetBusinessMatchInfoPayloadDto,
  GetStatisticsPayloadDto
} from "@match/common/dto/business-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';
/**
 * 商业赛系统控制器
 * 严格基于old项目businessMatch.js的接口设计
 * 
 * 核心接口：
 * - business.getBusinessMatchInfo: 获取商业赛信息
 * - business.businessSearch: 商业赛搜索
 * - business.businessMatch: 商业赛匹配
 * - business.buyBusinessMatch: 购买商业赛次数
 */
@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class BusinessController extends BaseController {
  constructor(private readonly businessService: BusinessService) {
    super('BusinessController');
  }

  /**
   * 获取商业赛信息
   * 基于old项目的getBusinessMatchInfo接口
   */
  @MessagePattern('business.getBusinessMatchInfo')
  @Cacheable({
    key: 'business:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300 // 5分钟缓存
  })
  async getBusinessMatchInfo(@Payload() payload: GetBusinessMatchInfoPayloadDto): Promise<XResponse<GetBusinessMatchInfoResponseDto>> {
    this.logger.log(`获取商业赛信息: ${payload.characterId}`);
    
    const result = await this.businessService.getBusinessMatchInfo(payload);
    return this.fromResult(result);
  }

  /**
   * 商业赛搜索
   * 基于old项目的businessSearch接口
   */
  @MessagePattern('business.businessSearch')
  @CacheEvict({
    key: 'business:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async businessSearch(@Payload() payload: BusinessSearchPayloadDto): Promise<XResponse<BusinessSearchResponseDto>> {
    this.logger.log(`商业赛搜索: ${payload.characterId}, 搜索: ${payload.name}`);
    
    const result = await this.businessService.businessSearch(payload);
    return this.fromResult(result);
  }

  /**
   * 商业赛匹配
   * 基于old项目的businessMatch接口
   */
  @MessagePattern('business.businessMatch')
  @CacheEvict({
    key: 'business:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async businessMatch(@Payload() payload: BusinessMatchPayloadDto): Promise<XResponse<BusinessMatchResponseDto>> {
    this.logger.log(`商业赛匹配: ${payload.characterId} vs ${payload.enemyUid}`);
    
    const result = await this.businessService.businessMatch(payload);
    return this.fromResult(result);
  }

  /**
   * 购买商业赛次数
   * 基于old项目的buyBusinessMatch接口
   */
  @MessagePattern('business.buyBusinessMatch')
  @CacheEvict({
    key: 'business:info:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async buyBusinessMatch(@Payload() payload: BuyBusinessMatchPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`购买商业赛次数: ${payload.characterId}, 数量: ${payload.num}`);
    
    const result = await this.businessService.buyBusinessMatch(payload);
    return this.fromResult(result);
  }

  /**
   * 获取商业赛统计信息（管理接口）
   * TODO: 实现统计信息获取逻辑
   */
  @MessagePattern('business.getStatistics')
  async getStatistics(@Payload() payload: GetStatisticsPayloadDto): Promise<XResponse<any>> {
    this.logger.log('获取商业赛统计信息');
    
    return this.toSuccessResponse(undefined);
  }

  /**
   * 重置每日战斗次数（管理接口）
   * TODO: 实现重置逻辑
   */
  @MessagePattern('business.resetDailyFightTimes')
  async resetDailyFightTimes(@Payload() payload: GetStatisticsPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`重置每日战斗次数: ${payload.characterId || 'all'}`);
    
    return this.toSuccessResponse(undefined);
  }
}
