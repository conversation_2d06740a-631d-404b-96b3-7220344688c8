import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { XResult, XResultUtils } from '@libs/common/types/result.type';
import { RepositoryResultWrapper } from '@libs/common/repository';
import { WarOfFaith, WarOfFaithDocument, WarParticipant, BattleRecord } from '../schemas/war-of-faith.schema';

/**
 * 信仰之战数据访问层
 * 基于old项目的信仰之战数据管理
 * 
 * 🎯 核心功能：
 * - 战争状态管理
 * - 参赛选手管理
 * - 战斗记录管理
 * - 占领历史管理
 * - 挑战次数管理
 * 
 * 🔧 数据操作：
 * - 战争信息CRUD
 * - 参赛者增删改查
 * - 战斗记录存储
 * - 统计数据聚合
 * - 历史数据清理
 * 
 * old项目对应：
 * - WarOfFaith.js中的数据操作
 * - 战争状态持久化
 * - 战斗记录存储
 */
@Injectable()
export class WarOfFaithRepository extends BaseRepository<WarOfFaithDocument> {
  constructor(
    @InjectModel(WarOfFaith.name) 
    private readonly warOfFaithModel: Model<WarOfFaithDocument>,
  ) {
    super(warOfFaithModel, 'WarOfFaithRepository');
  }

  /**
   * 获取当前活跃的战争
   */
  async getCurrentWar(serverId: string): Promise<XResult<WarOfFaithDocument | null>> {
    return RepositoryResultWrapper.wrapNullable(async () => {
      return await this.warOfFaithModel
        .findOne({ 
          serverId, 
          status: { $in: ['preparation', 'battle'] } 
        })
        .sort({ startTime: -1 })
        .exec();
    });
  }

  /**
   * 创建新的战争
   */
  async createNewWar(warData: {
    warId: string;
    serverId: string;
    season: number;
    duration: number;
  }): Promise<XResult<WarOfFaithDocument>> {
    return RepositoryResultWrapper.wrap(async () => {
      const war = new this.warOfFaithModel({
        ...warData,
        status: 'preparation',
        startTime: Date.now(),
        participants: [],
        battles: [],
        occupations: [],
        challenges: [],
        currentOccupierId: 0,
        lastUpdateTime: Date.now()
      });

      return await war.save();
    });
  }

  /**
   * 添加参赛选手
   */
  async addParticipant(warId: string, participant: WarParticipant): Promise<XResult<WarOfFaithDocument | null>> {
    return RepositoryResultWrapper.wrapNullable(async () => {
      const war = await this.warOfFaithModel.findOne({ warId }).exec();
      if (!war) {
        throw new Error('战争不存在');
      }

      // 检查是否已存在
      const existing = war.participants.find(p => p.playerId === participant.playerId);
      if (existing) {
        throw new Error('玩家已参赛');
      }

      war.participants.push(participant);

      war.lastUpdateTime = Date.now();
      return await war.save();
    });
  }

  /**
   * 移除参赛选手
   */
  async removeParticipant(warId: string, playerId: string): Promise<XResult<WarOfFaithDocument | null>> {
    return RepositoryResultWrapper.wrapNullable(async () => {
      const war = await this.warOfFaithModel.findOne({ warId }).exec();
      if (!war) {
        throw new Error('战争不存在');
      }

      const index = war.participants.findIndex(p => p.playerId === playerId);
      if (index === -1) {
        throw new Error('玩家未参赛');
      }

      war.participants.splice(index, 1);

      war.lastUpdateTime = Date.now();
      return await war.save();
    });
  }

  /**
   * 添加战斗记录
   */
  async addBattleRecord(warId: string, battle: BattleRecord): Promise<XResult<WarOfFaithDocument | null>> {
    return RepositoryResultWrapper.wrapNullable(async () => {
      const war = await this.warOfFaithModel.findOne({ warId }).exec();
      if (!war) {
        throw new Error('战争不存在');
      }

      // 添加战斗记录
      war.battles.push(battle);

      // 更新参赛者战绩
      const attacker = war.participants.find(p => p.playerId === battle.attackerId);
      const defender = war.participants.find(p => p.playerId === battle.defenderId);

      if (battle.result === 'attacker_win') {
        if (attacker) attacker.wins += 1;
        if (defender) defender.losses += 1;
      } else if (battle.result === 'defender_win') {
        if (attacker) attacker.losses += 1;
        if (defender) defender.wins += 1;
      }

      // 保持最多1000条战斗记录
      if (war.battles.length > 1000) {
        war.battles = war.battles.slice(-1000);
      }
      war.lastUpdateTime = Date.now();
      return await war.save();
    });
  }

  /**
   * 更新占领状态
   */
  async updateOccupation(warId: string, beliefId: number, beliefName: string): Promise<XResult<WarOfFaithDocument | null>> {
    return RepositoryResultWrapper.wrapNullable(async () => {
      const war = await this.warOfFaithModel.findOne({ warId }).exec();
      if (!war) {
        throw new Error('战争不存在');
      }

      // 结束当前占领
      const currentOccupation = war.occupations.find(o => o.status === 'active');
      if (currentOccupation) {
        currentOccupation.status = 'ended';
        currentOccupation.duration = Date.now() - currentOccupation.occupyTime;
      }

      // 开始新占领
      war.occupations.push({
        beliefId,
        beliefName,
        occupyTime: Date.now(),
        duration: 0,
        status: 'active',
        defenseCount: 0,
        challengeCount: 0
      });

      war.currentOccupierId = beliefId;
      war.lastUpdateTime = Date.now();
      return await war.save();
    });
  }

  /**
   * 更新挑战记录
   */
  async updateChallengeRecord(warId: string, playerId: string, beliefId: number): Promise<XResult<WarOfFaithDocument | null>> {
    return RepositoryResultWrapper.wrapNullable(async () => {
      const war = await this.warOfFaithModel.findOne({ warId }).exec();
      if (!war) {
        throw new Error('战争不存在');
      }

      // 更新挑战记录
      let record = war.challenges.find(c => c.playerId === playerId);

      if (!record) {
        record = {
          playerId,
          beliefId,
          usedChallenges: 0,
          maxChallenges: 3,
          lastChallengeTime: Date.now(),
          resetTime: Date.now()
        };
        war.challenges.push(record);
      }

      record.usedChallenges += 1;
      record.lastChallengeTime = Date.now();
      war.lastUpdateTime = Date.now();
      return await war.save();
    });
  }

  /**
   * 获取战斗历史
   */
  async getBattleHistory(
    serverId: string,
    page: number = 1,
    limit: number = 20,
    beliefId?: number
  ): Promise<XResult<{
    battles: BattleRecord[];
    total: number;
    page: number;
    totalPages: number;
  }>> {
    return RepositoryResultWrapper.wrap(async () => {
      const matchConditions: any = { serverId };
      if (beliefId) {
        matchConditions.$or = [
          { 'battles.attackerBeliefId': beliefId },
          { 'battles.defenderBeliefId': beliefId }
        ];
      }

      const pipeline: any[] = [
        { $match: matchConditions },
        { $unwind: '$battles' },
        { $sort: { 'battles.battleTime': -1 } },
        {
          $facet: {
            battles: [
              { $skip: (page - 1) * limit },
              { $limit: limit },
              { $replaceRoot: { newRoot: '$battles' } }
            ],
            totalCount: [
              { $count: 'count' }
            ]
          }
        }
      ];

      const result = await this.warOfFaithModel.aggregate(pipeline).exec();
      const data = result[0];
      const battles = data.battles || [];
      const total = data.totalCount[0]?.count || 0;

      return {
        battles,
        total,
        page,
        totalPages: Math.ceil(total / limit)
      };
    });
  }

  /**
   * 获取参赛选手统计
   */
  async getParticipantStats(serverId: string, beliefId?: number): Promise<XResult<{
    participants: WarParticipant[];
    totalCount: number;
    beliefGroups: Record<number, WarParticipant[]>;
  }>> {
    return RepositoryResultWrapper.wrap(async () => {
      const matchConditions: any = { serverId, status: { $in: ['preparation', 'battle'] } };

      const war = await this.warOfFaithModel.findOne(matchConditions).exec();
      if (!war) {
        return {
          participants: [],
          totalCount: 0,
          beliefGroups: {}
        };
      }

      let participants = war.participants;
      if (beliefId) {
        participants = participants.filter(p => p.beliefId === beliefId);
      }

      // 按信仰分组
      const beliefGroups: Record<number, WarParticipant[]> = {};
      participants.forEach(participant => {
        if (!beliefGroups[participant.beliefId]) {
          beliefGroups[participant.beliefId] = [];
        }
        beliefGroups[participant.beliefId].push(participant);
      });

      return {
        participants,
        totalCount: participants.length,
        beliefGroups
      };
    });
  }

  /**
   * 获取占领历史
   */
  async getOccupationHistory(serverId: string, beliefId?: number): Promise<XResult<any[]>> {
    return RepositoryResultWrapper.wrap(async () => {
      const matchConditions: any = { serverId };
      if (beliefId) {
        matchConditions['occupations.beliefId'] = beliefId;
      }

      const pipeline: any[] = [
        { $match: matchConditions },
        { $unwind: '$occupations' },
        { $sort: { 'occupations.occupyTime': -1 } },
        { $replaceRoot: { newRoot: '$occupations' } }
      ];

      return await this.warOfFaithModel.aggregate(pipeline).exec();
    });
  }

  /**
   * 获取战争统计
   */
  async getWarStats(serverId: string): Promise<XResult<any>> {
    return RepositoryResultWrapper.wrap(async () => {
      const war = await this.warOfFaithModel
        .findOne({ serverId, status: { $in: ['preparation', 'battle'] } })
        .exec();

      if (!war) {
        return {
          totalBattles: 0,
          beliefStats: {},
          participationStats: {},
          winRateStats: {}
        };
      }

      // 计算战斗统计
      const battleStats = this.calculateBattleStats(war.battles);
      
      return {
        totalBattles: battleStats.totalBattles,
        beliefStats: battleStats.beliefStats,
        participationStats: this.calculateParticipationStats(war.participants),
        winRateStats: this.calculateWinRateStats(war.participants)
      };
    });
  }

  /**
   * 结束战争
   */
  async endWar(warId: string): Promise<XResult<WarOfFaithDocument | null>> {
    return RepositoryResultWrapper.wrapNullable(async () => {
      return await this.warOfFaithModel.findOneAndUpdate(
        { warId },
        { 
          status: 'ended',
          endTime: Date.now(),
          lastUpdateTime: Date.now()
        },
        { new: true }
      ).exec();
    });
  }

  /**
   * 重置每日挑战次数
   */
  async resetDailyChallenges(serverId: string): Promise<XResult<void>> {
    return RepositoryResultWrapper.wrap(async () => {
      const war = await this.warOfFaithModel
        .findOne({ serverId, status: { $in: ['preparation', 'battle'] } })
        .exec();

      if (war) {
        // 重置每日挑战次数
        const now = Date.now();
        war.challenges.forEach(record => {
          record.usedChallenges = 0;
          record.resetTime = now;
        });
        await war.save();
      }
    });
  }

  /**
   * 清理过期战争数据
   */
  async cleanExpiredWars(daysToKeep: number = 30): Promise<XResult<number>> {
    return RepositoryResultWrapper.wrap(async () => {
      const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);
      
      const result = await this.warOfFaithModel.deleteMany({
        status: 'ended',
        endTime: { $lt: cutoffTime }
      }).exec();

      return result.deletedCount || 0;
    });
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 计算战斗统计
   */
  private calculateBattleStats(battles: BattleRecord[]): any {
    const stats = {
      totalBattles: battles.length,
      beliefStats: new Map()
    };

    battles.forEach(battle => {
      // 统计攻击者信仰
      if (!stats.beliefStats.has(battle.attackerBeliefId)) {
        stats.beliefStats.set(battle.attackerBeliefId, { wins: 0, losses: 0, battles: 0 });
      }
      const attackerStats = stats.beliefStats.get(battle.attackerBeliefId);
      attackerStats.battles += 1;
      if (battle.result === 'attacker_win') {
        attackerStats.wins += 1;
      } else if (battle.result === 'defender_win') {
        attackerStats.losses += 1;
      }

      // 统计防守者信仰
      if (!stats.beliefStats.has(battle.defenderBeliefId)) {
        stats.beliefStats.set(battle.defenderBeliefId, { wins: 0, losses: 0, battles: 0 });
      }
      const defenderStats = stats.beliefStats.get(battle.defenderBeliefId);
      defenderStats.battles += 1;
      if (battle.result === 'defender_win') {
        defenderStats.wins += 1;
      } else if (battle.result === 'attacker_win') {
        defenderStats.losses += 1;
      }
    });

    return {
      totalBattles: stats.totalBattles,
      beliefStats: Object.fromEntries(stats.beliefStats)
    };
  }

  /**
   * 计算参与度统计
   */
  private calculateParticipationStats(participants: WarParticipant[]): any {
    const beliefParticipation: Record<number, number> = {};
    
    participants.forEach(participant => {
      beliefParticipation[participant.beliefId] = (beliefParticipation[participant.beliefId] || 0) + 1;
    });

    return {
      totalParticipants: participants.length,
      beliefParticipation,
      averageLevel: participants.reduce((sum, p) => sum + p.level, 0) / participants.length || 0,
      averagePower: participants.reduce((sum, p) => sum + p.power, 0) / participants.length || 0
    };
  }

  /**
   * 计算胜率统计
   */
  private calculateWinRateStats(participants: WarParticipant[]): any {
    const beliefWinRates: Record<number, { wins: number; total: number; rate: number }> = {};

    participants.forEach(participant => {
      const total = participant.wins + participant.losses;
      const rate = total > 0 ? participant.wins / total : 0;

      if (!beliefWinRates[participant.beliefId]) {
        beliefWinRates[participant.beliefId] = { wins: 0, total: 0, rate: 0 };
      }

      beliefWinRates[participant.beliefId].wins += participant.wins;
      beliefWinRates[participant.beliefId].total += total;
    });

    // 计算每个信仰的总胜率
    Object.keys(beliefWinRates).forEach(beliefIdStr => {
      const beliefId = parseInt(beliefIdStr);
      const stats = beliefWinRates[beliefId];
      stats.rate = stats.total > 0 ? stats.wins / stats.total : 0;
    });

    return beliefWinRates;
  }
}
