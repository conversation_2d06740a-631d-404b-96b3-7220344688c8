/**
 * Trophy模块的Payload DTO定义
 * 
 * 为trophy.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 杯赛副本数据相关 ====================

/**
 * 获取杯赛副本数据Payload DTO
 * @MessagePattern('trophy.getTrophyCopyData')
 * 扩展GetTrophyCopyDataDto，合并BasePayloadDto内容
 */
export class GetTrophyCopyDataPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

// ==================== 2. PVE杯赛战斗相关 ====================

/**
 * PVE杯赛战斗Payload DTO
 * @MessagePattern('trophy.pveTrophyBattle')
 * 扩展PveTrophyBattleDto，合并BasePayloadDto内容
 */
export class PveTrophyBattlePayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '杯赛ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '杯赛ID必须是数字' })
  trophyId: number;

  @ApiProperty({ description: '球队副本ID', example: 2001 })
  @Expose()
  @IsNumber({}, { message: '球队副本ID必须是数字' })
  teamCopyId: number;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

// ==================== 3. 购买杯赛次数相关 ====================

/**
 * 购买杯赛次数Payload DTO
 * @MessagePattern('trophy.buyTrophyTimes')
 * 扩展BuyTrophyTimesDto，合并BasePayloadDto内容
 */
export class BuyTrophyTimesPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiProperty({ description: '杯赛ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '杯赛ID必须是数字' })
  trophyId: number;

  @ApiPropertyOptional({ description: '队伍ID（可选，用于购买特定队伍的次数）', example: 101 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '队伍ID必须是数字' })
  teamId?: number;

  @ApiProperty({ description: '购买次数', example: 5 })
  @Expose()
  @IsNumber({}, { message: '购买次数必须是数字' })
  num: number;

  @ApiPropertyOptional({ description: '服务器ID（可选）', example: 'server_001' })
  @Expose()
  @IsOptional()
  @IsString({ message: '服务器ID必须是字符串' })
  serverId?: string;
}

// ==================== 4. 管理接口相关 ====================

/**
 * 获取杯赛统计信息Payload DTO
 * @MessagePattern('trophy.getStatistics')
 */
export class GetStatisticsPayloadDto extends BasePayloadDto {
  // 仅继承BasePayloadDto，无需额外字段
}

/**
 * 重置每日杯赛次数Payload DTO
 * @MessagePattern('trophy.resetDailyTimes')
 */
export class ResetDailyTimesPayloadDto extends BasePayloadDto {
  @ApiPropertyOptional({ description: '角色ID（可选，不指定则重置所有）', example: 'char_12345' })
  @Expose()
  @IsOptional()
  @IsString({ message: '角色ID必须是字符串' })
  characterId?: string;
}

/**
 * 获取杯赛战斗历史Payload DTO
 * @MessagePattern('trophy.getBattleHistory')
 */
export class GetBattleHistoryPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '角色ID', example: 'char_12345' })
  @Expose()
  @IsString({ message: '角色ID必须是字符串' })
  characterId: string;

  @ApiPropertyOptional({ description: '获取数量限制（可选）', example: 50 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '获取数量限制必须是数字' })
  limit?: number;
}
