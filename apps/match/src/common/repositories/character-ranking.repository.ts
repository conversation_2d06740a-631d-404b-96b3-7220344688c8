import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { CharacterRanking } from '../schemas/ranking.schema';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { XResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 角色排名数据访问层
 * 继承BaseRepository，提供统一的数据访问接口和Result模式错误处理
 *
 * 🎯 核心功能：
 * - 玩家排名数据管理
 * - 排名数据CRUD操作
 * - 排名统计分析
 *
 * 🚀 性能优化：
 * - 使用BaseRepository的Lean查询优化
 * - 自动性能监控和慢查询检测
 * - 智能缓存策略
 */
@Injectable()
export class CharacterRankingRepository extends BaseRepository<CharacterRanking> {

  constructor(
    @InjectModel(CharacterRanking.name) characterRankingModel: Model<CharacterRanking>
  ) {
    super(characterRankingModel, 'CharacterRankingRepository');
  }

  // ==================== 玩家排名相关 ====================

  /**
   * 根据玩家UID查找排名数据
   * 使用BaseRepository的findOne方法优化性能
   */
  async findCharacterRanking(uid: string): Promise<XResult<CharacterRanking | null>> {
    return this.findOne({ uid });
  }

  /**
   * 根据玩家UID查找排名数据（Lean查询优化版本）
   */
  async findCharacterRankingLean(uid: string): Promise<XResult<any | null>> {
    return this.findOneLean({ uid });
  }

  /**
   * 创建或更新玩家排名数据
   * 使用BaseRepository的upsert方法优化性能
   * 已修复：正确调用BaseRepository.upsert方法，方法名保持不变因为参数类型兼容
   */
  async upsertCharacterRanking(uid: string, characterRankingData: Partial<CharacterRanking>): Promise<XResult<CharacterRanking>> {
    const upsertResult = await super.upsert(
      { uid },
      { ...characterRankingData, lastUpdateTime: new Date() }
    );

    if (XResultUtils.isFailure(upsertResult)) {
      return XResultUtils.error(`玩家排名upsert失败: ${upsertResult.message}`, upsertResult.code);
    }

    // 从BaseRepository的upsert结果中提取document
    return XResultUtils.ok(upsertResult.data.document);
  }

  /**
   * 更新玩家排名数据
   * 使用BaseRepository的updateOne方法优化性能
   */
  async updateCharacterRanking(
    uid: string,
    updateData: Partial<CharacterRanking>,
    session?: ClientSession
  ): Promise<XResult<CharacterRanking | null>> {
    return this.updateOne(
      { uid },
      { ...updateData, lastUpdateTime: new Date() },
      session
    );
  }

  /**
   * 删除玩家排名数据
   * 使用BaseRepository的deleteOne方法优化性能
   */
  async deleteCharacterRanking(uid: string): Promise<XResult<boolean>> {
    return this.deleteOne({ uid });
  }

  /**
   * 批量查询玩家排名数据
   * 使用BaseRepository的findMany方法优化性能
   */
  async findCharacterRankings(uids: string[]): Promise<XResult<CharacterRanking[]>> {
    return this.findMany({ uid: { $in: uids } });
  }

  /**
   * 批量查询玩家排名数据（Lean查询优化版本）
   */
  async findCharacterRankingsLean(uids: string[]): Promise<XResult<any[]>> {
    return this.findManyLean({ uid: { $in: uids } }, {
      select: ['uid', 'lastUpdateTime', 'rankings']
    });
  }

  /**
   * 获取排名统计信息
   * 使用BaseRepository的count和aggregate方法优化性能
   */
  async getCharacterRankingStatistics(): Promise<XResult<any>> {
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    // 并行执行统计查询
    const [totalResult, activeResult] = await Promise.all([
      this.count({}),
      this.count({
        lastUpdateTime: { $gte: oneDayAgo }
      })
    ]);

    // 检查查询结果
    if (XResultUtils.isFailure(totalResult) || XResultUtils.isFailure(activeResult)) {
      return XResultUtils.error('获取角色排名统计信息失败', 'CHARACTER_RANKING_STATISTICS_QUERY_FAILED');
    }

    return XResultUtils.ok({
      totalCharacters: totalResult.data,
      activeToday: activeResult.data,
      timestamp: new Date(),
    });
  }

  // ========== 重写BaseRepository方法以添加业务特定逻辑 ==========

  /**
   * 重写数据验证方法，添加角色排名特定的验证规则
   */
  protected validateData(data: Partial<CharacterRanking>, operation: 'create' | 'update'): XResult<void> {
    if (operation === 'create') {
      if (!data.uid) {
        return XResultUtils.error('玩家UID不能为空', 'UID_REQUIRED');
      }
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 重写缓存TTL配置，针对角色排名数据的访问模式优化
   */
  protected getCacheTTL(operation: string): number {
    const customTTLs: Record<string, number> = {
      'findCharacterRanking': 300,          // 角色排名缓存5分钟
      'findCharacterRankingLean': 180,      // 角色排名简介缓存3分钟
      'getCharacterRankingStatistics': 60,  // 统计信息缓存1分钟
      'findCharacterRankings': 180,         // 批量查询缓存3分钟
    };

    return customTTLs[operation] || super.getCacheTTL(operation);
  }
}
