import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { Tournament, TournamentBattleRecord } from '../schemas/tournament.schema';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { XResult, PaginationResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 锦标赛数据访问层
 * 继承BaseRepository，提供统一的数据访问接口和Result模式错误处理
 *
 * 🎯 核心功能：
 * - 锦标赛数据CRUD操作
 * - 战斗记录管理
 * - 锦标赛统计分析
 * - 过期数据清理
 * - 批量操作支持
 *
 * 🚀 性能优化：
 * - 使用BaseRepository的Lean查询优化
 * - 自动性能监控和慢查询检测
 * - 智能缓存策略
 * - 并行查询优化
 */
@Injectable()
export class TournamentRepository extends BaseRepository<Tournament> {
  constructor(
    @InjectModel(Tournament.name) tournamentModel: Model<Tournament>
  ) {
    super(tournamentModel, 'TournamentRepository');
  }

  // ========== 业务特定方法 ==========

  /**
   * 根据玩家UID查找锦标赛数据
   * 使用BaseRepository的findOne方法优化性能
   */
  async findByUid(uid: string): Promise<XResult<Tournament | null>> {
    return this.findOne({ uid });
  }

  /**
   * 根据玩家UID查找锦标赛数据（Lean查询优化版本）
   */
  async findByUidLean(uid: string): Promise<XResult<any | null>> {
    return this.findOneLean({ uid });
  }

  /**
   * 创建新的锦标赛数据
   * 使用BaseRepository的createOne方法，自动应用Result模式和性能监控
   */
  async create(tournamentData: Partial<Tournament>): Promise<XResult<Tournament>> {
    return this.createOne(tournamentData);
  }

  /**
   * 更新锦标赛数据
   * 使用BaseRepository的updateOne方法优化性能
   */
  async updateByUid(
    uid: string,
    updateData: Partial<Tournament>,
    session?: ClientSession
  ): Promise<XResult<Tournament | null>> {
    return this.updateOne(
      { uid },
      { ...updateData, lastUpdateTime: new Date() },
      session
    );
  }

  /**
   * 创建或更新锦标赛数据（按UID）
   * 使用BaseRepository的upsert方法优化性能
   * 已修复：重命名方法避免与BaseRepository.upsert签名冲突
   */
  async upsertByUid(uid: string, tournamentData: Partial<Tournament>): Promise<XResult<Tournament>> {
    const upsertResult = await super.upsert(
      { uid },
      { ...tournamentData, lastUpdateTime: new Date() }
    );

    if (XResultUtils.isFailure(upsertResult)) {
      return XResultUtils.error(`锦标赛数据upsert失败: ${upsertResult.message}`, upsertResult.code);
    }

    // 从BaseRepository的upsert结果中提取document
    return XResultUtils.ok(upsertResult.data.document);
  }

  /**
   * 添加战斗记录到指定杯赛
   * 使用BaseRepository的事务支持确保数据一致性
   */
  async addBattleRecord(uid: string, cupType: string, battleRecord: TournamentBattleRecord): Promise<XResult<Tournament | null>> {
    return this.withTransaction(async (session) => {
      const tournamentResult = await this.findByUid(uid);
      if (XResultUtils.isFailure(tournamentResult) || !tournamentResult.data) {
        return XResultUtils.error(`锦标赛数据不存在: ${uid}`, 'TOURNAMENT_NOT_FOUND');
      }

      const tournament = tournamentResult.data;
      let combatList: TournamentBattleRecord[] = [];

      switch (cupType) {
        case 'middleEast':
          combatList = tournament.middleEastCup?.combatList || [];
          break;
        case 'gulf':
          combatList = tournament.gulfCup?.combatList || [];
          break;
        case 'mls':
          combatList = tournament.mls?.combatList || [];
          break;
        default:
          return XResultUtils.error(`未知的杯赛类型: ${cupType}`, 'INVALID_CUP_TYPE');
      }

      // 限制战斗记录数量为50条
      if (combatList.length >= 50) {
        const removeCount = combatList.length - 50 + 1;
        combatList.splice(0, removeCount);
      }

      // 添加新记录
      combatList.push(battleRecord);

      // 按时间排序（最新的在前）
      combatList.sort((a, b) => {
        return new Date(b.battleTime).getTime() - new Date(a.battleTime).getTime();
      });

      // 更新对应的杯赛数据
      const updateData: any = {};
      updateData[`${cupType}Cup.combatList`] = combatList;

      return await this.updateByUid(uid, updateData, session);
    });
  }

  /**
   * 重置每日数据
   * 使用BaseRepository的事务支持确保数据一致性
   */
  async resetDailyData(uid: string): Promise<XResult<Tournament | null>> {
    return this.withTransaction(async (session) => {
      const tournamentResult = await this.findByUid(uid);
      if (XResultUtils.isFailure(tournamentResult) || !tournamentResult.data) {
        return XResultUtils.ok(null);
      }

      const tournament = tournamentResult.data;
      const now = new Date();
      const updateData: any = {};

      // 重置世界杯数据
      if (tournament.worldCup) {
        updateData['worldCup.joinCount'] = 1; // 默认每日1次
        updateData['worldCup.buyCount'] = 0;
        updateData['worldCup.reTime'] = now;
      }

      // 重置区域杯赛数据
      ['middleEastCup', 'gulfCup', 'mls'].forEach(cupType => {
        const cupData = tournament[cupType];
        if (cupData) {
          updateData[`${cupType}.contestNum`] = 0;
          updateData[`${cupType}.isBegin`] = 0;
          updateData[`${cupType}.rivalTeamList`] = [];
          updateData[`${cupType}.awardList`] = [];
          updateData[`${cupType}.combatList`] = [];
          updateData[`${cupType}.goal`] = 0;
          updateData[`${cupType}.fumble`] = 0;
          updateData[`${cupType}.diff`] = 0;
          updateData[`${cupType}.win`] = 0;
          updateData[`${cupType}.flashTime`] = now;
        }
      });

      return await this.updateByUid(uid, updateData, session);
    });
  }

  /**
   * 删除锦标赛数据
   * 使用BaseRepository的deleteOne方法优化性能
   */
  async deleteByUid(uid: string): Promise<XResult<boolean>> {
    const result = await this.deleteOne({ uid });
    if (XResultUtils.isSuccess(result)) {
      return XResultUtils.ok(result.data !== null);
    }
    return XResultUtils.ok(false);
  }

  /**
   * 批量查询锦标赛数据
   * 使用BaseRepository的findMany方法优化性能
   */
  async findByUids(uids: string[]): Promise<XResult<Tournament[]>> {
    return this.findMany({ uid: { $in: uids } });
  }

  /**
   * 批量查询锦标赛数据（Lean查询优化版本）
   */
  async findByUidsLean(uids: string[]): Promise<XResult<any[]>> {
    return this.findManyLean({ uid: { $in: uids } }, {
      select: ['uid', 'lastUpdateTime', 'worldCup', 'middleEastCup', 'gulfCup', 'mls']
    });
  }

  /**
   * 获取锦标赛统计信息
   * 使用BaseRepository的count方法和并行查询优化性能
   */
  async getStatistics(): Promise<XResult<any>> {
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    // 并行执行统计查询
    const [totalResult, activeResult, worldCupResult] = await Promise.all([
      this.count({}),
      this.count({
        lastUpdateTime: { $gte: oneDayAgo }
      }),
      this.count({ 'worldCup.isJoin': 1 })
    ]);

    // 检查查询结果
    if (XResultUtils.isFailure(totalResult) ||
        XResultUtils.isFailure(activeResult) ||
        XResultUtils.isFailure(worldCupResult)) {
      return XResultUtils.error('获取锦标赛统计信息失败', 'STATISTICS_QUERY_FAILED');
    }

    return XResultUtils.ok({
      totalCharacters: totalResult.data,
      activeToday: activeResult.data,
      worldCupParticipants: worldCupResult.data,
      timestamp: new Date(),
    });
  }

  /**
   * 批量重置每日数据（管理接口）
   * 使用BaseRepository的updateMany方法优化性能
   */
  async batchResetDailyData(): Promise<XResult<number>> {
    const now = new Date();
    const result = await this.updateMany(
      {},
      {
        $set: {
          'worldCup.joinCount': 1,
          'worldCup.buyCount': 0,
          'worldCup.reTime': now,
          'middleEastCup.contestNum': 0,
          'middleEastCup.isBegin': 0,
          'middleEastCup.rivalTeamList': [],
          'middleEastCup.awardList': [],
          'middleEastCup.combatList': [],
          'middleEastCup.goal': 0,
          'middleEastCup.fumble': 0,
          'middleEastCup.diff': 0,
          'middleEastCup.win': 0,
          'middleEastCup.flashTime': now,
          'gulfCup.contestNum': 0,
          'gulfCup.isBegin': 0,
          'gulfCup.rivalTeamList': [],
          'gulfCup.awardList': [],
          'gulfCup.combatList': [],
          'gulfCup.goal': 0,
          'gulfCup.fumble': 0,
          'gulfCup.diff': 0,
          'gulfCup.win': 0,
          'gulfCup.flashTime': now,
          'mls.contestNum': 0,
          'mls.isBegin': 0,
          'mls.rivalTeamList': [],
          'mls.awardList': [],
          'mls.combatList': [],
          'mls.goal': 0,
          'mls.fumble': 0,
          'mls.diff': 0,
          'mls.win': 0,
          'mls.flashTime': now,
          lastUpdateTime: now,
        }
      }
    );

    if (XResultUtils.isSuccess(result)) {
      const modifiedCount = result.data.modifiedCount || 0;
      this.logger.log(`批量重置每日数据完成: ${modifiedCount}个玩家`);
      return XResultUtils.ok(modifiedCount);
    }

    return XResultUtils.ok(0);
  }

  // ========== 重写BaseRepository方法以添加业务特定逻辑 ==========

  /**
   * 重写数据验证方法，添加锦标赛特定的验证规则
   */
  protected validateData(data: Partial<Tournament>, operation: 'create' | 'update'): XResult<void> {
    if (operation === 'create') {
      if (!data.uid) {
        return XResultUtils.error('玩家UID不能为空', 'UID_REQUIRED');
      }
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 重写缓存TTL配置，针对锦标赛数据的访问模式优化
   */
  protected getCacheTTL(operation: string): number {
    const customTTLs: Record<string, number> = {
      'findByUid': 300,                  // 锦标赛数据缓存5分钟
      'findByUidLean': 180,              // 锦标赛简介缓存3分钟
      'getStatistics': 60,               // 统计信息缓存1分钟
      'findByUids': 180,                 // 批量查询缓存3分钟
    };

    return customTTLs[operation] || super.getCacheTTL(operation);
  }
}
