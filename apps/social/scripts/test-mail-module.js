/**
 * 邮件系统测试模块
 * 基于Hero服务球探系统的成功测试模式
 * 
 * 测试内容：
 * 1. 获取邮件列表
 * 2. 发送邮件
 * 3. 读取邮件
 * 4. 领取邮件附件
 * 5. 删除邮件
 * 6. 批量操作邮件
 * 7. 获取系统邮件
 */

const chalk = require('chalk');

class MailModuleTester {
  constructor(socket, testData) {
    this.socket = socket;
    this.testData = testData;
    this.testResults = [];
    this.mailData = null;
  }

  /**
   * WebSocket调用封装 - 使用Hero服务的成功模式
   */
  async callWebSocket(command, data = {}) {
    return new Promise((resolve, reject) => {
      const messageId = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      const message = {
        id: messageId,
        command: command,
        payload: {
          characterId: this.testData.characterId,
          serverId: this.testData.serverId || 'server_001',
          token: this.testData.token,
          ...data
        }
      };

      console.log(chalk.gray(`📤 发送消息: ${command}`));

      // 设置响应监听器
      const responseHandler = (response) => {
        if (response.id === messageId) {
          console.log(chalk.cyan(`📨 收到响应: ${command}`));
          this.socket.off('message', responseHandler);
          resolve(response);
        }
      };

      this.socket.on('message', responseHandler);

      // 发送消息
      this.socket.emit('message', message);

      // 设置超时
      setTimeout(() => {
        this.socket.off('message', responseHandler);
        reject(new Error(`消息超时: ${command}`));
      }, 10000);
    });
  }

  /**
   * 测试获取邮件列表
   */
  async testGetMailList() {
    console.log(chalk.yellow('📬 测试获取邮件列表...'));

    try {
      const response = await this.callWebSocket('social.mail.getList', {
        playerId: this.testData.characterId,
        page: 1,
        limit: 20
      });

      const success = response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success && data) {
        console.log(chalk.green('✅ 获取邮件列表成功'));
        console.log(chalk.gray(`   邮件数量: ${data.mails ? data.mails.length : 0}`));
        console.log(chalk.gray(`   未读邮件: ${data.unreadCount || 0}`));
        
        this.mailData = data;
        this.testResults.push({
          test: '获取邮件列表',
          success: true,
          data: data
        });
      } else {
        const errorMsg = response?.payload?.data?.message || '未知错误';
        console.log(chalk.red(`❌ 获取邮件列表失败: ${errorMsg}`));
        this.testResults.push({
          test: '获取邮件列表',
          success: false,
          error: errorMsg
        });
      }
    } catch (error) {
      console.log(chalk.red(`❌ 获取邮件列表异常: ${error.message}`));
      this.testResults.push({
        test: '获取邮件列表',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试发送邮件
   */
  async testSendMail() {
    console.log(chalk.yellow('📤 测试发送邮件...'));

    try {
      const targetPlayerId = 'test-target-player-123';
      const response = await this.callWebSocket('social.mail.send', {
        receiverUid: targetPlayerId,
        senderUid: this.testData.characterId,
        mailId: 1001, // 邮件模板ID
        mailType: 1, // 邮件类型
        attachList: [
          {
            itemId: 10001,
            quantity: 1
          }
        ]
      });

      const success = response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success) {
        console.log(chalk.green('✅ 发送邮件成功'));
        console.log(chalk.gray(`   收件人: ${targetPlayerId}`));
        console.log(chalk.gray(`   邮件标题: 测试邮件标题`));
        
        this.testResults.push({
          test: '发送邮件',
          success: true,
          data: data
        });
      } else {
        const errorMsg = response?.payload?.data?.message || '未知错误';
        console.log(chalk.red(`❌ 发送邮件失败: ${errorMsg}`));
        this.testResults.push({
          test: '发送邮件',
          success: false,
          error: errorMsg
        });
      }
    } catch (error) {
      console.log(chalk.red(`❌ 发送邮件异常: ${error.message}`));
      this.testResults.push({
        test: '发送邮件',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试读取邮件
   */
  async testReadMail() {
    console.log(chalk.yellow('📖 测试读取邮件...'));

    try {
      const mailId = 'test-mail-123';
      const response = await this.callWebSocket('social.mail.read', {
        playerId: this.testData.characterId,
        mailUid: mailId
      });

      const success = response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success && data) {
        console.log(chalk.green('✅ 读取邮件成功'));
        console.log(chalk.gray(`   邮件ID: ${mailId}`));
        console.log(chalk.gray(`   邮件标题: ${data.title || 'N/A'}`));
        
        this.testResults.push({
          test: '读取邮件',
          success: true,
          data: data
        });
      } else {
        const errorMsg = response?.payload?.data?.message || '未知错误';
        console.log(chalk.red(`❌ 读取邮件失败: ${errorMsg}`));
        this.testResults.push({
          test: '读取邮件',
          success: false,
          error: errorMsg
        });
      }
    } catch (error) {
      console.log(chalk.red(`❌ 读取邮件异常: ${error.message}`));
      this.testResults.push({
        test: '读取邮件',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试领取邮件附件
   */
  async testClaimAttachments() {
    console.log(chalk.yellow('🎁 测试领取邮件附件...'));

    try {
      const mailId = 'test-mail-123';
      const response = await this.callWebSocket('social.mail.claimAttachment', {
        playerId: this.testData.characterId,
        mailUid: mailId
      });

      const success = response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success) {
        console.log(chalk.green('✅ 领取邮件附件成功'));
        console.log(chalk.gray(`   邮件ID: ${mailId}`));
        console.log(chalk.gray(`   附件数量: ${data.attachments ? data.attachments.length : 0}`));
        
        this.testResults.push({
          test: '领取邮件附件',
          success: true,
          data: data
        });
      } else {
        const errorMsg = response?.payload?.data?.message || '未知错误';
        console.log(chalk.red(`❌ 领取邮件附件失败: ${errorMsg}`));
        this.testResults.push({
          test: '领取邮件附件',
          success: false,
          error: errorMsg
        });
      }
    } catch (error) {
      console.log(chalk.red(`❌ 领取邮件附件异常: ${error.message}`));
      this.testResults.push({
        test: '领取邮件附件',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试删除邮件
   */
  async testDeleteMail() {
    console.log(chalk.yellow('🗑️ 测试删除邮件...'));

    try {
      const mailId = 'test-mail-123';
      const response = await this.callWebSocket('social.mail.delete', {
        playerId: this.testData.characterId,
        mailUid: mailId
      });

      const success = response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success) {
        console.log(chalk.green('✅ 删除邮件成功'));
        console.log(chalk.gray(`   邮件ID: ${mailId}`));
        
        this.testResults.push({
          test: '删除邮件',
          success: true,
          data: data
        });
      } else {
        const errorMsg = response?.payload?.data?.message || '未知错误';
        console.log(chalk.red(`❌ 删除邮件失败: ${errorMsg}`));
        this.testResults.push({
          test: '删除邮件',
          success: false,
          error: errorMsg
        });
      }
    } catch (error) {
      console.log(chalk.red(`❌ 删除邮件异常: ${error.message}`));
      this.testResults.push({
        test: '删除邮件',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试批量操作邮件
   */
  async testBatchOperations() {
    console.log(chalk.yellow('📦 测试批量操作邮件...'));

    try {
      const mailUids = ['test-mail-1', 'test-mail-2', 'test-mail-3'];
      const response = await this.callWebSocket('social.mail.batchDelete', {
        playerId: this.testData.characterId,
        mailUids: mailUids
      });

      const success = response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success) {
        console.log(chalk.green('✅ 批量删除邮件成功'));
        console.log(chalk.gray(`   操作类型: 批量删除`));
        console.log(chalk.gray(`   操作邮件数量: ${mailUids.length}`));
        
        this.testResults.push({
          test: '批量删除邮件',
          success: true,
          data: data
        });
      } else {
        const errorMsg = response?.payload?.data?.message || '未知错误';
        console.log(chalk.red(`❌ 批量删除邮件失败: ${errorMsg}`));
        this.testResults.push({
          test: '批量删除邮件',
          success: false,
          error: errorMsg
        });
      }
    } catch (error) {
      console.log(chalk.red(`❌ 批量删除邮件异常: ${error.message}`));
      this.testResults.push({
        test: '批量删除邮件',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试批量领取附件
   */
  async testBatchClaimAttachments() {
    console.log(chalk.yellow('🎁📦 测试批量领取附件...'));

    try {
      const mailUids = ['test-mail-1', 'test-mail-2', 'test-mail-3'];
      const response = await this.callWebSocket('social.mail.batchClaim', {
        playerId: this.testData.characterId,
        mailUids: mailUids
      });

      const success = response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success) {
        console.log(chalk.green('✅ 批量领取附件成功'));
        console.log(chalk.gray(`   操作邮件数量: ${mailUids.length}`));

        this.testResults.push({
          test: '批量领取附件',
          success: true,
          data: data
        });
      } else {
        const errorMsg = response?.payload?.data?.message || '未知错误';
        console.log(chalk.red(`❌ 批量领取附件失败: ${errorMsg}`));
        this.testResults.push({
          test: '批量领取附件',
          success: false,
          error: errorMsg
        });
      }
    } catch (error) {
      console.log(chalk.red(`❌ 批量领取附件异常: ${error.message}`));
      this.testResults.push({
        test: '批量领取附件',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 运行所有测试
   */
  async runTests() {
    console.log(chalk.cyan('🔧 开始邮件系统测试'));

    // 按顺序执行测试
    await this.testGetMailList();
    await this.testSendMail();
    await this.testReadMail();
    await this.testClaimAttachments();
    await this.testDeleteMail();
    await this.testBatchOperations();
    await this.testBatchClaimAttachments();

    // 返回测试结果
    return this.testResults;
  }
}

module.exports = MailModuleTester;
