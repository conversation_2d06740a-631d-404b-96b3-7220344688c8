/**
 * 好友系统Schema
 * 基于old项目friends.js实体迁移
 */

import { Pro<PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

// 好友状态枚举
export enum FriendStatus {
  PENDING = 0,      // 待确认
  ACCEPTED = 1,     // 已接受
  BLOCKED = 2,      // 已屏蔽
  DELETED = 3,      // 已删除
}

// 好友信息子文档
@Schema({ _id: false })
export class FriendInfo {
  @Prop({ required: true })
  characterId: string;         // 好友玩家ID

  @Prop({ required: true })
  name: string;             // 好友名称

  @Prop({ default: 1 })
  level: number;            // 好友等级

  @Prop({ default: 0 })
  honor: number;            // 段位

  @Prop({ default: 0 })
  longitude: number;        // 经度

  @Prop({ default: 0 })
  latitude: number;         // 纬度

  @Prop({ default: '' })
  faceIcon: string;         // 头像

  @Prop({ default: '' })
  faceUrl: string;          // 头像URL

  @Prop({ default: 0 })
  vip: number;              // VIP等级

  @Prop({ default: 0 })
  trophy: number;           // 奖杯数

  @Prop({ default: Date.now })
  addTime: number;          // 添加时间

  @Prop({ default: Date.now })
  lastOnlineTime: number;   // 最后在线时间

  @Prop({ default: false })
  isOnline: boolean;        // 是否在线
}

// 申请记录子文档
@Schema({ _id: false })
export class FriendApply {
  @Prop({ required: true })
  characterId: string;         // 申请者玩家ID

  @Prop({ required: true })
  name: string;             // 申请者名称

  @Prop({ default: 1 })
  level: number;            // 申请者等级

  @Prop({ default: 0 })
  honor: number;            // 段位

  @Prop({ default: '' })
  faceIcon: string;         // 头像

  @Prop({ default: '' })
  faceUrl: string;          // 头像URL

  @Prop({ default: 0 })
  vip: number;              // VIP等级

  @Prop({ default: 0 })
  trophy: number;           // 奖杯数

  @Prop({ default: Date.now })
  applyTime: number;        // 申请时间

  @Prop({ default: '' })
  message: string;          // 申请消息
}

// 主好友系统Schema
@Schema({ 
  collection: 'friends', 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class Friend {
  // 基础标识信息
  @Prop({ required: true, unique: true })
  friendId: string;         // 好友记录ID

  @Prop({ required: true, index: true })
  characterId: string;         // 玩家ID

  // 好友列表（基于old项目friends Map结构）
  @Prop({ type: [FriendInfo], default: [] })
  friends: FriendInfo[];    // 好友列表

  // 申请列表（基于old项目applyList Map结构）
  @Prop({ type: [FriendApply], default: [] })
  applyList: FriendApply[]; // 收到的好友申请

  // 发出的申请列表（基于old项目relationApply Map结构）
  @Prop({ type: [FriendApply], default: [] })
  relationApply: FriendApply[]; // 发出的好友申请

  // 黑名单
  @Prop({ type: [String], default: [] })
  blackList: string[];      // 黑名单玩家ID列表

  // 统计信息
  @Prop({ default: 0 })
  totalFriends: number;     // 总好友数

  @Prop({ default: 0 })
  onlineFriends: number;    // 在线好友数

  @Prop({ default: 0 })
  pendingApplies: number;   // 待处理申请数

  @Prop({ default: Date.now })
  lastUpdateTime: number;   // 最后更新时间

  // 虚拟字段：好友数量限制检查
  get canAddMoreFriends(): boolean {
    return this.friends.length < 100; // 最大好友数限制
  }

  // 虚拟字段：是否有待处理申请
  get hasPendingApplies(): boolean {
    return this.applyList.length > 0;
  }

  // 虚拟字段：在线好友列表
  get onlineFriendsList(): FriendInfo[] {
    return this.friends.filter(friend => friend.isOnline);
  }
}

export const FriendSchema = SchemaFactory.createForClass(Friend);

// 定义方法接口 - 基于FriendService的真实业务逻辑
export interface FriendMethods {
  // 现有方法
  addFriend(friendInfo: FriendInfo): boolean;
  removeFriend(characterId: string): boolean;
  addApply(applyInfo: FriendApply): boolean;
  removeApply(characterId: string): boolean;
  acceptApply(characterId: string): boolean;
  rejectApply(characterId: string): boolean;
  blockCharacter(characterId: string): boolean;
  unblockCharacter(characterId: string): boolean;
  updateFriendStatus(characterId: string, isOnline: boolean): void;
  getFriendDistance(characterId: string, longitude: number, latitude: number): number;

  // 新增实用业务方法 - 基于FriendService真实逻辑

  // 好友查询 - 基于FriendService
  getFriend(characterId: string): FriendInfo | null;
  hasFriend(characterId: string): boolean;
  isBlocked(characterId: string): boolean;
  hasApplyFrom(characterId: string): boolean;
  hasSentApplyTo(characterId: string): boolean;

  // 位置管理 - 基于FriendService
  updateFriendLocation(characterId: string, longitude: number, latitude: number): boolean;
  getNearbyFriends(longitude: number, latitude: number, radius: number): FriendInfo[];
  getFriendsWithDistance(longitude: number, latitude: number): any[];

  // 统计分析 - 基于FriendService
  updateOnlineFriendsCount(): void;
  updatePendingAppliesCount(): void;
  getOnlineFriends(): FriendInfo[];
  getRecentlyAddedFriends(days: number): FriendInfo[];

  // 申请管理 - 基于FriendService
  cleanExpiredApplies(expireHours: number): number;
  getApplyByCharacterId(characterId: string): FriendApply | null;
  getSentApplyByCharacterId(characterId: string): FriendApply | null;

  // 数据验证 - 基于FriendService
  validateFriendData(): { isValid: boolean; errors: string[] };
  canAddFriend(characterId: string): boolean;
  canSendApply(characterId: string): boolean;

  // 数据转换 - 基于FriendService
  toFriendListDto(): any;
  getFriendStats(): any;
  toClientFriendData(): any;
}

// 定义Document类型
export type FriendDocument = Friend & Document & FriendMethods;

// 创建索引
FriendSchema.index({ friendId: 1 }, { unique: true });
FriendSchema.index({ characterId: 1 });
FriendSchema.index({ 'friends.characterId': 1 });
FriendSchema.index({ 'applyList.characterId': 1 });

// 添加虚拟字段
FriendSchema.virtual('canAddMoreFriends').get(function() {
  return this.friends.length < 100;
});

FriendSchema.virtual('hasPendingApplies').get(function() {
  return this.applyList.length > 0;
});

FriendSchema.virtual('onlineFriendsList').get(function() {
  return this.friends.filter(friend => friend.isOnline);
});

// 添加实例方法
FriendSchema.methods.addFriend = function(friendInfo: FriendInfo) {
  if (!this.canAddMoreFriends) return false;
  
  const existingFriend = this.friends.find(f => f.characterId === friendInfo.characterId);
  if (existingFriend) return false;
  
  this.friends.push(friendInfo);
  this.totalFriends = this.friends.length;
  this.lastUpdateTime = Date.now();
  
  return true;
};

FriendSchema.methods.removeFriend = function(characterId: string) {
  const index = this.friends.findIndex(f => f.characterId === characterId);
  if (index === -1) return false;
  
  this.friends.splice(index, 1);
  this.totalFriends = this.friends.length;
  this.lastUpdateTime = Date.now();
  
  return true;
};

FriendSchema.methods.addApply = function(applyInfo: FriendApply) {
  const existingApply = this.applyList.find(a => a.characterId === applyInfo.characterId);
  if (existingApply) return false;
  
  this.applyList.push(applyInfo);
  this.pendingApplies = this.applyList.length;
  this.lastUpdateTime = Date.now();
  
  return true;
};

FriendSchema.methods.removeApply = function(characterId: string) {
  const index = this.applyList.findIndex(a => a.characterId === characterId);
  if (index === -1) return false;
  
  this.applyList.splice(index, 1);
  this.pendingApplies = this.applyList.length;
  this.lastUpdateTime = Date.now();
  
  return true;
};

FriendSchema.methods.acceptApply = function(characterId: string) {
  const applyIndex = this.applyList.findIndex(a => a.characterId === characterId);
  if (applyIndex === -1) return false;
  
  const apply = this.applyList[applyIndex];
  const friendInfo: FriendInfo = {
    characterId: apply.characterId,
    name: apply.name,
    level: apply.level,
    honor: apply.honor,
    longitude: 0,
    latitude: 0,
    faceIcon: apply.faceIcon,
    faceUrl: apply.faceUrl,
    vip: apply.vip,
    trophy: apply.trophy,
    addTime: Date.now(),
    lastOnlineTime: Date.now(),
    isOnline: true,
  };
  
  if (this.addFriend(friendInfo)) {
    this.removeApply(characterId);
    return true;
  }
  
  return false;
};

FriendSchema.methods.rejectApply = function(characterId: string) {
  return this.removeApply(characterId);
};

FriendSchema.methods.blockCharacter = function(characterId: string) {
  if (this.blackList.includes(characterId)) return false;
  
  this.blackList.push(characterId);
  this.removeFriend(characterId); // 移除好友关系
  this.removeApply(characterId);  // 移除申请
  this.lastUpdateTime = Date.now();
  
  return true;
};

FriendSchema.methods.unblockCharacter = function(characterId: string) {
  const index = this.blackList.indexOf(characterId);
  if (index === -1) return false;
  
  this.blackList.splice(index, 1);
  this.lastUpdateTime = Date.now();
  
  return true;
};

FriendSchema.methods.updateFriendStatus = function(characterId: string, isOnline: boolean) {
  const friend = this.friends.find(f => f.characterId === characterId);
  if (!friend) return;
  
  friend.isOnline = isOnline;
  if (!isOnline) {
    friend.lastOnlineTime = Date.now();
  }
  
  this.onlineFriends = this.friends.filter(f => f.isOnline).length;
  this.lastUpdateTime = Date.now();
};

// 计算距离（基于old项目的地理位置计算）
FriendSchema.methods.getFriendDistance = function(characterId: string, longitude: number, latitude: number) {
  const friend = this.friends.find(f => f.characterId === characterId);
  if (!friend || !friend.longitude || !friend.latitude) return -1;
  
  const R = 6371393; // 地球半径（米）
  const lat1 = latitude * Math.PI / 180;
  const lat2 = friend.latitude * Math.PI / 180;
  const deltaLat = (friend.latitude - latitude) * Math.PI / 180;
  const deltaLng = (friend.longitude - longitude) * Math.PI / 180;
  
  const a = Math.sin(deltaLat/2) * Math.sin(deltaLat/2) +
            Math.cos(lat1) * Math.cos(lat2) *
            Math.sin(deltaLng/2) * Math.sin(deltaLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  
  return R * c; // 距离（米）
};

// ==================== 新增实用业务方法实现 ====================

/**
 * 获取指定好友信息
 * 基于FriendService: getFriendList方法中的好友查询逻辑
 */
FriendSchema.methods.getFriend = function(characterId: string): FriendInfo | null {
  return this.friends.find(f => f.characterId === characterId) || null;
};

/**
 * 检查是否为好友
 * 基于FriendService: 好友关系检查逻辑
 */
FriendSchema.methods.hasFriend = function(characterId: string): boolean {
  return this.friends.some(f => f.characterId === characterId);
};

/**
 * 检查是否被屏蔽
 * 基于FriendService: blockCharacter方法中的屏蔽检查逻辑
 */
FriendSchema.methods.isBlocked = function(characterId: string): boolean {
  return this.blackList.includes(characterId);
};

/**
 * 检查是否有来自指定玩家的申请
 * 基于FriendService: handleFriendApply方法中的申请检查逻辑
 */
FriendSchema.methods.hasApplyFrom = function(characterId: string): boolean {
  return this.applyList.some(a => a.characterId === characterId);
};

/**
 * 检查是否已向指定玩家发送申请
 * 基于FriendService: addFriend方法中的申请检查逻辑
 */
FriendSchema.methods.hasSentApplyTo = function(characterId: string): boolean {
  return this.relationApply.some(a => a.characterId === characterId);
};

/**
 * 更新好友位置信息
 * 基于FriendService: updateCharacterLocation方法逻辑
 */
FriendSchema.methods.updateFriendLocation = function(characterId: string, longitude: number, latitude: number): boolean {
  const friend = this.getFriend(characterId);
  if (!friend) return false;

  friend.longitude = longitude;
  friend.latitude = latitude;
  this.lastUpdateTime = Date.now();

  return true;
};

/**
 * 获取附近的好友
 * 基于FriendService: findNearbyCharacters方法中的距离计算逻辑
 */
FriendSchema.methods.getNearbyFriends = function(longitude: number, latitude: number, radius: number): FriendInfo[] {
  return this.friends.filter(friend => {
    if (!friend.longitude || !friend.latitude) return false;

    const distance = this.getFriendDistance(friend.characterId, longitude, latitude);
    return distance !== -1 && distance <= radius;
  });
};

/**
 * 获取带距离信息的好友列表
 * 基于FriendService: getFriendDistances方法逻辑
 */
FriendSchema.methods.getFriendsWithDistance = function(longitude: number, latitude: number): any[] {
  return this.friends.map(friend => {
    const distance = this.getFriendDistance(friend.characterId, longitude, latitude);
    return {
      ...friend,
      distance: distance,
      distanceText: this.formatDistance(distance)
    };
  }).sort((a, b) => a.distance - b.distance);
};

/**
 * 格式化距离显示
 * 基于FriendService: formatDistance方法逻辑
 */
FriendSchema.methods.formatDistance = function(distance: number): string {
  if (distance === -1) return '未知';
  if (distance < 1000) return `${Math.round(distance)}米`;
  return `${(distance / 1000).toFixed(1)}公里`;
};

/**
 * 更新在线好友数量
 * 基于FriendService: updateCharacterOnlineStatus方法中的统计更新逻辑
 */
FriendSchema.methods.updateOnlineFriendsCount = function(): void {
  this.onlineFriends = this.friends.filter(f => f.isOnline).length;
  this.lastUpdateTime = Date.now();
};

/**
 * 更新待处理申请数量
 * 基于FriendService: 申请统计逻辑
 */
FriendSchema.methods.updatePendingAppliesCount = function(): void {
  this.pendingApplies = this.applyList.length;
  this.lastUpdateTime = Date.now();
};

/**
 * 获取在线好友列表
 * 基于FriendService: getFriendList方法中的在线好友过滤逻辑
 */
FriendSchema.methods.getOnlineFriends = function(): FriendInfo[] {
  return this.friends.filter(f => f.isOnline);
};

/**
 * 获取最近添加的好友
 * 基于FriendService: 好友统计分析需求
 */
FriendSchema.methods.getRecentlyAddedFriends = function(days: number = 7): FriendInfo[] {
  const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000);
  return this.friends.filter(f => f.addTime > cutoffTime);
};

/**
 * 清理过期申请
 * 基于FriendService: 申请管理逻辑
 */
FriendSchema.methods.cleanExpiredApplies = function(expireHours: number = 72): number {
  const expireTime = Date.now() - (expireHours * 60 * 60 * 1000);

  const originalCount = this.applyList.length;
  this.applyList = this.applyList.filter(apply => apply.applyTime > expireTime);

  const removedCount = originalCount - this.applyList.length;
  if (removedCount > 0) {
    this.updatePendingAppliesCount();
  }

  return removedCount;
};

/**
 * 根据玩家ID获取申请信息
 * 基于FriendService: handleFriendApply方法中的申请查询逻辑
 */
FriendSchema.methods.getApplyByCharacterId = function(characterId: string): FriendApply | null {
  return this.applyList.find(a => a.characterId === characterId) || null;
};

/**
 * 根据玩家ID获取发出的申请信息
 * 基于FriendService: addFriend方法中的申请查询逻辑
 */
FriendSchema.methods.getSentApplyByCharacterId = function(characterId: string): FriendApply | null {
  return this.relationApply.find(a => a.characterId === characterId) || null;
};

/**
 * 验证好友数据
 * 基于FriendService: 数据验证逻辑
 */
FriendSchema.methods.validateFriendData = function(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // 检查基础字段
  if (!this.friendId || this.friendId.trim() === '') {
    errors.push('好友记录ID不能为空');
  }

  if (!this.characterId || this.characterId.trim() === '') {
    errors.push('玩家ID不能为空');
  }

  // 检查统计数据一致性
  if (this.totalFriends !== this.friends.length) {
    errors.push('好友总数与实际好友列表长度不一致');
  }

  if (this.pendingApplies !== this.applyList.length) {
    errors.push('待处理申请数与实际申请列表长度不一致');
  }

  // 检查好友数量限制
  if (this.friends.length > 100) {
    errors.push('好友数量超过最大限制(100)');
  }

  // 检查重复好友
  const friendIds = this.friends.map(f => f.characterId);
  const uniqueFriendIds = [...new Set(friendIds)];
  if (friendIds.length !== uniqueFriendIds.length) {
    errors.push('存在重复的好友');
  }

  return { isValid: errors.length === 0, errors };
};
