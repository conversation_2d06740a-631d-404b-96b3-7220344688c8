/**
 * 邮件模块
 * 基于old项目email.js实体迁移
 */

import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { MailController } from './mail.controller';
import { MailService } from './mail.service';
import { MailRepository } from '@social/common/repositories/mail.repository';
import { Mail, MailSchema } from '@social/common/schemas/mail.schema';
import { PushClientService } from '@libs/common/push/push-client.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Mail.name, schema: MailSchema },
    ]),
  ],
  controllers: [MailController],
  providers: [MailService, MailRepository, PushClientService],
  exports: [MailService, MailRepository],
})
export class MailModule {}
