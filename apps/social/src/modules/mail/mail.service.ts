/**
 * 邮件系统服务 - 已适配Result模式
 *
 * 核心功能：
 * - 邮件列表管理
 * - 邮件读取和删除
 * - 邮件附件领取
 * - 系统邮件发送
 * - 批量邮件操作
 * - 邮件统计分析
 *
 * Result模式适配：
 * - 继承BaseService基类，获得统一的业务操作框架
 * - 所有public方法返回XResult<T>类型
 * - 使用executeBusinessOperation包装关键业务操作
 * - 标准化的错误处理和微服务调用
 * - 移除所有try-catch异常处理
 */

import { Injectable, Logger } from '@nestjs/common';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';
import { MailRepository } from '@social/common/repositories/mail.repository';
import {
  CreateMailDto,
  SendMailRewardDto,
  CreateEditMailDto,
  MailListDto,
  MailOperationResultDto,
  ClaimAttachmentDto,
  ReadMailDto,
  DeleteMailDto,
  GetMailListDto
} from '@social/common/dto/mail.dto';
import { MailType, MailStatus } from '@social/common/schemas/mail.schema';
import { XResult, XResultUtils, RepositoryResultWrapper } from '@libs/common/types/result.type';
import { BaseService } from '@libs/common/service/base-service';
import { MicroserviceClientService } from '@libs/service-mesh';
import { PushClientService } from '@libs/common/push/push-client.service';

@Injectable()
export class MailService extends BaseService {
  private readonly MAX_MAIL_COUNT = 50; // 最大邮件数量

  constructor(
    private readonly mailRepository: MailRepository,
    private readonly pushClientService: PushClientService,
    microserviceClient: MicroserviceClientService, // 传递给BaseService
  ) {
    // 调用BaseService构造函数，传入服务名称和微服务客户端
    super('MailService', microserviceClient);
  }

  /**
   * 获取邮件列表（基于old项目getMailList方法）
   * 已适配Result模式：直接返回XResult，无需executeBusinessOperation包装
   */
  async getMailList(dto: GetMailListDto): Promise<XResult<MailListDto>> {
    const { characterId, page = 1, limit = 20 } = dto;

    // 删除过期邮件
    const deleteExpiredResult = await this.mailRepository.deleteExpiredMails();
    if (XResultUtils.isFailure(deleteExpiredResult)) {
      this.logger.warn(`删除过期邮件失败: ${deleteExpiredResult.message}`);
    }

    // 获取邮件列表
    const mailListResult = await this.mailRepository.findByReceiver(characterId, page, limit);
    if (XResultUtils.isFailure(mailListResult)) {
      return XResultUtils.error(`获取邮件列表失败: ${mailListResult.message}`, mailListResult.code);
    }

    const { mails, total } = mailListResult.data;

    // 统计未读邮件数量
    const unreadCountResult = await this.mailRepository.countUnreadByReceiver(characterId);
    if (XResultUtils.isFailure(unreadCountResult)) {
      return XResultUtils.error(`统计未读邮件失败: ${unreadCountResult.message}`, unreadCountResult.code);
    }

    const unreadCount = unreadCountResult.data;

    // 转换为DTO格式
    const mailList = mails.map(mail => ({
      uid: mail.uid,
      senderUid: mail.senderUid,
      receiverUid: mail.receiverUid,
      mailType: mail.mailType,
      title: mail.title,
      content: mail.content,
      attachList: mail.attachList,
      status: mail.status,
      sendTime: mail.sendTime,
      readTime: mail.readTime,
      expireTime: mail.expireTime,
      remainingTime: mail.remainingTime,
      hasAttachment: mail.hasAttachment,
      canClaimAttachment: mail.canClaimAttachment,
    }));

    this.logger.log(`获取邮件列表: ${characterId}, 总数: ${total}, 未读: ${unreadCount}`);

    return XResultUtils.ok({
      mails: mailList,
      total,
      unreadCount,
      isHaveNewMail: unreadCount > 0,
    });
  }



  /**
   * 读取邮件（基于old项目readMail方法）
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async readMail(dto: ReadMailDto): Promise<XResult<MailOperationResultDto>> {
    return this.executeBusinessOperation(async () => {
      const { characterId, mailUid } = dto;

      // 查找邮件
      const mailResult = await this.mailRepository.findByUid(mailUid);
      if (XResultUtils.isFailure(mailResult)) {
        return XResultUtils.error(`查询邮件失败: ${mailResult.message}`, mailResult.code);
      }

      const mail = mailResult.data;
      if (!mail) {
        return XResultUtils.error(ErrorMessages[ErrorCode.MAIL_NOT_FOUND], ErrorCode.MAIL_NOT_FOUND);
      }

      // 检查邮件所有者
      if (mail.receiverUid !== characterId) {
        return XResultUtils.failure('无权限访问该邮件', ErrorCode.MAIL_NOT_FOUND, {
          mailOwner: mail.receiverUid,
          requestUser: characterId
        });
      }

      // 标记为已读
      if (mail.status === MailStatus.UNREAD) {
        const markReadResult = await this.mailRepository.markAsRead(mailUid);
        if (XResultUtils.isFailure(markReadResult)) {
          return XResultUtils.error(`标记邮件已读失败: ${markReadResult.message}`, markReadResult.code);
        }
      }

      this.logger.log(`邮件已读: ${characterId}, 邮件: ${mailUid}`);

      return XResultUtils.ok({
        success: true,
        mailUid,
        operationTime: Date.now(),
        message: '邮件已读',
      });
    }, {
      reason: 'read_mail'
    });
  }

  /**
   * 领取邮件附件（基于old项目claimMailAttachment方法）
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async claimMailAttachment(characterId: string, mailUid: string): Promise<XResult<MailOperationResultDto>> {
    return this.executeBusinessOperation(async () => {
      // 查找邮件
      const mailResult = await this.mailRepository.findByUid(mailUid);
      if (XResultUtils.isFailure(mailResult)) {
        return XResultUtils.error(`查询邮件失败: ${mailResult.message}`, mailResult.code);
      }

      const mail = mailResult.data;
      if (!mail) {
        return XResultUtils.error(ErrorMessages[ErrorCode.MAIL_NOT_FOUND], ErrorCode.MAIL_NOT_FOUND);
      }

      // 检查邮件所有者
      if (mail.receiverUid !== characterId) {
        return XResultUtils.failure('无权限访问该邮件', ErrorCode.MAIL_NOT_FOUND, {
          mailOwner: mail.receiverUid,
          requestUser: characterId
        });
      }

      // 检查是否可以领取附件
      if (!mail.canClaimAttachment) {
        return XResultUtils.failure('该邮件没有可领取的附件', ErrorCode.MAIL_NO_ATTACHMENT, {
          hasAttachment: mail.hasAttachment,
          attachmentClaimed: !mail.canClaimAttachment
        });
      }

      // 获取附件奖励
      const rewards = mail.attachList;

      // 标记附件为已领取
      const markClaimedResult = await this.mailRepository.markAttachmentClaimed(mailUid);
      if (XResultUtils.isFailure(markClaimedResult)) {
        return XResultUtils.error(`标记附件已领取失败: ${markClaimedResult.message}`, markClaimedResult.code);
      }

      // TODO: 发放奖励到玩家背包

      this.logger.log(`邮件附件领取成功: ${characterId}, 邮件: ${mailUid}, 奖励数量: ${rewards.length}`);

      return XResultUtils.ok({
        success: true,
        mailUid,
        operationTime: Date.now(),
        rewards,
        message: '附件领取成功',
      });
    }, {
      reason: 'claim_mail_attachment',
      metadata: { characterId, mailUid }
    });
  }

  /**
   * 删除邮件（基于old项目deleteMail方法）
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async deleteMail(characterId: string, mailUid: string): Promise<XResult<MailOperationResultDto>> {
    return this.executeBusinessOperation(async () => {
      // 查找邮件
      const mailResult = await this.mailRepository.findByUid(mailUid);
      if (XResultUtils.isFailure(mailResult)) {
        return XResultUtils.error(`查询邮件失败: ${mailResult.message}`, mailResult.code);
      }

      const mail = mailResult.data;
      if (!mail) {
        return XResultUtils.error(ErrorMessages[ErrorCode.MAIL_NOT_FOUND], ErrorCode.MAIL_NOT_FOUND);
      }

      // 检查邮件所有者
      if (mail.receiverUid !== characterId) {
        return XResultUtils.failure('无权限删除该邮件', ErrorCode.MAIL_NOT_FOUND, {
          mailOwner: mail.receiverUid,
          requestUser: characterId
        });
      }

      // 删除邮件
      const deleteResult = await this.mailRepository.delete(mailUid);
      if (XResultUtils.isFailure(deleteResult)) {
        return XResultUtils.error(`删除邮件失败: ${deleteResult.message}`, deleteResult.code);
      }

      if (!deleteResult.data) {
        return XResultUtils.error(ErrorMessages[ErrorCode.MAIL_DELETE_FAILED], ErrorCode.MAIL_DELETE_FAILED);
      }

      this.logger.log(`邮件删除成功: ${characterId}, 邮件: ${mailUid}`);

      return XResultUtils.ok({
        success: true,
        mailUid,
        operationTime: Date.now(),
        message: '邮件删除成功',
      });
    }, {
      reason: 'delete_mail',
      metadata: { characterId, mailUid }
    });
  }

  /**
   * 添加邮件（基于old项目addMail方法）
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async addMail(receiverUid: string, mailId: number, mailType: MailType, mailInfo: any): Promise<XResult<MailOperationResultDto>> {
    return this.executeBusinessOperation(async () => {
      // 删除最老的邮件
      const deleteOldestResult = await this.deleteOldestMailIfNeeded(receiverUid);
      if (XResultUtils.isFailure(deleteOldestResult)) {
        this.logger.warn(`删除最老邮件失败: ${deleteOldestResult.message}`);
      }

      // 生成邮件UID
      const mailUid = this.generateMailUid();

      // 从配置获取邮件模板
      const mailTemplateResult = await this.getMailTemplate(mailId);
      if (XResultUtils.isFailure(mailTemplateResult)) {
        return XResultUtils.error(`获取邮件模板失败: ${mailTemplateResult.message}`, mailTemplateResult.code);
      }

      const mailTemplate = mailTemplateResult.data;

      // 创建邮件数据
      const currentTime = Date.now();
      const createMailDto: CreateMailDto = {
        senderUid: mailInfo.senderUid || 'system',
        receiverUid,
        mailType,
        title: mailTemplate?.title || '系统邮件',
        content: mailTemplate?.content || '',
        attachList: mailInfo.attachList || [],
        specialAttachInfo: mailInfo.specialAttachInfo,
        sendTime: mailInfo.sendTime || currentTime, // 添加必需的发送时间
        expireTime: mailInfo.expireTime || (currentTime + (7 * 24 * 60 * 60 * 1000)), // 7天过期
        param1: mailInfo.param1,
        param2: mailInfo.param2,
        param3: mailInfo.param3,
        param4: mailInfo.param4,
      };

      // 保存邮件
      const savedMailResult = await this.mailRepository.create({
        ...createMailDto,
        uid: mailUid,
      });
      if (XResultUtils.isFailure(savedMailResult)) {
        return XResultUtils.error(`保存邮件失败: ${savedMailResult.message}`, savedMailResult.code);
      }

      this.logger.log(`邮件添加成功: ${receiverUid}, 邮件ID: ${mailUid}`);

      return XResultUtils.ok({
        success: true,
        mailUid,
        operationTime: Date.now(),
        message: '邮件添加成功',
      });
    }, {
      reason: 'add_mail',
      metadata: { receiverUid, mailId, mailType }
    });
  }

  /**
   * 添加可编辑邮件（基于old项目addEditMail方法）
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async addEditMail(dto: CreateEditMailDto): Promise<XResult<MailOperationResultDto>> {
    return this.executeBusinessOperation(async () => {
      const { receiverUid, senderUid, delType, title, content, attachList } = dto;

      // 删除最老的邮件
      const deleteOldestResult = await this.deleteOldestMailIfNeeded(receiverUid);
      if (XResultUtils.isFailure(deleteOldestResult)) {
        this.logger.warn(`删除最老邮件失败: ${deleteOldestResult.message}`);
      }

      // 生成邮件UID
      const mailUid = this.generateMailUid();

      // 创建可编辑邮件数据
      const createMailDto: CreateMailDto = {
        senderUid,
        receiverUid,
        mailType: MailType.FRIEND,
        title,
        content,
        attachList: attachList || [],
        expireTime: Date.now() + (7 * 24 * 60 * 60 * 1000), // 7天过期
      };

      // 保存邮件
      const savedMailResult = await this.mailRepository.create({
        ...createMailDto,
        uid: mailUid,
      });
      if (XResultUtils.isFailure(savedMailResult)) {
        return XResultUtils.error(`保存可编辑邮件失败: ${savedMailResult.message}`, savedMailResult.code);
      }

      this.logger.log(`可编辑邮件添加成功: ${receiverUid}, 邮件ID: ${mailUid}`);

      return XResultUtils.ok({
        success: true,
        mailUid,
        operationTime: Date.now(),
        message: '可编辑邮件添加成功',
      });
    }, {
      reason: 'add_edit_mail'
    });
  }

  /**
   * 批量删除过期邮件（基于old项目checkMail方法）
   * 已适配Result模式：直接返回XResult，无需executeBusinessOperation包装
   */
  async cleanExpiredMails(): Promise<XResult<number>> {
    const deleteResult = await this.mailRepository.deleteExpiredMails();
    if (XResultUtils.isFailure(deleteResult)) {
      return XResultUtils.error(`清理过期邮件失败: ${deleteResult.message}`, deleteResult.code);
    }

    const deletedCount = deleteResult.data;
    this.logger.log(`清理过期邮件: ${deletedCount} 封`);
    return XResultUtils.ok(deletedCount);
  }

  /**
   * 获取邮件统计信息
   * 已适配Result模式：直接返回XResult，无需executeBusinessOperation包装
   */
  async getMailStatistics(characterId: string): Promise<XResult<any>> {
    const [
      totalCountResult,
      unreadCountResult,
      withAttachmentResult
    ] = await Promise.all([
      this.mailRepository.countByReceiver(characterId),
      this.mailRepository.countUnreadByReceiver(characterId),
      this.mailRepository.findWithAttachmentsByReceiver(characterId)
    ]);

    if (XResultUtils.isFailure(totalCountResult)) {
      return XResultUtils.error(`获取邮件总数失败: ${totalCountResult.message}`, totalCountResult.code);
    }
    if (XResultUtils.isFailure(unreadCountResult)) {
      return XResultUtils.error(`获取未读邮件数失败: ${unreadCountResult.message}`, unreadCountResult.code);
    }
    if (XResultUtils.isFailure(withAttachmentResult)) {
      return XResultUtils.error(`获取附件邮件数失败: ${withAttachmentResult.message}`, withAttachmentResult.code);
    }

    const totalCount = totalCountResult.data;
    const unreadCount = unreadCountResult.data;
    const withAttachmentCount = withAttachmentResult.data.length;

    return XResultUtils.ok({
      totalCount,
      unreadCount,
      withAttachmentCount,
      readRate: totalCount > 0 ? ((totalCount - unreadCount) / totalCount) * 100 : 0,
    });
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 检查邮件数量并删除最老的邮件
   * 已适配Result模式：返回XResult类型
   */
  private async deleteOldestMailIfNeeded(receiverUid: string): Promise<XResult<void>> {
    const mailCountResult = await this.mailRepository.countByReceiver(receiverUid);
    if (XResultUtils.isFailure(mailCountResult)) {
      return XResultUtils.error(`获取邮件数量失败: ${mailCountResult.message}`, mailCountResult.code);
    }

    const mailCount = mailCountResult.data;
    if (mailCount >= this.MAX_MAIL_COUNT) {
      const oldestMailResult = await this.mailRepository.findOldestByReceiver(receiverUid);
      if (XResultUtils.isFailure(oldestMailResult)) {
        return XResultUtils.error(`查找最老邮件失败: ${oldestMailResult.message}`, oldestMailResult.code);
      }

      const oldestMail = oldestMailResult.data;
      if (oldestMail) {
        const deleteResult = await this.mailRepository.delete(oldestMail.uid);
        if (XResultUtils.isFailure(deleteResult)) {
          return XResultUtils.error(`删除最老邮件失败: ${deleteResult.message}`, deleteResult.code);
        }
        this.logger.log(`删除最老邮件: ${receiverUid}, 邮件: ${oldestMail.uid}`);
      }
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 生成邮件UID
   */
  private generateMailUid(): string {
    return `mail_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 发送新邮件推送通知
   */
  private async publishNewMailEvents(receiverId: string, mailResults: MailOperationResultDto[]): Promise<void> {
    try {
      for (const mailResult of mailResults) {
        // 准备推送数据
        const mailInfo = {
          mailUid: mailResult.mailUid,
          message: mailResult.message || '您有新邮件',
          operationTime: mailResult.operationTime || Date.now(),
          hasRewards: mailResult.rewards && mailResult.rewards.length > 0,
        };

        // 使用推送客户端发送通知
        await this.pushClientService.notifyUser(receiverId, 'new_mail', {
          type: 'new_mail',
          data: mailInfo,
          timestamp: new Date()
        }, 'social');
      }

      this.logger.debug(`📧 Sent ${mailResults.length} new mail notifications to user: ${receiverId}`);
    } catch (error) {
      this.logger.error(`Failed to send new mail notifications: ${error.message}`);
    }
  }

  /**
   * 获取邮件模板（从配置表）
   * 已适配Result模式：返回XResult类型
   */
  private async getMailTemplate(mailId: number): Promise<XResult<any>> {
    // TODO: 从配置表获取邮件模板
    return XResultUtils.ok({
      title: `系统邮件 #${mailId}`,
      content: '这是一封系统邮件',
    });
  }

  // ==================== 缺失功能补充 ====================

  /**
   * 发送奖励邮件（支持附件分割）
   * 对应old项目中的sendMailReward方法
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async sendMailReward(
    receiverUid: string,
    senderUid: string,
    mailId: number,
    mailType: MailType,
    attachList: any[],
    specialAttachInfo?: any,
    param1?: any,
    param2?: any,
    param3?: any,
    param4?: any
  ): Promise<XResult<MailOperationResultDto[]>> {
    return this.executeBusinessOperation(async () => {
      const results: MailOperationResultDto[] = [];
      const MAX_ATTACH_PER_MAIL = 10; // 每封邮件最大附件数量

      if (attachList.length === 0) {
        // 无附件邮件
        const mailInfo = this.makeMailInfo(senderUid, [], specialAttachInfo, param1, param2, param3, param4);
        const result = await this.addMail(receiverUid, mailId, mailType, mailInfo);
        if (XResultUtils.isFailure(result)) {
          return XResultUtils.error(`发送无附件邮件失败: ${result.message}`, result.code);
        }
        results.push(result.data);
      } else {
        // 分割附件发送多封邮件
        const chunks = this.chunkArray(attachList, MAX_ATTACH_PER_MAIL);

        for (const chunk of chunks) {
          const validatedChunk = this.validateAttachments(chunk);
          if (validatedChunk.length > 0) {
            const mailInfo = this.makeMailInfo(senderUid, validatedChunk, specialAttachInfo, param1, param2, param3, param4);
            const result = await this.addMail(receiverUid, mailId, mailType, mailInfo);
            if (XResultUtils.isFailure(result)) {
              this.logger.warn(`发送奖励邮件失败: ${result.message}`);
              continue;
            }
            results.push(result.data);
          }
        }
      }

      // TODO: 更新红点提示状态

      // 发布新邮件事件
      if (results.length > 0) {
        await this.publishNewMailEvents(receiverUid, results);
      }

      this.logger.log(`奖励邮件发送完成: ${receiverUid}, 邮件数量: ${results.length}`);
      return XResultUtils.ok(results);
    }, {
      reason: 'send_mail_reward',
      metadata: { receiverUid, senderUid, mailId, attachCount: attachList.length }
    });
  }

  /**
   * 批量删除邮件
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async batchDeleteMails(characterId: string, mailUids: string[]): Promise<XResult<MailOperationResultDto>> {
    return this.executeBusinessOperation(async () => {
      let deletedCount = 0;

      for (const mailUid of mailUids) {
        const deleteResult = await this.deleteMail(characterId, mailUid);
        if (XResultUtils.isSuccess(deleteResult)) {
          deletedCount++;
        } else {
          this.logger.warn(`删除邮件失败: ${mailUid}, ${deleteResult.message}`);
        }
      }

      this.logger.log(`批量删除邮件完成: ${characterId}, 删除数量: ${deletedCount}/${mailUids.length}`);

      return XResultUtils.ok({
        success: true,
        operationTime: Date.now(),
        message: `成功删除 ${deletedCount} 封邮件`,
        deletedCount,
        totalRequested: mailUids.length,
      });
    }, {
      reason: 'batch_delete_mails',
      metadata: { characterId, mailCount: mailUids.length }
    });
  }

  /**
   * 批量领取附件
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async batchClaimAttachments(characterId: string, mailUids: string[]): Promise<XResult<MailOperationResultDto>> {
    return this.executeBusinessOperation(async () => {
      const allRewards = [];
      let claimedCount = 0;

      for (const mailUid of mailUids) {
        const result = await this.claimMailAttachment(characterId, mailUid);
        if (XResultUtils.isSuccess(result)) {
          const claimData = result.data;
          if (claimData.success && claimData.rewards) {
            allRewards.push(...claimData.rewards);
            claimedCount++;
          }
        } else {
          this.logger.warn(`领取邮件附件失败: ${mailUid}, ${result.message}`);
        }
      }

      // TODO: 批量发放奖励到玩家背包

      this.logger.log(`批量领取附件完成: ${characterId}, 领取数量: ${claimedCount}/${mailUids.length}`);

      return XResultUtils.ok({
        success: true,
        operationTime: Date.now(),
        rewards: allRewards,
        message: `成功领取 ${claimedCount} 封邮件的附件`,
        claimedCount,
        totalRequested: mailUids.length,
      });
    }, {
      reason: 'batch_claim_attachments',
      metadata: { characterId, mailCount: mailUids.length }
    });
  }

  // ==================== 辅助方法 ====================

  /**
   * 构造邮件信息
   */
  private makeMailInfo(senderUid: string, attachList: any[], specialAttachInfo?: any, param1?: any, param2?: any, param3?: any, param4?: any): any {
    const currentTime = Date.now();
    return {
      senderUid,
      attachList,
      specialAttachInfo,
      param1,
      param2,
      param3,
      param4,
      // 添加必需的时间字段
      sendTime: currentTime,
      expireTime: currentTime + (7 * 24 * 60 * 60 * 1000), // 7天后过期
    };
  }

  /**
   * 验证附件格式
   */
  private validateAttachments(attachList: any[]): any[] {
    return attachList.filter(attach => {
      // 使用小写字段名，符合JavaScript命名规范
      if (!attach || attach.itemType === undefined || attach.resId === undefined || attach.num === undefined) {
        this.logger.warn('无效的附件格式', attach);
        return false;
      }

      // 确保param1有默认值
      if (!attach.param1 && attach.param1 !== 0) {
        attach.param1 = 0;
      }

      return true;
    });
  }

  /**
   * 数组分块
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }
}
