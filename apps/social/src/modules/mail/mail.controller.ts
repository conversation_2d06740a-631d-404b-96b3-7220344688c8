import { <PERSON>, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { MailService } from './mail.service';

import { XResponse } from '@libs/common/types/result.type';

import {
  BatchClaimAttachmentsPayloadDto,
  BatchDeleteMailsPayloadDto,
  ClaimAttachmentPayloadDto, DeleteMailPayloadDto,
  GetMailListPayloadDto,
  ReadMailPayloadDto,
  SendMailPayloadDto
} from "@social/common/dto/mail-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';

@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class MailController extends BaseController {
  constructor(private readonly mailService: MailService) {
    super('MailController');
  }

  /**
   * 发送邮件
   */
  @MessagePattern('mail.send')
  async sendMail(@Payload() payload: SendMailPayloadDto): Promise<XResponse<any>> {
    this.logger.log('发送邮件');
    const result = await this.mailService.sendMailReward(
      payload.receiverUid,
      payload.senderUid,
      payload.mailId,
      payload.mailType,
      payload.attachList,
      payload.specialAttachInfo,
      payload.param1,
      payload.param2,
      payload.param3,
      payload.param4
    );
    return this.fromResult(result);
  }

  /**
   * 获取邮件列表
   */
  @MessagePattern('mail.getList')
  async getMailList(@Payload() payload: GetMailListPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取邮件列表: ${payload.characterId}`);
    const result = await this.mailService.getMailList({
      characterId: payload.characterId,
      page: payload.page,
      limit: payload.limit,
    });
    return this.fromResult(result);
  }

  /**
   * 读取邮件
   */
  @MessagePattern('mail.read')
  async readMail(@Payload() payload: ReadMailPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`读取邮件: ${payload.characterId}, ${payload.mailUid}`);
    const result = await this.mailService.readMail({
      characterId: payload.characterId,
      mailUid: payload.mailUid,
    });
    return this.fromResult(result);
  }

  /**
   * 删除邮件
   */
  @MessagePattern('mail.delete')
  async deleteMail(@Payload() payload: DeleteMailPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`删除邮件: ${payload.characterId}, ${payload.mailUid}`);
    const result = await this.mailService.deleteMail(payload.characterId, payload.mailUid);
    return this.fromResult(result);
  }

  /**
   * 领取邮件附件
   */
  @MessagePattern('mail.claimAttachment')
  async claimMailAttachment(@Payload() payload: ClaimAttachmentPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`领取邮件附件: ${payload.characterId}, ${payload.mailUid}`);
    const result = await this.mailService.claimMailAttachment(payload.characterId, payload.mailUid);
    return this.fromResult(result);
  }

  /**
   * 批量删除邮件
   */
  @MessagePattern('mail.batchDelete')
  async batchDeleteMails(@Payload() payload: BatchDeleteMailsPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`批量删除邮件: ${payload.characterId}, 数量: ${payload.mailUids.length}`);
    const result = await this.mailService.batchDeleteMails(payload.characterId, payload.mailUids);
    return this.fromResult(result);
  }

  /**
   * 批量领取附件
   */
  @MessagePattern('mail.batchClaim')
  async batchClaimAttachments(@Payload() payload: BatchClaimAttachmentsPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`批量领取附件: ${payload.characterId}, 数量: ${payload.mailUids.length}`);
    const result = await this.mailService.batchClaimAttachments(payload.characterId, payload.mailUids);
    return this.fromResult(result);
  }
}
