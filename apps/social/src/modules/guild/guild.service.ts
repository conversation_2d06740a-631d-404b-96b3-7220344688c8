/**
 * 公会系统服务 - 已适配Result模式
 *
 * 核心功能：
 * - 公会创建和管理
 * - 成员申请和审批
 * - 职位变更和权限管理
 * - 公会搜索和排行榜
 * - 公会信息维护
 *
 * Result模式适配：
 * - 继承BaseService基类，获得统一的业务操作框架
 * - 所有public方法返回XResult<T>类型
 * - 使用executeBusinessOperation包装关键业务操作
 * - 标准化的错误处理和微服务调用
 * - 移除所有try-catch异常处理
 */

import { Injectable, Logger } from '@nestjs/common';
import { ErrorCode, ErrorMessages } from '@libs/game-constants';
import { GuildRepository } from '@social/common/repositories/guild.repository';
import { XResult, XResultUtils, RepositoryResultWrapper } from '@libs/common/types/result.type';
import { BaseService } from '@libs/common/service/base-service';
import { MicroserviceClientService } from '@libs/service-mesh';

// 公会职位枚举
enum GuildPosition {
  PRESIDENT = 1,      // 会长
  VICE_PRESIDENT = 2, // 副会长
  MEMBER = 3,         // 普通成员
}
import { 
  CreateGuildDto,
  ApplyJoinGuildDto,
  ProcessApplicationDto,
  LeaveGuildDto,
  UpdateGuildDto,
  GuildInfoDto,
  GuildMemberListDto,
  GuildApplicationListDto,
  GuildOperationResultDto,
  SearchGuildDto,
  GuildListDto,
  ChangePositionDto,
  TransferPresidencyDto
} from '@social/common/dto/guild.dto';


@Injectable()
export class GuildService extends BaseService {

  constructor(
    private readonly guildRepository: GuildRepository,
    microserviceClient: MicroserviceClientService, // 传递给BaseService
  ) {
    // 调用BaseService构造函数，传入服务名称和微服务客户端
    super('GuildService', microserviceClient);
  }

  /**
   * 创建公会（基于old项目createGuild方法）
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async createGuild(
    characterId: string,
    characterName: string,
    guildName: string,
    faceId: number,
    strength: number,
    gid: string,
    faceUrl: string,
    frontendId?: string,
    sessionId?: string
  ): Promise<XResult<GuildOperationResultDto>> {
    return this.executeBusinessOperation(async () => {
      // 检查玩家是否已在公会中
      const existingGuildResult = await this.guildRepository.findByCharacterId(characterId);
      if (XResultUtils.isFailure(existingGuildResult)) {
        return XResultUtils.error(`查询玩家公会状态失败: ${existingGuildResult.message}`, existingGuildResult.code);
      }

      if (existingGuildResult.data) {
        return XResultUtils.error(ErrorMessages[ErrorCode.ALREADY_IN_GUILD], ErrorCode.ALREADY_IN_GUILD);
      }

      // 检查公会名称是否已存在
      const nameExistsResult = await this.guildRepository.findByName(guildName);
      if (XResultUtils.isFailure(nameExistsResult)) {
        return XResultUtils.error(`检查公会名称失败: ${nameExistsResult.message}`, nameExistsResult.code);
      }

      if (nameExistsResult.data) {
        return XResultUtils.error(ErrorMessages[ErrorCode.GUILD_NAME_EXISTS], ErrorCode.GUILD_NAME_EXISTS);
      }

      // 生成公会ID
      const guildId = this.generateGuildId();

      // 创建公会数据
      const guildData = {
        guildId: guildId,
        creator: characterName,
        createTime: Date.now(),
        guildNotice: "欢迎大家加入公会",
        faceId,
        guildName: guildName,
        guildLevel: 1,
        exp: 0,
        contribute: 0,
        allCharacter: [{
          characterId,
          characterName,
          isOnLine: 1,
          leaveTime: Date.now(),
          exp: 0,
          contribute: 0,
          faceUrl,
          strength,
          pos: GuildPosition.PRESIDENT,
          gid,
          frontendId,
          sessionId,
          joinTime: Date.now(),
        }],
        approvalList: [],
        vicePresident: 0,
        notifyList: [{
          time: Date.now(),
          text: `${characterName} 创建了公会`,
        }],
      };

      // 保存公会
      const savedGuildResult = await this.guildRepository.create(guildData);
      if (XResultUtils.isFailure(savedGuildResult)) {
        return XResultUtils.error(`创建公会失败: ${savedGuildResult.message}`, savedGuildResult.code);
      }

      this.logger.log(`公会创建成功: ${guildName}, 会长: ${characterName}`);

      return XResultUtils.ok({
        success: true,
        guildId,
        operationTime: Date.now(),
        message: '公会创建成功',
      });
    }, {
      reason: 'create_guild',
      metadata: { characterId, guildName, characterName }
    });
  }

  /**
   * 申请加入公会（基于old项目applyJoinGuild方法）
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async applyJoinGuild(
    guildId: string,
    characterId: string,
    characterName: string,
    gid: string,
    faceUrl: string,
    strength: number,
    frontendId?: string,
    sessionId?: string
  ): Promise<XResult<GuildOperationResultDto>> {
    return this.executeBusinessOperation(async () => {
      // 检查玩家是否已在公会中
      const existingGuildResult = await this.guildRepository.findByCharacterId(characterId);
      if (XResultUtils.isFailure(existingGuildResult)) {
        return XResultUtils.error(`查询玩家公会状态失败: ${existingGuildResult.message}`, existingGuildResult.code);
      }

      if (existingGuildResult.data) {
        return XResultUtils.error(ErrorMessages[ErrorCode.ALREADY_IN_GUILD], ErrorCode.ALREADY_IN_GUILD);
      }

      // 查找目标公会
      const guildResult = await this.guildRepository.findById(guildId);
      if (XResultUtils.isFailure(guildResult)) {
        return XResultUtils.error(`查询公会信息失败: ${guildResult.message}`, guildResult.code);
      }

      const guild = guildResult.data;
      if (!guild) {
        return XResultUtils.error(ErrorMessages[ErrorCode.GUILD_NOT_FOUND], ErrorCode.GUILD_NOT_FOUND);
      }

      // 检查公会是否已满
      if (guild.isFull) {
        return XResultUtils.failure('公会已满', ErrorCode.GUILD_FULL, {
          currentMembers: guild.memberCount,
          maxMembers: 10
        });
      }

      // 检查是否已有申请
      const existingApplication = guild.approvalList.find(app => app.characterId === characterId);
      if (existingApplication) {
        return XResultUtils.failure('已有待处理的申请', ErrorCode.GUILD_APPLICATION_EXISTS, {
          applicationTime: existingApplication.askTime
        });
      }

      // 添加申请
      const applicationData = {
        characterId,
        characterName,
        askTime: Date.now(),
        gid,
        faceUrl,
        strength,
        frontendId,
        sessionId,
      };

      const addApplicationResult = await this.guildRepository.addApplication(guildId, applicationData);
      if (XResultUtils.isFailure(addApplicationResult)) {
        return XResultUtils.error(`添加申请失败: ${addApplicationResult.message}`, addApplicationResult.code);
      }

      // 添加日志
      const addNotifyResult = await this.guildRepository.addNotify(guildId, `${characterName} 申请加入公会`);
      if (XResultUtils.isFailure(addNotifyResult)) {
        this.logger.warn(`添加公会日志失败: ${addNotifyResult.message}`);
      }

      this.logger.log(`公会申请提交: ${characterName} -> ${guild.guildName}`);

      return XResultUtils.ok({
        success: true,
        guildId,
        operationTime: Date.now(),
        message: '申请已提交',
      });
    }, {
      reason: 'apply_join_guild',
      metadata: { guildId, characterId, characterName }
    });
  }

  /**
   * 处理公会申请（基于old项目processApplication方法）
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async processApplication(
    guildId: string,
    approverId: string,
    applicantId: string,
    approved: boolean
  ): Promise<XResult<GuildOperationResultDto>> {
    return this.executeBusinessOperation(async () => {
      // 查找公会
      const guildResult = await this.guildRepository.findById(guildId);
      if (XResultUtils.isFailure(guildResult)) {
        return XResultUtils.error(`查询公会信息失败: ${guildResult.message}`, guildResult.code);
      }

      const guild = guildResult.data;
      if (!guild) {
        return XResultUtils.error(ErrorMessages[ErrorCode.GUILD_NOT_FOUND], ErrorCode.GUILD_NOT_FOUND);
      }

      // 检查处理者权限
      const approver = guild.allCharacter.find(member => member.characterId === approverId);
      if (!approver || (approver.pos !== GuildPosition.PRESIDENT && approver.pos !== GuildPosition.VICE_PRESIDENT)) {
        return XResultUtils.failure('无权限处理申请', ErrorCode.GUILD_NO_PERMISSION, {
          requiredPositions: ['会长', '副会长'],
          currentPosition: approver?.pos || '非成员'
        });
      }

      // 查找申请
      const application = guild.approvalList.find(app => app.characterId === applicantId);
      if (!application) {
        return XResultUtils.error(ErrorMessages[ErrorCode.GUILD_APPLICATION_NOT_FOUND], ErrorCode.GUILD_APPLICATION_NOT_FOUND);
      }

      // 移除申请
      const removeApplicationResult = await this.guildRepository.removeApplication(guildId, applicantId);
      if (XResultUtils.isFailure(removeApplicationResult)) {
        return XResultUtils.error(`移除申请失败: ${removeApplicationResult.message}`, removeApplicationResult.code);
      }

      if (approved) {
        // 检查公会是否还有空位
        if (guild.isFull) {
          return XResultUtils.failure('公会已满，无法通过申请', ErrorCode.GUILD_FULL, {
            currentMembers: guild.memberCount,
            maxMembers: 10
          });
        }

        // 添加成员
        const memberData = {
          characterId: application.characterId,
          characterName: application.characterName,
          isOnLine: 1,
          leaveTime: Date.now(),
          exp: 0,
          contribute: 0,
          faceUrl: application.faceUrl,
          strength: application.strength,
          pos: GuildPosition.MEMBER,
          gid: application.gid,
          frontendId: application.frontendId,
          sessionId: application.sessionId,
          joinTime: Date.now(),
        };

        const addMemberResult = await this.guildRepository.addMember(guildId, memberData);
        if (XResultUtils.isFailure(addMemberResult)) {
          return XResultUtils.error(`添加成员失败: ${addMemberResult.message}`, addMemberResult.code);
        }

        // 添加日志
        const addNotifyResult = await this.guildRepository.addNotify(guildId, `${application.characterName} 加入了公会`);
        if (XResultUtils.isFailure(addNotifyResult)) {
          this.logger.warn(`添加公会日志失败: ${addNotifyResult.message}`);
        }

        this.logger.log(`公会申请通过: ${application.characterName} 加入 ${guild.guildName}`);
      } else {
        // 添加日志
        const addNotifyResult = await this.guildRepository.addNotify(guildId, `${application.characterName} 的申请被拒绝`);
        if (XResultUtils.isFailure(addNotifyResult)) {
          this.logger.warn(`添加公会日志失败: ${addNotifyResult.message}`);
        }

        this.logger.log(`公会申请拒绝: ${application.characterName} -> ${guild.guildName}`);
      }

      return XResultUtils.ok({
        success: true,
        guildId,
        operationTime: Date.now(),
        message: approved ? '申请已通过' : '申请已拒绝',
      });
    }, {
      reason: 'process_application',
      metadata: { guildId, approverId, applicantId, approved }
    });
  }

  /**
   * 退出公会（基于old项目leaveGuild方法）
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async leaveGuild(characterId: string, exitType: number = 1): Promise<XResult<GuildOperationResultDto>> {
    return this.executeBusinessOperation(async () => {
      // 查找玩家所在公会
      const guildResult = await this.guildRepository.findByCharacterId(characterId);
      if (XResultUtils.isFailure(guildResult)) {
        return XResultUtils.error(`查询玩家公会状态失败: ${guildResult.message}`, guildResult.code);
      }

      const guild = guildResult.data;
      if (!guild) {
        return XResultUtils.error(ErrorMessages[ErrorCode.NOT_IN_GUILD], ErrorCode.NOT_IN_GUILD);
      }

      // 查找成员信息
      const member = guild.allCharacter.find(m => m.characterId === characterId);
      if (!member) {
        return XResultUtils.error(ErrorMessages[ErrorCode.NOT_IN_GUILD], ErrorCode.NOT_IN_GUILD);
      }

      // 检查是否是会长
      if (member.pos === GuildPosition.PRESIDENT) {
        return XResultUtils.failure('会长不能直接退出公会', ErrorCode.GUILD_PRESIDENT_CANNOT_LEAVE, {
          suggestion: '请先转让会长职位再退出公会',
          currentPosition: '会长'
        });
      }

      // 移除成员
      const removeMemberResult = await this.guildRepository.removeMember(guild.guildId, characterId);
      if (XResultUtils.isFailure(removeMemberResult)) {
        return XResultUtils.error(`移除成员失败: ${removeMemberResult.message}`, removeMemberResult.code);
      }

      // 添加日志
      const exitText = exitType === 1 ? '主动退出' : '被踢出';
      const addNotifyResult = await this.guildRepository.addNotify(guild.guildId, `${member.characterName} ${exitText}了公会`);
      if (XResultUtils.isFailure(addNotifyResult)) {
        this.logger.warn(`添加公会日志失败: ${addNotifyResult.message}`);
      }

      this.logger.log(`公会退出: ${member.characterName} ${exitText} ${guild.guildName}`);

      return XResultUtils.ok({
        success: true,
        guildId: guild.guildId,
        operationTime: Date.now(),
        message: '退出公会成功',
      });
    }, {
      reason: 'leave_guild',
      metadata: { characterId, exitType }
    });
  }

  /**
   * 获取公会信息（基于old项目getGuildInfo方法）
   * 已适配Result模式：直接返回XResult，无需executeBusinessOperation包装
   */
  async getGuildInfo(guildId: string): Promise<XResult<GuildInfoDto>> {
    const guildResult = await this.guildRepository.findById(guildId);
    if (XResultUtils.isFailure(guildResult)) {
      return XResultUtils.error(`查询公会信息失败: ${guildResult.message}`, guildResult.code);
    }

    const guild = guildResult.data;
    if (!guild) {
      return XResultUtils.error(ErrorMessages[ErrorCode.GUILD_NOT_FOUND], ErrorCode.GUILD_NOT_FOUND);
    }

    return XResultUtils.ok({
      guildId: guild.guildId,
      guildName: guild.guildName,
      creator: guild.creator,
      createTime: guild.createTime,
      guildNotice: guild.guildNotice,
      faceId: guild.faceId,
      guildLevel: guild.guildLevel,
      exp: guild.exp,
      contribute: guild.contribute,
      memberCount: guild.memberCount,
      maxMembers: 10, // TODO: 从配置获取
      onlineMemberCount: guild.onlineMemberCount,
      vicePresidentCount: guild.vicePresidents?.length || 0,
      maxVicePresidents: 2, // TODO: 从配置获取
      applicationCount: guild.pendingApplications || 0,
      isFull: guild.isFull,
    });
  }

  /**
   * 获取公会成员列表（基于old项目getGuildMembers方法）
   * 已适配Result模式：直接返回XResult，无需executeBusinessOperation包装
   */
  async getGuildMembers(guildId: string): Promise<XResult<GuildMemberListDto>> {
    const guildResult = await this.guildRepository.findById(guildId);
    if (XResultUtils.isFailure(guildResult)) {
      return XResultUtils.error(`查询公会信息失败: ${guildResult.message}`, guildResult.code);
    }

    const guild = guildResult.data;
    if (!guild) {
      return XResultUtils.error(ErrorMessages[ErrorCode.GUILD_NOT_FOUND], ErrorCode.GUILD_NOT_FOUND);
    }

    const members = guild.allCharacter.map(member => ({
      characterId: member.characterId,
      characterName: member.characterName,
      isOnLine: member.isOnLine,
      leaveTime: member.leaveTime,
      exp: member.exp,
      contribute: member.contribute,
      faceUrl: member.faceUrl,
      strength: member.strength,
      pos: member.pos,
      gid: member.gid,
      frontendId: member.frontendId,
      sessionId: member.sessionId,
      joinTime: member.joinTime,
    }));

    return XResultUtils.ok({
      members,
      total: members.length,
      maxMembers: 10, // TODO: 从配置获取
    });
  }

  /**
   * 更新公会信息（基于old项目updateGuildInfo方法）
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async updateGuildInfo(guildId: string, operatorId: string, updateData: UpdateGuildDto): Promise<XResult<GuildOperationResultDto>> {
    return this.executeBusinessOperation(async () => {
      const guildResult = await this.guildRepository.findById(guildId);
      if (XResultUtils.isFailure(guildResult)) {
        return XResultUtils.error(`查询公会信息失败: ${guildResult.message}`, guildResult.code);
      }

      const guild = guildResult.data;
      if (!guild) {
        return XResultUtils.error(ErrorMessages[ErrorCode.GUILD_NOT_FOUND], ErrorCode.GUILD_NOT_FOUND);
      }

      // 检查操作者权限
      const operator = guild.allCharacter.find(member => member.characterId === operatorId);
      if (!operator || (operator.pos !== GuildPosition.PRESIDENT && operator.pos !== GuildPosition.VICE_PRESIDENT)) {
        return XResultUtils.failure('无权限更新公会信息', ErrorCode.GUILD_NO_PERMISSION, {
          requiredPositions: ['会长', '副会长'],
          currentPosition: operator?.pos || '非成员'
        });
      }

      // 更新公会信息
      const updates: any = {};
      if (updateData.notice !== undefined) {
        updates.notice = updateData.notice;
      }
      if (updateData.faceId !== undefined) {
        updates.faceId = updateData.faceId;
      }

      const updateResult = await this.guildRepository.update(guildId, updates);
      if (XResultUtils.isFailure(updateResult)) {
        return XResultUtils.error(`更新公会信息失败: ${updateResult.message}`, updateResult.code);
      }

      // 添加日志
      const addNotifyResult = await this.guildRepository.addNotify(guildId, `${operator.characterName} 更新了公会信息`);
      if (XResultUtils.isFailure(addNotifyResult)) {
        this.logger.warn(`添加公会日志失败: ${addNotifyResult.message}`);
      }

      this.logger.log(`公会信息更新: ${guild.guildName}, 操作者: ${operator.characterName}`);

      return XResultUtils.ok({
        success: true,
        guildId,
        operationTime: Date.now(),
        message: '公会信息更新成功',
      });
    }, {
      reason: 'update_guild_info',
      metadata: { guildId, operatorId, updateData }
    });
  }

  /**
   * 踢出成员（基于old项目kickMember方法）
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async kickMember(guildId: string, operatorId: string, targetCharacterId: string): Promise<XResult<GuildOperationResultDto>> {
    return this.executeBusinessOperation(async () => {
      const guildResult = await this.guildRepository.findById(guildId);
      if (XResultUtils.isFailure(guildResult)) {
        return XResultUtils.error(`查询公会信息失败: ${guildResult.message}`, guildResult.code);
      }

      const guild = guildResult.data;
      if (!guild) {
        return XResultUtils.error(ErrorMessages[ErrorCode.GUILD_NOT_FOUND], ErrorCode.GUILD_NOT_FOUND);
      }

      // 检查操作者权限
      const operator = guild.allCharacter.find(member => member.characterId === operatorId);
      if (!operator || (operator.pos !== GuildPosition.PRESIDENT && operator.pos !== GuildPosition.VICE_PRESIDENT)) {
        return XResultUtils.failure('无权限踢出成员', ErrorCode.GUILD_NO_PERMISSION, {
          requiredPositions: ['会长', '副会长'],
          currentPosition: operator?.pos || '非成员'
        });
      }

      // 查找目标成员
      const targetMember = guild.allCharacter.find(member => member.characterId === targetCharacterId);
      if (!targetMember) {
        return XResultUtils.error(ErrorMessages[ErrorCode.GUILD_MEMBER_NOT_FOUND], ErrorCode.GUILD_MEMBER_NOT_FOUND);
      }

      // 不能踢出会长
      if (targetMember.pos === GuildPosition.PRESIDENT) {
        return XResultUtils.failure('不能踢出会长', ErrorCode.GUILD_CANNOT_KICK_PRESIDENT, {
          targetPosition: '会长',
          suggestion: '请先转让会长职位'
        });
      }

      // 副会长不能踢出副会长
      if (operator.pos === GuildPosition.VICE_PRESIDENT && targetMember.pos === GuildPosition.VICE_PRESIDENT) {
        return XResultUtils.failure('副会长不能踢出副会长', ErrorCode.GUILD_NO_PERMISSION, {
          operatorPosition: '副会长',
          targetPosition: '副会长'
        });
      }

      // 移除成员
      const removeMemberResult = await this.guildRepository.removeMember(guildId, targetCharacterId);
      if (XResultUtils.isFailure(removeMemberResult)) {
        return XResultUtils.error(`移除成员失败: ${removeMemberResult.message}`, removeMemberResult.code);
      }

      // 添加日志
      const addNotifyResult = await this.guildRepository.addNotify(guildId, `${targetMember.characterName} 被 ${operator.characterName} 踢出公会`);
      if (XResultUtils.isFailure(addNotifyResult)) {
        this.logger.warn(`添加公会日志失败: ${addNotifyResult.message}`);
      }

      this.logger.log(`踢出成员: ${targetMember.characterName} 被 ${operator.characterName} 踢出 ${guild.guildName}`);

      return XResultUtils.ok({
        success: true,
        guildId,
        operationTime: Date.now(),
        message: '成员已被踢出',
      });
    }, {
      reason: 'kick_member',
      metadata: { guildId, operatorId, targetCharacterId }
    });
  }







  // ==================== 私有辅助方法 ====================



  /**
   * 生成公会ID
   */
  private generateGuildId(): string {
    return `guild_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // ==================== 缺失功能补充 ====================

  /**
   * 获取申请列表
   * 对应old项目中的getApprovalList方法
   * 已适配Result模式：直接返回XResult，无需executeBusinessOperation包装
   */
  async getApplicationList(characterId: string, guildId: string): Promise<XResult<any>> {
    const guildResult = await this.guildRepository.findByGuildId(guildId);
    if (XResultUtils.isFailure(guildResult)) {
      return XResultUtils.error(`查询公会信息失败: ${guildResult.message}`, guildResult.code);
    }

    const guild = guildResult.data;
    if (!guild) {
      return XResultUtils.error(ErrorMessages[ErrorCode.GUILD_NOT_FOUND], ErrorCode.GUILD_NOT_FOUND);
    }

    // 检查权限
    if (!this.checkJurisdiction(guild, characterId)) {
      return XResultUtils.failure('无权限查看申请列表', ErrorCode.GUILD_NO_PERMISSION, {
        requiredPositions: ['会长', '副会长']
      });
    }

    // 按申请时间排序
    const sortedApplications = guild.approvalList.sort((a, b) => a.askTime - b.askTime);

    this.logger.log(`获取申请列表: ${guildId}, 申请数量: ${sortedApplications.length}`);

    return XResultUtils.ok({
      approvalList: sortedApplications,
      total: sortedApplications.length,
    });
  }

  /**
   * 同意加入申请
   * 对应old项目中的agreeJoinAssociation方法
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async approveApplication(characterId: string, guildId: string, agreeId: string): Promise<XResult<GuildOperationResultDto>> {
    return this.executeBusinessOperation(async () => {
      const guildResult = await this.guildRepository.findByGuildId(guildId);
      if (XResultUtils.isFailure(guildResult)) {
        return XResultUtils.error(`查询公会信息失败: ${guildResult.message}`, guildResult.code);
      }

      const guild = guildResult.data;
      if (!guild) {
        return XResultUtils.error(ErrorMessages[ErrorCode.GUILD_NOT_FOUND], ErrorCode.GUILD_NOT_FOUND);
      }

      // 检查权限
      if (!this.checkJurisdiction(guild, characterId)) {
        return XResultUtils.failure('无权限处理申请', ErrorCode.GUILD_NO_PERMISSION, {
          requiredPositions: ['会长', '副会长']
        });
      }

      // 检查人数限制
      if (guild.allCharacter.length >= 10) {
        return XResultUtils.failure('公会人数已满', ErrorCode.GUILD_MEMBER_LIMIT, {
          currentMembers: guild.allCharacter.length,
          maxMembers: 10
        });
      }

      // 查找申请
      const applicationIndex = guild.approvalList.findIndex(app => app.characterId === agreeId);
      if (applicationIndex === -1) {
        return XResultUtils.error(ErrorMessages[ErrorCode.APPLICATION_NOT_FOUND], ErrorCode.APPLICATION_NOT_FOUND);
      }

      const application = guild.approvalList[applicationIndex];

      // 添加成员
      const newMember = {
        characterId: agreeId,
        characterName: application.characterName,
        pos: 3, // 普通成员
        joinTime: Date.now(),
        contribute: 0,
        isOnLine: 1,
        leaveTime: 0,
        exp: 0, // 添加缺失的exp属性
        gid: application.gid,
        faceUrl: application.faceUrl,
        strength: application.strength,
        frontendId: application.frontendId,
        sessionId: application.sessionId,
      };

      guild.allCharacter.push(newMember);

      // 删除申请
      guild.approvalList.splice(applicationIndex, 1);

      // 添加公会日志
      this.addNotify(guild, 'member_join', application.characterName);

      // 保存更改 - 使用RepositoryResultWrapper.wrap包装guild.save()
      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await guild.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存公会数据失败: ${saveResult.message}`, saveResult.code);
      }

      // TODO: 通知Character服务更新玩家公会状态

      this.logger.log(`同意申请成功: ${guildId}, 新成员: ${agreeId}`);

      return XResultUtils.ok({
        success: true,
        operationTime: Date.now(),
        message: '成员加入成功',
        newMember,
      });
    }, {
      reason: 'approve_application',
      metadata: { characterId, guildId, agreeId }
    });
  }

  /**
   * 拒绝加入申请
   * 对应old项目中的agreeJoinAssociation方法（type=2）
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async rejectApplication(characterId: string, guildId: string, rejectId: string): Promise<XResult<GuildOperationResultDto>> {
    return this.executeBusinessOperation(async () => {
      const guildResult = await this.guildRepository.findByGuildId(guildId);
      if (XResultUtils.isFailure(guildResult)) {
        return XResultUtils.error(`查询公会信息失败: ${guildResult.message}`, guildResult.code);
      }

      const guild = guildResult.data;
      if (!guild) {
        return XResultUtils.error(ErrorMessages[ErrorCode.GUILD_NOT_FOUND], ErrorCode.GUILD_NOT_FOUND);
      }

      // 检查权限
      if (!this.checkJurisdiction(guild, characterId)) {
        return XResultUtils.failure('无权限处理申请', ErrorCode.GUILD_NO_PERMISSION, {
          requiredPositions: ['会长', '副会长']
        });
      }

      // 查找申请
      const applicationIndex = guild.approvalList.findIndex(app => app.characterId === rejectId);
      if (applicationIndex === -1) {
        return XResultUtils.error(ErrorMessages[ErrorCode.APPLICATION_NOT_FOUND], ErrorCode.APPLICATION_NOT_FOUND);
      }

      // 删除申请
      guild.approvalList.splice(applicationIndex, 1);

      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await guild.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存公会数据失败: ${saveResult.message}`, saveResult.code);
      }

      // TODO: 通知Character服务更新玩家申请状态

      this.logger.log(`拒绝申请成功: ${guildId}, 申请者: ${rejectId}`);

      return XResultUtils.ok({
        success: true,
        operationTime: Date.now(),
        message: '申请已拒绝',
      });
    }, {
      reason: 'reject_application',
      metadata: { characterId, guildId, rejectId }
    });
  }

  /**
   * 职位变更
   * 对应old项目中的changeAssociationPosition方法
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async changePosition(characterId: string, guildId: string, targetCharacterId: string, newPosition: number): Promise<XResult<GuildOperationResultDto>> {
    return this.executeBusinessOperation(async () => {
      const guildResult = await this.guildRepository.findByGuildId(guildId);
      if (XResultUtils.isFailure(guildResult)) {
        return XResultUtils.error(`查询公会信息失败: ${guildResult.message}`, guildResult.code);
      }

      const guild = guildResult.data;
      if (!guild) {
        return XResultUtils.error(ErrorMessages[ErrorCode.GUILD_NOT_FOUND], ErrorCode.GUILD_NOT_FOUND);
      }

      const executorPos = this.getCharacterPosition(guild, characterId);
      const targetPos = this.getCharacterPosition(guild, targetCharacterId);

      if (executorPos === 0 || targetPos === 0) {
        return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NOT_IN_GUILD], ErrorCode.CHARACTER_NOT_IN_GUILD);
      }

      // 权限检查
      if (executorPos === 3) {
        return XResultUtils.failure('普通成员无权限变更职位', ErrorCode.GUILD_NO_PERMISSION, {
          currentPosition: '普通成员',
          requiredPositions: ['会长', '副会长']
        });
      }

      // 不能操作比自己职位高或相等的人
      if (newPosition < executorPos || executorPos === targetPos) {
        return XResultUtils.failure('无权限操作该成员职位', ErrorCode.GUILD_NO_PERMISSION, {
          executorPosition: executorPos,
          targetPosition: targetPos,
          newPosition: newPosition
        });
      }

      // 职位没变化
      if (targetPos === newPosition) {
        return XResultUtils.ok({
          success: true,
          operationTime: Date.now(),
          message: '职位无变化',
        });
      }

      // 副会长数量限制
      if (executorPos === 1 && newPosition === 2 && targetPos !== 2) {
        if (guild.vicePresident >= 2) {
          return XResultUtils.failure('副会长数量已达上限', ErrorCode.VICE_PRESIDENT_LIMIT, {
            currentVicePresidents: guild.vicePresident,
            maxVicePresidents: 2
          });
        }
        guild.vicePresident++;
      }

      // 会长转让
      if (executorPos === 1 && newPosition === 1) {
        if (targetPos === 2) {
          guild.vicePresident--;
        }
        this.setCharacterPosition(guild, characterId, 3); // 原会长变成普通成员
      }

      // 撤销副会长
      if (executorPos === 1 && newPosition === 3 && targetPos === 2) {
        guild.vicePresident--;
      }

      // 设置新职位
      this.setCharacterPosition(guild, targetCharacterId, newPosition);

      // 添加公会日志
      const targetCharacter = guild.allCharacter.find(p => p.characterId === targetCharacterId);
      this.addNotify(guild, 'position_change', targetCharacter?.characterName, newPosition);

      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await guild.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存公会数据失败: ${saveResult.message}`, saveResult.code);
      }

      this.logger.log(`职位变更成功: ${guildId}, ${targetCharacterId} -> 职位${newPosition}`);

      return XResultUtils.ok({
        success: true,
        operationTime: Date.now(),
        message: '职位变更成功',
        newPosition,
      });
    }, {
      reason: 'change_position',
      metadata: { characterId, guildId, targetCharacterId, newPosition }
    });
  }

  /**
   * 转让会长
   * 对应old项目中的changeAssociationPosition方法（会长转让逻辑）
   * 已适配Result模式：使用executeBusinessOperation包装业务逻辑
   */
  async transferPresidency(characterId: string, guildId: string, newPresidentId: string): Promise<XResult<GuildOperationResultDto>> {
    return this.executeBusinessOperation(async () => {
      const guildResult = await this.guildRepository.findByGuildId(guildId);
      if (XResultUtils.isFailure(guildResult)) {
        return XResultUtils.error(`查询公会信息失败: ${guildResult.message}`, guildResult.code);
      }

      const guild = guildResult.data;
      if (!guild) {
        return XResultUtils.error(ErrorMessages[ErrorCode.GUILD_NOT_FOUND], ErrorCode.GUILD_NOT_FOUND);
      }

      // 检查是否是会长
      const executorPos = this.getCharacterPosition(guild, characterId);
      if (executorPos !== 1) {
        return XResultUtils.failure('只有会长可以转让会长职位', ErrorCode.ONLY_PRESIDENT_CAN_TRANSFER, {
          currentPosition: executorPos === 2 ? '副会长' : executorPos === 3 ? '普通成员' : '非成员'
        });
      }

      const targetPos = this.getCharacterPosition(guild, newPresidentId);
      if (targetPos === 0) {
        return XResultUtils.error(ErrorMessages[ErrorCode.CHARACTER_NOT_IN_GUILD], ErrorCode.CHARACTER_NOT_IN_GUILD);
      }

      // 如果新会长原来是副会长，减少副会长数量
      if (targetPos === 2) {
        guild.vicePresident--;
      }

      // 设置新会长
      this.setCharacterPosition(guild, newPresidentId, 1);
      // 原会长变成普通成员
      this.setCharacterPosition(guild, characterId, 3);

      // 更新创建者
      guild.creator = newPresidentId;

      // 添加公会日志
      const newPresident = guild.allCharacter.find(p => p.characterId === newPresidentId);
      this.addNotify(guild, 'presidency_transfer', newPresident?.characterName);

      const saveResult = await RepositoryResultWrapper.wrap(async () => {
        return await guild.save();
      });
      if (XResultUtils.isFailure(saveResult)) {
        return XResultUtils.error(`保存公会数据失败: ${saveResult.message}`, saveResult.code);
      }

      this.logger.log(`会长转让成功: ${guildId}, 新会长: ${newPresidentId}`);

      return XResultUtils.ok({
        success: true,
        operationTime: Date.now(),
        message: '会长转让成功',
        newPresident: newPresidentId,
      });
    }, {
      reason: 'transfer_presidency',
      metadata: { characterId, guildId, newPresidentId }
    });
  }

  /**
   * 搜索公会
   * 对应old项目中的searchGuilds方法
   * 已适配Result模式：直接返回XResult，无需executeBusinessOperation包装
   */
  async searchGuilds(keyword: string, page: number = 1, limit: number = 20): Promise<XResult<any>> {
    const guildsResult = await this.guildRepository.searchGuilds(keyword, page, limit);
    if (XResultUtils.isFailure(guildsResult)) {
      return XResultUtils.error(`搜索公会失败: ${guildsResult.message}`, guildsResult.code);
    }

    const guilds = guildsResult.data;
    const guildList = guilds.map(guild => ({
      guildId: guild.guildId,
      guildName: guild.guildName,
      guildLevel: guild.guildLevel,
      memberCount: guild.allCharacter.length,
      maxMembers: 10,
      creator: guild.creator,
      guildNotice: guild.guildNotice,
      isJoinable: guild.allCharacter.length < 10,
    }));

    this.logger.log(`搜索公会: ${keyword}, 找到: ${guildList.length}个`);

    return XResultUtils.ok({
      guilds: guildList,
      total: guildList.length,
      page,
      limit,
    });
  }

  /**
   * 获取公会排行榜
   * 对应old项目中的getGuildRanking方法
   * 已适配Result模式：直接返回XResult，无需executeBusinessOperation包装
   */
  async getGuildRanking(page: number = 1, limit: number = 50): Promise<XResult<any>> {
    const rankingsResult = await this.guildRepository.getGuildRanking(page, limit);
    if (XResultUtils.isFailure(rankingsResult)) {
      return XResultUtils.error(`获取公会排行榜失败: ${rankingsResult.message}`, rankingsResult.code);
    }

    const rankings = rankingsResult.data;
    const rankingList = rankings.map((guild, index) => ({
      rank: (page - 1) * limit + index + 1,
      guildId: guild.guildId,
      guildName: guild.guildName,
      guildLevel: guild.guildLevel,
      memberCount: guild.allCharacter.length,
      totalStrength: guild.allCharacter.reduce((sum, character) => sum + (character.strength || 0), 0),
      creator: guild.creator,
    }));

    this.logger.log(`获取公会排行榜: 第${page}页, ${rankingList.length}个公会`);

    return XResultUtils.ok({
      rankings: rankingList,
      page,
      limit,
      total: rankingList.length,
    });
  }

  /**
   * 权限检查
   * 对应old项目中的checkJurisdiction方法
   */
  checkJurisdiction(guild: any, characterId: string): boolean {
    const character = guild.allCharacter.find(p => p.characterId === characterId);
    if (!character) {
      return false;
    }

    // 只有会长和副会长有管理权限
    return character.pos === 1 || character.pos === 2;
  }

  /**
   * 添加公会日志
   * 对应old项目中的addNotify方法
   */
  addNotify(guild: any, type: string, characterName?: string, extra?: any): void {
    const notify = {
      type,
      characterName,
      extra,
      time: Date.now(),
      message: this.generateNotifyMessage(type, characterName, extra),
    };

    // 添加到日志列表
    if (!guild.notifyList) {
      guild.notifyList = [];
    }

    guild.notifyList.unshift(notify);

    // 保持最多50条日志
    if (guild.notifyList.length > 50) {
      guild.notifyList = guild.notifyList.slice(0, 50);
    }

    this.logger.log(`添加公会日志: ${guild.guildId}, 类型: ${type}, 消息: ${notify.message}`);
  }

  // ==================== 辅助方法 ====================

  /**
   * 获取玩家职位
   */
  private getCharacterPosition(guild: any, characterId: string): number {
    const character = guild.allCharacter.find(p => p.characterId === characterId);
    return character ? character.pos : 0;
  }

  /**
   * 设置玩家职位
   */
  private setCharacterPosition(guild: any, characterId: string, position: number): void {
    const character = guild.allCharacter.find(p => p.characterId === characterId);
    if (character) {
      character.pos = position;
    }
  }

  /**
   * 生成通知消息
   */
  private generateNotifyMessage(type: string, characterName?: string, extra?: any): string {
    switch (type) {
      case 'member_join':
        return `${characterName} 加入了公会`;
      case 'member_leave':
        return `${characterName} 离开了公会`;
      case 'member_kick':
        return `${characterName} 被踢出公会`;
      case 'position_change':
        const positionNames = { 1: '会长', 2: '副会长', 3: '成员' };
        return `${characterName} 被任命为${positionNames[extra] || '成员'}`;
      case 'presidency_transfer':
        return `${characterName} 成为新的会长`;
      case 'guild_upgrade':
        return `公会升级到 ${extra} 级`;
      default:
        return `公会活动: ${type}`;
    }
  }
}
