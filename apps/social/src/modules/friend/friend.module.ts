/**
 * 好友模块
 * 基于old项目friends.js实体迁移
 */

import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

// Friend相关组件
import { FriendController } from './friend.controller';
import { FriendService } from './friend.service';
import { Friend, FriendSchema } from '@social/common/schemas/friend.schema';
import { FriendRepository } from '@social/common/repositories/friend.repository';
import { PushClientService } from '@libs/common/push/push-client.service';

@Module({
  imports: [
    // 注册Friend Schema
    MongooseModule.forFeature([
      { name: Friend.name, schema: FriendSchema },
    ]),
  ],
  controllers: [FriendController],
  providers: [FriendService, FriendRepository, PushClientService],
  exports: [FriendService, FriendRepository],
})
export class FriendModule {}
