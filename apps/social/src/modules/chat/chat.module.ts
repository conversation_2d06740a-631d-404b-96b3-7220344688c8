/**
 * 聊天模块
 * 处理游戏内聊天功能
 */

import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ChatController } from './chat.controller';
import { ChatService } from './chat.service';
import {
  ChatMessage,
  ChatMessageSchema,
  ChatChannel,
  ChatChannelSchema
} from '../../common/schemas/chat.schema';
import { ChatChannelRepository } from "@social/common/repositories/chat-channel.repository";
import { ChatMessageRepository } from "@social/common/repositories/chat-message.repository";
import { PushClientService } from '@libs/common/push/push-client.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ChatMessage.name, schema: ChatMessageSchema },
      { name: ChatChannel.name, schema: ChatChannelSchema },
    ]),
  ],
  controllers: [ChatController],
  providers: [ChatService, ChatChannelRepository, ChatMessageRepository, PushClientService],
  exports: [ChatService, ChatChannelRepository, ChatMessageRepository],
})
export class ChatModule {}
