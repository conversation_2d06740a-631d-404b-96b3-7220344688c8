import { <PERSON>, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ChatService } from './chat.service';

import { XResponse } from '@libs/common/types/result.type';

import {
  GetHistoryPayloadDto,
  GetPrivateHistoryPayloadDto,
  JoinChannelPayloadDto,
  LeaveChannelPayloadDto,
  SendMessagePayloadDto
} from "@social/common/dto/chat-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';

@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class ChatController extends BaseController {
  constructor(private readonly chatService: ChatService) {
    super('ChatController');
  }

  /**
   * 发送聊天消息
   */
  @MessagePattern('chat.sendMessage')
  async sendMessage(@Payload() payload: SendMessagePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`发送聊天消息: ${payload.characterId}`);

    // 设置发送者信息
    const messageData = {
      ...payload,
      senderId: payload.characterId,
      serverId: payload.serverId,
    };

    const result = await this.chatService.sendMessage(messageData);
    return this.fromResult(result);
  }

  /**
   * 获取聊天历史
   */
  @MessagePattern('chat.getHistory')
  async getChatHistory(@Payload() payload: GetHistoryPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取聊天历史: ${payload.channelId}`);
    const result = await this.chatService.getChatHistory(payload);
    return this.fromResult(result);
  }

  /**
   * 获取私聊历史
   */
  @MessagePattern('chat.getPrivateHistory')
  async getPrivateHistory(@Payload() payload: GetPrivateHistoryPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取私聊历史: ${payload.senderId} <-> ${payload.receiverId}`);
    const result = await this.chatService.getPrivateChatHistory(payload);
    return this.fromResult(result);
  }

  /**
   * 加入聊天频道
   */
  @MessagePattern('chat.joinChannel')
  async joinChannel(@Payload() payload: JoinChannelPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`加入聊天频道: ${payload.characterId} -> ${payload.channelId}`);
    const result = await this.chatService.joinChannel(
      payload.characterId,
      payload.channelId,
      payload.serverId
    );
    return this.fromResult(result);
  }

  /**
   * 离开聊天频道
   */
  @MessagePattern('chat.leaveChannel')
  async leaveChannel(@Payload() payload: LeaveChannelPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`离开聊天频道: ${payload.characterId} -> ${payload.channelId}`);
    const result = await this.chatService.leaveChannel(payload.characterId, payload.channelId);
    return this.fromResult(result);
  }
}
