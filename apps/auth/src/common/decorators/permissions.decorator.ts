import { SetMetadata } from '@nestjs/common';

export const PERMISSIONS_KEY = 'permissions';

export interface PermissionRequirement {
  resource: string;
  action: string;
  conditions?: Record<string, any>;
}

/**
 * 权限装饰器
 * 用于标记需要特定权限才能访问的路由或控制器
 */
export const RequirePermissions = (...permissions: PermissionRequirement[]) =>
  SetMetadata(PERMISSIONS_KEY, permissions);

/**
 * 简化的权限装饰器
 * 用于快速定义资源和操作权限
 */
export const Permission = (resource: string, action: string, conditions?: Record<string, any>) =>
  RequirePermissions({ resource, action, conditions });

/**
 * 资源权限装饰器
 * 用于定义对特定资源的权限要求
 */
export const ResourcePermission = (resource: string) => {
  return {
    /**
     * 创建权限
     */
    create: (conditions?: Record<string, any>) =>
      Permission(resource, 'create', conditions),

    /**
     * 读取权限
     */
    read: (conditions?: Record<string, any>) =>
      Permission(resource, 'read', conditions),

    /**
     * 更新权限
     */
    update: (conditions?: Record<string, any>) =>
      Permission(resource, 'update', conditions),

    /**
     * 删除权限
     */
    delete: (conditions?: Record<string, any>) =>
      Permission(resource, 'delete', conditions),

    /**
     * 列表权限
     */
    list: (conditions?: Record<string, any>) =>
      Permission(resource, 'list', conditions),

    /**
     * 管理权限
     */
    manage: (conditions?: Record<string, any>) =>
      Permission(resource, 'manage', conditions),

    /**
     * 自定义操作权限
     */
    action: (action: string, conditions?: Record<string, any>) =>
      Permission(resource, action, conditions),
  };
};

/**
 * 常用资源权限
 */
export const Permissions = {
  // 用户权限
  User: ResourcePermission('user'),
  
  // 角色权限
  Role: ResourcePermission('role'),
  
  // 权限管理
  Permission: ResourcePermission('permission'),
  
  // 游戏权限
  Game: ResourcePermission('game'),
  
  // 团队权限
  Team: ResourcePermission('team'),
  
  // 比赛权限
  Match: ResourcePermission('match'),
  
  // 球员权限
  Player: ResourcePermission('player'),
  
  // 转会权限
  Transfer: ResourcePermission('transfer'),
  
  // 财务权限
  Finance: ResourcePermission('finance'),
  
  // 系统权限
  System: ResourcePermission('system'),
  
  // 审计权限
  Audit: ResourcePermission('audit'),
  
  // 报告权限
  Report: ResourcePermission('report'),
  
  // 设置权限
  Settings: ResourcePermission('settings'),
};

/**
 * 条件权限装饰器
 */
export const ConditionalPermissions = {
  /**
   * 所有者权限 - 只有资源所有者才能访问
   */
  owner: (resource: string, action: string, ownerField = 'params.userId') =>
    Permission(resource, action, { owner: ownerField }),

  /**
   * 时间限制权限 - 只在特定时间范围内允许访问
   */
  timeRestricted: (resource: string, action: string, startTime: string, endTime: string) =>
    Permission(resource, action, { timeRange: { start: startTime, end: endTime } }),

  /**
   * IP限制权限 - 只允许特定IP范围访问
   */
  ipRestricted: (resource: string, action: string, ipRanges: string[]) =>
    Permission(resource, action, { ipRange: ipRanges }),

  /**
   * 设备限制权限 - 只允许特定设备类型访问
   */
  deviceRestricted: (resource: string, action: string, deviceTypes: string[]) =>
    Permission(resource, action, { deviceTypes }),

  /**
   * 用户属性权限 - 基于用户属性的权限检查
   */
  userAttribute: (resource: string, action: string, attributes: Record<string, any>) =>
    Permission(resource, action, { userAttributes: attributes }),

  /**
   * 地理位置限制权限
   */
  geoRestricted: (resource: string, action: string, countries: string[], regions?: string[]) =>
    Permission(resource, action, { 
      geoRestrictions: { countries, regions: regions || [] } 
    }),
};

/**
 * 组合权限装饰器
 */
export const CombinedPermissions = {
  /**
   * 需要任一权限 - 用户只需要拥有其中一个权限即可
   */
  anyOf: (...permissions: PermissionRequirement[]) =>
    SetMetadata(PERMISSIONS_KEY, { type: 'any', permissions }),

  /**
   * 需要所有权限 - 用户必须拥有所有权限
   */
  allOf: (...permissions: PermissionRequirement[]) =>
    SetMetadata(PERMISSIONS_KEY, { type: 'all', permissions }),
};

/**
 * 管理员权限装饰器
 */
export const AdminPermissions = {
  /**
   * 系统管理员权限
   */
  systemAdmin: () => Permission('system', 'admin'),

  /**
   * 用户管理员权限
   */
  userAdmin: () => Permission('user', 'admin'),

  /**
   * 内容管理员权限
   */
  contentAdmin: () => Permission('content', 'admin'),

  /**
   * 安全管理员权限
   */
  securityAdmin: () => Permission('security', 'admin'),

  /**
   * 审计管理员权限
   */
  auditAdmin: () => Permission('audit', 'admin'),

  /**
   * 统计查看权限
   */
  statisticsView: () => Permission('statistics', 'view'),
};

/**
 * 游戏特定权限装饰器
 */
export const GamePermissions = {
  /**
   * 游戏管理权限
   */
  manage: () => Permission('game', 'manage'),

  /**
   * 游戏参与权限
   */
  play: () => Permission('game', 'play'),

  /**
   * 游戏观看权限
   */
  watch: () => Permission('game', 'watch'),

  /**
   * 游戏统计权限
   */
  stats: () => Permission('game', 'stats'),

  /**
   * 游戏配置权限
   */
  configure: () => Permission('game', 'configure'),

  /**
   * 团队管理权限
   */
  teamManage: () => Permission('team', 'manage'),

  /**
   * 球员交易权限
   */
  playerTrade: () => Permission('player', 'trade'),

  /**
   * 财务管理权限
   */
  financeManage: () => Permission('finance', 'manage'),
};
