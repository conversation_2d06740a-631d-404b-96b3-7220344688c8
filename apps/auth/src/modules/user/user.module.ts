import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

// 控制器
import { UsersController } from './controllers/users.controller';

// 服务
import { UsersService } from './services/users.service';

// 仓储
import { UserRepository } from './repositories/user.repository';

// 实体
import { User, UserSchema } from './entities/user.entity';

// 依赖模块
import { SecurityModule } from '../security/security.module';

/**
 * 用户管理模块
 * 
 * 负责用户基础信息的管理，包括：
 * - 用户CRUD操作
 * - 用户信息查询和更新
 * - 用户状态管理
 * - 用户数据转换和格式化
 * 
 * 职责范围：
 * - 用户基础信息管理
 * - 用户状态和配置管理
 * - 用户数据的查询和统计
 * - 用户信息的安全处理
 * 
 * 设计原则：
 * - 单一职责：只处理用户基础信息
 * - 数据安全：敏感信息脱敏处理
 * - 接口抽象：提供IUserRepository接口
 */
@Module({
  imports: [
    // MongoDB Schema注册
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
    ]),
    
    // 导入SecurityModule以获取PasswordService
    SecurityModule,
  ],
  controllers: [
    UsersController,
  ],
  providers: [
    // 服务层
    UsersService,

    // 仓储层
    UserRepository,
    
    // 接口实现注册 - 供其他模块通过接口注入使用
    {
      provide: 'IUserRepository',
      useClass: UserRepository,
    },
    {
      provide: 'IUsersService',
      useClass: UsersService,
    },
  ],
  exports: [
    // 服务导出
    UsersService,

    // 仓储导出
    UserRepository,
    
    // 接口导出 - 供依赖注入使用
    'IUserRepository',
    'IUsersService',
  ],
})
export class UserModule {
  constructor() {
    console.log('✅ 用户管理模块已初始化 - 提供用户基础信息管理功能');
  }
}
