/**
 * 测试限流禁用功能
 * 验证在开发环境中限流是否被正确禁用
 */

const axios = require('axios');

// 配置
const AUTH_BASE_URL = 'http://127.0.0.1:3001';
const TEST_ITERATIONS = 20; // 测试20次快速请求

class RateLimitDisabledTest {
  constructor() {
    this.successCount = 0;
    this.errorCount = 0;
    this.rateLimitedCount = 0;
  }

  async runTest() {
    console.log('🚀 开始测试限流禁用功能...');
    console.log(`📊 将发送 ${TEST_ITERATIONS} 个快速请求到登录端点`);
    console.log('');

    const startTime = Date.now();

    // 并发发送多个请求
    const promises = [];
    for (let i = 0; i < TEST_ITERATIONS; i++) {
      promises.push(this.testLoginRequest(i + 1));
    }

    await Promise.all(promises);

    const endTime = Date.now();
    const duration = endTime - startTime;

    this.printResults(duration);
  }

  async testLoginRequest(requestNumber) {
    try {
      const response = await axios.post(`${AUTH_BASE_URL}/auth/login`, {
        username: 'testuser',
        password: 'wrongpassword'
      }, {
        validateStatus: () => true, // 接受所有状态码
        timeout: 5000
      });

      const status = response.status;
      const message = response.data?.message || 'No message';

      console.log(`请求 ${requestNumber.toString().padStart(2, '0')}: 状态码 ${status} - ${message}`);

      if (status === 429) {
        this.rateLimitedCount++;
        console.log(`   ❌ 请求 ${requestNumber} 被限流！`);
      } else if (status === 401 || status === 400) {
        this.successCount++;
        console.log(`   ✅ 请求 ${requestNumber} 正常处理（认证失败但未被限流）`);
      } else {
        this.errorCount++;
        console.log(`   ⚠️ 请求 ${requestNumber} 其他错误: ${status}`);
      }

    } catch (error) {
      this.errorCount++;
      console.log(`   💥 请求 ${requestNumber} 网络错误: ${error.message}`);
    }
  }

  printResults(duration) {
    console.log('');
    console.log('📊 测试结果统计:');
    console.log('================');
    console.log(`总请求数: ${TEST_ITERATIONS}`);
    console.log(`成功处理: ${this.successCount} (${(this.successCount / TEST_ITERATIONS * 100).toFixed(1)}%)`);
    console.log(`被限流: ${this.rateLimitedCount} (${(this.rateLimitedCount / TEST_ITERATIONS * 100).toFixed(1)}%)`);
    console.log(`其他错误: ${this.errorCount} (${(this.errorCount / TEST_ITERATIONS * 100).toFixed(1)}%)`);
    console.log(`总耗时: ${duration}ms`);
    console.log(`平均响应时间: ${(duration / TEST_ITERATIONS).toFixed(1)}ms`);
    console.log('');

    // 判断测试结果
    if (this.rateLimitedCount === 0) {
      console.log('🎉 测试通过！限流已成功禁用');
      console.log('✅ 所有请求都没有被限流拦截');
    } else {
      console.log('❌ 测试失败！仍有请求被限流');
      console.log('🔧 请检查以下配置:');
      console.log('   - NODE_ENV=development');
      console.log('   - RATE_LIMIT_ENABLED=false');
      console.log('   - 确保使用了 .env.development 配置文件');
    }

    console.log('');
    console.log('🔍 如果测试失败，请检查:');
    console.log('1. 环境变量配置是否正确');
    console.log('2. Auth服务是否重启以加载新配置');
    console.log('3. RateLimitConfigService 是否正确注入');
    console.log('4. ThrottlerBehindProxyGuard 是否使用了新的配置服务');
  }

  async testSpecificEndpoints() {
    console.log('');
    console.log('🔍 测试特定端点的限流状态...');
    
    const endpoints = [
      { path: '/auth/login', method: 'POST', data: { username: 'test', password: 'test' } },
      { path: '/auth/register', method: 'POST', data: { 
        username: 'testuser', 
        email: '<EMAIL>', 
        password: 'Test123456!',
        confirmPassword: 'Test123456!',
        profile: { firstName: 'Test', lastName: 'User' },
        acceptTerms: true
      }},
      { path: '/auth/reset-password', method: 'POST', data: { email: '<EMAIL>' } },
    ];

    for (const endpoint of endpoints) {
      console.log(`\n测试端点: ${endpoint.method} ${endpoint.path}`);
      
      // 快速发送5个请求
      const promises = [];
      for (let i = 0; i < 5; i++) {
        promises.push(this.testEndpoint(endpoint, i + 1));
      }
      
      await Promise.all(promises);
    }
  }

  async testEndpoint(endpoint, requestNumber) {
    try {
      const response = await axios({
        method: endpoint.method.toLowerCase(),
        url: `${AUTH_BASE_URL}${endpoint.path}`,
        data: endpoint.data,
        validateStatus: () => true,
        timeout: 5000
      });

      const status = response.status;
      const isRateLimited = status === 429;
      
      console.log(`  请求 ${requestNumber}: ${status} ${isRateLimited ? '❌ 被限流' : '✅ 正常'}`);
      
      return !isRateLimited;
    } catch (error) {
      console.log(`  请求 ${requestNumber}: 错误 - ${error.message}`);
      return false;
    }
  }
}

// 运行测试
async function runTest() {
  const test = new RateLimitDisabledTest();
  
  try {
    await test.runTest();
    await test.testSpecificEndpoints();
  } catch (error) {
    console.error('❌ 测试执行失败:', error.message);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runTest().catch(console.error);
}

module.exports = RateLimitDisabledTest;
