/**
 * 生产环境配置调试工具
 * 检查Auth服务的实际环境变量配置
 */

const axios = require('axios');

const AUTH_BASE_URL = 'http://127.0.0.1:3001';

async function debugProductionEnvironment() {
  console.log('🔍 生产环境配置调试');
  console.log('=' .repeat(50));

  // 1. 检查本地环境变量
  console.log('\n📋 本地环境变量:');
  console.log(`NODE_ENV: "${process.env.NODE_ENV}"`);
  console.log(`RATE_LIMIT_ENABLED: "${process.env.RATE_LIMIT_ENABLED}"`);
  console.log(`RATE_LIMIT_LOGIN_ENABLED: "${process.env.RATE_LIMIT_LOGIN_ENABLED}"`);
  console.log(`RATE_LIMIT_LOGIN_TTL: "${process.env.RATE_LIMIT_LOGIN_TTL}"`);
  console.log(`RATE_LIMIT_LOGIN_LIMIT: "${process.env.RATE_LIMIT_LOGIN_LIMIT}"`);

  // 2. 测试Auth服务响应头
  console.log('\n🔍 测试Auth服务限流响应头:');
  try {
    const response = await axios.post(`${AUTH_BASE_URL}/auth/login`, {
      username: 'test',
      password: 'wrong'
    }, {
      validateStatus: () => true,
      timeout: 5000
    });

    console.log(`状态码: ${response.status}`);
    console.log('响应头:');
    
    // 检查限流相关的响应头
    const rateLimitHeaders = [
      'x-ratelimit-limit',
      'x-ratelimit-remaining', 
      'x-ratelimit-reset',
      'retry-after'
    ];

    rateLimitHeaders.forEach(header => {
      const value = response.headers[header];
      if (value !== undefined) {
        console.log(`  ${header}: ${value}`);
      }
    });

    if (response.status === 429) {
      console.log('✅ 限流已触发！');
      console.log('响应体:', response.data);
    } else {
      console.log('❌ 限流未触发');
    }

  } catch (error) {
    console.log(`❌ 请求失败: ${error.message}`);
  }

  // 3. 快速测试限流
  console.log('\n🚀 快速限流测试（发送10个请求）:');
  let rateLimitedCount = 0;
  let successCount = 0;

  const promises = [];
  for (let i = 0; i < 10; i++) {
    promises.push(sendTestRequest(i + 1));
  }

  const results = await Promise.all(promises);
  
  results.forEach(result => {
    if (result.rateLimited) {
      rateLimitedCount++;
    } else if (result.success) {
      successCount++;
    }
  });

  console.log(`\n📊 快速测试结果:`);
  console.log(`成功处理: ${successCount}`);
  console.log(`被限流: ${rateLimitedCount}`);

  if (rateLimitedCount > 0) {
    console.log('✅ 限流功能正常工作！');
  } else {
    console.log('❌ 限流功能未生效');
    console.log('\n🔧 可能的原因:');
    console.log('1. NODE_ENV 不是 production');
    console.log('2. RATE_LIMIT_ENABLED 不是 true');
    console.log('3. 限流阈值设置过高');
    console.log('4. 限流配置未正确加载');
  }
}

async function sendTestRequest(num) {
  try {
    const response = await axios.post(`${AUTH_BASE_URL}/auth/login`, {
      username: 'test',
      password: 'wrong'
    }, {
      validateStatus: () => true,
      timeout: 5000
    });
    
    const isRateLimited = response.status === 429;
    const isSuccess = response.status >= 200 && response.status < 500 && !isRateLimited;
    
    const status = isRateLimited ? '❌ 限流' : (isSuccess ? '✅ 正常' : '⚠️ 错误');
    console.log(`  请求 ${num.toString().padStart(2, '0')}: ${response.status} ${status}`);
    
    return { 
      rateLimited: isRateLimited, 
      success: isSuccess, 
      status: response.status 
    };
  } catch (error) {
    console.log(`  请求 ${num.toString().padStart(2, '0')}: 网络错误`);
    return { rateLimited: false, success: false, error: error.message };
  }
}

if (require.main === module) {
  debugProductionEnvironment().catch(console.error);
}
