/**
 * 全面的限流测试工具
 * 测试不同环境下的限流效果是否正确
 */

const axios = require('axios');

const AUTH_BASE_URL = 'http://127.0.0.1:3001';

class RateLimitTester {
  constructor() {
    this.results = {
      total: 0,
      success: 0,
      rateLimited: 0,
      errors: 0,
      responses: []
    };
  }

  async testCurrentEnvironment() {
    console.log('🔍 检测当前环境限流配置...');

    try {
      // 检查健康状态获取环境信息
      const healthResponse = await axios.get(`${AUTH_BASE_URL}/health`, {
        timeout: 5000,
        validateStatus: () => true
      });

      if (healthResponse.status === 200) {
        console.log('✅ Auth服务运行正常');
        console.log(`📋 服务状态: ${healthResponse.data.status || 'unknown'}`);
      }
    } catch (error) {
      console.log('❌ 无法连接到Auth服务，请确保服务已启动');
      return false;
    }

    return true;
  }

  async runBasicRateLimitTest(requestCount = 10) {
    console.log(`\n🚀 基础限流测试 - 发送${requestCount}个并发请求`);
    console.log('=' .repeat(50));

    this.resetResults();

    // 并发发送请求
    const promises = [];
    for (let i = 0; i < requestCount; i++) {
      promises.push(this.sendTestRequest(i + 1, 'login'));
    }

    const startTime = Date.now();
    await Promise.all(promises);
    const duration = Date.now() - startTime;

    this.printBasicResults(duration);
    return this.results.rateLimited === 0;
  }

  async runIntensiveRateLimitTest(requestCount = 50, intervalMs = 100) {
    console.log(`\n🔥 密集限流测试 - ${requestCount}个请求，间隔${intervalMs}ms`);
    console.log('=' .repeat(50));

    this.resetResults();

    const startTime = Date.now();

    // 按间隔发送请求
    for (let i = 0; i < requestCount; i++) {
      await this.sendTestRequest(i + 1, 'login');
      if (i < requestCount - 1) {
        await this.sleep(intervalMs);
      }
    }

    const duration = Date.now() - startTime;

    this.printIntensiveResults(duration, intervalMs);
    return this.results.rateLimited === 0;
  }

  async testDifferentEndpoints() {
    console.log('\n🎯 多端点限流测试');
    console.log('=' .repeat(50));

    const endpoints = [
      { name: 'login', path: '/auth/login', data: { username: 'test', password: 'test' } },
      { name: 'register', path: '/auth/register', data: {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'Test123456!',
        confirmPassword: 'Test123456!',
        profile: { firstName: 'Test', lastName: 'User' },
        acceptTerms: true
      }},
      { name: 'reset-password', path: '/auth/reset-password', data: { email: '<EMAIL>' } }
    ];

    const results = {};

    for (const endpoint of endpoints) {
      console.log(`\n测试端点: ${endpoint.name}`);
      this.resetResults();

      // 对每个端点发送5个快速请求
      const promises = [];
      for (let i = 0; i < 5; i++) {
        promises.push(this.sendTestRequest(i + 1, endpoint.name, endpoint.path, endpoint.data));
      }

      await Promise.all(promises);

      results[endpoint.name] = {
        total: this.results.total,
        rateLimited: this.results.rateLimited,
        success: this.results.success
      };

      console.log(`  结果: ${this.results.success}成功, ${this.results.rateLimited}限流, ${this.results.errors}错误`);
    }

    return results;
  }

  async sendTestRequest(num, type, customPath = null, customData = null) {
    try {
      const path = customPath || '/auth/login';
      const data = customData || { username: 'test', password: 'wrong' };

      const response = await axios.post(`${AUTH_BASE_URL}${path}`, data, {
        validateStatus: () => true,
        timeout: 5000,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': `RateLimitTester-${num}`
        }
      });

      const isRateLimited = response.status === 429;
      const isSuccess = response.status >= 200 && response.status < 500 && !isRateLimited;
      const isError = response.status >= 500;

      this.results.total++;
      if (isRateLimited) {
        this.results.rateLimited++;
        console.log(`  请求 ${num.toString().padStart(2, '0')}: ${response.status} ❌ 被限流`);
      } else if (isSuccess) {
        this.results.success++;
        console.log(`  请求 ${num.toString().padStart(2, '0')}: ${response.status} ✅ 正常处理`);
      } else if (isError) {
        this.results.errors++;
        console.log(`  请求 ${num.toString().padStart(2, '0')}: ${response.status} ⚠️ 服务错误`);
      }

      this.results.responses.push({
        num,
        status: response.status,
        rateLimited: isRateLimited,
        timestamp: Date.now()
      });

      return { rateLimited: isRateLimited, status: response.status };
    } catch (error) {
      this.results.total++;
      this.results.errors++;
      console.log(`  请求 ${num.toString().padStart(2, '0')}: 网络错误 - ${error.message}`);
      return { rateLimited: false, error: error.message };
    }
  }

  resetResults() {
    this.results = {
      total: 0,
      success: 0,
      rateLimited: 0,
      errors: 0,
      responses: []
    };
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  printBasicResults(duration) {
    console.log('\n📊 基础测试结果:');
    console.log(`总请求数: ${this.results.total}`);
    console.log(`成功处理: ${this.results.success} (${(this.results.success / this.results.total * 100).toFixed(1)}%)`);
    console.log(`被限流: ${this.results.rateLimited} (${(this.results.rateLimited / this.results.total * 100).toFixed(1)}%)`);
    console.log(`服务错误: ${this.results.errors} (${(this.results.errors / this.results.total * 100).toFixed(1)}%)`);
    console.log(`总耗时: ${duration}ms`);
    console.log(`平均响应时间: ${(duration / this.results.total).toFixed(1)}ms`);

    if (this.results.rateLimited === 0) {
      console.log('\n✅ 基础测试通过！限流已禁用');
    } else {
      console.log('\n❌ 基础测试失败！检测到限流');
    }
  }

  printIntensiveResults(duration, intervalMs) {
    console.log('\n📊 密集测试结果:');
    console.log(`总请求数: ${this.results.total}`);
    console.log(`成功处理: ${this.results.success}`);
    console.log(`被限流: ${this.results.rateLimited}`);
    console.log(`服务错误: ${this.results.errors}`);
    console.log(`总耗时: ${duration}ms`);
    console.log(`预期耗时: ${this.results.total * intervalMs}ms`);
    console.log(`请求频率: ${(this.results.total / (duration / 1000)).toFixed(2)} req/s`);

    if (this.results.rateLimited === 0) {
      console.log('\n✅ 密集测试通过！限流已禁用');
    } else {
      console.log('\n❌ 密集测试失败！检测到限流');
      console.log(`限流触发率: ${(this.results.rateLimited / this.results.total * 100).toFixed(1)}%`);
    }
  }

  async runFullTest() {
    console.log('🧪 开始全面限流测试');
    console.log('🔍 当前环境应该是开发环境，限流应该被禁用');
    console.log('');

    // 1. 检查服务状态
    const serviceOk = await this.testCurrentEnvironment();
    if (!serviceOk) {
      return false;
    }

    // 2. 基础并发测试
    const basicPassed = await this.runBasicRateLimitTest(10);

    // 3. 密集请求测试
    const intensivePassed = await this.runIntensiveRateLimitTest(20, 50);

    // 4. 多端点测试
    const endpointResults = await this.testDifferentEndpoints();

    // 5. 总结
    console.log('\n🎯 测试总结');
    console.log('=' .repeat(50));

    const allEndpointsPassed = Object.values(endpointResults).every(result => result.rateLimited === 0);
    const allTestsPassed = basicPassed && intensivePassed && allEndpointsPassed;

    if (allTestsPassed) {
      console.log('🎉 所有测试通过！');
      console.log('✅ 开发环境限流已正确禁用');
      console.log('✅ 所有端点都没有限流限制');
      console.log('✅ 并发和密集请求都能正常处理');
    } else {
      console.log('❌ 部分测试失败！');
      if (!basicPassed) console.log('  - 基础并发测试失败');
      if (!intensivePassed) console.log('  - 密集请求测试失败');
      if (!allEndpointsPassed) console.log('  - 部分端点仍有限流');

      console.log('\n🔧 故障排除建议:');
      console.log('1. 检查环境变量: NODE_ENV=development');
      console.log('2. 检查配置: RATE_LIMIT_ENABLED=false');
      console.log('3. 重启Auth服务以加载新配置');
      console.log('4. 检查ThrottlerBehindProxyGuard的环境判断逻辑');
    }

    return allTestsPassed;
  }
}

// 运行测试
async function runTest() {
  const tester = new RateLimitTester();
  await tester.runFullTest();
}

if (require.main === module) {
  runTest().catch(console.error);
}

module.exports = RateLimitTester;
