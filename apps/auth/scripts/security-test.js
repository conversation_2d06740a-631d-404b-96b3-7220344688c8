/**
 * 安全测试脚本
 * 测试认证服务的安全防护能力
 */

const axios = require('axios');
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const dotenv = require('dotenv');
const database = require('./utils/database');
const config = require('./utils/config');
const Logger = require('./utils/logger');

// 加载环境变量
dotenv.config();

const logger = new Logger('SecurityTest');

// 安全测试结果接口
class SecurityTestResult {
  constructor(testName) {
    this.testName = testName;
    this.passed = false;
    this.details = '';
    this.severity = 'low'; // low, medium, high, critical
    this.recommendations = [];
  }
}

/**
 * 创建HTTP客户端
 */
function createHttpClient() {
  // 尝试多个可能的URL
  const possibleURLs = [
    `http://localhost:3001`,
    `http://127.0.0.1:3001`,
    `http://${config.get('app.host')}:${config.get('app.port')}`
  ];

  // 使用第一个URL作为默认
  return axios.create({
    baseURL: possibleURLs[0],
    timeout: 10000,
    validateStatus: () => true, // 不抛出HTTP错误
    family: 4 // 强制使用IPv4
  });
}

/**
 * 测试SQL注入防护
 */
async function testSqlInjectionProtection() {
  logger.subtitle('测试SQL注入防护...');
  
  const result = new SecurityTestResult('SQL注入防护');
  const client = createHttpClient();
  
  const sqlPayloads = [
    "'; DROP TABLE users; --",
    "' OR '1'='1",
    "' UNION SELECT * FROM users --",
    "admin'--",
    "admin' /*",
    "' OR 1=1#",
    "'; INSERT INTO users VALUES('hacker','hacked'); --",
    "' OR 'x'='x",
    "1' AND (SELECT COUNT(*) FROM users) > 0 --"
  ];
  
  let vulnerabilityFound = false;
  const vulnerablePayloads = [];
  
  for (const payload of sqlPayloads) {
    try {
      const response = await client.post('/auth/login', {
        username: payload,
        password: 'test',
        deviceInfo: {
          type: 'web',
          name: 'Security Test',
          fingerprint: 'security-test',
          userAgent: 'Security Test Agent',
          ipAddress: '127.0.0.1',
        },
      });
      
      // 检查是否返回了意外的成功响应或数据库错误
      if (response.status === 200 || 
          (response.data && response.data.message && 
           response.data.message.toLowerCase().includes('sql'))) {
        vulnerabilityFound = true;
        vulnerablePayloads.push(payload);
      }
    } catch (error) {
      // 预期的错误，说明防护有效
    }
  }
  
  result.passed = !vulnerabilityFound;
  result.details = vulnerabilityFound 
    ? `发现SQL注入漏洞，载荷: ${vulnerablePayloads.join(', ')}`
    : '所有SQL注入载荷都被成功阻止';
  result.severity = vulnerabilityFound ? 'critical' : 'low';
  
  if (vulnerabilityFound) {
    result.recommendations.push('实施参数化查询');
    result.recommendations.push('添加输入验证和过滤');
    result.recommendations.push('使用ORM框架防护');
  }
  
  return result;
}

/**
 * 测试XSS防护
 */
async function testXssProtection() {
  logger.subtitle('测试XSS防护...');
  
  const result = new SecurityTestResult('XSS防护');
  const client = createHttpClient();
  
  const xssPayloads = [
    '<script>alert("XSS")</script>',
    'javascript:alert("XSS")',
    '<img src="x" onerror="alert(\'XSS\')">',
    '<svg onload="alert(\'XSS\')">',
    '"><script>alert("XSS")</script>',
    '<iframe src="javascript:alert(\'XSS\')"></iframe>',
    '<body onload="alert(\'XSS\')">',
    '<input type="text" value="<script>alert(\'XSS\')</script>">'
  ];
  
  let vulnerabilityFound = false;
  const vulnerablePayloads = [];
  
  for (const payload of xssPayloads) {
    try {
      const response = await client.post('/auth/register', {
        username: `test${Date.now()}`,
        email: '<EMAIL>',
        password: 'TestPassword123!',
        confirmPassword: 'TestPassword123!',
        profile: {
          firstName: payload,
          lastName: 'Test',
          language: 'zh',
        },
        acceptTerms: true,
      });
      
      // 检查响应中是否包含未转义的脚本
      const responseText = JSON.stringify(response.data);
      if (responseText.includes('<script>') || responseText.includes('javascript:')) {
        vulnerabilityFound = true;
        vulnerablePayloads.push(payload);
      }
    } catch (error) {
      // 预期的错误，说明验证有效
    }
  }
  
  result.passed = !vulnerabilityFound;
  result.details = vulnerabilityFound 
    ? `发现XSS漏洞，载荷: ${vulnerablePayloads.join(', ')}`
    : '所有XSS载荷都被成功过滤';
  result.severity = vulnerabilityFound ? 'high' : 'low';
  
  if (vulnerabilityFound) {
    result.recommendations.push('实施输出编码');
    result.recommendations.push('使用内容安全策略(CSP)');
    result.recommendations.push('验证和过滤用户输入');
  }
  
  return result;
}

/**
 * 测试暴力破解防护
 */
async function testBruteForceProtection() {
  logger.subtitle('测试暴力破解防护...');
  
  const result = new SecurityTestResult('暴力破解防护');
  const client = createHttpClient();
  
  const maxAttempts = 20;
  let blockedAttempts = 0;
  let successfulAttempts = 0;
  
  for (let i = 0; i < maxAttempts; i++) {
    try {
      const response = await client.post('/auth/login', {
        username: 'nonexistent',
        password: 'wrongpassword'
      });
      
      if (response.status === 429) {
        blockedAttempts++;
      } else if (response.status === 401 || response.status === 400) {
        successfulAttempts++;
      }
      
      // 添加延迟避免过快请求
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      // 忽略网络错误
    }
  }
  
  const protectionActive = blockedAttempts > 0;
  const protectionRatio = blockedAttempts / maxAttempts;
  
  result.passed = protectionActive && protectionRatio > 0.3; // 至少30%的请求被阻止
  result.details = `在${maxAttempts}次尝试中，${blockedAttempts}次被限流阻止，${successfulAttempts}次正常响应`;
  result.severity = protectionActive ? 'low' : 'high';
  
  if (!protectionActive) {
    result.recommendations.push('实施登录尝试限制');
    result.recommendations.push('添加账户锁定机制');
    result.recommendations.push('使用CAPTCHA验证');
  }
  
  return result;
}

/**
 * 测试JWT令牌安全性
 */
async function testTokenSecurity() {
  logger.subtitle('测试JWT令牌安全性...');
  
  const result = new SecurityTestResult('JWT令牌安全性');
  const client = createHttpClient();
  
  const issues = [];
  
  // 测试弱JWT密钥
  try {
    const weakToken = jwt.sign(
      { sub: '123', name: 'Test User' },
      'weak', // 弱密钥
      { expiresIn: '1h' }
    );

    const response = await client.post('/auth/verify-token', {
      token: weakToken
    });

    // 检查响应体中的valid字段，而不是HTTP状态码
    if (response.data && response.data.data && response.data.data.valid === true) {
      issues.push('接受了使用弱密钥签名的JWT令牌');
    }
  } catch (error) {
    // 预期的错误
  }
  
  // 测试过期令牌
  try {
    const expiredToken = jwt.sign(
      { sub: '123', name: 'Test User', exp: Math.floor(Date.now() / 1000) - 3600 },
      config.get('jwt.secret')
    );

    const response = await client.post('/auth/verify-token', {
      token: expiredToken
    });

    // 检查响应体中的valid字段，而不是HTTP状态码
    if (response.data && response.data.data && response.data.data.valid === true) {
      issues.push('接受了过期的JWT令牌');
    }
  } catch (error) {
    // 预期的错误
  }
  
  // 测试无签名令牌
  try {
    const unsignedToken = 'eyJhbGciOiJub25lIiwidHlwIjoiSldUIn0.eyJzdWIiOiIxMjMiLCJuYW1lIjoiVGVzdCBVc2VyIn0.';

    const response = await client.post('/auth/verify-token', {
      token: unsignedToken
    });

    // 检查响应体中的valid字段，而不是HTTP状态码
    if (response.data && response.data.data && response.data.data.valid === true) {
      issues.push('接受了无签名的JWT令牌');
    }
  } catch (error) {
    // 预期的错误
  }
  
  result.passed = issues.length === 0;
  result.details = issues.length > 0 ? issues.join('; ') : 'JWT令牌安全检查通过';
  result.severity = issues.length > 0 ? 'high' : 'low';
  
  if (issues.length > 0) {
    result.recommendations.push('使用强密钥签名JWT');
    result.recommendations.push('严格验证令牌过期时间');
    result.recommendations.push('拒绝无签名令牌');
  }
  
  return result;
}

/**
 * 测试密码策略
 */
async function testPasswordPolicy() {
  logger.subtitle('测试密码策略...');
  
  const result = new SecurityTestResult('密码策略');
  const client = createHttpClient();
  
  const weakPasswords = [
    '123456',
    'password',
    'admin',
    'qwerty',
    '12345678',
    'abc123',
    'password123',
    '111111',
    'welcome',
    'login'
  ];
  
  let weakPasswordAccepted = false;
  const acceptedPasswords = [];
  
  for (const password of weakPasswords) {
    try {
      const response = await client.post('/auth/register', {
        username: `test${Date.now()}${Math.random()}`,
        email: `test${Date.now()}${Math.random()}@example.com`,
        password,
        confirmPassword: password,
        profile: {
          firstName: 'Test',
          lastName: 'User',
          language: 'zh',
        },
        acceptTerms: true,
      });
      
      if (response.status === 201) {
        weakPasswordAccepted = true;
        acceptedPasswords.push(password);
      }
      
      // 添加延迟
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      // 预期的错误
    }
  }
  
  result.passed = !weakPasswordAccepted;
  result.details = weakPasswordAccepted 
    ? `弱密码被接受: ${acceptedPasswords.join(', ')}`
    : '所有弱密码都被拒绝';
  result.severity = weakPasswordAccepted ? 'medium' : 'low';
  
  if (weakPasswordAccepted) {
    result.recommendations.push('实施强密码策略');
    result.recommendations.push('要求密码包含大小写字母、数字和特殊字符');
    result.recommendations.push('设置最小密码长度');
    result.recommendations.push('检查常见弱密码字典');
  }
  
  return result;
}

/**
 * 测试输入验证
 */
async function testInputValidation() {
  logger.subtitle('测试输入验证...');
  
  const result = new SecurityTestResult('输入验证');
  const client = createHttpClient();
  
  const invalidInputs = [
    { email: 'not-an-email' },
    { username: '' },
    { password: '' },
    { username: 'a'.repeat(1000) }, // 超长用户名
    { email: 'test@' + 'a'.repeat(1000) + '.com' }, // 超长邮箱
    { username: 'test\x00user' }, // 包含空字节
    { email: '<EMAIL>\r\n' }, // 包含换行符
  ];
  
  let validationBypassFound = false;
  const bypassedInputs = [];
  
  for (const input of invalidInputs) {
    try {
      const response = await client.post('/auth/register', {
        username: 'test',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        confirmPassword: 'TestPassword123!',
        profile: {
          firstName: 'Test',
          lastName: 'User',
          language: 'zh',
        },
        acceptTerms: true,
        ...input,
      });
      
      if (response.status === 201) {
        validationBypassFound = true;
        bypassedInputs.push(Object.keys(input)[0]);
      }
      
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      // 预期的错误
    }
  }
  
  result.passed = !validationBypassFound;
  result.details = validationBypassFound 
    ? `发现输入验证绕过: ${bypassedInputs.join(', ')}`
    : '输入验证正常工作';
  result.severity = validationBypassFound ? 'medium' : 'low';
  
  if (validationBypassFound) {
    result.recommendations.push('实施严格的输入验证');
    result.recommendations.push('限制输入长度');
    result.recommendations.push('过滤特殊字符');
    result.recommendations.push('使用白名单验证');
  }
  
  return result;
}

/**
 * 测试权限控制
 */
async function testAccessControl() {
  logger.subtitle('测试权限控制...');
  
  const result = new SecurityTestResult('权限控制');
  const client = createHttpClient();
  
  try {
    // 测试未授权访问受保护的端点
    const response = await client.post('/auth/user-info');

    const accessDenied = response.status === 401 || response.status === 403;

    result.passed = accessDenied;
    result.details = accessDenied
      ? '未授权访问被正确阻止'
      : `未授权访问未被阻止，状态码: ${response.status}`;
    result.severity = accessDenied ? 'low' : 'high';

    if (!accessDenied) {
      result.recommendations.push('实施严格的访问控制');
      result.recommendations.push('验证用户身份和权限');
      result.recommendations.push('使用基于角色的访问控制(RBAC)');
    }
  } catch (error) {
    result.passed = false;
    result.details = `权限控制测试失败: ${error.message}`;
    result.severity = 'medium';
  }
  
  return result;
}

/**
 * 生成安全测试报告
 */
function generateSecurityReport(results) {
  logger.separator('=', 60);
  logger.title('🔒 认证服务安全测试报告');
  
  const passed = results.filter(r => r.passed).length;
  const failed = results.filter(r => r.passed === false).length;
  const critical = results.filter(r => r.severity === 'critical').length;
  const high = results.filter(r => r.severity === 'high').length;
  const medium = results.filter(r => r.severity === 'medium').length;
  const low = results.filter(r => r.severity === 'low').length;
  
  logger.info('📊 测试概览:');
  logger.log(`   通过: ${passed}/${results.length}`);
  logger.log(`   失败: ${failed}/${results.length}`);
  logger.log(`   成功率: ${((passed / results.length) * 100).toFixed(1)}%`);
  logger.log('');
  
  logger.info('⚠️ 风险等级分布:');
  logger.log(`   严重: ${critical}`);
  logger.log(`   高: ${high}`);
  logger.log(`   中: ${medium}`);
  logger.log(`   低: ${low}`);
  logger.log('');
  
  logger.info('📋 详细结果:');
  results.forEach((result, index) => {
    const status = result.passed ? '✅' : '❌';
    const severity = result.severity.toUpperCase();
    logger.log(`   ${index + 1}. ${status} ${result.testName} [${severity}]`);
    logger.log(`      ${result.details}`);
    
    if (result.recommendations.length > 0) {
      logger.log(`      建议: ${result.recommendations.join('; ')}`);
    }
    logger.log('');
  });
  
  // 安全评级
  let securityGrade = 'A';
  if (critical > 0) securityGrade = 'F';
  else if (high > 0) securityGrade = 'D';
  else if (medium > 1) securityGrade = 'C';
  else if (medium > 0 || failed > 0) securityGrade = 'B';
  
  logger.info(`🏆 安全评级: ${securityGrade}`);
  
  if (securityGrade === 'A') {
    logger.success('安全防护优秀！');
  } else if (securityGrade === 'B') {
    logger.warn('安全防护良好，但有改进空间');
  } else {
    logger.fail('发现安全问题，需要立即修复！');
  }
  
  logger.separator('=', 60);
  
  return {
    grade: securityGrade,
    passed,
    failed,
    riskDistribution: { critical, high, medium, low }
  };
}

/**
 * 主安全测试函数
 */
async function securityTest() {
  const startTime = Date.now();
  
  try {
    logger.start('🔒 开始认证服务安全测试...');
    
    // 连接数据库
    await database.connect();
    
    const results = [];
    
    // 1. 测试SQL注入防护
    const sqlResult = await testSqlInjectionProtection();
    results.push(sqlResult);
    
    // 2. 测试XSS防护
    const xssResult = await testXssProtection();
    results.push(xssResult);
    
    // 3. 测试暴力破解防护
    const bruteForceResult = await testBruteForceProtection();
    results.push(bruteForceResult);
    
    // 4. 测试JWT令牌安全性
    const tokenResult = await testTokenSecurity();
    results.push(tokenResult);
    
    // 5. 测试密码策略
    const passwordResult = await testPasswordPolicy();
    results.push(passwordResult);
    
    // 6. 测试输入验证
    const inputResult = await testInputValidation();
    results.push(inputResult);
    
    // 7. 测试权限控制
    const accessResult = await testAccessControl();
    results.push(accessResult);
    
    // 生成安全报告
    const report = generateSecurityReport(results);
    
    const duration = Date.now() - startTime;
    logger.complete(`安全测试完成！耗时: ${(duration / 1000).toFixed(2)}秒`);
    
    return {
      results,
      report,
      duration
    };
    
  } catch (error) {
    logger.error('❌ 安全测试失败:', error);
    throw error;
  } finally {
    await database.disconnect();
  }
}

// 运行安全测试
if (require.main === module) {
  securityTest().catch((error) => {
    console.error('安全测试失败:', error);
    process.exit(1);
  });
}

module.exports = { securityTest, SecurityTestResult };
