#!/usr/bin/env node

/**
 * 通过网关测试Auth服务限流功能
 * 符合安全架构设计：客户端 → 网关 → Auth服务
 */

const axios = require('axios');

// 配置
const GATEWAY_URL = 'http://127.0.0.1:3000'; // 网关地址
const AUTH_ENDPOINT = '/api/auth/login';      // 通过网关的Auth端点
const TEST_PAYLOAD = {
  username: 'test-user',
  password: 'wrong-password'
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 发送请求的函数
async function sendRequest(requestNumber) {
  try {
    const response = await axios.post(`${GATEWAY_URL}${AUTH_ENDPOINT}`, TEST_PAYLOAD, {
      validateStatus: () => true, // 接受所有状态码
      timeout: 5000
    });
    
    return {
      status: response.status,
      success: true,
      error: null
    };
  } catch (error) {
    return {
      status: null,
      success: false,
      error: error.message
    };
  }
}

// 等待函数
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 测试正常频率请求
async function testNormalRate() {
  colorLog('blue', '\n📊 测试1: 正常请求频率（间隔2秒）');
  console.log('发送3个请求，间隔2秒（正常频率）');
  
  let successCount = 0;
  let rateLimitCount = 0;
  let errorCount = 0;
  
  for (let i = 1; i <= 3; i++) {
    const result = await sendRequest(i);
    
    if (!result.success) {
      console.log(`  请求 ${i.toString().padStart(2, '0')}: 错误 - ${result.error}`);
      errorCount++;
    } else if (result.status === 429) {
      console.log(`  请求 ${i.toString().padStart(2, '0')}: ${result.status} ❌ 被限流`);
      rateLimitCount++;
    } else {
      console.log(`  请求 ${i.toString().padStart(2, '0')}: ${result.status} ✅ 正常`);
      successCount++;
    }
    
    if (i < 3) {
      console.log('  等待2秒...');
      await sleep(2000);
    }
  }
  
  console.log(`结果: ${successCount}成功, ${rateLimitCount}限流, ${errorCount}错误`);
  
  if (rateLimitCount === 0) {
    colorLog('green', '✅ 正常频率测试通过');
    return true;
  } else {
    colorLog('red', '❌ 正常频率测试失败：正常请求被限流');
    return false;
  }
}

// 测试超限频率请求
async function testHighRate() {
  colorLog('yellow', '\n🚨 测试2: 超限请求频率（快速并发）');
  console.log('快速发送15个请求（超限频率）');
  
  const promises = [];
  for (let i = 1; i <= 15; i++) {
    promises.push(sendRequest(i));
  }
  
  const results = await Promise.all(promises);
  
  let successCount = 0;
  let rateLimitCount = 0;
  let errorCount = 0;
  
  results.forEach((result, index) => {
    const requestNumber = index + 1;
    
    if (!result.success) {
      console.log(`  请求 ${requestNumber.toString().padStart(2, '0')}: 错误 - ${result.error}`);
      errorCount++;
    } else if (result.status === 429) {
      console.log(`  请求 ${requestNumber.toString().padStart(2, '0')}: ${result.status} ❌ 被限流`);
      rateLimitCount++;
    } else {
      console.log(`  请求 ${requestNumber.toString().padStart(2, '0')}: ${result.status} ✅ 正常`);
      successCount++;
    }
  });
  
  console.log(`结果: ${successCount}成功, ${rateLimitCount}限流, ${errorCount}错误`);
  
  if (rateLimitCount > 0) {
    colorLog('green', '✅ 限流正确触发');
    const rateLimitRate = (rateLimitCount / results.length * 100).toFixed(1);
    console.log(`限流触发率: ${rateLimitRate}%`);
    return true;
  } else {
    colorLog('red', '❌ 限流测试失败：超限请求未被限流');
    return false;
  }
}

// 主测试函数
async function main() {
  try {
    // 显示环境信息
    colorLog('cyan', '🔍 环境信息:');
    console.log(`当前目录: ${process.cwd()}`);
    console.log(`Node.js版本: ${process.version}`);
    
    colorLog('magenta', '\n🏭 通过网关的Auth服务限流测试');
    colorLog('cyan', '🔍 测试网关代理到Auth服务的限流是否正确启用');
    console.log('==================================================');
    
    // 检查网关是否可访问
    try {
      await axios.get(`${GATEWAY_URL}/health`, { timeout: 3000 });
      colorLog('green', '✅ 网关服务可访问');
    } catch (error) {
      colorLog('red', '❌ 网关服务不可访问，请先启动网关服务');
      console.log(`网关地址: ${GATEWAY_URL}`);
      process.exit(1);
    }
    
    // 执行测试
    const test1Passed = await testNormalRate();
    const test2Passed = await testHighRate();
    
    // 总结
    colorLog('cyan', '\n🎯 通过网关的限流测试总结');
    console.log('==================================================');
    console.log(`正常频率测试: ${test1Passed ? '✅ 通过' : '❌ 失败'}`);
    console.log(`超限触发测试: ${test2Passed ? '✅ 通过' : '❌ 失败'}`);
    
    if (test1Passed && test2Passed) {
      colorLog('green', '\n🎉 通过网关的限流测试通过！');
      colorLog('green', '✅ 限流保护正常工作');
      colorLog('green', '✅ 正常请求不受影响');
      colorLog('green', '✅ 超限请求被正确拦截');
      colorLog('green', '✅ 符合安全架构设计');
    } else {
      colorLog('red', '\n❌ 限流测试失败！');
      process.exit(1);
    }
    
  } catch (error) {
    colorLog('red', `\n❌ 测试执行失败: ${error.message}`);
    process.exit(1);
  }
}

// 运行测试
main();
