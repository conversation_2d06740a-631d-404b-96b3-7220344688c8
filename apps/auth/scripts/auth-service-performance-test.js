/**
 * Auth服务专项性能测试
 * 测试实际运行中的Auth服务API性能
 */

const axios = require('axios');
const dotenv = require('dotenv');
const config = require('./utils/config');
const Logger = require('./utils/logger');

// 加载环境变量
dotenv.config();

const logger = new Logger('AuthServicePerformanceTest');

/**
 * 性能测试结果类
 */
class PerformanceResult {
  constructor(operation) {
    this.operation = operation;
    this.totalTime = 0;
    this.averageTime = 0;
    this.minTime = Infinity;
    this.maxTime = 0;
    this.successCount = 0;
    this.errorCount = 0;
    this.throughput = 0;
    this.times = [];
    this.errors = [];
  }

  addResult(time, success = true, error = null) {
    this.times.push(time);
    this.totalTime += time;
    this.minTime = Math.min(this.minTime, time);
    this.maxTime = Math.max(this.maxTime, time);
    
    if (success) {
      this.successCount++;
    } else {
      this.errorCount++;
      if (error) {
        this.errors.push(error);
      }
    }
  }

  calculate() {
    if (this.times.length > 0) {
      this.averageTime = this.totalTime / this.times.length;
      this.throughput = (this.successCount / this.totalTime) * 1000; // ops/sec
    }
    
    if (this.minTime === Infinity) {
      this.minTime = 0;
    }
  }

  getPercentile(percentile) {
    if (this.times.length === 0) return 0;
    
    const sorted = [...this.times].sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[index];
  }
}

/**
 * 检查Auth服务状态
 */
async function checkAuthServiceHealth() {
  // 尝试多个可能的URL
  const possibleURLs = [
    `http://localhost:3001`,
    `http://127.0.0.1:3001`,
    `http://${config.get('app.host')}:${config.get('app.port')}`
  ];

  for (const baseURL of possibleURLs) {
    try {
      logger.progress(`检查Auth服务状态: ${baseURL}...`);
      const response = await axios.get(`${baseURL}/health`, {
        timeout: 5000,
        family: 4 // 强制使用IPv4
      });

      if (response.status === 200) {
        logger.success(`Auth服务运行正常: ${baseURL}`);
        return { healthy: true, baseURL };
      }
    } catch (error) {
      logger.verbose(`尝试连接 ${baseURL} 失败: ${error.message}`);
      continue;
    }
  }

  logger.error('Auth服务未运行或不可访问');
  logger.info('请先启动Auth服务，在根目录执行:');
  logger.info('  npm run start:auth');
  return { healthy: false, baseURL: null };
}

/**
 * 测试用户注册性能
 */
async function testRegistrationPerformance(baseURL, iterations = 50) {
  logger.subtitle('测试用户注册性能...');
  
  const result = new PerformanceResult('user_registration');
  const testUsers = [];
  
  for (let i = 0; i < iterations; i++) {
    const startTime = Date.now();
    
    try {
      // 生成安全的随机密码：大写字母+小写字母+数字+特殊字符
      const randomNum = Math.floor(Math.random() * 1000);
      const securePassword = `SecureP@ss${randomNum}!`;

      const userData = {
        username: `perftest_${Date.now()}_${i}`,
        email: `perftest_${Date.now()}_${i}@example.com`,
        password: securePassword,
        confirmPassword: securePassword,
        profile: {
          firstName: 'Performance',
          lastName: 'Test',
          language: 'zh'
        },
        acceptTerms: true
      };
      
      const response = await axios.post(`${baseURL}/auth/register`, userData, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        },
        family: 4 // 强制使用IPv4
      });
      
      const operationTime = Date.now() - startTime;
      const success = response.status === 201;
      
      result.addResult(operationTime, success, success ? null : response.data);
      
      if (success) {
        testUsers.push({
          username: userData.username,
          email: userData.email,
          password: securePassword
        });
      }
      
    } catch (error) {
      const operationTime = Date.now() - startTime;
      result.addResult(operationTime, false, error.response?.data || error.message);
    }
    
    if (i % 10 === 0) {
      logger.progress(`注册测试进度: ${i}/${iterations}`);
    }
  }
  
  result.calculate();
  return { result, testUsers };
}

/**
 * 测试用户登录性能
 */
async function testLoginPerformance(baseURL, testUsers) {
  logger.subtitle('测试用户登录性能...');
  
  const result = new PerformanceResult('user_login');
  const tokens = [];
  
  for (let i = 0; i < testUsers.length; i++) {
    const startTime = Date.now();
    
    try {
      const loginData = {
        username: testUsers[i].username,
        password: testUsers[i].password
      };
      
      const response = await axios.post(`${baseURL}/auth/login`, loginData, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        },
        family: 4 // 强制使用IPv4
      });
      
      const operationTime = Date.now() - startTime;
      const success = response.status === 200;
      
      result.addResult(operationTime, success, success ? null : response.data);
      
      if (success && response.data.data && response.data.data.tokens && response.data.data.tokens.accessToken) {
        tokens.push(response.data.data.tokens.accessToken);
      }
      
    } catch (error) {
      const operationTime = Date.now() - startTime;
      result.addResult(operationTime, false, error.response?.data || error.message);
    }
    
    if (i % 10 === 0) {
      logger.progress(`登录测试进度: ${i}/${testUsers.length}`);
    }
  }
  
  result.calculate();
  return { result, tokens };
}

/**
 * 测试令牌验证性能
 */
async function testTokenVerificationPerformance(baseURL, tokens) {
  logger.subtitle('测试令牌验证性能...');
  
  const result = new PerformanceResult('token_verification');
  
  for (let i = 0; i < tokens.length; i++) {
    const startTime = Date.now();
    
    try {
      const response = await axios.post(`${baseURL}/auth/verify-token`, {
        token: tokens[i]
      }, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        },
        family: 4 // 强制使用IPv4
      });
      
      const operationTime = Date.now() - startTime;
      const success = response.status === 200;
      
      result.addResult(operationTime, success, success ? null : response.data);
      
    } catch (error) {
      const operationTime = Date.now() - startTime;
      result.addResult(operationTime, false, error.response?.data || error.message);
    }
    
    if (i % 10 === 0) {
      logger.progress(`令牌验证进度: ${i}/${tokens.length}`);
    }
  }
  
  result.calculate();
  return result;
}

/**
 * 测试并发登录性能
 */
async function testConcurrentLoginPerformance(baseURL, testUsers, concurrency = 10) {
  logger.subtitle(`测试并发登录性能 (${concurrency}个并发)...`);
  
  const result = new PerformanceResult('concurrent_login');
  const startTime = Date.now();
  
  // 选择测试用户
  const selectedUsers = testUsers.slice(0, Math.min(testUsers.length, concurrency * 5));
  
  const promises = [];
  
  for (let i = 0; i < concurrency; i++) {
    const promise = (async () => {
      for (let j = 0; j < Math.ceil(selectedUsers.length / concurrency); j++) {
        const userIndex = i * Math.ceil(selectedUsers.length / concurrency) + j;
        if (userIndex >= selectedUsers.length) break;
        
        const user = selectedUsers[userIndex];
        const operationStart = Date.now();
        
        try {
          const loginData = {
            username: user.username,
            password: user.password
          };
          
          const response = await axios.post(`${baseURL}/auth/login`, loginData, {
            timeout: 10000,
            family: 4 // 强制使用IPv4
          });
          
          const operationTime = Date.now() - operationStart;
          result.addResult(operationTime, response.status === 200);
        } catch (error) {
          const operationTime = Date.now() - operationStart;
          result.addResult(operationTime, false, error.message);
        }
      }
    })();
    
    promises.push(promise);
  }
  
  await Promise.all(promises);
  
  result.totalTime = Date.now() - startTime;
  result.calculate();
  return result;
}

/**
 * 打印性能测试结果
 */
function printResults(testName, result) {
  logger.info(`\n📈 ${testName} 性能测试结果:`);
  logger.log(`   操作次数: ${result.successCount + result.errorCount}`);
  logger.log(`   成功次数: ${result.successCount}`);
  logger.log(`   失败次数: ${result.errorCount}`);
  logger.log(`   成功率: ${((result.successCount / (result.successCount + result.errorCount)) * 100).toFixed(2)}%`);
  logger.log(`   总时间: ${result.totalTime}ms`);
  logger.log(`   平均时间: ${result.averageTime.toFixed(2)}ms`);
  logger.log(`   最小时间: ${result.minTime}ms`);
  logger.log(`   最大时间: ${result.maxTime}ms`);
  logger.log(`   P50: ${result.getPercentile(50).toFixed(2)}ms`);
  logger.log(`   P95: ${result.getPercentile(95).toFixed(2)}ms`);
  logger.log(`   P99: ${result.getPercentile(99).toFixed(2)}ms`);
  logger.log(`   吞吐量: ${result.throughput.toFixed(2)} ops/sec`);
  
  if (result.errorCount > 0 && result.errors.length > 0) {
    logger.warn(`   常见错误:`);
    result.errors.slice(0, 3).forEach((error, index) => {
      if (typeof error === 'object') {
        logger.warn(`     ${index + 1}. ${JSON.stringify(error, null, 2)}`);
      } else {
        logger.warn(`     ${index + 1}. ${error}`);
      }
    });
  }
}

/**
 * 生成性能报告
 */
function generatePerformanceReport(results) {
  logger.separator('=', 60);
  logger.title('🚀 Auth服务性能测试报告');
  
  logger.info('📊 测试概览:');
  const totalOperations = results.reduce((sum, result) => sum + result.successCount + result.errorCount, 0);
  const totalSuccessful = results.reduce((sum, result) => sum + result.successCount, 0);
  const totalFailed = results.reduce((sum, result) => sum + result.errorCount, 0);
  const overallSuccessRate = (totalSuccessful / totalOperations) * 100;
  
  logger.log(`   总操作数: ${totalOperations}`);
  logger.log(`   成功操作: ${totalSuccessful}`);
  logger.log(`   失败操作: ${totalFailed}`);
  logger.log(`   总体成功率: ${overallSuccessRate.toFixed(2)}%`);
  logger.log('');
  
  logger.info('⚡ 关键性能指标:');
  results.forEach(result => {
    logger.log(`   ${result.operation}:`);
    logger.log(`     平均响应时间: ${result.averageTime.toFixed(2)}ms`);
    logger.log(`     吞吐量: ${result.throughput.toFixed(2)} ops/sec`);
    logger.log(`     P95响应时间: ${result.getPercentile(95).toFixed(2)}ms`);
    logger.log(`     成功率: ${((result.successCount / (result.successCount + result.errorCount)) * 100).toFixed(2)}%`);
  });
  logger.log('');
  
  // 性能评级
  const avgResponseTime = results.reduce((sum, result) => sum + result.averageTime, 0) / results.length;
  let performanceGrade = 'A';
  
  if (avgResponseTime > 200) performanceGrade = 'B';
  if (avgResponseTime > 500) performanceGrade = 'C';
  if (avgResponseTime > 1000) performanceGrade = 'D';
  if (overallSuccessRate < 95) performanceGrade = 'D';
  
  logger.info(`🏆 Auth服务性能评级: ${performanceGrade}`);
  
  if (performanceGrade === 'A') {
    logger.success('Auth服务性能表现优秀！');
  } else if (performanceGrade === 'B') {
    logger.warn('Auth服务性能表现良好，但有改进空间');
  } else {
    logger.fail('Auth服务性能需要优化');
  }
  
  logger.separator('=', 60);
  
  return {
    totalOperations,
    successRate: overallSuccessRate,
    avgResponseTime,
    performanceGrade
  };
}

/**
 * 主测试函数
 */
async function runAuthServicePerformanceTest() {
  const startTime = Date.now();
  
  try {
    logger.start('🚀 开始Auth服务专项性能测试...');
    
    // 1. 检查服务状态
    const healthCheck = await checkAuthServiceHealth();
    if (!healthCheck.healthy) {
      logger.error('Auth服务未运行，无法进行性能测试');
      return;
    }
    
    const baseURL = healthCheck.baseURL;
    const results = [];
    
    // 2. 测试用户注册性能
    const { result: regResult, testUsers } = await testRegistrationPerformance(baseURL, 30);
    results.push(regResult);
    printResults('用户注册', regResult);
    
    if (testUsers.length === 0) {
      logger.error('没有成功创建测试用户，无法继续测试');
      return;
    }
    
    // 3. 测试用户登录性能
    const { result: loginResult, tokens } = await testLoginPerformance(baseURL, testUsers);
    results.push(loginResult);
    printResults('用户登录', loginResult);
    
    // 4. 测试令牌验证性能
    if (tokens.length > 0) {
      const verifyResult = await testTokenVerificationPerformance(baseURL, tokens);
      results.push(verifyResult);
      printResults('令牌验证', verifyResult);
    }
    
    // 5. 测试并发登录性能
    const concurrentResult = await testConcurrentLoginPerformance(baseURL, testUsers, 10);
    results.push(concurrentResult);
    printResults('并发登录', concurrentResult);
    
    // 生成性能报告
    const report = generatePerformanceReport(results);
    
    const duration = Date.now() - startTime;
    logger.complete(`Auth服务性能测试完成！耗时: ${(duration / 1000).toFixed(2)}秒`);
    
    return {
      results,
      report,
      duration,
      testUsers: testUsers.length
    };
    
  } catch (error) {
    logger.error('❌ Auth服务性能测试失败:', error);
    throw error;
  }
}

// 运行测试
if (require.main === module) {
  runAuthServicePerformanceTest().catch((error) => {
    console.error('Auth服务性能测试失败:', error);
    process.exit(1);
  });
}

module.exports = { runAuthServicePerformanceTest };
