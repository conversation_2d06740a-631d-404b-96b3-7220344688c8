/**
 * 生产环境限流测试工具
 * 验证生产环境下限流是否正确启用
 */

const axios = require('axios');

const AUTH_BASE_URL = 'http://127.0.0.1:3001';

class ProductionRateLimitTester {
  constructor() {
    this.results = {
      total: 0,
      success: 0,
      rateLimited: 0,
      errors: 0,
      responses: []
    };
  }

  async testProductionRateLimit() {
    console.log('🏭 生产环境限流测试');
    console.log('🔍 当前环境应该启用限流保护');
    console.log('');

    // 1. 检查服务状态
    const serviceOk = await this.checkServiceStatus();
    if (!serviceOk) {
      return false;
    }

    // 2. 测试正常请求频率
    console.log('📊 测试正常请求频率（应该通过）');
    const normalPassed = await this.testNormalRequestRate();

    // 3. 测试超限请求频率
    console.log('\n🚨 测试超限请求频率（应该被限流）');
    const overLimitTriggered = await this.testOverLimitRequests();

    // 4. 测试限流恢复
    console.log('\n⏰ 测试限流恢复（等待窗口重置）');
    const recoveryPassed = await this.testRateLimitRecovery();

    // 5. 总结
    this.printProductionTestSummary(normalPassed, overLimitTriggered, recoveryPassed);

    return normalPassed && overLimitTriggered && recoveryPassed;
  }

  async checkServiceStatus() {
    try {
      const response = await axios.get(`${AUTH_BASE_URL}/health`, {
        timeout: 5000,
        validateStatus: () => true
      });
      
      if (response.status === 200) {
        console.log('✅ Auth服务运行正常');
        return true;
      } else {
        console.log('❌ Auth服务状态异常');
        return false;
      }
    } catch (error) {
      console.log('❌ 无法连接到Auth服务');
      return false;
    }
  }

  async testNormalRequestRate() {
    console.log('发送5个请求，间隔2秒（正常频率）');
    this.resetResults();

    for (let i = 0; i < 5; i++) {
      await this.sendTestRequest(i + 1);
      if (i < 4) {
        console.log('  等待2秒...');
        await this.sleep(2000);
      }
    }

    const passed = this.results.rateLimited === 0;
    console.log(`结果: ${this.results.success}成功, ${this.results.rateLimited}限流`);
    
    if (passed) {
      console.log('✅ 正常频率测试通过');
    } else {
      console.log('❌ 正常频率被误限流');
    }

    return passed;
  }

  async testOverLimitRequests() {
    console.log('快速发送20个请求（超限频率）');
    this.resetResults();

    // 快速发送大量请求
    const promises = [];
    for (let i = 0; i < 20; i++) {
      promises.push(this.sendTestRequest(i + 1));
    }

    await Promise.all(promises);

    const triggered = this.results.rateLimited > 0;
    console.log(`结果: ${this.results.success}成功, ${this.results.rateLimited}限流`);
    
    if (triggered) {
      console.log('✅ 限流正确触发');
      console.log(`限流触发率: ${(this.results.rateLimited / this.results.total * 100).toFixed(1)}%`);
    } else {
      console.log('❌ 限流未触发（可能配置有误）');
    }

    return triggered;
  }

  async testRateLimitRecovery() {
    console.log('等待限流窗口重置（60秒）...');
    
    // 等待限流窗口重置
    await this.sleep(65000); // 等待65秒确保窗口重置
    
    console.log('测试限流恢复（发送3个请求）');
    this.resetResults();

    for (let i = 0; i < 3; i++) {
      await this.sendTestRequest(i + 1);
      await this.sleep(1000);
    }

    const recovered = this.results.rateLimited === 0;
    console.log(`结果: ${this.results.success}成功, ${this.results.rateLimited}限流`);
    
    if (recovered) {
      console.log('✅ 限流正确恢复');
    } else {
      console.log('❌ 限流未恢复');
    }

    return recovered;
  }

  async sendTestRequest(num) {
    try {
      const response = await axios.post(`${AUTH_BASE_URL}/auth/login`, {
        username: 'test',
        password: 'wrong'
      }, {
        validateStatus: () => true,
        timeout: 5000
      });
      
      const isRateLimited = response.status === 429;
      const isSuccess = response.status >= 200 && response.status < 500 && !isRateLimited;
      
      this.results.total++;
      if (isRateLimited) {
        this.results.rateLimited++;
        console.log(`  请求 ${num.toString().padStart(2, '0')}: ${response.status} ❌ 被限流`);
      } else if (isSuccess) {
        this.results.success++;
        console.log(`  请求 ${num.toString().padStart(2, '0')}: ${response.status} ✅ 正常`);
      } else {
        this.results.errors++;
        console.log(`  请求 ${num.toString().padStart(2, '0')}: ${response.status} ⚠️ 错误`);
      }
      
      return { rateLimited: isRateLimited, status: response.status };
    } catch (error) {
      this.results.total++;
      this.results.errors++;
      console.log(`  请求 ${num.toString().padStart(2, '0')}: 网络错误`);
      return { rateLimited: false, error: error.message };
    }
  }

  resetResults() {
    this.results = {
      total: 0,
      success: 0,
      rateLimited: 0,
      errors: 0,
      responses: []
    };
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  printProductionTestSummary(normalPassed, overLimitTriggered, recoveryPassed) {
    console.log('\n🎯 生产环境限流测试总结');
    console.log('=' .repeat(50));
    
    console.log(`正常频率测试: ${normalPassed ? '✅ 通过' : '❌ 失败'}`);
    console.log(`超限触发测试: ${overLimitTriggered ? '✅ 通过' : '❌ 失败'}`);
    console.log(`限流恢复测试: ${recoveryPassed ? '✅ 通过' : '❌ 失败'}`);
    
    const allPassed = normalPassed && overLimitTriggered && recoveryPassed;
    
    if (allPassed) {
      console.log('\n🎉 生产环境限流测试全部通过！');
      console.log('✅ 限流保护正常工作');
      console.log('✅ 正常请求不受影响');
      console.log('✅ 超限请求被正确拦截');
      console.log('✅ 限流窗口正确重置');
    } else {
      console.log('\n❌ 生产环境限流测试失败！');
      console.log('\n🔧 故障排除建议:');
      
      if (!normalPassed) {
        console.log('- 正常请求被误限流，检查限流阈值配置');
      }
      if (!overLimitTriggered) {
        console.log('- 限流未触发，检查限流是否启用');
        console.log('- 验证环境变量: NODE_ENV=production');
        console.log('- 验证配置: RATE_LIMIT_ENABLED=true');
      }
      if (!recoveryPassed) {
        console.log('- 限流窗口未重置，检查TTL配置');
      }
    }
  }
}

// 环境检查和测试启动
async function runProductionTest() {
  const nodeEnv = process.env.NODE_ENV;
  
  console.log('🔍 环境检查:');
  console.log(`NODE_ENV: ${nodeEnv}`);
  console.log(`RATE_LIMIT_ENABLED: ${process.env.RATE_LIMIT_ENABLED}`);
  
  if (nodeEnv === 'development') {
    console.log('\n⚠️ 警告: 当前是开发环境');
    console.log('生产环境限流测试需要在生产环境配置下运行');
    console.log('请设置 NODE_ENV=production 后重新测试');
    return;
  }
  
  const tester = new ProductionRateLimitTester();
  await tester.testProductionRateLimit();
}

if (require.main === module) {
  runProductionTest().catch(console.error);
}

module.exports = ProductionRateLimitTester;
