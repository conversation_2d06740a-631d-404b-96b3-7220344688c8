/**
 * 性能测试脚本
 * 测试认证服务的各项性能指标
 */

const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const axios = require('axios');
const dotenv = require('dotenv');
const database = require('./utils/database');
const config = require('./utils/config');
const Logger = require('./utils/logger');

// 加载环境变量
dotenv.config();

const logger = new Logger('PerformanceTest');

// 性能测试结果接口
class PerformanceResult {
  constructor(operation) {
    this.operation = operation;
    this.totalTime = 0;
    this.averageTime = 0;
    this.minTime = Infinity;
    this.maxTime = 0;
    this.successCount = 0;
    this.errorCount = 0;
    this.throughput = 0;
    this.times = [];
  }

  addResult(time, success = true) {
    this.times.push(time);
    this.totalTime += time;
    this.minTime = Math.min(this.minTime, time);
    this.maxTime = Math.max(this.maxTime, time);
    
    if (success) {
      this.successCount++;
    } else {
      this.errorCount++;
    }
  }

  calculate() {
    if (this.times.length > 0) {
      this.averageTime = this.totalTime / this.times.length;
      this.throughput = (this.successCount / this.totalTime) * 1000; // ops/sec
    }
    
    if (this.minTime === Infinity) {
      this.minTime = 0;
    }
  }

  getPercentile(percentile) {
    if (this.times.length === 0) return 0;
    
    const sorted = [...this.times].sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[index];
  }
}

/**
 * 测试密码哈希性能
 */
async function testPasswordHashing(iterations = 1000) {
  logger.subtitle('测试密码哈希性能...');
  
  const result = new PerformanceResult('password_hashing');
  const saltRounds = config.get('security.bcryptRounds', 12);
  
  for (let i = 0; i < iterations; i++) {
    const startTime = Date.now();
    
    try {
      await bcrypt.hash('TestPassword123!', saltRounds);
      const operationTime = Date.now() - startTime;
      result.addResult(operationTime, true);
    } catch (error) {
      const operationTime = Date.now() - startTime;
      result.addResult(operationTime, false);
    }
    
    // 显示进度
    if (i % 100 === 0) {
      logger.progress(`密码哈希进度: ${i}/${iterations}`);
    }
  }
  
  result.calculate();
  return result;
}

/**
 * 测试JWT令牌生成性能
 */
async function testJwtGeneration(iterations = 1000) {
  logger.subtitle('测试JWT令牌生成性能...');
  
  const result = new PerformanceResult('jwt_generation');
  const secret = config.get('jwt.secret');
  const payload = {
    sub: 'test-user-id',
    username: 'testuser',
    email: '<EMAIL>',
    roles: ['user'],
    permissions: ['game:play']
  };
  
  for (let i = 0; i < iterations; i++) {
    const startTime = Date.now();
    
    try {
      jwt.sign(payload, secret, { expiresIn: '15m' });
      const operationTime = Date.now() - startTime;
      result.addResult(operationTime, true);
    } catch (error) {
      const operationTime = Date.now() - startTime;
      result.addResult(operationTime, false);
    }
    
    if (i % 100 === 0) {
      logger.progress(`JWT生成进度: ${i}/${iterations}`);
    }
  }
  
  result.calculate();
  return result;
}

/**
 * 测试JWT令牌验证性能
 */
async function testJwtVerification(iterations = 1000) {
  logger.subtitle('测试JWT令牌验证性能...');
  
  const result = new PerformanceResult('jwt_verification');
  const secret = config.get('jwt.secret');
  const payload = {
    sub: 'test-user-id',
    username: 'testuser',
    email: '<EMAIL>',
    roles: ['user'],
    permissions: ['game:play']
  };
  
  // 预生成令牌
  const token = jwt.sign(payload, secret, { expiresIn: '15m' });
  
  for (let i = 0; i < iterations; i++) {
    const startTime = Date.now();
    
    try {
      jwt.verify(token, secret);
      const operationTime = Date.now() - startTime;
      result.addResult(operationTime, true);
    } catch (error) {
      const operationTime = Date.now() - startTime;
      result.addResult(operationTime, false);
    }
    
    if (i % 100 === 0) {
      logger.progress(`JWT验证进度: ${i}/${iterations}`);
    }
  }
  
  result.calculate();
  return result;
}

/**
 * 测试数据库查询性能
 */
async function testDatabaseQueries(iterations = 500) {
  logger.subtitle('测试数据库查询性能...');
  
  const result = new PerformanceResult('database_queries');
  
  // 用户Schema
  const userSchema = new mongoose.Schema({
    username: String,
    email: String,
    passwordHash: String,
    roles: [String],
    createdAt: { type: Date, default: Date.now }
  });
  
  const User = mongoose.model('TestUser', userSchema);
  
  // 创建测试数据
  const testUsers = [];
  for (let i = 0; i < 100; i++) {
    testUsers.push({
      username: `perftest${i}`,
      email: `perftest${i}@example.com`,
      passwordHash: 'hashedpassword',
      roles: ['user']
    });
  }
  
  try {
    await User.insertMany(testUsers);
    
    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now();
      
      try {
        const randomIndex = Math.floor(Math.random() * testUsers.length);
        await User.findOne({ username: `perftest${randomIndex}` });
        const operationTime = Date.now() - startTime;
        result.addResult(operationTime, true);
      } catch (error) {
        const operationTime = Date.now() - startTime;
        result.addResult(operationTime, false);
      }
      
      if (i % 50 === 0) {
        logger.progress(`数据库查询进度: ${i}/${iterations}`);
      }
    }
    
    // 清理测试数据
    await User.deleteMany({ username: /^perftest/ });
    
  } catch (error) {
    logger.error('数据库查询测试失败:', error);
  }
  
  result.calculate();
  return result;
}

/**
 * 测试Redis操作性能
 */
async function testRedisOperations(iterations = 1000) {
  logger.subtitle('测试Redis操作性能...');
  
  const result = new PerformanceResult('redis_operations');
  
  try {
    const redis = database.getRedis();
    
    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now();
      
      try {
        const key = `perf:test:${i}`;
        const value = JSON.stringify({ userId: `user${i}`, timestamp: Date.now() });
        
        await redis.setex(key, 60, value);
        await redis.get(key);
        await redis.del(key);
        
        const operationTime = Date.now() - startTime;
        result.addResult(operationTime, true);
      } catch (error) {
        const operationTime = Date.now() - startTime;
        result.addResult(operationTime, false);
      }
      
      if (i % 100 === 0) {
        logger.progress(`Redis操作进度: ${i}/${iterations}`);
      }
    }
  } catch (error) {
    logger.error('Redis操作测试失败:', error);
  }
  
  result.calculate();
  return result;
}

/**
 * 测试Auth服务API性能
 */
async function testAuthServiceAPI(iterations = 100) {
  logger.subtitle('测试Auth服务API性能...');

  const result = new PerformanceResult('auth_service_api');
  const baseURL = `http://${config.get('app.host')}:${config.get('app.port')}`;

  // 检查服务是否运行
  try {
    const healthCheck = await axios.get(`${baseURL}/health`, { timeout: 5000 });
    if (healthCheck.status !== 200) {
      throw new Error('Auth服务未运行或不健康');
    }
  } catch (error) {
    logger.warn('Auth服务未运行，跳过API性能测试');
    logger.info('请先启动Auth服务: npm run start:dev');
    result.addResult(0, false);
    result.calculate();
    return result;
  }

  const testUsers = [];

  // 创建测试用户
  for (let i = 0; i < Math.min(iterations, 50); i++) {
    const startTime = Date.now();

    try {
      const userData = {
        username: `perftest${i}_${Date.now()}`,
        email: `perftest${i}_${Date.now()}@example.com`,
        password: 'TestPassword123!',
        confirmPassword: 'TestPassword123!',
        profile: {
          firstName: 'Perf',
          lastName: 'Test',
          language: 'zh'
        },
        acceptTerms: true
      };

      const response = await axios.post(`${baseURL}/auth/register`, userData, {
        timeout: 10000
      });

      if (response.status === 201) {
        testUsers.push({
          username: userData.username,
          email: userData.email,
          password: userData.password
        });
      }

      const operationTime = Date.now() - startTime;
      result.addResult(operationTime, response.status === 201);
    } catch (error) {
      const operationTime = Date.now() - startTime;
      result.addResult(operationTime, false);
    }

    if (i % 10 === 0) {
      logger.progress(`注册测试进度: ${i}/${Math.min(iterations, 50)}`);
    }
  }

  // 测试登录性能
  for (let i = 0; i < testUsers.length; i++) {
    const startTime = Date.now();

    try {
      const loginData = {
        username: testUsers[i].username,
        password: testUsers[i].password,
        deviceInfo: {
          type: 'web',
          name: 'Performance Test',
          fingerprint: `perf-test-${i}`,
          userAgent: 'Performance Test Agent',
          ipAddress: '127.0.0.1'
        }
      };

      const response = await axios.post(`${baseURL}/auth/login`, loginData, {
        timeout: 10000
      });

      const operationTime = Date.now() - startTime;
      result.addResult(operationTime, response.status === 200);
    } catch (error) {
      const operationTime = Date.now() - startTime;
      result.addResult(operationTime, false);
    }

    if (i % 10 === 0) {
      logger.progress(`登录测试进度: ${i}/${testUsers.length}`);
    }
  }

  result.calculate();
  return result;
}

/**
 * 测试并发性能
 */
async function testConcurrentOperations(concurrency = 20, iterations = 50) {
  logger.subtitle(`测试并发性能 (${concurrency}个并发, ${iterations}次迭代)...`);

  const result = new PerformanceResult('concurrent_operations');
  const startTime = Date.now();

  const promises = [];

  for (let i = 0; i < concurrency; i++) {
    const promise = (async () => {
      for (let j = 0; j < iterations; j++) {
        const operationStart = Date.now();

        try {
          // 模拟复合操作：密码哈希 + JWT生成
          const hash = await bcrypt.hash(`password${i}${j}`, 10);
          const token = jwt.sign(
            { sub: `user${i}${j}`, hash },
            config.get('jwt.secret'),
            { expiresIn: '15m' }
          );

          const operationTime = Date.now() - operationStart;
          result.addResult(operationTime, true);
        } catch (error) {
          const operationTime = Date.now() - operationStart;
          result.addResult(operationTime, false);
        }
      }
    })();

    promises.push(promise);
  }

  await Promise.all(promises);

  result.totalTime = Date.now() - startTime;
  result.calculate();
  return result;
}

/**
 * 测试内存使用情况
 */
function testMemoryUsage() {
  logger.subtitle('测试内存使用情况...');
  
  const usage = process.memoryUsage();
  
  const result = {
    rss: Math.round(usage.rss / 1024 / 1024 * 100) / 100, // MB
    heapTotal: Math.round(usage.heapTotal / 1024 / 1024 * 100) / 100, // MB
    heapUsed: Math.round(usage.heapUsed / 1024 / 1024 * 100) / 100, // MB
    external: Math.round(usage.external / 1024 / 1024 * 100) / 100, // MB
    arrayBuffers: Math.round(usage.arrayBuffers / 1024 / 1024 * 100) / 100 // MB
  };
  
  logger.info('内存使用情况:');
  logger.log(`  RSS: ${result.rss} MB`);
  logger.log(`  堆总计: ${result.heapTotal} MB`);
  logger.log(`  堆使用: ${result.heapUsed} MB`);
  logger.log(`  外部: ${result.external} MB`);
  logger.log(`  数组缓冲区: ${result.arrayBuffers} MB`);
  
  return result;
}

/**
 * 打印性能测试结果
 */
function printResults(testName, result) {
  logger.info(`\n📈 ${testName} 性能测试结果:`);
  logger.log(`   操作次数: ${result.successCount + result.errorCount}`);
  logger.log(`   成功次数: ${result.successCount}`);
  logger.log(`   失败次数: ${result.errorCount}`);
  logger.log(`   成功率: ${((result.successCount / (result.successCount + result.errorCount)) * 100).toFixed(2)}%`);
  logger.log(`   总时间: ${result.totalTime}ms`);
  logger.log(`   平均时间: ${result.averageTime.toFixed(2)}ms`);
  logger.log(`   最小时间: ${result.minTime}ms`);
  logger.log(`   最大时间: ${result.maxTime}ms`);
  logger.log(`   P50: ${result.getPercentile(50).toFixed(2)}ms`);
  logger.log(`   P95: ${result.getPercentile(95).toFixed(2)}ms`);
  logger.log(`   P99: ${result.getPercentile(99).toFixed(2)}ms`);
  logger.log(`   吞吐量: ${result.throughput.toFixed(2)} ops/sec`);
}

/**
 * 生成性能报告
 */
function generatePerformanceReport(results, memoryUsage) {
  logger.separator('=', 60);
  logger.title('🚀 认证服务性能测试报告');
  
  logger.info('📊 测试概览:');
  const totalOperations = results.reduce((sum, result) => sum + result.successCount + result.errorCount, 0);
  const totalSuccessful = results.reduce((sum, result) => sum + result.successCount, 0);
  const totalFailed = results.reduce((sum, result) => sum + result.errorCount, 0);
  const overallSuccessRate = (totalSuccessful / totalOperations) * 100;
  
  logger.log(`   总操作数: ${totalOperations}`);
  logger.log(`   成功操作: ${totalSuccessful}`);
  logger.log(`   失败操作: ${totalFailed}`);
  logger.log(`   总体成功率: ${overallSuccessRate.toFixed(2)}%`);
  logger.log('');
  
  logger.info('⚡ 性能指标:');
  results.forEach(result => {
    logger.log(`   ${result.operation}:`);
    logger.log(`     平均响应时间: ${result.averageTime.toFixed(2)}ms`);
    logger.log(`     吞吐量: ${result.throughput.toFixed(2)} ops/sec`);
    logger.log(`     P95响应时间: ${result.getPercentile(95).toFixed(2)}ms`);
  });
  logger.log('');
  
  logger.info('💾 内存使用:');
  logger.log(`   RSS: ${memoryUsage.rss} MB`);
  logger.log(`   堆使用: ${memoryUsage.heapUsed} MB`);
  logger.log(`   堆利用率: ${((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100).toFixed(2)}%`);
  logger.log('');
  
  // 性能评级
  const avgResponseTime = results.reduce((sum, result) => sum + result.averageTime, 0) / results.length;
  let performanceGrade = 'A';
  
  if (avgResponseTime > 100) performanceGrade = 'B';
  if (avgResponseTime > 500) performanceGrade = 'C';
  if (avgResponseTime > 1000) performanceGrade = 'D';
  if (overallSuccessRate < 95) performanceGrade = 'D';
  
  logger.info(`🏆 性能评级: ${performanceGrade}`);
  
  if (performanceGrade === 'A') {
    logger.success('性能表现优秀！');
  } else if (performanceGrade === 'B') {
    logger.warn('性能表现良好，但有改进空间');
  } else {
    logger.fail('性能表现需要优化');
  }
  
  logger.separator('=', 60);
}

/**
 * 主性能测试函数
 */
async function performanceTest() {
  const startTime = Date.now();
  
  try {
    logger.start('🚀 开始认证服务性能测试...');
    
    // 连接数据库
    await database.connect();
    
    const results = [];
    const iterations = config.get('test.performanceIterations', 1000);
    
    // 1. 测试密码哈希性能
    const hashResult = await testPasswordHashing(Math.floor(iterations / 10));
    results.push(hashResult);
    printResults('密码哈希', hashResult);
    
    // 2. 测试JWT生成性能
    const jwtGenResult = await testJwtGeneration(iterations);
    results.push(jwtGenResult);
    printResults('JWT生成', jwtGenResult);
    
    // 3. 测试JWT验证性能
    const jwtVerifyResult = await testJwtVerification(iterations);
    results.push(jwtVerifyResult);
    printResults('JWT验证', jwtVerifyResult);
    
    // 4. 测试Auth服务API性能
    const apiResult = await testAuthServiceAPI(Math.floor(iterations / 10));
    results.push(apiResult);
    printResults('Auth服务API', apiResult);

    // 5. 测试数据库查询性能
    const dbResult = await testDatabaseQueries(Math.floor(iterations / 2));
    results.push(dbResult);
    printResults('数据库查询', dbResult);

    // 6. 测试Redis操作性能
    const redisResult = await testRedisOperations(iterations);
    results.push(redisResult);
    printResults('Redis操作', redisResult);

    // 7. 测试并发性能
    const concurrentResult = await testConcurrentOperations(20, 50);
    results.push(concurrentResult);
    printResults('并发操作', concurrentResult);
    
    // 8. 测试内存使用
    const memoryUsage = testMemoryUsage();
    
    // 生成性能报告
    generatePerformanceReport(results, memoryUsage);
    
    const duration = Date.now() - startTime;
    logger.complete(`性能测试完成！总耗时: ${(duration / 1000).toFixed(2)}秒`);
    
    return {
      results,
      memoryUsage,
      duration,
      summary: {
        totalOperations: results.reduce((sum, r) => sum + r.successCount + r.errorCount, 0),
        successRate: (results.reduce((sum, r) => sum + r.successCount, 0) / 
                     results.reduce((sum, r) => sum + r.successCount + r.errorCount, 0)) * 100,
        avgResponseTime: results.reduce((sum, r) => sum + r.averageTime, 0) / results.length
      }
    };
    
  } catch (error) {
    logger.error('❌ 性能测试失败:', error);
    throw error;
  } finally {
    await database.disconnect();
  }
}

// 运行性能测试
if (require.main === module) {
  performanceTest().catch((error) => {
    console.error('性能测试失败:', error);
    process.exit(1);
  });
}

module.exports = { performanceTest, PerformanceResult };
