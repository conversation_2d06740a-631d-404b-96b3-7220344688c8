/**
 * 荣誉墙Repository
 * 继承BaseRepository，提供统一的数据访问接口和Result模式错误处理
 * 基于old项目honorWall.js数据访问逻辑
 */

import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { Honor, HonorDocument, HonorTaskStatus } from '../schemas/honor.schema';
import { BaseRepository } from '@libs/common/repository/base-repository';
import { XResult, PaginationResult, XResultUtils } from '@libs/common/types/result.type';

/**
 * 荣誉墙数据访问层
 * 继承BaseRepository，提供统一的数据访问接口和Result模式错误处理
 *
 * 🎯 核心功能：
 * - 荣誉记录CRUD操作
 * - 荣誉任务管理
 * - 荣誉等级计算
 * - 荣誉统计分析
 * - 批量操作支持
 *
 * 🚀 性能优化：
 * - 使用BaseRepository的Lean查询优化
 * - 自动性能监控和慢查询检测
 * - 智能缓存策略
 * - 并行查询优化
 */
@Injectable()
export class HonorRepository extends BaseRepository<HonorDocument> {
  constructor(
    @InjectModel(Honor.name) honorModel: Model<HonorDocument>
  ) {
    super(honorModel, 'HonorRepository');
  }

  // ========== 业务特定方法 ==========

  /**
   * 根据玩家ID查找荣誉记录
   * 使用BaseRepository的findOne方法优化性能
   */
  async findByCharacterId(uid: string): Promise<XResult<HonorDocument | null>> {
    return this.findOne({ uid });
  }

  /**
   * 根据玩家ID查找荣誉记录（Lean查询优化版本）
   */
  async findByCharacterIdLean(uid: string): Promise<XResult<any | null>> {
    return this.findOneLean({ uid });
  }

  /**
   * 创建荣誉记录
   * 使用BaseRepository的createOne方法，自动应用Result模式和性能监控
   */
  async create(honorData: Partial<Honor>): Promise<XResult<HonorDocument>> {
    const honorWithDefaults = {
      ...honorData,
      currentLevel: 1,
      totalExperience: 0,
      totalTasksCompleted: 0,
      totalRewardsClaimed: 0,
      honorTasks: this.initDefaultTasks(),
      lastUpdateTime: Date.now(),
    };

    const result = await this.createOne(honorWithDefaults);
    if (XResultUtils.isSuccess(result)) {
      this.logger.log(`荣誉记录创建成功: ${result.data.uid}`);
    }
    return result;
  }

  /**
   * 获取或创建荣誉记录
   * 使用BaseRepository的事务支持确保数据一致性
   */
  async getOrCreateHonor(uid: string, serverId: string): Promise<XResult<HonorDocument>> {
    const honorResult = await this.findByCharacterId(uid);

    if (XResultUtils.isSuccess(honorResult) && honorResult.data) {
      return honorResult;
    }

    // 创建新荣誉记录
    const createResult = await this.create({
      uid,
      serverId,
    });

    if (XResultUtils.isSuccess(createResult)) {
      this.logger.log(`创建新荣誉记录: ${uid}`);
    }

    return createResult;
  }

  /**
   * 更新荣誉记录
   * 使用BaseRepository的updateOne方法优化性能
   */
  async update(
    uid: string,
    updateData: Partial<Honor>,
    session?: ClientSession
  ): Promise<XResult<HonorDocument | null>> {
    const result = await this.updateOne(
      { uid },
      { ...updateData, lastUpdateTime: Date.now() },
      session
    );

    if (XResultUtils.isSuccess(result) && result.data) {
      this.logger.log(`荣誉记录更新成功: ${uid}`);
    }

    return result;
  }

  /**
   * 删除荣誉记录
   * 使用BaseRepository的deleteOne方法优化性能
   */
  async delete(uid: string): Promise<XResult<boolean>> {
    const result = await this.deleteOne({ uid });
    if (XResultUtils.isSuccess(result) && result.data) {
      this.logger.log(`荣誉记录删除成功: ${uid}`);
      return XResultUtils.ok(true);
    }
    return XResultUtils.ok(false);
  }

  /**
   * 查找可领取奖励的玩家
   * 使用BaseRepository的findMany方法优化性能
   */
  async findCharactersWithClaimableRewards(): Promise<XResult<HonorDocument[]>> {
    return this.findMany({
      'honorTasks.status': HonorTaskStatus.COMPLETED
    });
  }

  /**
   * 查找可领取奖励的玩家（Lean查询优化版本）
   */
  async findCharactersWithClaimableRewardsLean(): Promise<XResult<any[]>> {
    return this.findManyLean({
      'honorTasks.status': HonorTaskStatus.COMPLETED
    }, {
      select: ['uid', 'serverId', 'currentLevel', 'honorTasks']
    });
  }

  /**
   * 获取荣誉排行榜
   * 使用BaseRepository的findMany方法优化性能
   */
  async getHonorRanking(limit: number = 100): Promise<XResult<HonorDocument[]>> {
    return this.findMany({}, {
      sort: {
        currentLevel: -1,
        totalExperience: -1,
        totalTasksCompleted: -1
      },
      limit
    });
  }

  /**
   * 获取荣誉排行榜（Lean查询优化版本）
   */
  async getHonorRankingLean(limit: number = 100): Promise<XResult<any[]>> {
    return this.findManyLean({}, {
      sort: {
        currentLevel: -1,
        totalExperience: -1,
        totalTasksCompleted: -1
      },
      limit,
      select: ['uid', 'serverId', 'currentLevel', 'totalExperience', 'totalTasksCompleted']
    });
  }

  /**
   * 统计荣誉数据
   * 使用BaseRepository的aggregate方法优化性能和错误处理
   */
  async getHonorStats(): Promise<XResult<any>> {
    const pipeline = [
      {
        $group: {
          _id: null,
          totalCharacters: { $sum: 1 },
          avgLevel: { $avg: '$currentLevel' },
          avgExperience: { $avg: '$totalExperience' },
          totalTasksCompleted: { $sum: '$totalTasksCompleted' },
          totalRewardsClaimed: { $sum: '$totalRewardsClaimed' },
          maxLevel: { $max: '$currentLevel' },
          maxExperience: { $max: '$totalExperience' },
        }
      }
    ];

    const result = await this.aggregate(pipeline);
    if (XResultUtils.isSuccess(result) && result.data.length > 0) {
      return XResultUtils.ok(result.data[0]);
    }

    // 返回默认统计信息
    return XResultUtils.ok({
      totalCharacters: 0,
      avgLevel: 0,
      avgExperience: 0,
      totalTasksCompleted: 0,
      totalRewardsClaimed: 0,
      maxLevel: 0,
      maxExperience: 0,
    });
  }

  /**
   * 批量更新荣誉记录
   * 使用BaseRepository的批量操作方法优化性能
   */
  async batchUpdate(updates: Array<{ uid: string; updateData: Partial<Honor> }>): Promise<XResult<void>> {
    // 如果只有一个更新操作，使用updateOne优化
    if (updates.length === 1) {
      const { uid, updateData } = updates[0];
      const result = await this.update(uid, updateData);
      return XResultUtils.isSuccess(result) ? XResultUtils.ok(undefined) : XResultUtils.error('批量更新失败', 'BATCH_UPDATE_FAILED');
    }

    // 多个更新操作，使用事务确保数据一致性
    return this.withTransaction(async (session) => {
      const updatePromises = updates.map(({ uid, updateData }) =>
        this.updateOne(
          { uid },
          { ...updateData, lastUpdateTime: Date.now() },
          session
        )
      );

      const results = await Promise.all(updatePromises);
      const failedUpdates = results.filter(result => XResultUtils.isFailure(result));

      if (failedUpdates.length > 0) {
        return XResultUtils.error(`批量更新失败: ${failedUpdates.length}/${updates.length}`, 'BATCH_UPDATE_PARTIAL_FAILED');
      }

      this.logger.log(`批量更新荣誉记录: ${updates.length} 条记录`);
      return XResultUtils.ok(undefined);
    });
  }

  // ========== 重写BaseRepository方法以添加业务特定逻辑 ==========

  /**
   * 重写数据验证方法，添加荣誉特定的验证规则
   */
  protected validateData(data: Partial<Honor>, operation: 'create' | 'update'): XResult<void> {
    if (operation === 'create') {
      if (!data.uid) {
        return XResultUtils.error('玩家UID不能为空', 'UID_REQUIRED');
      }

      if (!data.serverId) {
        return XResultUtils.error('服务器ID不能为空', 'SERVER_ID_REQUIRED');
      }
    }

    if (data.currentLevel !== undefined && (data.currentLevel < 1 || data.currentLevel > 100)) {
      return XResultUtils.error('荣誉等级必须在1-100之间', 'INVALID_HONOR_LEVEL');
    }

    if (data.totalExperience !== undefined && data.totalExperience < 0) {
      return XResultUtils.error('总经验不能为负数', 'INVALID_TOTAL_EXPERIENCE');
    }

    if (data.totalTasksCompleted !== undefined && data.totalTasksCompleted < 0) {
      return XResultUtils.error('完成任务数不能为负数', 'INVALID_TASKS_COMPLETED');
    }

    if (data.totalRewardsClaimed !== undefined && data.totalRewardsClaimed < 0) {
      return XResultUtils.error('领取奖励数不能为负数', 'INVALID_REWARDS_CLAIMED');
    }

    return XResultUtils.ok(undefined);
  }

  /**
   * 重写缓存TTL配置，针对荣誉数据的访问模式优化
   */
  protected getCacheTTL(operation: string): number {
    const customTTLs: Record<string, number> = {
      'findByCharacterId': 300,          // 荣誉记录缓存5分钟
      'findByCharacterIdLean': 180,      // 荣誉简介缓存3分钟
      'getHonorStats': 600,              // 统计信息缓存10分钟
      'getHonorRanking': 300,            // 排行榜缓存5分钟
      'findCharactersWithClaimableRewards': 120, // 可领取奖励缓存2分钟
    };

    return customTTLs[operation] || super.getCacheTTL(operation);
  }

  // ==================== 私有方法 ====================

  /**
   * 初始化默认任务
   */
  private initDefaultTasks(): any[] {
    // TODO: 从配置表加载默认荣誉任务
    return [
      {
        taskId: 1001,
        taskType: 1,
        currentProgress: 0,
        targetProgress: 10,
        status: HonorTaskStatus.IN_PROGRESS,
        startTime: Date.now(),
        completeTime: 0,
        claimTime: 0,
        rewards: { itemType: 'ITEM', resId: 2001, num: 1 },
      },
      {
        taskId: 1002,
        taskType: 2,
        currentProgress: 0,
        targetProgress: 5,
        status: HonorTaskStatus.IN_PROGRESS,
        startTime: Date.now(),
        completeTime: 0,
        claimTime: 0,
        rewards: { itemType: 'ITEM', resId: 2002, num: 1 },
      },
    ];
  }
}
