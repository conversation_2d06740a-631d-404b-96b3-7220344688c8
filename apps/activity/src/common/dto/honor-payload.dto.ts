/**
 * Honor模块的Payload DTO定义
 * 
 * 为honor.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsObject, Length, Min, Max } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 荣誉信息相关 ====================

/**
 * 获取荣誉墙信息Payload DTO
 * @MessagePattern('honor.getInfo')
 * 基于真实接口结构：{ uid: string; serverId: string; injectedContext?: InjectedContext }
 */
export class GetHonorInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;
}

// ==================== 2. 荣誉任务相关 ====================

/**
 * 领取荣誉任务奖励Payload DTO
 * @MessagePattern('honor.claimTaskReward')
 * 基于真实接口结构：{ uid: string; serverId: string; taskId: number; injectedContext?: InjectedContext }
 */
export class ClaimTaskRewardPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '任务ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '任务ID必须是数字' })
  @Min(1, { message: '任务ID不能小于1' })
  @Max(999999, { message: '任务ID不能大于999999' })
  taskId: number;
}

/**
 * 更新荣誉任务进度Payload DTO
 * @MessagePattern('honor.updateProgress')
 * 基于真实接口结构：{ uid: string; serverId: string; taskId: number; progress: number; injectedContext?: InjectedContext }
 */
export class UpdateProgressPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '任务ID', example: 1001 })
  @Expose()
  @IsNumber({}, { message: '任务ID必须是数字' })
  @Min(1, { message: '任务ID不能小于1' })
  @Max(999999, { message: '任务ID不能大于999999' })
  taskId: number;

  @ApiProperty({ description: '进度值', example: 50 })
  @Expose()
  @IsNumber({}, { message: '进度值必须是数字' })
  @Min(0, { message: '进度值不能小于0' })
  @Max(999999, { message: '进度值不能大于999999' })
  progress: number;
}

/**
 * 添加荣誉任务Payload DTO
 * @MessagePattern('honor.addTask')
 * 基于真实接口结构：{ uid: string; serverId: string; taskConfig: any; injectedContext?: InjectedContext }
 */
export class AddTaskPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ 
    description: '任务配置', 
    type: 'object',
    example: { taskId: 1001, taskType: 1, targetValue: 100, rewardItems: [] }
  })
  @Expose()
  @IsObject({ message: '任务配置必须是对象' })
  taskConfig: any;
}

// ==================== 3. 荣誉等级相关 ====================

/**
 * 领取荣誉等级奖励Payload DTO
 * @MessagePattern('honor.claimLevelReward')
 * 基于真实接口结构：{ uid: string; serverId: string; level: number; injectedContext?: InjectedContext }
 */
export class ClaimLevelRewardPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '荣誉等级', example: 5 })
  @Expose()
  @IsNumber({}, { message: '荣誉等级必须是数字' })
  @Min(1, { message: '荣誉等级不能小于1' })
  @Max(100, { message: '荣誉等级不能大于100' })
  level: number;
}

// ==================== 4. 荣誉触发相关 ====================

/**
 * 触发荣誉任务检查Payload DTO
 * @MessagePattern('honor.trigger')
 * 基于真实接口结构：{ uid: string; serverId: string; triggerType: number; param?: any; injectedContext?: InjectedContext }
 */
export class TriggerHonorTaskPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ 
    description: '触发类型', 
    example: 1,
    enum: [1, 2, 3, 4, 5],
    enumName: 'TriggerType'
  })
  @Expose()
  @IsNumber({}, { message: '触发类型必须是数字' })
  @Min(1, { message: '触发类型不能小于1' })
  @Max(100, { message: '触发类型不能大于100' })
  triggerType: number;

  @ApiPropertyOptional({ 
    description: '触发参数（可选）', 
    type: 'object',
    example: { value: 1, extraData: {} }
  })
  @Expose()
  @IsOptional()
  @IsObject({ message: '触发参数必须是对象' })
  param?: any;
}

// ==================== 5. 荣誉排行榜相关 ====================

/**
 * 获取荣誉排行榜Payload DTO
 * @MessagePattern('honor.getRanking')
 * 基于真实接口结构：{ limit?: number; injectedContext?: InjectedContext }
 */
export class GetHonorRankingPayloadDto extends BasePayloadDto {
  @ApiPropertyOptional({ description: '排行榜数量限制', example: 100, minimum: 1, maximum: 500 })
  @Expose()
  @IsOptional()
  @IsNumber({}, { message: '排行榜数量限制必须是数字' })
  @Min(1, { message: '排行榜数量限制不能小于1' })
  @Max(500, { message: '排行榜数量限制不能大于500' })
  limit?: number;
}
