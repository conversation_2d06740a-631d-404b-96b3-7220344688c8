/**
 * 任务系统相关的数据传输对象
 */

import { IsString, IsNumber, IsOptional, IsBoolean, IsArray, IsEnum, Min, Max, Length, ValidateNested } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { TaskStatus, TaskType } from '../schemas/task.schema';

// 更新任务进度DTO
export class UpdateTaskProgressDto {
  @ApiProperty({ description: '任务ID' })
  @IsNumber()
  taskId: number;

  @ApiProperty({ description: '进度值' })
  @IsNumber()
  @Min(0)
  progress: number;

  @ApiPropertyOptional({ description: '是否强制完成' })
  @IsOptional()
  @IsBoolean()
  forceComplete?: boolean;
}

// 领取任务奖励DTO
export class ClaimTaskRewardDto {
  @ApiProperty({ description: '任务ID' })
  @IsNumber()
  taskId: number;

  @ApiPropertyOptional({ description: '任务类型' })
  @IsOptional()
  @IsEnum(TaskType)
  taskType?: TaskType;
}

// 获取任务列表DTO
export class GetTaskListDto {
  @ApiProperty({ description: '玩家ID' })
  @IsString()
  characterId: string;

  @ApiPropertyOptional({ description: '任务类型过滤' })
  @IsOptional()
  @IsEnum(TaskType)
  taskType?: TaskType;

  @ApiPropertyOptional({ description: '任务状态过滤' })
  @IsOptional()
  @IsEnum(TaskStatus)
  status?: TaskStatus;

  @ApiPropertyOptional({ description: '是否只显示可领取的任务' })
  @IsOptional()
  @IsBoolean()
  claimableOnly?: boolean;

  @ApiPropertyOptional({ description: '是否包含已过期任务' })
  @IsOptional()
  @IsBoolean()
  includeExpired?: boolean;
}

// 刷新任务DTO
export class RefreshTaskDto {
  @ApiProperty({ description: '任务类型' })
  @IsEnum(TaskType)
  taskType: TaskType;

  @ApiPropertyOptional({ description: '是否强制刷新' })
  @IsOptional()
  @IsBoolean()
  forceRefresh?: boolean;
}

// 任务奖励响应DTO
export class TaskRewardDto {
  @ApiProperty({ description: '奖励类型' })
  type: string;

  @ApiProperty({ description: '物品ID' })
  itemId: number;

  @ApiProperty({ description: '数量' })
  quantity: number;

  @ApiProperty({ description: '奖励描述' })
  description: string;
}

// 任务进度响应DTO
export class TaskProgressDto {
  @ApiProperty({ description: '任务ID' })
  taskId: number;

  @ApiProperty({ description: '当前进度值' })
  currentValue: number;

  @ApiProperty({ description: '目标值' })
  targetValue: number;

  @ApiProperty({ description: '任务状态' })
  status: TaskStatus;

  @ApiProperty({ description: '开始时间' })
  startTime: number;

  @ApiProperty({ description: '完成时间' })
  completeTime: number;

  @ApiProperty({ description: '领取时间' })
  claimTime: number;

  @ApiProperty({ description: '任务奖励' })
  rewards: TaskRewardDto[];

  @ApiProperty({ description: '任务描述' })
  description: string;

  @ApiProperty({ description: '过期时间' })
  expireTime: number;

  @ApiProperty({ description: '是否可以领取奖励' })
  canClaim: boolean;

  @ApiProperty({ description: '进度百分比' })
  progressPercent: number;

  @ApiProperty({ description: '是否已过期' })
  isExpired: boolean;
}

// 任务列表响应DTO
export class TaskListDto {
  @ApiProperty({ description: '每日任务' })
  dailyTasks: TaskProgressDto[];

  @ApiProperty({ description: '每周任务' })
  weeklyTasks: TaskProgressDto[];

  @ApiProperty({ description: '成就任务' })
  achievementTasks: TaskProgressDto[];

  @ApiProperty({ description: '新手任务' })
  newbieTasks: TaskProgressDto[];

  @ApiProperty({ description: '活动任务' })
  eventTasks: TaskProgressDto[];

  @ApiProperty({ description: '主线任务' })
  mainTasks: TaskProgressDto[];

  @ApiProperty({ description: '待领取奖励数量' })
  pendingRewards: number;

  @ApiProperty({ description: '今日任务完成度' })
  dailyProgress: {
    completed: number;
    total: number;
    percent: number;
  };

  @ApiProperty({ description: '是否需要每日刷新' })
  needsDailyRefresh: boolean;

  @ApiProperty({ description: '是否需要每周刷新' })
  needsWeeklyRefresh: boolean;
}

// 任务统计DTO
export class TaskStatsDto {
  @ApiProperty({ description: '玩家ID' })
  @IsString()
  characterId: string;

  @ApiPropertyOptional({ description: '统计时间范围（天）' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(365)
  days?: number;

  @ApiPropertyOptional({ description: '任务类型过滤' })
  @IsOptional()
  @IsEnum(TaskType)
  taskType?: TaskType;
}

// 任务统计响应DTO
export class TaskStatsResponseDto {
  @ApiProperty({ description: '总完成任务数' })
  totalCompletedTasks: number;

  @ApiProperty({ description: '总领取奖励数' })
  totalClaimedRewards: number;

  @ApiProperty({ description: '今日完成任务数' })
  dailyCompletedCount: number;

  @ApiProperty({ description: '本周完成任务数' })
  weeklyCompletedCount: number;

  @ApiProperty({ description: '任务类型分布' })
  taskTypeDistribution: Record<string, number>;

  @ApiProperty({ description: '完成率统计' })
  completionRate: {
    daily: number;
    weekly: number;
    achievement: number;
    newbie: number;
    event: number;
    main: number;
  };

  @ApiProperty({ description: '奖励统计' })
  rewardStats: {
    totalRewards: number;
    rewardTypes: Record<string, number>;
  };
}

// 批量更新任务进度DTO
export class BatchUpdateTaskProgressDto {
  @ApiProperty({ description: '任务进度更新列表' })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateTaskProgressDto)
  updates: UpdateTaskProgressDto[];
}

// 添加新任务DTO
export class AddTaskDto {
  @ApiProperty({ description: '任务类型' })
  @IsEnum(TaskType)
  taskType: TaskType;

  @ApiProperty({ description: '任务ID' })
  @IsNumber()
  taskId: number;

  @ApiProperty({ description: '目标值' })
  @IsNumber()
  @Min(1)
  targetValue: number;

  @ApiProperty({ description: '任务描述' })
  @IsString()
  @Length(1, 200)
  description: string;

  @ApiProperty({ description: '任务奖励' })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TaskRewardDto)
  rewards: TaskRewardDto[];

  @ApiPropertyOptional({ description: '过期时间' })
  @IsOptional()
  @IsNumber()
  expireTime?: number;
}
