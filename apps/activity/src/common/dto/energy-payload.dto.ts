/**
 * Energy模块的Payload DTO定义
 * 
 * 为energy.controller.ts中的所有@MessagePattern接口生成对应的Payload DTO
 * 所有DTO都继承自BasePayloadDto，基于真实的接口数据结构
 * 包含详细的校验规则和@Expose()装饰器
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, Length, Min, Max } from 'class-validator';
import { Expose } from 'class-transformer';
import { BasePayloadDto } from '@libs/common/dto/base-payload.dto';

// ==================== 1. 精力信息相关 ====================

/**
 * 获取精力信息Payload DTO
 * @MessagePattern('energy.getInfo')
 * 基于真实接口结构：{ uid: string; serverId: string; injectedContext?: InjectedContext }
 */
export class GetEnergyInfoPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;
}

// ==================== 2. 精力领取相关 ====================

/**
 * 领取每日精力Payload DTO
 * @MessagePattern('energy.takeDaily')
 * 基于真实接口结构：{ uid: string; serverId: string; timeSlot: number; injectedContext?: InjectedContext }
 */
export class TakeDailyEnergyPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ 
    description: '时段', 
    example: 1,
    enum: [1, 2, 3, 4],
    enumName: 'TimeSlot'
  })
  @Expose()
  @IsNumber({}, { message: '时段必须是数字' })
  @Min(1, { message: '时段不能小于1' })
  @Max(4, { message: '时段不能大于4' })
  timeSlot: number; // 1=早上, 2=中午, 3=下午, 4=晚上
}

// ==================== 3. 精力消耗相关 ====================

/**
 * 消耗精力Payload DTO
 * @MessagePattern('energy.consume')
 * 基于真实接口结构：{ uid: string; serverId: string; amount: number; injectedContext?: InjectedContext }
 */
export class ConsumeEnergyPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '消耗数量', example: 10 })
  @Expose()
  @IsNumber({}, { message: '消耗数量必须是数字' })
  @Min(1, { message: '消耗数量不能小于1' })
  @Max(999, { message: '消耗数量不能大于999' })
  amount: number;
}

// ==================== 4. 精力奖励相关 ====================

/**
 * 领取精力消耗奖励Payload DTO
 * @MessagePattern('energy.claimReward')
 * 基于真实接口结构：{ uid: string; serverId: string; rewardIndex: number; injectedContext?: InjectedContext }
 */
export class ClaimConsumeRewardPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;

  @ApiProperty({ description: '奖励索引', example: 1 })
  @Expose()
  @IsNumber({}, { message: '奖励索引必须是数字' })
  @Min(0, { message: '奖励索引不能小于0' })
  @Max(99, { message: '奖励索引不能大于99' })
  rewardIndex: number;
}

// ==================== 5. 礼包购买相关 ====================

/**
 * 购买限时阵型重置礼包Payload DTO
 * @MessagePattern('energy.buyFormationGift')
 * 基于真实接口结构：{ uid: string; serverId: string; injectedContext?: InjectedContext }
 */
export class BuyTeamFormationGiftPayloadDto extends BasePayloadDto {
  @ApiProperty({ description: '用户ID', example: 'user_12345' })
  @Expose()
  @IsString({ message: '用户ID必须是字符串' })
  @Length(1, 50, { message: '用户ID长度必须在1-50个字符之间' })
  uid: string;

  @ApiProperty({ description: '服务器ID', example: 'server_001' })
  @Expose()
  @IsString({ message: '服务器ID必须是字符串' })
  @Length(1, 50, { message: '服务器ID长度必须在1-50个字符之间' })
  serverId: string;
}

// ==================== 6. 统计信息相关 ====================

/**
 * 获取精力统计信息Payload DTO
 * @MessagePattern('energy.getStats')
 * 基于真实接口结构：{ adminToken?: string; injectedContext?: InjectedContext }
 */
export class GetEnergyStatsPayloadDto extends BasePayloadDto {
  @ApiPropertyOptional({ description: '管理员令牌', example: 'admin_token_123' })
  @Expose()
  @IsOptional()
  @IsString({ message: '管理员令牌必须是字符串' })
  adminToken?: string;
}
