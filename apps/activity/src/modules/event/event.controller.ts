import { <PERSON>, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { EventService } from './event.service';

import { XResponse } from '@libs/common/types/result.type';

import {
  BuyBestFootballPayloadDto,
  BuySlotsPayloadDto,
  BuyTurntablePayloadDto,
  ClaimEventRewardPayloadDto,
  GetEventListPayloadDto,
  GetEventProgressPayloadDto,
  JoinEventPayloadDto,
  WeekDayEncorePayloadDto
} from "@activity/common/dto/event-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';

@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class EventController extends BaseController {
  constructor(private readonly eventService: EventService) {
    super('EventController');
  }

  /**
   * 获取活动列表
   */
  @MessagePattern('event.getList')
  async getEventList(@Payload() payload: GetEventListPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取活动列表: ${payload.characterId}`);
    const result = await this.eventService.getActivityList(payload.characterId);
    return this.fromResult(result);
  }

  /**
   * 参与活动
   */
  @MessagePattern('event.join')
  async joinEvent(@Payload() payload: JoinEventPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`参与活动: ${payload.characterId} -> ${payload.eventId}`);
    const result = await this.eventService.getActivityInfo(payload.characterId, payload.eventId);
    return this.fromResult(result);
  }

  /**
   * 领取活动奖励
   */
  @MessagePattern('event.claimReward')
  async claimEventReward(@Payload() payload: ClaimEventRewardPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`领取活动奖励: ${payload.characterId} -> ${payload.eventId}`);
    const result = await this.eventService.claimActivityReward(payload.characterId, payload.eventId, payload.rewardId);
    return this.fromResult(result);
  }

  /**
   * 获取活动进度
   */
  @MessagePattern('event.getProgress')
  async getEventProgress(@Payload() payload: GetEventProgressPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取活动进度: ${payload.characterId} -> ${payload.eventId}`);
    const result = await this.eventService.getActivityInfo(payload.characterId, payload.eventId);
    return this.fromResult(result);
  }

  /**
   * 最佳11人抽奖
   * @payload.index 抽奖类型 1=单抽, 2=十连抽
   * 基于old项目: Act.prototype.buyBestFootball
   */
  @MessagePattern('event.buyBestFootball')
  async buyBestFootball(@Payload() payload: BuyBestFootballPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`最佳11人抽奖: ${JSON.stringify(payload)}`);

    const result = await this.eventService.buyBestFootball(
      payload.characterId,
      payload.index
    );

    return this.fromResult(result);
  }

  /**
   * 老虎机抽奖
   * @payload.index 抽奖类型 1=单抽, 2=十连抽
   * 基于old项目: Act.prototype.buyTurntable
   */
  @MessagePattern('event.buyTurntable')
  async buyTurntable(@Payload() payload: BuyTurntablePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`老虎机抽奖: ${JSON.stringify(payload)}`);

    const result = await this.eventService.buyTurntable(
      payload.characterId,
      payload.frequencyType
    );

    return this.fromResult(result);
  }

  /**
   * 拉霸抽奖
   * @payload.frequencyType 抽奖类型 1=单抽, 2=十连抽
   * @payload.securityMoney 保底金额
   * 基于old项目: Act.prototype.buySlots
   */
  @MessagePattern('event.buySlots')
  async buySlots(@Payload() payload: BuySlotsPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`拉霸抽奖: ${JSON.stringify(payload)}`);

    const result = await this.eventService.buySlots(
      payload.characterId,
      payload.frequencyType,
      payload.securityMoney || 0
    );

    return this.fromResult(result);
  }

  /**
   * 周末返场抽奖
   * 基于old项目: Act.prototype.weekDayEncore
   */
  @MessagePattern('event.weekDayEncore')
  async weekDayEncore(@Payload() payload: WeekDayEncorePayloadDto): Promise<XResponse<any>> {
    this.logger.log(`周末返场抽奖: ${JSON.stringify(payload)}`);

    const result = await this.eventService.weekDayEncore(payload.characterId);

    return this.fromResult(result);
  }
}
