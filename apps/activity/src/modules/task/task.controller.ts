import { <PERSON>, Logger, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { TaskService } from './task.service';

import { TaskType } from '@activity/common/schemas/task.schema';
import { <PERSON><PERSON><PERSON>, CacheEvict, CachePut } from '@libs/redis';

import { XResponse } from '@libs/common/types/result.type';

import {
  AddTaskPayloadDto, BatchRefreshTasksPayloadDto,
  BatchUpdateActivityTasksPayloadDto,
  BatchUpdateTaskProgressPayloadDto, CheckTaskExistsPayloadDto, CheckTaskListFullPayloadDto,
  ClaimTaskRewardPayloadDto, DeleteTaskPayloadDto, GetDailyTasksPayloadDto, GetNewbieTasksPayloadDto,
  GetTaskLeaderboardPayloadDto,
  GetTaskListPayloadDto, GetTaskStatsPayloadDto,
  RefreshTaskPayloadDto, TriggerTaskPayloadDto,
  UpdateActivityTaskProgressPayloadDto,
  UpdateGoldCoachTaskProgressPayloadDto,
  UpdateTaskProgressPayloadDto
} from "@activity/common/dto/task-payload.dto";
import { BaseController, BasePayload } from '@libs/common/controller/base-controller';
import { StandardMicroserviceValidationPipe } from '@libs/common/pipes';

@Controller()
@UsePipes(StandardMicroserviceValidationPipe)
export class TaskController extends BaseController {
  constructor(private readonly taskService: TaskService) {
    super('TaskController');
  }

  // ==================== 任务列表管理 ====================

  /**
   * 获取任务列表
   */
  @MessagePattern('task.getList')
  @Cacheable({ 
    key: 'task:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getTaskList(@Payload() payload: GetTaskListPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取任务列表: ${payload.characterId}`);
    const result = await this.taskService.getTaskList(payload.characterId, payload);

    return this.fromResult(result);
  }

  /**
   * 更新任务进度
   */
  @MessagePattern('task.updateProgress')
  @CacheEvict({ 
    key: 'task:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async updateTaskProgress(@Payload() payload: UpdateTaskProgressPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`更新任务进度: ${payload.characterId}, 任务: ${payload.taskId}, 进度: ${payload.progress}`);
    const result = await this.taskService.updateTaskProgress(payload.characterId, payload);
    return this.fromResult(result);
  }

  /**
   * 批量更新任务进度
   */
  @MessagePattern('task.batchUpdateProgress')
  @CacheEvict({ 
    key: 'task:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async batchUpdateTaskProgress(@Payload() payload: BatchUpdateTaskProgressPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`批量更新任务进度: ${payload.characterId}, 数量: ${payload.updates.length}`);
    const result = await this.taskService.batchUpdateTaskProgress(payload.characterId, payload);
    return this.fromResult(result);
  }

  /**
   * 领取任务奖励
   */
  @MessagePattern('task.claimReward')
  @CacheEvict({ 
    key: 'task:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async claimTaskReward(@Payload() payload: ClaimTaskRewardPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`领取任务奖励: ${payload.characterId}, 任务: ${payload.taskId}`);
    const result = await this.taskService.claimTaskReward(payload);
    return this.fromResult(result);
  }

  // ==================== 任务刷新管理 ====================

  /**
   * 刷新任务
   */
  @MessagePattern('task.refresh')
  @CacheEvict({ 
    key: 'task:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async refreshTasks(@Payload() payload: RefreshTaskPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`刷新任务: ${payload.characterId}, 类型: ${payload.taskType}`);
    const result = await this.taskService.refreshTasks(payload.characterId, payload);
    return this.fromResult(result);
  }

  /**
   * 添加新任务
   */
  @MessagePattern('task.add')
  @CacheEvict({ 
    key: 'task:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async addTask(@Payload() payload: AddTaskPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`添加新任务: ${payload.characterId}, 任务: ${payload.taskId}, 类型: ${payload.taskType}`);
    const result = await this.taskService.addTask(payload.characterId, payload);
    return this.fromResult(result);
  }

  // ==================== 任务统计 ====================

  /**
   * 获取任务统计
   */
  @MessagePattern('task.getStats')
  @Cacheable({ 
    key: 'task:stats:#{payload.query.characterId}:#{payload.query.days}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 1800
  })
  async getTaskStats(@Payload() payload: GetTaskStatsPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取任务统计: ${payload.characterId}`);
    const stats = await this.taskService.getTaskStats(payload);
    return this.fromResult(stats);
  }

  /**
   * 获取任务排行榜
   */
  @MessagePattern('task.getLeaderboard')
  @Cacheable({ 
    key: 'task:leaderboard:#{payload.serverId}:#{payload.taskType}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 600
  })
  async getTaskLeaderboard(@Payload() payload: GetTaskLeaderboardPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取任务排行榜: 类型: ${payload.taskType}`);
    const result = await this.taskService.getTaskLeaderboard(payload.taskType, payload.limit);
    return this.fromResult(result);
  }

  // ==================== 管理接口 ====================

  /**
   * 批量刷新任务（定时任务用）
   */
  @MessagePattern('task.batchRefresh')
  async batchRefreshTasks(@Payload() payload: BatchRefreshTasksPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`批量刷新任务: 类型 ${payload.taskType}`);
    const result = await this.taskService.batchRefreshTasks(payload.taskType);
    return this.fromResult(result);
  }

  // ==================== 特殊任务类型 ====================

  /**
   * 获取每日任务
   */
  @MessagePattern('task.getDailyTasks')
  @Cacheable({ 
    key: 'task:daily:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getDailyTasks(@Payload() payload: GetDailyTasksPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取每日任务: ${payload.characterId}`);
    const result = await this.taskService.getTaskList(payload.characterId, {
      characterId: payload.characterId,
      taskType: TaskType.DAILY,
    });

    return this.fromResult(result);
  }

  /**
   * 获取新手任务
   */
  @MessagePattern('task.getNewbieTasks')
  @Cacheable({ 
    key: 'task:newbie:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 600
  })
  async getNewbieTasks(@Payload() payload: GetNewbieTasksPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`获取新手任务: ${payload.characterId}`);
    const result = await this.taskService.getTaskList(payload.characterId, {
      characterId: payload.characterId,
      taskType: TaskType.NEWBIE,
    });

    return this.fromResult(result);
  }

  // ==================== 缺失功能补充 ====================

  /**
   * 触发任务进度更新
   * 对应old项目中最核心的triggerTask方法
   */
  @MessagePattern('task.trigger')
  @CacheEvict({
    key: 'task:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async triggerTask(@Payload() payload: TriggerTaskPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`触发任务: ${payload.characterId}, 类型: ${payload.triggerType}, 参数: ${payload.arg1}, ${payload.arg2}, ${payload.arg3}`);
    const result = await this.taskService.triggerTask(
      payload.characterId,
      payload.triggerType,
      payload.arg1,
      payload.arg2,
      payload.arg3,
      payload.arg4
    );

    return this.fromResult(result);
  }

  /**
   * 删除任务
   */
  @MessagePattern('task.delete')
  @CacheEvict({
    key: 'task:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async deleteTask(@Payload() payload: DeleteTaskPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`删除任务: ${payload.characterId}, 类型: ${payload.taskType}, 任务: ${payload.taskId}`);
    const result = await this.taskService.deleteTask(
      payload.characterId,
      payload.taskType,
      payload.taskId
    );
    
    return this.fromResult(result);
  }

  /**
   * 检查任务列表是否已满
   */
  @MessagePattern('task.checkFull')
  async checkTaskListFull(@Payload() payload: CheckTaskListFullPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`检查任务列表容量: ${payload.characterId}, 类型: ${payload.taskType}`);
    const result = await this.taskService.checkTaskListFull(
      payload.characterId,
      payload.taskType
    );

    return this.fromResult(result);
  }

  /**
   * 检查任务是否存在
   */
  @MessagePattern('task.checkExists')
  async checkTaskExists(@Payload() payload: CheckTaskExistsPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`检查任务是否存在: ${payload.characterId}, 任务: ${payload.taskId}`);
    const result = await this.taskService.checkTaskExists(
      payload.characterId,
      payload.taskId
    );
    return this.fromResult(result);
  }

  // ==================== 活动任务功能补充 ====================

  /**
   * 更新活动任务进度
   * 对应old项目act.js中的updateTaskProgress方法
   */
  @MessagePattern('task.updateActivityProgress')
  @CacheEvict({
    key: 'task:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async updateActivityTaskProgress(@Payload() payload: UpdateActivityTaskProgressPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`更新活动任务进度: ${payload.characterId}, 活动: ${payload.activityId}, 任务: ${payload.taskId}, 进度: ${payload.progress}`);
    const result = await this.taskService.updateActivityTaskProgress(
      payload.characterId,
      payload.activityId,
      payload.taskId,
      payload.progress
    );

    return this.fromResult(result);
  }

  /**
   * 更新金牌教练任务进度
   * 对应old项目act.js中的updateGoldCoachTaskProgress方法
   */
  @MessagePattern('task.updateGoldCoachProgress')
  @CacheEvict({
    key: 'task:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async updateGoldCoachTaskProgress(@Payload() payload: UpdateGoldCoachTaskProgressPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`更新金牌教练任务进度: ${payload.characterId}, 行为: ${payload.coachAction}`);
    const result = await this.taskService.updateGoldCoachTaskProgress(
      payload.characterId,
      payload.coachAction,
      payload.param
    );

    return this.fromResult(result);
  }

  /**
   * 批量更新活动任务
   */
  @MessagePattern('task.batchUpdateActivity')
  @CacheEvict({
    key: 'task:list:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async batchUpdateActivityTasks(@Payload() payload: BatchUpdateActivityTasksPayloadDto): Promise<XResponse<any>> {
    this.logger.log(`批量更新活动任务: ${payload.characterId}, 更新数量: ${payload.updates.length}`);
    const result = await this.taskService.batchUpdateActivityTasks(
      payload.characterId,
      payload.updates
    );

    return this.fromResult(result);
  }
}
