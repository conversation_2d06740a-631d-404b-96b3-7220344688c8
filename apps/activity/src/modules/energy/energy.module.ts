import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { EnergyController } from './energy.controller';
import { EnergyService } from './energy.service';
import { EnergyRepository } from '@activity/common/repositories/energy.repository';
import { Energy, EnergySchema } from '@activity/common/schemas/energy.schema';

/**
 * 精力系统模块
 * 对应old项目中的everyDayEnergy.js和commonActivity.js
 */
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Energy.name, schema: EnergySchema },
    ]),
  ],
  controllers: [EnergyController],
  providers: [EnergyService, EnergyRepository],
  exports: [EnergyService, EnergyRepository],
})
export class EnergyModule {}
