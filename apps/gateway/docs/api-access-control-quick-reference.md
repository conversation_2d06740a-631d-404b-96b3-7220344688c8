# API访问控制快速参考指南

## 🚀 快速开始

### 选择方案

| 方案 | 优势 | 适用场景 |
|------|------|----------|
| **方案1** | 语义最清晰，不易遗漏 | 推荐用于新项目或安全要求高的项目 |
| **方案2A** | 利用原生功能，框架兼容性好 | 适合希望使用NestJS原生功能的团队 |
| **方案2B** | 功能最丰富，扩展性强 | 适合需要复杂权限控制的企业级项目 |

---

## 📋 方案1：自定义装饰器（推荐）

### 快速实现

```typescript
// 1. 创建装饰器
// libs/common/src/decorators/secure-message-pattern.decorator.ts
import { MessagePattern } from '@nestjs/microservices';
import { SetMetadata } from '@nestjs/common';

const INTERNAL_APIS = new Set<string>();

export const InternalMessagePattern = (pattern: string) => {
  INTERNAL_APIS.add(pattern);
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    MessagePattern(pattern)(target, propertyKey, descriptor);
    SetMetadata('internal-api', true)(target, propertyKey, descriptor);
  };
};

export const PublicMessagePattern = MessagePattern;
export const isInternalApi = (pattern: string) => INTERNAL_APIS.has(pattern);
```

```typescript
// 2. 创建守卫
// apps/gateway/src/guards/internal-api.guard.ts
import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { isInternalApi } from '@common/decorators/secure-message-pattern.decorator';

@Injectable()
export class InternalApiGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToWs().getData();
    const pattern = `${request.service}.${request.action}`;
    
    if (isInternalApi(pattern)) {
      throw new ForbiddenException(`内部API ${pattern} 禁止客户端访问`);
    }
    return true;
  }
}
```

```typescript
// 3. 应用守卫
// apps/gateway/src/app.module.ts
import { APP_GUARD } from '@nestjs/core';
import { InternalApiGuard } from './guards/internal-api.guard';

@Module({
  providers: [
    { provide: APP_GUARD, useClass: InternalApiGuard },
  ],
})
export class AppModule {}
```

### 使用方式

```typescript
// apps/character/src/modules/character/character.controller.ts
import { PublicMessagePattern, InternalMessagePattern } from '@common/decorators';

@Controller()
export class CharacterController {
  
  // ✅ 客户端可访问
  @PublicMessagePattern('character.getInfo')
  @PublicMessagePattern('character.getList')
  @PublicMessagePattern('character.create')
  
  // 🔒 仅内部调用
  @InternalMessagePattern('character.currency.add')
  @InternalMessagePattern('character.currency.subtract')
  @InternalMessagePattern('character.addResource')
}
```

---

## 🏷️ 方案2A：extras参数

### 快速实现

```typescript
// libs/common/src/decorators/extras-access-control.decorator.ts
import { MessagePattern } from '@nestjs/microservices';

export enum ApiAccess {
  PUBLIC = 'public',
  INTERNAL = 'internal'
}

const INTERNAL_APIS = new Set<string>();

export const SecureMessagePattern = (pattern: string, access: ApiAccess = ApiAccess.PUBLIC) => {
  if (access === ApiAccess.INTERNAL) {
    INTERNAL_APIS.add(pattern);
  }
  
  const extras = { access };
  return MessagePattern(pattern, extras);
};

export const PublicAPI = (pattern: string) => SecureMessagePattern(pattern, ApiAccess.PUBLIC);
export const InternalAPI = (pattern: string) => SecureMessagePattern(pattern, ApiAccess.INTERNAL);
export const isInternalApi = (pattern: string) => INTERNAL_APIS.has(pattern);
```

### 使用方式

```typescript
import { SecureMessagePattern, PublicAPI, InternalAPI, ApiAccess } from '@common/decorators';

@Controller()
export class CharacterController {
  
  // 方式1：使用SecureMessagePattern
  @SecureMessagePattern('character.getInfo', ApiAccess.PUBLIC)
  
  // 方式2：使用便捷装饰器
  @PublicAPI('character.getList')
  @InternalAPI('character.currency.add')
}
```

---

## 🔧 方案2B：元数据装饰器

### 快速实现

```typescript
// libs/common/src/decorators/metadata-access-control.decorator.ts
import { SetMetadata } from '@nestjs/common';

export const API_ACCESS_KEY = 'api-access';
export enum ApiAccess {
  PUBLIC = 'public',
  INTERNAL = 'internal'
}

export const ApiAccessControl = (access: ApiAccess) => SetMetadata(API_ACCESS_KEY, access);
export const PublicApi = () => ApiAccessControl(ApiAccess.PUBLIC);
export const InternalApi = () => ApiAccessControl(ApiAccess.INTERNAL);
```

### 使用方式

```typescript
import { MessagePattern } from '@nestjs/microservices';
import { PublicApi, InternalApi } from '@common/decorators';

@Controller()
export class CharacterController {
  
  @MessagePattern('character.getInfo')
  @PublicApi()
  async getCharacterInfo() { /* ... */ }
  
  @MessagePattern('character.currency.add')
  @InternalApi()
  async addCurrency() { /* ... */ }
}
```

---

## 🛡️ 安全最佳实践

### 1. API分类原则

```typescript
// ✅ 客户端可访问的API
'character.getInfo'          // 查询自己的信息
'character.getList'          // 查询自己的角色列表
'character.create'           // 创建角色
'character.login'            // 角色登录

// 🔒 内部API（绝对不能客户端访问）
'character.currency.add'     // 增加货币
'character.currency.subtract' // 扣除货币
'character.addResource'      // 添加资源
'character.levelup'          // 角色升级
'character.updateBuff'       // 更新buff
```

### 2. 命名约定

```typescript
// 推荐的API命名模式
'service.entity.action'      // 如：character.currency.add
'service.action'             // 如：character.getInfo

// 内部API建议包含明确标识
'service.internal.action'    // 如：character.internal.resetData
'service.admin.action'       // 如：character.admin.banUser
```

### 3. 错误处理

```typescript
// 统一的错误响应格式
{
  "code": 403,
  "message": "内部API character.currency.add 禁止客户端访问",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "character.currency.add"
}
```

---

## 🔍 调试和测试

### 查看已注册的API

```typescript
// 在应用启动后查看
import { getInternalApis } from '@common/decorators';
console.log('内部APIs:', getInternalApis());
```

### 测试脚本

```typescript
// apps/gateway/scripts/test-api-security.ts
import { io } from 'socket.io-client';

const socket = io('ws://localhost:3001');

// 测试公开API（应该成功）
socket.emit('microservice_call', {
  service: 'character',
  action: 'getInfo',
  data: { characterId: 'test_123' }
});

// 测试内部API（应该被拦截）
socket.emit('microservice_call', {
  service: 'character',
  action: 'currency.add',
  data: { characterId: 'test_123', amount: 999999 }
});
```

### 日志监控

```bash
# 启动时应该看到
🔒 注册内部API: character.currency.add
🔒 注册内部API: character.currency.subtract

# 拦截时应该看到
🚨 拦截内部API调用: character.currency.add, 用户: user_123
```

---

## ⚡ 性能优化

### 1. 缓存优化

```typescript
const API_CACHE = new Map<string, boolean>();

export const isInternalApi = (pattern: string): boolean => {
  if (API_CACHE.has(pattern)) {
    return API_CACHE.get(pattern)!;
  }
  
  const result = INTERNAL_APIS.has(pattern);
  API_CACHE.set(pattern, result);
  return result;
};
```

### 2. 早期拦截

```typescript
// 在网关的最早阶段进行拦截
@Injectable()
export class EarlySecurityInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToWs().getData();
    const pattern = `${request.service}.${request.action}`;
    
    if (isInternalApi(pattern)) {
      throw new ForbiddenException('访问被拒绝');
    }
    
    return next.handle();
  }
}
```

---

## 🚨 常见问题

### Q: 微服务间调用被误拦截？

```typescript
// A: 在守卫中添加服务间调用检测
canActivate(context: ExecutionContext): boolean {
  const request = context.switchToWs().getData();
  
  // 检查服务间调用标识
  if (request.serviceAuth && this.validateServiceAuth(request.serviceAuth)) {
    return true; // 允许服务间调用
  }
  
  // 其他拦截逻辑...
}
```

### Q: 如何添加新的内部API？

```typescript
// A: 只需要更改装饰器即可
// 从
@PublicMessagePattern('character.newFeature')

// 改为
@InternalMessagePattern('character.newFeature')
```

### Q: 如何临时开放内部API用于调试？

```typescript
// A: 使用环境变量控制
const isDevelopment = process.env.NODE_ENV === 'development';

export const DebugMessagePattern = (pattern: string) => {
  return isDevelopment 
    ? PublicMessagePattern(pattern)
    : InternalMessagePattern(pattern);
};
```

---

## 📝 检查清单

### 实施前检查

- [ ] 确定使用哪种方案
- [ ] 创建装饰器文件
- [ ] 创建安全守卫
- [ ] 应用到网关模块

### 实施后检查

- [ ] 所有敏感API已标记为内部API
- [ ] 客户端无法调用内部API
- [ ] 微服务间调用正常
- [ ] 日志记录正常
- [ ] 性能影响可接受

### 代码审查检查

- [ ] 新API有明确的访问级别标记
- [ ] 敏感操作使用内部API
- [ ] 公开API有适当的权限验证
- [ ] 错误处理统一规范

---

## 🎯 总结

选择合适的方案，按照快速实现步骤操作，遵循安全最佳实践，就能有效防止客户端直接调用敏感内部API，保护系统安全。

**推荐顺序**：方案1 > 方案2A > 方案2B
