# 通用推送服务使用指南

## 概述

Gateway提供了完全业务无关的通用推送服务，任何业务模块都可以通过标准协议轻松实现实时推送功能。

## 架构设计理念

### 核心原则
- **Gateway只负责推送，不处理业务逻辑**
- **业务服务准备完整的推送数据**
- **统一的推送协议和数据格式**
- **完全解耦的微服务架构**

### 核心组件

1. **WebSocketGateway** - 集成业务推送功能
   - 直接集成业务推送处理，参照GlobalMessaging模式
   - 订阅统一的推送频道 `gateway:push`
   - 支持单用户、多用户、游戏房间推送
   - 完全业务无关，代码精简

2. **PushClientService** - 推送客户端工具
   - 为业务服务提供统一的推送接口
   - 将推送请求发送到Gateway
   - 支持业务类型标识

3. **GlobalMessagingModule** - 系统级消息服务
   - 处理系统公告、维护通知、活动通知
   - 跨服务器消息广播
   - 消息持久化和统计
   - 管理员发布的官方消息

## 为新业务添加推送支持

### 超简单！只需2步：

### 步骤1：在业务服务中注入PushClientService

```typescript
// 在业务服务中注入PushClientService
import { PushClientService } from '@libs/common/push/push-client.service';

constructor(
  private readonly pushClientService: PushClientService,
  // 其他依赖...
) {}
```

### 步骤2：在业务逻辑中发送推送

```typescript
// 任务完成推送
async completeTask(userId: string, taskId: string): Promise<XResult<any>> {
  // 执行业务逻辑...

  // 发送任务完成推送（业务服务准备好完整数据）
  await this.pushClientService.sendTaskCompleteNotification(userId, {
    taskId,
    taskName: 'Daily Login',
    rewards: ['coins:100', 'exp:50'],
    experience: 50
  });

  return XResultUtils.ok(result);
}

// 邮件推送
async sendMail(receiverId: string, mailInfo: any): Promise<XResult<any>> {
  // 执行业务逻辑...

  // 发送邮件推送
  await this.pushClientService.sendMailNotification(receiverId, {
    mailUid: mailInfo.uid,
    title: mailInfo.title,
    hasAttachment: mailInfo.hasAttachment
  });

  return XResultUtils.ok(result);
}

// 自定义推送
async customNotification(userId: string): Promise<void> {
  await this.pushClientService.notifyUser(userId, 'custom_event', {
    type: 'custom_notification',
    data: { /* 任意数据 */ },
    timestamp: new Date()
  });
}
```

**就这么简单！** Gateway会自动处理推送，无需任何额外配置。

## 推送协议规范

### 标准推送数据格式
```typescript
interface PushNotification {
  pushType: 'user' | 'users' | 'room';  // 推送类型
  targetUserId?: string;      // 单用户推送目标
  targetUserIds?: string[];   // 多用户推送目标
  roomId?: string;           // 游戏房间推送目标（比赛、俱乐部、私聊房间）
  eventName: string;         // WebSocket事件名称
  payload: any;              // 推送给客户端的完整数据
  businessType?: string;     // 业务类型标识（如'social', 'match', 'task'）
  options?: {                // 推送选项
    requireAck?: boolean;
    priority?: 'low' | 'normal' | 'high';
    expireAt?: number;
  };
}
```

### 职责分工

#### WebSocketGateway（用户数据推送）
- ✅ 邮件到达通知
- ✅ 好友申请通知
- ✅ 私聊消息推送
- ✅ 任务完成通知
- ✅ 比赛房间消息
- ✅ 俱乐部房间消息

#### GlobalMessagingModule（系统级消息）
- ✅ 系统公告广播
- ✅ 维护通知
- ✅ 活动开始/结束通知
- ✅ 紧急警告
- ✅ 跨服务器消息

### WebSocket事件命名规范
- `new_mail` - 新邮件通知
- `friend_request` - 好友申请
- `private_message` - 私聊消息
- `task_complete` - 任务完成
- `achievement_unlock` - 成就解锁
- `match_start` - 比赛开始
- `system_announcement` - 系统公告

## 推送方法

### 基础推送方法
```typescript
// 推送给单个用户
await pushClientService.pushToUser(userId, {
  eventName: 'custom_event',
  payload: { /* 数据 */ }
});

// 推送给多个用户
await pushClientService.pushToUsers(userIds, {
  eventName: 'custom_event',
  payload: { /* 数据 */ }
});

// 推送给游戏房间（比赛、俱乐部、私聊房间）
await pushClientService.pushToRoom(roomId, {
  eventName: 'room_event',
  payload: { /* 数据 */ },
  businessType: 'match'  // 标识业务类型
});
```

### 便捷方法
```typescript
// 简单通知（推荐使用）
await pushClientService.notifyUser(userId, 'event_name', data, 'social');
await pushClientService.notifyUsers(userIds, 'event_name', data, 'task');
await pushClientService.notifyGameRoom(roomId, 'event_name', data, 'match');
```

## 最佳实践

1. **业务数据准备**：业务服务完全准备好推送数据，Gateway只负责推送
2. **错误处理**：推送失败不应影响业务逻辑执行
3. **性能考虑**：避免频繁推送，合并相似事件
4. **数据最小化**：只推送必要的数据，减少网络开销
5. **用户体验**：提供有意义的推送内容，避免垃圾通知
6. **完全解耦**：业务服务和Gateway通过标准协议通信，互不依赖

## 架构优势

### 1. 完全解耦
- Gateway不包含任何业务逻辑
- 业务服务独立管理推送数据
- 标准化的推送协议

### 2. 极简接入
- 新业务只需注入PushClientService
- 2行代码即可实现推送
- 无需修改Gateway代码

### 3. 高性能
- 基于Redis的高性能架构
- 支持跨实例推送
- 异步处理不阻塞业务

### 4. 易维护
- 清晰的职责分离
- 标准化的接口设计
- 完善的日志和监控

## 调试和监控

### 查看推送服务状态
```typescript
// 推送功能已集成到WebSocketGateway中
// 可通过Gateway日志查看推送状态
```

### 日志监控
- 推送请求：业务服务日志 `📤 Sent push request`
- 推送接收：Gateway日志 `📨 Received push notification`
- 推送执行：Gateway日志 `📤 Pushed to user/room`
