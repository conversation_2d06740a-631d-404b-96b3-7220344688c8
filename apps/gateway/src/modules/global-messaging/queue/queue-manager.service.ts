import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

// 核心服务
import { RedisService } from '@libs/redis';

// 接口定义
import { BaseGlobalMessage, MessagePriority } from '../interfaces/global-message.interface';

/**
 * 队列管理服务
 * 负责全服消息的队列管理、优先级处理和消息分发
 * 
 * 核心功能：
 * 1. 优先级队列：根据消息优先级进行排队
 * 2. 消息入队：将消息添加到适当的队列
 * 3. 消息出队：按优先级顺序取出消息
 * 4. 队列监控：监控队列状态和性能
 * 5. 队列清理：清理过期和无效的消息
 */
@Injectable()
export class QueueManagerService {
  private readonly logger = new Logger(QueueManagerService.name);
  private readonly queuePrefix = 'global_messages:priority';

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
  ) {}

  /**
   * 将消息加入队列
   */
  async enqueue(message: BaseGlobalMessage): Promise<void> {
    this.logger.debug(`📥 Enqueuing message: ${message.id}, priority: ${message.priority}`);

    try {
      const queueKey = this.getQueueKey(message.priority);
      const score = this.calculateScore(message);
      
      await this.redisService.zadd(
        queueKey,
        score,
        JSON.stringify(message),
        'global'
      );

      // 更新队列统计
      await this.updateQueueStats('enqueue', message.priority);

      this.logger.debug(`✅ Message enqueued successfully: ${message.id}`);

    } catch (error) {
      this.logger.error(`❌ Failed to enqueue message: ${message.id}`, error);
      throw error;
    }
  }

  /**
   * 从队列中取出消息
   */
  async dequeue(priority?: MessagePriority): Promise<BaseGlobalMessage | null> {
    try {
      // 如果指定了优先级，只从该优先级队列取消息
      if (priority) {
        return await this.dequeueFromPriority(priority);
      }

      // 按优先级顺序取消息（从高到低）
      const priorities = [
        MessagePriority.EMERGENCY,
        MessagePriority.URGENT,
        MessagePriority.HIGH,
        MessagePriority.MEDIUM,
        MessagePriority.LOW,
      ];

      for (const p of priorities) {
        const message = await this.dequeueFromPriority(p);
        if (message) {
          return message;
        }
      }

      return null;

    } catch (error) {
      this.logger.error('❌ Failed to dequeue message', error);
      return null;
    }
  }

  /**
   * 批量出队消息
   */
  async dequeueBatch(count: number, priority?: MessagePriority): Promise<BaseGlobalMessage[]> {
    this.logger.debug(`📤 Dequeuing batch: ${count} messages, priority: ${priority || 'all'}`);

    try {
      const messages: BaseGlobalMessage[] = [];

      for (let i = 0; i < count; i++) {
        const message = await this.dequeue(priority);
        if (message) {
          messages.push(message);
        } else {
          break; // 没有更多消息
        }
      }

      this.logger.debug(`✅ Batch dequeue completed: ${messages.length} messages`);
      return messages;

    } catch (error) {
      this.logger.error('❌ Failed to dequeue batch', error);
      return [];
    }
  }

  /**
   * 获取队列大小
   */
  async getQueueSize(priority?: MessagePriority): Promise<number> {
    try {
      if (priority) {
        const queueKey = this.getQueueKey(priority);
        return await this.redisService.zcard(queueKey, 'global');
      }

      // 获取所有优先级队列的总大小
      let totalSize = 0;
      const priorities = Object.values(MessagePriority);
      
      for (const p of priorities) {
        if (typeof p === 'number') {
          const queueKey = this.getQueueKey(p);
          const size = await this.redisService.zcard(queueKey, 'global');
          totalSize += size;
        }
      }

      return totalSize;

    } catch (error) {
      this.logger.error('❌ Failed to get queue size', error);
      return 0;
    }
  }

  /**
   * 查看队列中的消息（不移除）
   */
  async peekMessages(count: number = 10, priority?: MessagePriority): Promise<BaseGlobalMessage[]> {
    try {
      const messages: BaseGlobalMessage[] = [];

      if (priority) {
        const queueKey = this.getQueueKey(priority);
        const messageData = await this.redisService.zrange(queueKey, 0, count - 1, 'global');
        
        for (const data of messageData) {
          try {
            messages.push(JSON.parse(data as string));
          } catch (parseError) {
            this.logger.error('❌ Failed to parse message data', parseError);
          }
        }
      } else {
        // 从所有优先级队列中获取消息
        const priorities = [
          MessagePriority.EMERGENCY,
          MessagePriority.URGENT,
          MessagePriority.HIGH,
          MessagePriority.MEDIUM,
          MessagePriority.LOW,
        ];

        for (const p of priorities) {
          if (messages.length >= count) break;
          
          const remaining = count - messages.length;
          const queueKey = this.getQueueKey(p);
          const messageData = await this.redisService.zrange(queueKey, 0, remaining - 1, 'global');
          
          for (const data of messageData) {
            try {
              messages.push(JSON.parse(data as string));
            } catch (parseError) {
              this.logger.error('❌ Failed to parse message data', parseError);
            }
          }
        }
      }

      return messages;

    } catch (error) {
      this.logger.error('❌ Failed to peek messages', error);
      return [];
    }
  }

  /**
   * 移除指定消息
   */
  async removeMessage(messageId: string): Promise<boolean> {
    this.logger.debug(`🗑️ Removing message from queue: ${messageId}`);

    try {
      const priorities = Object.values(MessagePriority);
      
      for (const priority of priorities) {
        if (typeof priority === 'number') {
          const queueKey = this.getQueueKey(priority);
          const messages = await this.redisService.zrange(queueKey, 0, -1, 'global');
          
          for (const messageData of messages) {
            try {
              const message = JSON.parse(messageData as string);
              if (message.id === messageId) {
                await this.redisService.zrem(queueKey, messageData as string, 'global');
                await this.updateQueueStats('remove', priority);
                this.logger.debug(`✅ Message removed from queue: ${messageId}`);
                return true;
              }
            } catch (parseError) {
              this.logger.error('❌ Failed to parse message data during removal', parseError);
            }
          }
        }
      }

      this.logger.warn(`⚠️ Message not found in queue: ${messageId}`);
      return false;

    } catch (error) {
      this.logger.error(`❌ Failed to remove message: ${messageId}`, error);
      return false;
    }
  }

  /**
   * 清空队列
   */
  async clearQueue(priority?: MessagePriority): Promise<number> {
    this.logger.log(`🧹 Clearing queue, priority: ${priority || 'all'}`);

    try {
      let clearedCount = 0;

      if (priority) {
        const queueKey = this.getQueueKey(priority);
        const size = await this.redisService.zcard(queueKey, 'global');
        await this.redisService.del(queueKey, 'global');
        clearedCount = size;
      } else {
        const priorities = Object.values(MessagePriority);
        
        for (const p of priorities) {
          if (typeof p === 'number') {
            const queueKey = this.getQueueKey(p);
            const size = await this.redisService.zcard(queueKey, 'global');
            await this.redisService.del(queueKey, 'global');
            clearedCount += size;
          }
        }
      }

      this.logger.log(`✅ Queue cleared: ${clearedCount} messages removed`);
      return clearedCount;

    } catch (error) {
      this.logger.error('❌ Failed to clear queue', error);
      return 0;
    }
  }

  /**
   * 获取队列统计信息
   */
  async getQueueStats(): Promise<any> {
    try {
      const stats = {
        totalMessages: 0,
        priorityBreakdown: {},
        oldestMessage: null,
        newestMessage: null,
        avgWaitTime: 0,
      };

      const priorities = Object.values(MessagePriority);
      
      for (const priority of priorities) {
        if (typeof priority === 'number') {
          const queueKey = this.getQueueKey(priority);
          const size = await this.redisService.zcard(queueKey, 'global');
          
          stats.priorityBreakdown[priority] = size;
          stats.totalMessages += size;

          // 获取最旧和最新的消息时间
          if (size > 0) {
            const oldest = await this.redisService.zrange(queueKey, 0, 0, 'WITHSCORES', 'global');
            const newest = await this.redisService.zrange(queueKey, -1, -1, 'WITHSCORES', 'global');
            
            if (oldest.length > 0) {
              const oldestTime = new Date(parseInt(oldest[1] as string));
              if (!stats.oldestMessage || oldestTime < stats.oldestMessage) {
                stats.oldestMessage = oldestTime;
              }
            }
            
            if (newest.length > 0) {
              const newestTime = new Date(parseInt(newest[1] as string));
              if (!stats.newestMessage || newestTime > stats.newestMessage) {
                stats.newestMessage = newestTime;
              }
            }
          }
        }
      }

      // 计算平均等待时间
      if (stats.oldestMessage && stats.newestMessage) {
        stats.avgWaitTime = (stats.newestMessage.getTime() - stats.oldestMessage.getTime()) / stats.totalMessages;
      }

      return stats;

    } catch (error) {
      this.logger.error('❌ Failed to get queue stats', error);
      return null;
    }
  }

  /**
   * 从指定优先级队列中取出消息
   */
  private async dequeueFromPriority(priority: MessagePriority): Promise<BaseGlobalMessage | null> {
    const queueKey = this.getQueueKey(priority);
    
    try {
      // 使用ZPOPMIN获取分数最小的消息（最早的消息）
      const result = await this.redisService.zpopmin(queueKey, 1, 'global');
      
      if (result && result.length >= 2) {
        const messageData = result[0];
        const message = JSON.parse(messageData as string);
        
        // 更新队列统计
        await this.updateQueueStats('dequeue', priority);
        
        this.logger.debug(`📤 Message dequeued: ${message.id}, priority: ${priority}`);
        return message;
      }

      return null;

    } catch (error) {
      this.logger.error(`❌ Failed to dequeue from priority ${priority}`, error);
      return null;
    }
  }

  /**
   * 获取队列键名
   */
  private getQueueKey(priority: MessagePriority): string {
    return `${this.queuePrefix}:${priority}`;
  }

  /**
   * 计算消息分数（用于排序）
   */
  private calculateScore(message: BaseGlobalMessage): number {
    // 使用发布时间作为基础分数
    let score = message.publishAt.getTime();
    
    // 根据优先级调整分数（优先级越高，分数越小，越早被处理）
    const priorityOffset = (MessagePriority.EMERGENCY - message.priority) * 1000000;
    score -= priorityOffset;
    
    return score;
  }

  /**
   * 更新队列统计
   */
  private async updateQueueStats(operation: string, priority: MessagePriority): Promise<void> {
    const statsKey = `queue_stats:${priority}`;
    
    try {
      const stats = {
        enqueued: 0,
        dequeued: 0,
        removed: 0,
        lastOperation: new Date(),
      };

      // 获取现有统计
      const existingStatsData = await this.redisService.get(statsKey, 'global');
      if (existingStatsData) {
        Object.assign(stats, JSON.parse(existingStatsData as string));
      }

      // 更新统计
      switch (operation) {
        case 'enqueue':
          stats.enqueued += 1;
          break;
        case 'dequeue':
          stats.dequeued += 1;
          break;
        case 'remove':
          stats.removed += 1;
          break;
      }

      stats.lastOperation = new Date();

      await this.redisService.set(statsKey, JSON.stringify(stats), 3600 * 24 * 7, 'global');

    } catch (error) {
      this.logger.error('❌ Failed to update queue stats', error);
    }
  }
}
