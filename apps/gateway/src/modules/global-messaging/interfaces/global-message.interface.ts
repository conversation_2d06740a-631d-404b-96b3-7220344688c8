/**
 * 全服消息类型枚举
 */
export enum GlobalMessageType {
  SYSTEM_ANNOUNCEMENT = 'system_announcement',
  MAINTENANCE_NOTICE = 'maintenance_notice',
  EVENT_NOTIFICATION = 'event_notification',
  EMERGENCY_ALERT = 'emergency_alert',
  CROSS_SERVER_EVENT = 'cross_server_event',
  MARKETING_MESSAGE = 'marketing_message',
}

/**
 * 消息优先级枚举
 */
export enum MessagePriority {
  LOW = 1,
  MEDIUM = 2,
  HIGH = 3,
  URGENT = 4,
  EMERGENCY = 5,
}

/**
 * 消息目标定义
 */
export interface MessageTarget {
  scope: 'global' | 'servers' | 'users' | 'conditions';
  servers?: string[];                    // 指定区服列表
  users?: string[];                      // 指定用户列表
  conditions?: TargetCondition[];        // 条件筛选
}

/**
 * 目标条件定义
 */
export interface TargetCondition {
  field: string;                         // 筛选字段
  operator: 'eq' | 'ne' | 'gt' | 'lt' | 'in' | 'nin';
  value: any;                            // 筛选值
}

/**
 * 基础全服消息接口
 */
export interface BaseGlobalMessage {
  id: string;                            // 消息唯一ID
  type: GlobalMessageType;               // 消息类型
  priority: MessagePriority;             // 消息优先级
  title: string;                         // 消息标题
  content: string;                       // 消息内容
  createdAt: Date;                       // 创建时间
  publishAt: Date;                       // 发布时间
  expireAt?: Date;                       // 过期时间
  target: MessageTarget;                 // 目标定义
  metadata: Record<string, any>;         // 扩展元数据
}

/**
 * 系统公告消息
 */
export interface SystemAnnouncement extends BaseGlobalMessage {
  type: GlobalMessageType.SYSTEM_ANNOUNCEMENT;
  displayType: 'popup' | 'banner' | 'notification' | 'marquee';
  autoClose: boolean;                    // 是否自动关闭
  closeDelay?: number;                   // 自动关闭延迟（秒）
  actionButton?: {                       // 操作按钮
    text: string;
    action: string;
    url?: string;
  };
}

/**
 * 维护通知消息
 */
export interface MaintenanceNotice extends BaseGlobalMessage {
  type: GlobalMessageType.MAINTENANCE_NOTICE;
  maintenanceStart: Date;                // 维护开始时间
  maintenanceEnd: Date;                  // 维护结束时间
  affectedServers: string[];             // 受影响的区服
  compensationItems?: any[];             // 补偿物品
  forceLogout: boolean;                  // 是否强制登出
  countdownDisplay: boolean;             // 是否显示倒计时
}

/**
 * 活动通知消息
 */
export interface EventNotification extends BaseGlobalMessage {
  type: GlobalMessageType.EVENT_NOTIFICATION;
  eventId: string;                       // 活动ID
  eventType: 'cross_server' | 'global' | 'seasonal';
  startTime: Date;                       // 活动开始时间
  endTime: Date;                         // 活动结束时间
  rewards?: any[];                       // 活动奖励
  requirements?: any;                    // 参与要求
  jumpToEvent: boolean;                  // 是否跳转到活动页面
}

/**
 * 紧急警告消息
 */
export interface EmergencyAlert extends BaseGlobalMessage {
  type: GlobalMessageType.EMERGENCY_ALERT;
  alertLevel: 'warning' | 'critical' | 'emergency';
  action?: 'logout' | 'maintenance' | 'update' | 'none';
  countdown?: number;                    // 倒计时（秒）
  forceDisplay: boolean;                 // 强制显示
  blockGameplay: boolean;                // 是否阻止游戏操作
}

/**
 * 消息投递记录
 */
export interface MessageDeliveryRecord {
  id: string;
  messageId: string;
  userId: string;
  serverId: string;
  status: 'pending' | 'delivered' | 'failed' | 'acknowledged';
  deliveredAt?: Date;
  acknowledgedAt?: Date;
  failureReason?: string;
  retryCount: number;
}

/**
 * 消息统计信息
 */
export interface MessageStats {
  messageId: string;
  totalTargets: number;                  // 目标用户总数
  delivered: number;                     // 已投递数量
  failed: number;                        // 投递失败数量
  pending: number;                       // 待投递数量
  acknowledged: number;                  // 已确认数量
  deliveryRate: number;                  // 投递成功率
  acknowledgmentRate: number;            // 确认率
  avgDeliveryTime: number;               // 平均投递时间（秒）
}
