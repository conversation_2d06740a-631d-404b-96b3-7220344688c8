import { Injectable, Logger } from '@nestjs/common';
import {RedisPubSubService, RedisService } from '@libs/redis';
import { DataType, DATA_TYPES } from '@libs/redis/types/redis.types';

/**
 * 会话服务 - 专注多实例协调
 *
 * 核心职责：
 * 1. 跨实例用户定位
 * 2. 全局消息路由
 * 3. 多设备连接管理
 * 4. 业务状态同步（最小化）
 */

export interface UserConnection {
  userId: string;
  instanceId: string;        // 网关实例ID
  socketId: string;          // 当前socketId
  connectedAt: Date;         // 连接时间
  lastSeen: Date;           // 最后活跃时间
  // 设备信息
  deviceInfo?: {
    deviceId: string;
    deviceType: string;
  };
  // 游戏上下文信息
  gameContext: {
    characterId: string;     // 角色ID（游戏WebSocket必需）
    serverId: string;        // 服务器ID（游戏WebSocket必需）
    tokenScope: 'character'; // Token作用域（固定为character）
  };
}

@Injectable()
export class SessionService {
  private readonly logger = new Logger(SessionService.name);
  private readonly instanceId: string;

  // Redis键设计
  private readonly USER_CONNECTIONS_PREFIX = 'ws:user_connections:';  // 用户的所有连接
  private readonly INSTANCE_CONNECTIONS_PREFIX = 'ws:instance:';      // 实例的所有连接
  private readonly GLOBAL_USERS_SET = 'ws:online_users';              // 全局在线用户集合

  constructor(
    private readonly redisService: RedisService,
    private readonly redisPubSubService: RedisPubSubService, // 用于跨实例通信
  ) {
    this.instanceId = process.env.INSTANCE_ID || `gateway_${Date.now()}`;
  }

  /**
   * 用户连接时注册（支持单角色登录控制）
   */
  async registerConnection(
    userId: string,
    socketId: string,
    deviceInfo?: { deviceId: string; deviceType: string },
    gameContext?: { characterId: string; serverId: string }
  ): Promise<void> {
    // 游戏WebSocket必须提供游戏上下文
    if (!gameContext?.characterId || !gameContext?.serverId) {
      throw new Error('Game context (characterId and serverId) is required for game WebSocket connections');
    }

    // 检查是否已有该角色的连接（单角色登录控制）
    await this.handleSingleCharacterLogin(userId, gameContext.characterId, socketId);

    const connection: UserConnection = {
      userId,
      instanceId: this.instanceId,
      socketId,
      connectedAt: new Date(),
      lastSeen: new Date(),
      deviceInfo,
      gameContext: {
        characterId: gameContext.characterId,
        serverId: gameContext.serverId,
        tokenScope: 'character',
      },
    };

    const connectionKey = `${userId}:${this.instanceId}:${socketId}`;

    try {
      // 1. 添加到用户连接列表
      await this.redisService.hset(
        `${this.USER_CONNECTIONS_PREFIX}${userId}`,
        connectionKey,
        JSON.stringify(connection),
        DATA_TYPES.GLOBAL
      );

      // 2. 添加到实例连接列表
      await this.redisService.sadd(
        `${this.INSTANCE_CONNECTIONS_PREFIX}${this.instanceId}`,
        `${userId}:${socketId}`,
        DATA_TYPES.GLOBAL
      );

      // 3. 添加到全局在线用户集合
      await this.redisService.sadd(
        this.GLOBAL_USERS_SET,
        userId,
        DATA_TYPES.GLOBAL
      );

      // 4. 设置过期时间（防止僵尸数据）
      await this.redisService.expire(`${this.USER_CONNECTIONS_PREFIX}${userId}`, 3600, DATA_TYPES.GLOBAL);
      await this.redisService.expire(`${this.INSTANCE_CONNECTIONS_PREFIX}${this.instanceId}`, 3600, DATA_TYPES.GLOBAL);

      this.logger.debug(`Connection registered: ${userId} -> ${this.instanceId}:${socketId}`);
    } catch (error) {
      this.logger.error(`Failed to register connection: ${error.message}`);
    }
  }

  /**
   * 用户断开连接时清理
   */
  async unregisterConnection(userId: string, socketId: string): Promise<void> {
    const connectionKey = `${userId}:${this.instanceId}:${socketId}`;

    try {
      // 1. 从用户连接列表移除
      await this.redisService.hdel(
        `${this.USER_CONNECTIONS_PREFIX}${userId}`,
        [connectionKey],
        DATA_TYPES.GLOBAL
      );

      // 2. 从实例连接列表移除
      await this.redisService.srem(
        `${this.INSTANCE_CONNECTIONS_PREFIX}${this.instanceId}`,
        `${userId}:${socketId}`,
        DATA_TYPES.GLOBAL
      );

      // 3. 检查用户是否还有其他连接
      const connectionsData = await this.redisService.hgetall(
        `${this.USER_CONNECTIONS_PREFIX}${userId}`,
        DATA_TYPES.GLOBAL
      );
      const remainingConnections = Object.keys(connectionsData).length;

      if (remainingConnections === 0) {
        // 用户完全离线，从全局在线用户集合移除
        await this.redisService.srem(this.GLOBAL_USERS_SET, userId, DATA_TYPES.GLOBAL);
        await this.redisService.del(`${this.USER_CONNECTIONS_PREFIX}${userId}`, DATA_TYPES.GLOBAL);
      }

      this.logger.debug(`Connection unregistered: ${userId} -> ${this.instanceId}:${socketId}`);
    } catch (error) {
      this.logger.error(`Failed to unregister connection: ${error.message}`);
    }
  }

  /**
   * 获取用户的所有连接（跨实例）
   */
  async getUserConnections(userId: string): Promise<UserConnection[]> {
    try {
      const connectionsData = await this.redisService.hgetall(
        `${this.USER_CONNECTIONS_PREFIX}${userId}`,
        DATA_TYPES.GLOBAL
      );

      return Object.values(connectionsData).map(data => JSON.parse(data as string));
    } catch (error) {
      this.logger.error(`Failed to get user connections: ${error.message}`);
      return [];
    }
  }

  /**
   * 检查用户是否在线（任意实例）
   */
  async isUserOnline(userId: string): Promise<boolean> {
    try {
      return await this.redisService.sismember(this.GLOBAL_USERS_SET, userId, DATA_TYPES.GLOBAL);
    } catch (error) {
      this.logger.error(`Failed to check user online status: ${error.message}`);
      return false;
    }
  }

  /**
   * 获取在线用户数量
   */
  async getOnlineUserCount(): Promise<number> {
    try {
      const members = await this.redisService.smembers(this.GLOBAL_USERS_SET, DATA_TYPES.GLOBAL);
      return members.length;
    } catch (error) {
      this.logger.error(`Failed to get online user count: ${error.message}`);
      return 0;
    }
  }

  /**
   * 清理当前实例的所有连接（实例关闭时调用）
   */
  async cleanupInstanceConnections(): Promise<void> {
    try {
      const connections = await this.redisService.smembers(
        `${this.INSTANCE_CONNECTIONS_PREFIX}${this.instanceId}`,
        DATA_TYPES.GLOBAL
      );

      for (const connection of connections) {
        const [userId, socketId] = connection.split(':');
        await this.unregisterConnection(userId, socketId);
      }

      // 删除实例连接集合
      await this.redisService.del(
        `${this.INSTANCE_CONNECTIONS_PREFIX}${this.instanceId}`,
        DATA_TYPES.GLOBAL
      );

      this.logger.log(`Cleaned up ${connections.length} connections for instance ${this.instanceId}`);
    } catch (error) {
      this.logger.error(`Failed to cleanup instance connections: ${error.message}`);
    }
  }

  /**
   * 更新连接活跃时间
   */
  async updateLastSeen(userId: string, socketId: string): Promise<void> {
    const connectionKey = `${userId}:${this.instanceId}:${socketId}`;

    try {
      const connection = await this.redisService.hget<UserConnection>(
        `${this.USER_CONNECTIONS_PREFIX}${userId}`,
        connectionKey,
        DATA_TYPES.GLOBAL
      );

      connection.lastSeen = new Date();

      await this.redisService.hset(
        `${this.USER_CONNECTIONS_PREFIX}${userId}`,
        connectionKey,
        JSON.stringify(connection),
        DATA_TYPES.GLOBAL
      );
    } catch (error) {
      this.logger.error(`Failed to update last seen: ${error.message}`);
    }
  }

  /**
   * 处理单角色登录控制
   * 如果角色已在其他地方登录，断开旧连接
   */
  private async handleSingleCharacterLogin(userId: string, characterId: string, newSocketId: string): Promise<void> {
    try {
      // 获取用户的所有连接
      const existingConnections = await this.getUserConnections(userId);

      // 查找相同角色的连接
      const duplicateConnections = existingConnections.filter(
        conn => conn.gameContext?.characterId === characterId && conn.socketId !== newSocketId
      );

      if (duplicateConnections.length > 0) {
        this.logger.warn(`检测到角色 ${characterId} 重复登录，断开 ${duplicateConnections.length} 个旧连接`);

        for (const connection of duplicateConnections) {
          // 通过Redis发布消息，通知对应实例断开连接
          if (this.redisPubSubService) {
            await this.redisPubSubService.publish(`disconnect_character:${characterId}`, {
              reason: 'duplicate_login',
              characterId,
              userId,
              instanceId: connection.instanceId,
              socketId: connection.socketId,
              newSocketId,
              message: '您的角色在其他地方登录，连接已断开',
            });
          }

          // 清理旧连接的会话数据
          await this.unregisterConnection(userId, connection.socketId);
        }
      }
    } catch (error) {
      this.logger.error(`处理单角色登录控制失败: ${error.message}`);
    }
  }

  /**
   * 获取角色的当前连接
   */
  async getCharacterConnection(characterId: string): Promise<UserConnection | null> {
    try {
      // 这里需要遍历所有在线用户来查找角色连接
      // 在实际应用中，可以考虑维护一个角色到连接的映射表来优化性能
      const onlineUsers = await this.redisService.smembers(this.GLOBAL_USERS_SET, DATA_TYPES.GLOBAL);

      for (const userId of onlineUsers) {
        const connections = await this.getUserConnections(userId);
        const characterConnection = connections.find(
          conn => conn.gameContext?.characterId === characterId
        );

        if (characterConnection) {
          return characterConnection;
        }
      }

      return null;
    } catch (error) {
      this.logger.error(`获取角色连接失败: ${error.message}`);
      return null;
    }
  }
}
