import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway as WSGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import {Server, Socket} from 'socket.io';
import {Logger, UseGuards, UsePipes, ValidationPipe, OnModuleInit, OnApplicationShutdown} from '@nestjs/common';
import {ConfigService} from '@nestjs/config';
import {JwtService} from '@nestjs/jwt';
import {WsAuthGuard} from '../guards/ws-auth.guard';
import {
  PublicWsEvent,
  SkipWsRateLimit,
  WsRateLimit,
  WsRateLimitGuard
} from '../guards/ws-rate-limit.guard';
import { CharacterToken } from '../../../common/decorators/token-scope.decorator';

// 新增：模块化服务
import { MessageRouterService } from '../services/message-router.service';

import { GlobalBroadcastService } from '../../global-messaging/services/global-broadcast.service';
import {SessionService} from '../services/session.service';
import { RedisLockService, RedisService, RedisPubSubService } from '@libs/redis';
import { MicroserviceClientService } from '@libs/service-mesh';
import { MICROSERVICE_NAMES, MicroserviceName } from '@libs/shared/constants';

// 临时定义接口，直到 @shared 包可用
interface WSMessage {
  id: string;
  type: MessageType;
  service: string;
  action: string;
  payload: any;
  timestamp: number;
}

interface ServiceResponse {
  success: boolean;
  data?: any;
  error?: string;
}

enum MessageType {
  REQUEST = 'request',
  RESPONSE = 'response',
  ERROR = 'error',
}

// 新的客户端消息格式 - 使用 'service.action' 格式
interface ClientWSMessageDto {
  id: string; // 消息ID，用于关联请求和响应
  command: string;  // 格式: 'service.action' 或 'service.module.action'
  payload: any; // 消息载荷
}

// 原有的内部消息格式，解析后使用
interface WSMessageDto {
  id: string;
  service: string;
  action: string;
  payload: any;
}

// 增强的 Socket 接口
export interface AuthenticatedSocket extends Socket {
  userId?: string;
  user?: {
    id: string;
    username: string;
    email: string;
    roles: string[];
  };
  authenticated?: boolean;
  userRooms?: Set<string>; // 自定义房间跟踪，避免与 Socket.IO 内置 rooms 冲突
  lastActivity?: Date;
  connectionTime?: Date;
  metadata?: Record<string, any>;
}

// WebSocket 消息接口
export interface WebSocketMessage {
  event: string;
  data: any;
  timestamp: Date;
  messageId: string;
  userId?: string;
  room?: string;
}

// 房间信息接口
export interface RoomInfo {
  id: string;
  name: string;
  type: 'match' | 'chat' | 'lobby' | 'private';
  maxUsers: number;
  currentUsers: number;
  users: string[];
  metadata: Record<string, any>;
  createdAt: Date;
}

@WSGateway({
  cors: {
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
    credentials: true,
  },
  namespace: '/',
  transports: ['websocket', 'polling'],
  pingTimeout: 60000,
  pingInterval: 25000,
})
export class WebSocketGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect, OnModuleInit, OnApplicationShutdown
{
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(WebSocketGateway.name);

  // 🎛️ 配置选项：是否启用统一Result格式（核心基础设施配置）
  private readonly enableUnifiedResultFormat: boolean;

  // 新增：连接管理
  private readonly connectedClients = new Map<string, AuthenticatedSocket>();
  private readonly userSockets = new Map<string, Set<string>>();
  private readonly rooms = new Map<string, RoomInfo>();
  private readonly messageQueue = new Map<string, WebSocketMessage[]>();

  constructor(
    private readonly configService: ConfigService,
    private readonly jwtService: JwtService,
    private readonly sessionService: SessionService,
    private readonly redisLockService: RedisLockService,
    private readonly redisService: RedisService,
    private readonly redisPubSubService: RedisPubSubService,
    // 使用新的微服务公共库客户端
    private readonly microserviceClient: MicroserviceClientService,
    // 新增：模块化服务
    private readonly messageRouterService: MessageRouterService,

    private readonly globalBroadcastService: GlobalBroadcastService,
  ) {
    // 🎛️ 从环境变量读取配置，默认启用统一Result格式
    this.enableUnifiedResultFormat = this.configService.get<boolean>('WEBSOCKET_UNIFIED_RESULT_FORMAT', true);

    // 📊 记录配置状态
    this.logger.log(`🎛️ WebSocket统一Result格式: ${this.enableUnifiedResultFormat ? '启用' : '禁用'}`);
  }

  afterInit(server: Server) {
    this.logger.log('WebSocket Gateway initialized');

    // 设置服务器级别的中间件
    server.use(async (socket: AuthenticatedSocket, next) => {
      try {
        await this.authenticateSocket(socket);
        next();
      } catch (error) {
        this.logger.warn(`Socket authentication failed: ${error.message}`);
        next(error);
      }
    });

    // 启动清理任务
    this.startCleanupTasks();
  }

  async onModuleInit() {
    try {
      // 🔧 启动时清理可能的僵尸数据
      await this.sessionService.cleanupInstanceConnections();

      // 🔧 在模块初始化完成后订阅消息，确保RedisPubSubService已经初始化
      this.logger.log('WebSocket Gateway module initialized, subscribing to messages...');
      await this.subscribeToGlobalMessages();
      await this.subscribeToBusinessMessages();
    } catch (error) {
      this.logger.error(`Failed to initialize WebSocket Gateway: ${error.message}`);
      // 不抛出错误，允许服务继续启动，但记录错误
    }
  }

  async handleConnection(client: AuthenticatedSocket) {
    try {
      const clientId = client.id;
      const userId = client.userId;

      this.logger.log(`Client connected: ${clientId} (User: ${userId || 'anonymous'})`);

      // 记录连接信息
      client.connectionTime = new Date();
      client.lastActivity = new Date();
      // 注意：client.rooms 是 Socket.IO 的内置属性，不能直接设置
      // 我们使用自定义属性来跟踪用户房间
      client.userRooms = new Set();

      this.connectedClients.set(clientId, client);

      if (userId) {
        // 更新用户Socket映射
        if (!this.userSockets.has(userId)) {
          this.userSockets.set(userId, new Set());
        }
        this.userSockets.get(userId)!.add(clientId);

        // 注册到Redis（用于跨实例协调）
        await this.sessionService.registerConnection(
          userId,
          clientId,
          {
            deviceId: client.handshake.headers['x-device-id'] as string,
            deviceType: client.handshake.headers['x-device-type'] as string,
          },
          {
            characterId: client.metadata?.characterId as string,
            serverId: client.metadata?.serverId as string,
          }
        );

        // Join user to their personal room
        client.join(`user:${userId}`);

        // 发送用户上线事件
        this.broadcastUserStatus(userId, 'online');

        // 发送离线消息
        await this.deliverOfflineMessages(userId, client);
      }

      // 发送连接确认
      client.emit('connected', {
        clientId,
        userId,
        timestamp: new Date(),
        serverInfo: {
          version: '1.0.0',
          instanceId: process.env.GATEWAY_INSTANCE_ID,
          features: ['chat', 'match', 'notifications', 'microservices'],
        },
      });

      // 更新统计信息
      this.updateConnectionStats();

    } catch (error) {
      this.logger.error(`Error handling connection: ${error.message}`);
      client.disconnect(true);
    }
  }

  async handleDisconnect(client: AuthenticatedSocket) {
    const clientId = client.id;
    const userId = client.userId;

    this.logger.log(`Client disconnected: ${clientId} (User: ${userId || 'anonymous'})`);

    try {
      // 从房间中移除
      if (client.userRooms) {
        for (const roomId of client.userRooms) {
          await this.leaveRoomInternal(client, roomId);
        }
      }

      // 清理连接记录
      this.connectedClients.delete(clientId);

      if (userId) {
        const userSockets = this.userSockets.get(userId);
        if (userSockets) {
          userSockets.delete(clientId);

          // 如果用户在当前实例没有其他连接
          if (userSockets.size === 0) {
            this.userSockets.delete(userId);
          }
        }

        // 从Redis注销连接
        await this.sessionService.unregisterConnection(userId, clientId);

        // 检查用户是否完全离线
        const isStillOnline = await this.sessionService.isUserOnline(userId);
        if (!isStillOnline) {
          this.broadcastUserStatus(userId, 'offline');
        }
      }

      // 更新统计信息
      this.updateConnectionStats();

    } catch (error) {
      this.logger.error(`Error handling disconnect: ${error.message}`);
    }
  }

  @SubscribeMessage('message')
  @UseGuards(WsAuthGuard, WsRateLimitGuard)
  @WsRateLimit({ windowMs: 60000, max: 200 })  // 1分钟内最多200条消息
  @UsePipes(new ValidationPipe({ transform: true }))
  async handleMessage(
    @MessageBody() data: ClientWSMessageDto,
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    this.logger.debug(`🔥 handleMessage called with data: ${JSON.stringify(data)}`);
    try {
      // 简化：直接使用内存中的用户信息，无需查询Redis
      const userId = client.userId;
      if (!userId) {
        this.sendError(client, data.id, 'User not authenticated');
        return;
      }

      // 解析消息
      const parsedMessage = this.parseClientMessage(data);
      this.logger.debug(`🔍 Parsed message: service=${parsedMessage.service}, action=${parsedMessage.action}`);

      // 更新活跃时间（内存）
      client.lastActivity = new Date();

      // 异步更新Redis（不阻塞响应）
      setImmediate(async () => {
        try {
          await this.sessionService.updateLastSeen(userId, client.id);
        } catch (error) {
          this.logger.error(`Failed to update last seen for user ${userId}: ${error.message}`);
        }
      });

      // 路由消息
      this.logger.debug(`🚀 Routing message to service: ${parsedMessage.service}, action: ${parsedMessage.action}`);
      const response = await this.routeMessage(parsedMessage, client);
      this.logger.debug(`📨 Got response from routeMessage: ${JSON.stringify(response)}`);

      // 发送响应给客户端
      this.sendResponse(client, data.id, response);

    } catch (error) {
      this.logger.error(`Message handling error: ${error.message}`);
      this.sendError(client, data.id, error.message);
    }
  }



  @SubscribeMessage('ping')
  @PublicWsEvent()  // 公开事件，不需要认证
  @SkipWsRateLimit()  // 跳过限流，ping 消息需要快速响应
  handlePing(@ConnectedSocket() client: AuthenticatedSocket) {
    client.lastActivity = new Date();
    client.emit('pong', { timestamp: Date.now() });
  }

  @SubscribeMessage('heartbeat')
  @PublicWsEvent()  // 公开事件，不需要认证
  @WsRateLimit({ windowMs: 30000, max: 10 })  // 30秒内最多10次心跳
  async handleHeartbeat(@ConnectedSocket() client: AuthenticatedSocket) {
    client.lastActivity = new Date();
    client.emit('heartbeat_ack', { timestamp: new Date() });
  }

  @SubscribeMessage('send_chat_message')
  @CharacterToken()  // 聊天需要角色Token
  @UseGuards(WsAuthGuard, WsRateLimitGuard)
  @WsRateLimit({ windowMs: 60000, max: 50 })  // 1分钟内最多50条消息
  @UsePipes(new ValidationPipe({ transform: true }))
  async handleSendChatMessage(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { roomId?: string; targetUserId?: string; message: string; type?: string },
  ) {
    try {
      const { roomId, targetUserId, message, type = 'text' } = data;

      const messageData: WebSocketMessage = {
        event: 'chat_message',
        data: {
          message,
          type,
          sender: {
            id: client.userId,
            username: client.user?.username,
          },
        },
        timestamp: new Date(),
        messageId: this.generateMessageId(),
        userId: client.userId,
        room: roomId,
      };

      if (roomId) {
        // 房间消息
        this.server.to(roomId).emit('chat_message', messageData);
      } else if (targetUserId) {
        // 私聊消息
        await this.sendPrivateMessage(targetUserId, messageData);
      } else {
        client.emit('error', { message: 'Invalid message target' });
        return;
      }

      // 确认消息已发送
      client.emit('message_sent', {
        messageId: messageData.messageId,
        timestamp: messageData.timestamp,
      });

    } catch (error) {
      this.logger.error(`Error sending chat message: ${error.message}`);
      client.emit('error', { message: 'Failed to send message' });
    }
  }

  @SubscribeMessage('join_room')
  @CharacterToken()  // 需要角色Token，因为房间通常与区服相关
  @UseGuards(WsAuthGuard, WsRateLimitGuard)
  @WsRateLimit({ windowMs: 60000, max: 20 })  // 1分钟内最多加入20个房间
  async handleJoinRoom(
    @MessageBody() data: { room: string; password?: string },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    try {
      const userId = client.userId;
      if (!userId) {
        this.sendError(client, 'join_room', 'User not authenticated');
        return;
      }

      // Validate room access using microservice
      const hasAccess = await this.validateRoomAccess(userId, data.room);
      if (!hasAccess) {
        this.sendError(client, 'join_room', 'Access denied to room');
        return;
      }

      // 检查房间信息
      const roomInfo = await this.getRoomInfo(data.room);
      if (roomInfo && roomInfo.currentUsers >= roomInfo.maxUsers) {
        client.emit('error', { message: 'Room is full' });
        return;
      }

      // 加入房间
      await this.joinRoomInternal(client, data.room);

      client.emit('room_joined', {
        room: data.room,
        roomInfo: roomInfo ? this.sanitizeRoomInfo(roomInfo) : null,
        timestamp: new Date()
      });

      // 通知房间其他用户
      client.to(data.room).emit('user_joined', {
        userId: client.userId,
        user: client.user,
        timestamp: new Date(),
      });

      this.logger.log(`User ${userId} joined room: ${data.room}`);
    } catch (error) {
      this.logger.error(`Join room error: ${error.message}`);
      this.sendError(client, 'join_room', error.message);
    }
  }

  @SubscribeMessage('leave_room')
  @CharacterToken()  // 需要角色Token
  @UseGuards(WsAuthGuard, WsRateLimitGuard)
  @WsRateLimit({ windowMs: 60000, max: 30 })  // 1分钟内最多离开30个房间
  async handleLeaveRoom(
    @MessageBody() data: { room: string },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    try {
      await this.leaveRoomInternal(client, data.room);

      client.emit('room_left', {
        room: data.room,
        timestamp: new Date()
      });

      // 通知房间其他用户
      client.to(data.room).emit('user_left', {
        userId: client.userId,
        timestamp: new Date(),
      });

      const userId = client.userId;
      if (userId) {
        this.logger.log(`User ${userId} left room: ${data.room}`);
      }
    } catch (error) {
      this.logger.error(`Leave room error: ${error.message}`);
    }
  }

  // Public methods for other services to send messages
  async sendToUser(userId: string, event: string, data: any) {
    this.server.to(`user:${userId}`).emit(event, data);
  }

  async sendToRoom(room: string, event: string, data: any) {
    this.server.to(room).emit(event, data);
  }

  async broadcastToAll(event: string, data: any) {
    this.server.emit(event, data);
  }

  // 新增：跨实例消息发送
  async sendToUserGlobally(userId: string, event: string, data: any): Promise<void> {
    try {
      // 先尝试本实例
      const localSockets = this.userSockets.get(userId);
      if (localSockets && localSockets.size > 0) {
        this.server.to(`user:${userId}`).emit(event, data);
        return;
      }

      // 查找用户在其他实例的连接
      const connections = await this.sessionService.getUserConnections(userId);
      if (connections.length > 0) {
        // 通过Redis发布消息给其他实例
        await this.redisPubSubService.publish(`user_message:${userId}`, {
          event,
          data,
          targetInstances: connections.map(c => c.instanceId),
        });
      }
    } catch (error) {
      this.logger.error(`Failed to send message to user globally: ${error.message}`);
    }
  }

  // 应用关闭时清理
  async onApplicationShutdown(): Promise<void> {
    try {
      this.logger.log('WebSocket Gateway shutting down, cleaning up connections...');
      await this.sessionService.cleanupInstanceConnections();
      this.logger.log('WebSocket Gateway shutdown cleanup completed');
    } catch (error) {
      this.logger.error(`Failed to cleanup connections during shutdown: ${error.message}`);
    }
  }

  /**
   * 解析客户端消息，将 'service.action' 格式转换为内部格式
   * 支持格式：
   * - 'auth.verifyToken' -> service: 'auth', action: 'verifyToken'
   * - 'auth.user.login' -> service: 'auth', action: 'user.login'
   */
  private parseClientMessage(clientMessage: ClientWSMessageDto): WSMessageDto {
    const { id, command, payload } = clientMessage;

    // 按第一个点分割，第一部分是service，其余是action
    const parts = command.split('.');
    if (parts.length < 2) {
      throw new Error(`Invalid command format: '${command}'. Expected format: 'service.action' or 'service.module.action'`);
    }

    const service = parts[0];
    const actionPart = parts.slice(1).join('.');

    // 验证服务名是否有效
    const validServices = Object.values(MICROSERVICE_NAMES);
    if (!validServices.includes(service as MicroserviceName)) {
      throw new Error(`Invalid service name: '${service}'. Valid services: ${validServices.join(', ')}`);
    }

    this.logger.debug(`📝 Parsed command '${command}' -> service: '${service}', action: '${actionPart}'`);

    return {
      id,
      service,
      action: actionPart,
      payload
    };
  }

  private async routeMessage(message: WSMessageDto, client: AuthenticatedSocket): Promise<ServiceResponse> {
    const { service, action, payload } = message;
    this.logger.debug(`🔍 routeMessage started: service=${service}, action=${action}`);

    try {
      // 使用新的消息路由服务
      const routeResult = await this.messageRouterService.routeMessage(
        service,
        action,
        payload,
        client
      );

      if (!routeResult.success) {
        throw new Error(routeResult.error || 'Message routing failed');
      }

      return {
        success: true,
        data: routeResult.data
      };
    } catch (error) {
      this.logger.error(`❌ routeMessage error: ${error.message}`);
      throw error;
    }
  }









  // ==================== 模拟方法（用于测试） ====================



  /**
   * 使用Redis进行真实的数据结构操作
   */
  private async simulateRedisDataStructure(type: string, payload: any): Promise<any> {
    try {
      const testKey = `test:data_structure:${type}:${Date.now()}`;

      // 使用真正的Redis进行数据存储和检索
      switch (type) {
        case 'string':
          // 字符串类型
          await this.redisService.set(testKey, payload, 60); // 60秒过期
          const stringResult = await this.redisService.get<string>(testKey);
          await this.redisService.del(testKey); // 清理
          return stringResult;

        case 'object':
          // 对象类型
          await this.redisService.set(testKey, payload, 60);
          const objectResult = await this.redisService.get<any>(testKey);
          await this.redisService.del(testKey);
          return objectResult;

        case 'array':
          // 数组类型
          await this.redisService.set(testKey, payload, 60);
          const arrayResult = await this.redisService.get<any[]>(testKey);
          await this.redisService.del(testKey);
          return arrayResult;

        case 'nested':
          // 嵌套对象
          await this.redisService.set(testKey, payload, 60);
          const nestedResult = await this.redisService.get<any>(testKey);
          await this.redisService.del(testKey);
          return nestedResult;

        default:
          // 默认处理
          await this.redisService.set(testKey, payload, 60);
          const defaultResult = await this.redisService.get<any>(testKey);
          await this.redisService.del(testKey);
          return defaultResult;
      }
    } catch (error) {
      this.logger.error(`Redis data structure operation error: ${error.message}`);
      throw error;
    }
  }

  private async validateRoomAccess(userId: string, room: string): Promise<boolean> {
    if (room.startsWith('user:')) {
      return room === `user:${userId}`;
    }

    if (room.startsWith('match:')) {
      const matchId = room.split(':')[1];
      const result = await this.microserviceClient.call(MICROSERVICE_NAMES.MATCH_SERVICE, 'canAccess',
        { userId, matchId }
      );
      return result && result.success;
    }

    // Default deny
    return false;
  }

  /**
   * 发送响应给客户端（统一Result格式）
   *
   * 🎯 核心功能：将所有微服务响应统一转换为Result格式
   * - 成功响应：转换为 { code: 0, message: 'success', data: ... }
   * - 失败响应：转换为 { code: 1, message: '错误信息', data: null }
   * - 验证异常：转换为 { code: 1, message: '参数验证失败', data: null }
   *
   * 🔒 兼容性保证：
   * - 保持现有的WSMessage结构不变
   * - 保持MessageType.RESPONSE类型不变
   * - 只修改payload的内容格式
   *
   * @param client WebSocket客户端
   * @param messageId 消息ID
   * @param response 微服务响应或错误信息
   */
  private sendResponse(client: Socket, messageId: string, response: ServiceResponse) {
    let payload: any;

    if (this.enableUnifiedResultFormat) {
      // 🔄 统一转换为Result格式
      payload = this.normalizeToResultFormat(response, false);

      // 📊 记录响应日志
      this.logger.debug(`📤 发送统一响应: ${messageId}`, {
        success: payload.code === 0,
        code: payload.code,
        message: payload.message,
        hasData: !!payload.data
      });
    } else {
      // 🔄 保持原始格式（向后兼容）
      payload = response;

      this.logger.debug(`📤 发送原始响应: ${messageId}`, {
        success: response.success,
        hasData: !!response.data
      });
    }

    const wsResponse: WSMessage = {
      id: messageId,
      type: MessageType.RESPONSE, // 保持RESPONSE类型，不使用ERROR
      service: 'gateway',
      action: 'response',
      payload, // 根据配置使用不同格式的payload
      timestamp: Date.now(),
    };

    client.emit('message', wsResponse);
  }

  /**
   * 发送错误响应给客户端（统一Result格式）
   *
   * 🎯 核心功能：将网关层错误统一转换为Result格式
   * - 网关认证错误
   * - 网关路由错误
   * - 网关系统错误
   *
   * @param client WebSocket客户端
   * @param messageId 消息ID
   * @param error 错误信息
   */
  private sendError(client: AuthenticatedSocket, messageId: string, error: string) {
    let payload: any;
    let messageType: MessageType;
    let action: string;

    if (this.enableUnifiedResultFormat) {
      // 🔄 统一转换为Result格式
      payload = this.normalizeToResultFormat({ error }, true);
      messageType = MessageType.RESPONSE; // 统一使用RESPONSE类型
      action = 'response'; // 统一使用response action

      // 📊 记录错误日志
      this.logger.error(`📤 发送统一错误响应: ${messageId}`, {
        error,
        code: payload.code,
        message: payload.message
      });
    } else {
      // 🔄 保持原始格式（向后兼容）
      payload = { error };
      messageType = MessageType.ERROR; // 使用原始ERROR类型
      action = 'error'; // 使用原始error action

      this.logger.error(`📤 发送原始错误响应: ${messageId}`, { error });
    }

    const wsResponse: WSMessage = {
      id: messageId,
      type: messageType,
      service: 'gateway',
      action,
      payload,
      timestamp: Date.now(),
    };

    client.emit('message', wsResponse);
  }

  /**
   * 将响应标准化为Result格式
   *
   * 🎯 核心转换逻辑：
   * 1. 微服务成功响应 → { code: 0, message: 'success', data: ... }
   * 2. 微服务错误响应 → { code: 1, message: '错误信息', data: null }
   * 3. 网关层错误 → { code: 1, message: '错误信息', data: null }
   * 4. 验证管道异常 → { code: 1, message: '参数验证失败', data: null }
   *
   * @param response 原始响应或错误信息
   * @param isError 是否为错误响应
   * @returns 统一的Result格式响应
   */
  private normalizeToResultFormat(response: ServiceResponse | { error: string }, isError: boolean) {
    const timestamp = Date.now();

    // 🚨 处理错误情况
    if (isError || 'error' in response) {
      const errorMessage = 'error' in response ? response.error : '请求处理失败';

      return {
        code: 1,
        message: this.normalizeErrorMessage(errorMessage),
        data: null,
        timestamp,
      };
    }

    // ✅ 处理成功情况
    const serviceResponse = response as ServiceResponse;

    // 🔍 检查微服务响应格式
    if (serviceResponse.success === false) {
      // 微服务返回的业务错误
      return {
        code: 1,
        message: serviceResponse.error || '业务处理失败',
        data: null,
        timestamp,
      };
    }

    // 🎯 微服务成功响应
    if (serviceResponse.success === true || serviceResponse.data !== undefined) {
      return {
        code: 0,
        message: 'success',
        data: serviceResponse.data,
        timestamp,
      };
    }

    // 🔄 兼容性处理：直接返回的数据（非标准ServiceResponse格式）
    // 这种情况下，假设是成功响应
    return {
      code: 0,
      message: 'success',
      data: serviceResponse,
      timestamp,
    };
  }

  /**
   * 标准化错误消息
   *
   * 🎯 将各种异常类型的错误消息转换为用户友好的格式
   *
   * @param errorMessage 原始错误消息
   * @returns 标准化的错误消息
   */
  private normalizeErrorMessage(errorMessage: string): string {
    // 🔍 验证管道异常
    if (errorMessage.includes('参数验证失败') || errorMessage.includes('validation')) {
      return '参数验证失败';
    }

    // 🔍 微服务连接异常
    if (errorMessage.includes('timeout') || errorMessage.includes('TIMEOUT')) {
      return '服务请求超时，请稍后重试';
    }

    if (errorMessage.includes('connection') || errorMessage.includes('ECONNREFUSED')) {
      return '服务暂时不可用，请稍后重试';
    }

    // 🔍 认证授权异常
    if (errorMessage.includes('authentication') || errorMessage.includes('unauthorized')) {
      return '身份验证失败，请重新登录';
    }

    if (errorMessage.includes('permission') || errorMessage.includes('forbidden')) {
      return '权限不足，无法执行此操作';
    }

    // 🔍 业务异常（保持原始消息）
    if (errorMessage.includes('不存在') ||
        errorMessage.includes('已存在') ||
        errorMessage.includes('不能') ||
        errorMessage.includes('无法') ||
        errorMessage.includes('失败')) {
      return errorMessage;
    }

    // 🔄 默认错误消息
    return errorMessage || '请求处理失败，请稍后重试';
  }

  /**
   * 获取WebSocket网关状态
   */
  public getGatewayStatus() {
    return {
      connectedClients: this.connectedClients.size,
      totalRooms: this.rooms.size,
      uptime: process.uptime(),
      timestamp: new Date().toISOString()
    };
  }

  private async authenticateSocket(socket: AuthenticatedSocket): Promise<void> {
    const token = socket.handshake.auth?.token;

    if (!token) {
      throw new Error('Character token required for game connection');
    }

    try {
      // 先解码Token以确定类型，不验证签名
      const decoded = this.jwtService.decode(token as string) as any;

      // 强制要求角色Token
      if (!decoded || decoded.scope !== 'character') {
        throw new Error('Character token required for game connection');
      }

      // 验证角色Token
      const characterSecret = this.configService.get<string>('gateway.security.characterJwtSecret');
      const payload = this.jwtService.verify(token as string, { secret: characterSecret });

      // 验证必需的角色信息
      if (!payload.characterId || !payload.serverId) {
        throw new Error('Invalid character token: missing character or server information');
      }

      // 存储认证信息
      socket.userId = payload.sub;
      socket.user = {
        id: payload.sub,
        username: payload.username,
        email: payload.email,
        roles: payload.roles || [],
      };
      socket.authenticated = true;
      socket.metadata = {
        tokenScope: 'character',
        characterId: payload.characterId,
        serverId: payload.serverId,
      };

      // 注册连接
      await this.sessionService.registerConnection(
        payload.sub,
        socket.id,
        {
          deviceId: socket.handshake.headers['x-device-id'] as string,
          deviceType: socket.handshake.headers['x-device-type'] as string,
        },
        {
          characterId: payload.characterId,
          serverId: payload.serverId,
        }
      );
    } catch (error) {
      throw new Error('Invalid character token');
    }
  }

  private async joinRoomInternal(client: AuthenticatedSocket, roomId: string): Promise<void> {
    await client.join(roomId);
    client.userRooms?.add(roomId);

    const room = this.rooms.get(roomId);
    if (room && client.userId) {
      room.currentUsers++;
      room.users.push(client.userId);
    }
  }

  private async leaveRoomInternal(client: AuthenticatedSocket, roomId: string): Promise<void> {
    await client.leave(roomId);
    client.userRooms?.delete(roomId);

    const room = this.rooms.get(roomId);
    if (room && client.userId) {
      room.currentUsers = Math.max(0, room.currentUsers - 1);
      room.users = room.users.filter(id => id !== client.userId);
    }
  }

  private async getRoomInfo(roomId: string): Promise<RoomInfo | null> {
    // 首先检查本地缓存
    let room = this.rooms.get(roomId);

    if (!room) {
      // 如果是游戏相关房间，从微服务获取信息
      if (roomId.startsWith('match:')) {
        const matchId = roomId.split(':')[1];
        try {
          const response = await this.microserviceClient.call(
            MICROSERVICE_NAMES.MATCH_SERVICE,
            'getRoomInfo',
            { matchId }
          );
          if (response && response.success) {
            room = {
              id: roomId,
              name: response.data.name || `Match ${matchId}`,
              type: 'match',
              maxUsers: response.data.maxUsers || 100,
              currentUsers: 0,
              users: [],
              metadata: response.data.metadata || {},
              createdAt: new Date(response.data.createdAt),
            };
            this.rooms.set(roomId, room);
          }
        } catch (error) {
          this.logger.error(`Failed to get match room info: ${error.message}`);
        }
      }
    }

    return room || null;
  }

  private async sendPrivateMessage(targetUserId: string, message: WebSocketMessage): Promise<void> {
    const targetSockets = this.userSockets.get(targetUserId);

    if (targetSockets && targetSockets.size > 0) {
      // 用户在线，直接发送
      for (const socketId of targetSockets) {
        const socket = this.connectedClients.get(socketId);
        if (socket) {
          socket.emit('private_message', message);
        }
      }
    } else {
      // 用户离线，存储消息
      await this.storeOfflineMessage(targetUserId, message);
    }
  }

  private async storeOfflineMessage(userId: string, message: WebSocketMessage): Promise<void> {
    if (!this.messageQueue.has(userId)) {
      this.messageQueue.set(userId, []);
    }

    const userMessages = this.messageQueue.get(userId)!;
    userMessages.push(message);

    // 限制离线消息数量
    if (userMessages.length > 100) {
      userMessages.shift();
    }
  }

  private async deliverOfflineMessages(userId: string, client: AuthenticatedSocket): Promise<void> {
    const messages = this.messageQueue.get(userId);

    if (messages && messages.length > 0) {
      client.emit('offline_messages', {
        messages,
        count: messages.length,
      });

      // 清除已发送的离线消息
      this.messageQueue.delete(userId);
    }
  }

  private broadcastUserStatus(userId: string, status: 'online' | 'offline'): void {
    this.server.emit('user_status', {
      userId,
      status,
      timestamp: new Date(),
    });
  }

  private sanitizeRoomInfo(room: RoomInfo): Partial<RoomInfo> {
    return {
      id: room.id,
      name: room.name,
      type: room.type,
      maxUsers: room.maxUsers,
      currentUsers: room.currentUsers,
    };
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private updateConnectionStats(): void {
    const stats = {
      totalConnections: this.connectedClients.size,
      authenticatedConnections: Array.from(this.connectedClients.values()).filter(c => c.authenticated).length,
      totalRooms: this.rooms.size,
      timestamp: new Date(),
    };

    // 这里可以发送统计信息到监控系统
    this.logger.debug('Connection stats updated', stats);
  }

  private startCleanupTasks(): void {
    // 每5分钟清理不活跃的连接
    setInterval(() => {
      this.cleanupInactiveConnections();
    }, 5 * 60 * 1000);

    // 每小时清理空房间
    setInterval(() => {
      this.cleanupEmptyRooms();
    }, 60 * 60 * 1000);
  }

  private cleanupInactiveConnections(): void {
    const now = new Date();
    const timeout = 10 * 60 * 1000; // 10分钟超时

    for (const [clientId, client] of this.connectedClients) {
      if (client.lastActivity && (now.getTime() - client.lastActivity.getTime()) > timeout) {
        this.logger.log(`Disconnecting inactive client: ${clientId}`);
        client.disconnect(true);
      }
    }
  }

  private cleanupEmptyRooms(): void {
    for (const [roomId, room] of this.rooms) {
      if (room.currentUsers === 0) {
        this.rooms.delete(roomId);
        this.logger.log(`Cleaned up empty room: ${roomId}`);
      }
    }
  }

  // ==================== 消息处理 ====================

  /**
   * 订阅全服消息
   */
  private async subscribeToGlobalMessages(): Promise<void> {
    try {
      // 订阅全服广播频道
      await this.redisPubSubService.subscribe('global_messages:broadcast', (message) => {
        this.handleGlobalBroadcast(JSON.parse(message.data || message.toString()));
      });

      // 订阅系统公告频道
      await this.redisPubSubService.subscribe('global_messages:announcement', (message) => {
        this.handleSystemAnnouncement(JSON.parse(message.data || message.toString()));
      });

      // 订阅紧急通知频道
      await this.redisPubSubService.subscribe('global_messages:emergency', (message) => {
        this.handleEmergencyNotification(JSON.parse(message.data || message.toString()));
      });

      this.logger.log('✅ Successfully subscribed to global message channels');
    } catch (error) {
      this.logger.error('❌ Failed to subscribe to global messages:', error);
    }
  }

  /**
   * 处理全服广播消息
   */
  private async handleGlobalBroadcast(message: any): Promise<void> {
    this.logger.log(`📢 Handling global broadcast: ${message.type}`);

    try {
      // 根据消息目标筛选用户
      const targetUsers = await this.resolveMessageTargets(message.target);

      // 向目标用户广播消息
      for (const userId of targetUsers) {
        const userSockets = this.userSockets.get(userId);
        if (userSockets) {
          for (const socketId of userSockets) {
            const socket = this.connectedClients.get(socketId);
            if (socket) {
              socket.emit('global.message', {
                id: message.id,
                type: message.type,
                title: message.title,
                content: message.content,
                priority: message.priority,
                displayType: message.displayType || 'notification',
                timestamp: new Date(),
              });
            }
          }
        }
      }

      this.logger.log(`📢 Global broadcast sent to ${targetUsers.length} users`);
    } catch (error) {
      this.logger.error('❌ Failed to handle global broadcast:', error);
    }
  }

  /**
   * 处理系统公告
   */
  private async handleSystemAnnouncement(message: any): Promise<void> {
    this.logger.log(`📋 Handling system announcement: ${message.title}`);

    // 系统公告通常需要全服广播
    this.server.emit('global.announcement', {
      id: message.id,
      title: message.title,
      content: message.content,
      displayType: message.displayType || 'popup',
      autoClose: message.autoClose || false,
      closeDelay: message.closeDelay || 10,
      actionButton: message.actionButton,
      timestamp: new Date(),
    });

    this.logger.log('📋 System announcement broadcasted to all users');
  }

  /**
   * 处理紧急通知
   */
  private async handleEmergencyNotification(message: any): Promise<void> {
    this.logger.warn(`🚨 Handling emergency notification: ${message.alertLevel}`);

    // 紧急通知强制发送给所有在线用户
    this.server.emit('global.emergency', {
      id: message.id,
      alertLevel: message.alertLevel,
      title: message.title,
      content: message.content,
      action: message.action,
      countdown: message.countdown,
      forceDisplay: message.forceDisplay || true,
      blockGameplay: message.blockGameplay || false,
      timestamp: new Date(),
    });

    // 如果需要强制登出，执行相应操作
    if (message.action === 'logout') {
      setTimeout(() => {
        this.server.emit('global.force_logout', {
          reason: message.content,
          countdown: message.countdown || 60,
        });
      }, 1000);
    }

    this.logger.warn('🚨 Emergency notification sent to all users');
  }

  // ==================== 业务消息处理 ====================

  /**
   * 订阅业务消息推送
   */
  private async subscribeToBusinessMessages(): Promise<void> {
    try {
      // 订阅统一的业务推送频道
      await this.redisPubSubService.subscribe('gateway:push', (message) => {
        this.handleBusinessPush(JSON.parse(message.data || message.toString()));
      });

      this.logger.log('📡 Business messaging initialized, subscribed to gateway:push channel');
    } catch (error) {
      this.logger.error(`Failed to subscribe to business messages: ${error.message}`);
    }
  }

  /**
   * 处理业务推送通知
   */
  private async handleBusinessPush(notification: any): Promise<void> {
    try {
      this.logger.debug(`📨 Received business push: ${notification.eventName} (${notification.pushType})`);

      // 检查推送是否过期
      if (notification.options?.expireAt && Date.now() > notification.options.expireAt) {
        this.logger.warn(`Business push expired: ${notification.eventName}`);
        return;
      }

      // 根据推送类型执行相应操作
      switch (notification.pushType) {
        case 'user':
          await this.pushToUser(notification.targetUserId, notification.eventName, notification.payload);
          break;
        case 'users':
          await this.pushToUsers(notification.targetUserIds, notification.eventName, notification.payload);
          break;
        case 'room':
          await this.pushToRoom(notification.roomId, notification.eventName, notification.payload);
          break;
        default:
          this.logger.error(`Unknown business push type: ${notification.pushType}`);
      }
    } catch (error) {
      this.logger.error(`Failed to handle business push: ${error.message}`);
    }
  }

  /**
   * 推送给单个用户
   */
  private async pushToUser(userId: string, eventName: string, payload: any): Promise<void> {
    const userSockets = this.userSockets.get(userId);
    if (userSockets) {
      for (const socketId of userSockets) {
        const socket = this.connectedClients.get(socketId);
        if (socket) {
          socket.emit(eventName, payload);
        }
      }
      this.logger.debug(`📤 Pushed ${eventName} to user: ${userId}`);
    } else {
      this.logger.debug(`User ${userId} not online, push skipped`);
    }
  }

  /**
   * 推送给多个用户
   */
  private async pushToUsers(userIds: string[], eventName: string, payload: any): Promise<void> {
    let pushedCount = 0;
    for (const userId of userIds) {
      const userSockets = this.userSockets.get(userId);
      if (userSockets) {
        for (const socketId of userSockets) {
          const socket = this.connectedClients.get(socketId);
          if (socket) {
            socket.emit(eventName, payload);
          }
        }
        pushedCount++;
      }
    }
    this.logger.debug(`📤 Pushed ${eventName} to ${pushedCount}/${userIds.length} users`);
  }

  /**
   * 推送给游戏房间
   */
  private async pushToRoom(roomId: string, eventName: string, payload: any): Promise<void> {
    this.server.to(roomId).emit(eventName, payload);
    this.logger.debug(`📤 Pushed ${eventName} to room: ${roomId}`);
  }

  /**
   * 解析消息目标用户
   */
  private async resolveMessageTargets(target: any): Promise<string[]> {
    if (!target) {
      return [];
    }

    switch (target.scope) {
      case 'global':
        // 返回所有在线用户
        return Array.from(this.userSockets.keys());

      case 'servers':
        // 返回指定区服的用户
        return this.getUsersByServers(target.servers || []);

      case 'users':
        // 返回指定用户列表
        return target.users || [];

      case 'conditions':
        // 根据条件筛选用户
        return await this.getUsersByConditions(target.conditions || []);

      default:
        return [];
    }
  }

  /**
   * 根据区服获取用户列表
   */
  private getUsersByServers(serverIds: string[]): string[] {
    const targetUsers: string[] = [];

    for (const [userId, socketIds] of this.userSockets) {
      for (const socketId of socketIds) {
        const socket = this.connectedClients.get(socketId);
        if (socket?.data?.character?.serverId && serverIds.includes(socket.data.character.serverId)) {
          targetUsers.push(userId);
          break; // 每个用户只添加一次
        }
      }
    }

    return targetUsers;
  }

  /**
   * 根据条件筛选用户
   */
  private async getUsersByConditions(conditions: any[]): Promise<string[]> {
    // 这里可以实现复杂的用户筛选逻辑
    // 例如根据等级、VIP状态、最后登录时间等条件筛选
    // 暂时返回所有在线用户
    return Array.from(this.userSockets.keys());
  }

  /**
   * 添加全服消息处理的WebSocket事件
   */
  @SubscribeMessage('global.message.ack')
  async handleGlobalMessageAck(
    @MessageBody() data: { messageId: string },
    @ConnectedSocket() client: AuthenticatedSocket,
  ): Promise<void> {
    const userId = client.data.user?.id;
    if (!userId) {
      return;
    }

    this.logger.debug(`📨 Global message acknowledged: ${data.messageId} by user ${userId}`);

    // 记录消息确认状态（可以存储到数据库或Redis）
    try {
      await this.redisService.set(
        `global_message_ack:${data.messageId}:${userId}`,
        JSON.stringify({
          messageId: data.messageId,
          userId,
          acknowledgedAt: new Date(),
        }),
        3600 * 24 * 7, // 保存7天
        'global'
      );
    } catch (error) {
      this.logger.error('Failed to record message acknowledgment:', error);
    }
  }
}
