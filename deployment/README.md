# Football Game Server 部署文档

## 概述

本文档描述了 Football Game Server 的完整部署流程，包括全局服务和分区分服服务的部署策略。

## 架构概览

### 服务分类

**全局服务（单实例，多副本）：**
- `gateway` - API 网关服务 (端口: 3000+)
- `auth` - 认证服务 (端口: 3100+)

**分区分服服务（多实例，按区服部署）：**
- `character` - 角色服务 (端口: 3200+)
- `economy` - 经济服务 (端口: 3300+)
- `social` - 社交服务 (端口: 3400+)
- `activity` - 活动服务 (端口: 3500+)
- `match` - 比赛服务 (端口: 3600+)

### 端口分配策略

**全局服务：**
```
端口 = 基础端口 + 实例序号
例如：gateway-global-0 = 3000, gateway-global-1 = 3001
```

**分区分服服务：**
```
端口 = 基础端口 + (服务器编号-1)*10 + 实例序号
例如：character-server_001-0 = 3200, character-server_002-0 = 3210
```

## 快速开始

### 1. 环境准备

在目标服务器上运行环境初始化脚本：

```bash
# 下载并运行服务器初始化脚本
curl -fsSL https://raw.githubusercontent.com/your-repo/deployment/scripts/setup-server.sh | sudo bash

# 或者手动执行
sudo chmod +x deployment/scripts/setup-server.sh
sudo ./deployment/scripts/setup-server.sh
```

### 2. 配置服务器信息

编辑服务器配置文件：

```bash
# 生产环境
cp deployment/config/servers/production.env.example deployment/config/servers/production.env
vim deployment/config/servers/production.env

# 开发环境
cp deployment/config/servers/development.env.example deployment/config/servers/development.env
vim deployment/config/servers/development.env
```

### 3. 部署服务

```bash
# 给部署脚本执行权限
chmod +x deployment/deploy.sh

# 部署单个服务到指定区服
./deployment/deploy.sh character server_001 0 production

# 部署所有服务到指定区服
./deployment/deploy.sh all server_001 0 production

# 部署全局服务
./deployment/deploy.sh gateway global 0 production
./deployment/deploy.sh auth global 0 production
```

## 详细部署流程

### 1. 构建阶段

部署脚本会自动执行以下构建步骤：

```bash
# 构建指定服务
npm run build:character

# 或构建所有服务
npm run build:all
```

### 2. 打包阶段

脚本会创建包含以下内容的部署包：

```
package-name/
├── character/              # 编译后的服务代码
├── libs/                   # 共享库
├── .env                    # 根目录环境配置
├── .env.service           # 服务特定配置
├── .env.server            # 区服配置
├── package.json           # 简化的包配置
├── ecosystem.config.js    # PM2 配置
├── game-configs/          # 游戏配置文件
├── public/                # 静态资源
└── assets/                # 服务资源
```

### 3. 上传阶段

```bash
# 自动上传到配置的远程服务器
scp package.tar.gz user@server:/tmp/

# 解压并部署
ssh user@server "cd /tmp && tar -xzf package.tar.gz"
```

### 4. 部署阶段

远程服务器上的部署流程：

```bash
# 停止旧服务
pm2 stop service-name || true
pm2 delete service-name || true

# 备份旧版本
mv /opt/football-game-server/service-name /opt/football-game-server/service-name.backup.$(date +%Y%m%d_%H%M%S)

# 部署新版本
mv package-name /opt/football-game-server/service-name
chown -R deploy:deploy /opt/football-game-server/service-name

# 启动新服务
cd /opt/football-game-server/service-name
pm2 start ecosystem.config.js --env production
pm2 save
```

## 配置说明

### 环境变量配置

**根目录 .env：**
```env
# 数据库配置
MONGODB_URI=mongodb://localhost:27017/football-game
REDIS_URL=redis://localhost:6379

# 基础配置
NODE_ENV=production
LOG_LEVEL=info
```

**服务 .env.service：**
```env
# 服务特定配置
SERVICE_NAME=character
SERVICE_VERSION=1.0.0
```

**区服 .env.server：**
```env
# 区服配置
SERVER_ID=server_001
INSTANCE_INDEX=0
PORT=3200
```

### PM2 配置

每个服务都会生成独立的 PM2 配置：

```javascript
module.exports = {
  apps: [{
    name: 'character-server_001-0',
    script: 'character/main.js',
    cwd: '/opt/football-game-server/character-server_001-0',
    instances: 1,
    exec_mode: 'fork',
    env_production: {
      NODE_ENV: 'production',
      PORT: 3200,
      SERVER_ID: 'server_001',
      INSTANCE_INDEX: 0
    },
    // ... 其他配置
  }]
};
```

## 运维管理

### 服务管理

```bash
# 查看所有服务状态
pm2 list

# 查看特定服务
pm2 show character-server_001-0

# 重启服务
pm2 restart character-server_001-0

# 查看日志
pm2 logs character-server_001-0

# 监控服务
pm2 monit
```

### 健康检查

```bash
# 执行完整健康检查
./deployment/scripts/health-check.sh

# 仅检查 PM2 状态
./deployment/scripts/health-check.sh pm2

# 仅检查端口连通性
./deployment/scripts/health-check.sh ports

# 仅检查系统资源
./deployment/scripts/health-check.sh system
```

### 日志管理

日志文件位置：
```
/var/log/football-game-server/
├── service-name-error.log      # 错误日志
├── service-name-out.log        # 标准输出
└── service-name-combined.log   # 合并日志
```

日志轮转配置：
- 每日轮转
- 保留 30 天
- 自动压缩
- PM2 自动重载日志

### 备份策略

**自动备份：**
- 部署前自动备份当前版本
- 保留最近 7 个版本
- 备份位置：`/opt/football-game-server/backups/`

**手动备份：**
```bash
# 备份当前部署
cp -r /opt/football-game-server/service-name /opt/football-game-server/backups/service-name.$(date +%Y%m%d_%H%M%S)

# 恢复备份
pm2 stop service-name
mv /opt/football-game-server/service-name.backup.20231201_120000 /opt/football-game-server/service-name
pm2 start service-name
```

## 监控和告警

### Nginx 配置

使用提供的 Nginx 配置文件：

```bash
# 复制配置文件
sudo cp deployment/nginx/football-game-server.conf /etc/nginx/sites-available/
sudo ln -s /etc/nginx/sites-available/football-game-server.conf /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重载配置
sudo systemctl reload nginx
```

### SSL 证书

```bash
# 使用 Let's Encrypt 获取免费证书
sudo certbot --nginx -d game.yourdomain.com -d server001.game.yourdomain.com

# 自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

## 故障排除

### 常见问题

**1. 服务启动失败**
```bash
# 检查日志
pm2 logs service-name

# 检查配置
cat /opt/football-game-server/service-name/ecosystem.config.js

# 检查端口占用
netstat -tlnp | grep :3200
```

**2. 端口冲突**
```bash
# 查看端口使用情况
ss -tlnp | grep :3200

# 修改端口配置
vim /opt/football-game-server/service-name/.env.server
```

**3. 内存不足**
```bash
# 检查内存使用
free -h
pm2 monit

# 调整 PM2 内存限制
pm2 restart service-name --max-memory-restart 1G
```

### 紧急恢复

**回滚到上一版本：**
```bash
pm2 stop service-name
mv /opt/football-game-server/service-name /opt/football-game-server/service-name.failed
mv /opt/football-game-server/service-name.backup.latest /opt/football-game-server/service-name
pm2 start service-name
```

**完全重新部署：**
```bash
# 清理旧部署
pm2 delete service-name
rm -rf /opt/football-game-server/service-name

# 重新部署
./deployment/deploy.sh service-name server_001 0 production
```

## 性能优化

### 系统优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化网络参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
sysctl -p
```

### PM2 优化

```bash
# 启用集群模式（适用于 CPU 密集型服务）
pm2 start ecosystem.config.js --env production -i max

# 启用监控
pm2 install pm2-server-monit
```

## 安全建议

1. **网络安全**
   - 配置防火墙规则
   - 使用 VPN 或专用网络
   - 定期更新系统补丁

2. **访问控制**
   - 使用 SSH 密钥认证
   - 禁用 root 直接登录
   - 配置 fail2ban

3. **数据安全**
   - 启用数据库认证
   - 使用 SSL/TLS 加密
   - 定期备份数据

4. **应用安全**
   - 定期更新依赖包
   - 配置 HTTPS
   - 实施访问限流
