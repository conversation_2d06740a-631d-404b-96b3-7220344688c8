#!/bin/bash

# ========================================
# 部署脚本权限设置
# ========================================

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

log_info "设置部署脚本权限..."

# 设置主要脚本权限
chmod +x "$SCRIPT_DIR/deploy.sh"
chmod +x "$SCRIPT_DIR/quick-deploy.sh"

# 设置辅助脚本权限
chmod +x "$SCRIPT_DIR/scripts/setup-server.sh"
chmod +x "$SCRIPT_DIR/scripts/health-check.sh"
chmod +x "$SCRIPT_DIR/scripts/backup.sh"

# 设置配置文件权限
chmod 644 "$SCRIPT_DIR/config/deployment.env"
chmod 644 "$SCRIPT_DIR/config/servers/"*.env

# 设置 Nginx 配置权限
chmod 644 "$SCRIPT_DIR/nginx/football-game-server.conf"

# 设置 Docker 文件权限
chmod 644 "$SCRIPT_DIR/docker/Dockerfile"
chmod 644 "$SCRIPT_DIR/docker/docker-compose.yml"

log_success "权限设置完成!"

# 显示权限信息
log_info "脚本权限信息:"
ls -la "$SCRIPT_DIR"/*.sh
ls -la "$SCRIPT_DIR/scripts/"*.sh
