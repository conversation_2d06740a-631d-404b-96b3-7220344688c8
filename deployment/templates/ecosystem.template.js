// ========================================
// PM2 生态系统配置模板
// ========================================
// 
// 此文件是 PM2 配置的模板，部署脚本会根据实际参数生成具体配置
// 支持：
// - 全局服务（gateway、auth）
// - 分区分服服务（character、economy、social、activity、match）
// - 动态端口分配
// - 环境变量配置
// - 日志管理
// - 性能监控
// ========================================

module.exports = {
  apps: [
    // ========== 全局服务配置 ==========
    {
      name: 'gateway-global-0',
      script: 'gateway/main.js',
      cwd: '/opt/football-game-server/gateway-global-0',
      instances: 1,
      exec_mode: 'fork',
      
      // 环境变量
      env: {
        NODE_ENV: 'development',
        PORT: 3000,
        SERVER_ID: 'global',
        INSTANCE_INDEX: 0,
        SERVICE_NAME: 'gateway',
        SERVICE_TYPE: 'global'
      },
      
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000,
        SERVER_ID: 'global',
        INSTANCE_INDEX: 0,
        SERVICE_NAME: 'gateway',
        SERVICE_TYPE: 'global'
      },
      
      // 日志配置
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      error_file: '/var/log/football-game-server/gateway-global-0-error.log',
      out_file: '/var/log/football-game-server/gateway-global-0-out.log',
      log_file: '/var/log/football-game-server/gateway-global-0-combined.log',
      
      // 进程管理
      time: true,
      autorestart: true,
      max_restarts: 10,
      min_uptime: '10s',
      max_memory_restart: '1G',
      restart_delay: 4000,
      
      // 监控配置
      watch: false,
      ignore_watch: ['node_modules', 'logs', '*.log'],
      
      // 性能配置
      source_map_support: false,
      instance_var: 'INSTANCE_ID',
      
      // 健康检查
      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true
    },
    
    {
      name: 'auth-global-0',
      script: 'auth/main.js',
      cwd: '/opt/football-game-server/auth-global-0',
      instances: 1,
      exec_mode: 'fork',
      
      env: {
        NODE_ENV: 'development',
        PORT: 3100,
        SERVER_ID: 'global',
        INSTANCE_INDEX: 0,
        SERVICE_NAME: 'auth',
        SERVICE_TYPE: 'global'
      },
      
      env_production: {
        NODE_ENV: 'production',
        PORT: 3100,
        SERVER_ID: 'global',
        INSTANCE_INDEX: 0,
        SERVICE_NAME: 'auth',
        SERVICE_TYPE: 'global'
      },
      
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      error_file: '/var/log/football-game-server/auth-global-0-error.log',
      out_file: '/var/log/football-game-server/auth-global-0-out.log',
      log_file: '/var/log/football-game-server/auth-global-0-combined.log',
      
      time: true,
      autorestart: true,
      max_restarts: 10,
      min_uptime: '10s',
      max_memory_restart: '512M',
      restart_delay: 4000,
      
      watch: false,
      ignore_watch: ['node_modules', 'logs', '*.log'],
      source_map_support: false,
      instance_var: 'INSTANCE_ID'
    },
    
    // ========== 区服服务配置示例 ==========
    {
      name: 'character-server_001-0',
      script: 'character/main.js',
      cwd: '/opt/football-game-server/character-server_001-0',
      instances: 1,
      exec_mode: 'fork',
      
      env: {
        NODE_ENV: 'development',
        PORT: 3200,  // 基础端口 + (服务器编号-1)*10 + 实例序号
        SERVER_ID: 'server_001',
        INSTANCE_INDEX: 0,
        SERVICE_NAME: 'character',
        SERVICE_TYPE: 'regional'
      },
      
      env_production: {
        NODE_ENV: 'production',
        PORT: 3200,
        SERVER_ID: 'server_001',
        INSTANCE_INDEX: 0,
        SERVICE_NAME: 'character',
        SERVICE_TYPE: 'regional'
      },
      
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      error_file: '/var/log/football-game-server/character-server_001-0-error.log',
      out_file: '/var/log/football-game-server/character-server_001-0-out.log',
      log_file: '/var/log/football-game-server/character-server_001-0-combined.log',
      
      time: true,
      autorestart: true,
      max_restarts: 10,
      min_uptime: '10s',
      max_memory_restart: '1G',
      restart_delay: 4000,
      
      watch: false,
      ignore_watch: ['node_modules', 'logs', '*.log'],
      source_map_support: false,
      instance_var: 'INSTANCE_ID'
    }
  ],
  
  // ========== 部署配置 ==========
  deploy: {
    production: {
      user: 'deploy',
      host: ['your-production-server.com'],
      ref: 'origin/main',
      repo: '**************:your-username/football-game-server.git',
      path: '/opt/football-game-server',
      'post-deploy': 'npm install --production && pm2 reload ecosystem.config.js --env production',
      'pre-setup': 'apt update && apt install git -y'
    },
    
    staging: {
      user: 'deploy',
      host: ['your-staging-server.com'],
      ref: 'origin/develop',
      repo: '**************:your-username/football-game-server.git',
      path: '/opt/football-game-server-staging',
      'post-deploy': 'npm install --production && pm2 reload ecosystem.config.js --env staging'
    }
  }
};
