# Football Game Server 部署总结

## 🎯 部署架构概览

### 服务分类与端口分配

**全局服务（多实例负载均衡）：**
- `gateway` - API网关服务
  - 端口: 3000 + 实例序号 (3000, 3001, 3002...)
  - 部署: gateway-global-0, gateway-global-1...
- `auth` - 认证服务  
  - 端口: 3100 + 实例序号 (3100, 3101, 3102...)
  - 部署: auth-global-0, auth-global-1...

**分区分服服务（按区服动态端口）：**
- `character` - 角色服务 (基础端口: 3200)
- `economy` - 经济服务 (基础端口: 3300)  
- `social` - 社交服务 (基础端口: 3400)
- `activity` - 活动服务 (基础端口: 3500)
- `match` - 比赛服务 (基础端口: 3600)

**端口计算公式：**
```
区服端口 = 基础端口 + (服务器编号-1)*10 + 实例序号
例如：character-server_001-0 = 3200
     character-server_002-0 = 3210  
     character-server_001-1 = 3201
```

## 📦 部署文件结构

```
deployment/
├── deploy.sh                    # 主部署脚本
├── quick-deploy.sh             # 快速部署脚本
├── setup-permissions.sh       # 权限设置脚本
├── README.md                   # 详细部署文档
├── DEPLOYMENT_SUMMARY.md       # 本文件
├── config/
│   ├── deployment.env          # 部署配置
│   └── servers/
│       ├── production.env      # 生产环境配置
│       └── development.env     # 开发环境配置
├── scripts/
│   ├── setup-server.sh         # 服务器初始化
│   ├── health-check.sh         # 健康检查
│   └── backup.sh               # 备份脚本
├── templates/
│   └── ecosystem.template.js   # PM2配置模板
├── nginx/
│   └── football-game-server.conf # Nginx配置
├── docker/
│   ├── Dockerfile              # Docker镜像
│   └── docker-compose.yml      # Docker编排
└── packages/                   # 部署包输出目录
```

## 🚀 快速开始

### 1. 一键快速部署
```bash
# 交互式部署（推荐新手）
./deployment/quick-deploy.sh

# 选择部署方式、环境、服务等配置
# 脚本会自动完成所有部署步骤
```

### 2. 手动精确部署
```bash
# 部署单个服务到指定区服
./deployment/deploy.sh character server_001 0 production

# 部署全局服务
./deployment/deploy.sh gateway global 0 production

# 部署所有服务到指定区服  
./deployment/deploy.sh all server_001 0 production
```

### 3. Docker 容器部署
```bash
cd deployment/docker
docker-compose up -d
```

## 🔧 核心功能特性

### ✅ 智能打包系统
- **仅打包必要文件**：dist编译文件、配置文件、资源文件
- **排除源代码**：不部署任何TypeScript源码和开发依赖
- **自动压缩**：生成tar.gz压缩包，减少传输时间
- **版本管理**：自动生成带时间戳的版本标识

### ✅ 区服参数支持
- **动态端口分配**：根据服务器ID和实例序号自动计算端口
- **配置文件生成**：自动更新.env.server配置文件
- **PM2配置**：为每个实例生成独立的PM2配置

### ✅ 远程部署能力
- **SSH连接**：支持密钥和密码认证
- **自动上传**：SCP传输部署包到远程服务器
- **远程执行**：自动解压、备份、部署、启动服务
- **回滚机制**：部署失败时自动回滚到上一版本

### ✅ PM2进程管理
- **自动重启**：服务异常时自动重启
- **日志管理**：分离错误日志和输出日志
- **性能监控**：内存使用监控和限制
- **集群支持**：支持多实例负载均衡

### ✅ 健康检查系统
- **多维度检查**：PM2状态、端口连通性、HTTP响应、系统资源
- **自动化验证**：部署后自动执行健康检查
- **报告生成**：生成JSON格式的健康检查报告
- **告警机制**：异常时自动告警

### ✅ 备份恢复机制
- **自动备份**：部署前自动备份当前版本
- **版本保留**：保留最近7个版本的备份
- **一键恢复**：支持快速回滚到任意历史版本
- **增量备份**：仅备份变更文件，节省存储空间

## 📋 部署清单

### 必要文件清单
- [x] **编译文件**: `dist/apps/{service}/` 和 `dist/libs/`
- [x] **配置文件**: `.env`, `.env.service`, `.env.server`  
- [x] **资源文件**: `game-configs/`, `public/`, `assets/`
- [x] **PM2配置**: `ecosystem.config.js`
- [x] **包配置**: 简化的`package.json`

### 排除文件清单
- [x] **源代码**: `src/`, `apps/*/src/`, `libs/*/src/`
- [x] **开发依赖**: `node_modules/` (仅保留生产依赖)
- [x] **构建文件**: `tsconfig.json`, `nest-cli.json`
- [x] **测试文件**: `*.spec.ts`, `test/`, `e2e/`
- [x] **文档文件**: `README.md`, `docs/`

## 🌐 网络架构

### Nginx反向代理配置
```
客户端 → Nginx (80/443) → 后端服务
├── /api/* → Gateway (3000+)
├── /auth/* → Auth (3100+)  
├── /ws/* → Gateway WebSocket
└── /static/* → 静态文件服务
```

### 区服路由策略
```
server001.game.com → 区服001服务
├── /api/character/* → Character-server_001 (3200)
├── /api/economy/* → Economy-server_001 (3300)
├── /api/social/* → Social-server_001 (3400)
├── /api/activity/* → Activity-server_001 (3500)
└── /api/match/* → Match-server_001 (3600)
```

## 📊 监控与运维

### 服务监控
```bash
# 查看所有服务状态
pm2 list

# 实时监控面板
pm2 monit

# 查看服务日志
pm2 logs character-server_001-0

# 重启服务
pm2 restart character-server_001-0
```

### 健康检查
```bash
# 完整健康检查
./deployment/scripts/health-check.sh

# 仅检查PM2状态  
./deployment/scripts/health-check.sh pm2

# 仅检查端口连通性
./deployment/scripts/health-check.sh ports
```

### 备份管理
```bash
# 创建备份
./deployment/scripts/backup.sh create character server_001 0

# 恢复备份
./deployment/scripts/backup.sh restore backup.tar.gz character server_001 0

# 列出备份
./deployment/scripts/backup.sh list
```

## 🔒 安全配置

### 网络安全
- [x] **防火墙配置**：仅开放必要端口
- [x] **SSL/TLS加密**：HTTPS和WSS支持
- [x] **访问限流**：API请求频率限制
- [x] **IP白名单**：限制管理接口访问

### 应用安全  
- [x] **环境变量**：敏感信息通过环境变量配置
- [x] **权限控制**：服务运行在非root用户下
- [x] **日志脱敏**：敏感信息不记录到日志
- [x] **定期更新**：依赖包和系统补丁定期更新

## 🎛️ 环境配置

### 开发环境 (development)
- **服务器**: *************** (内网虚拟机)
- **数据库**: MongoDB单节点
- **缓存**: Redis单节点
- **日志级别**: debug
- **监控**: 基础监控

### 生产环境 (production)  
- **服务器**: 云服务器集群
- **数据库**: MongoDB副本集
- **缓存**: Redis集群
- **日志级别**: info
- **监控**: 全面监控 + 告警

## 📈 性能优化

### 系统优化
- **文件描述符**: 65536
- **网络参数**: TCP优化
- **内存管理**: PM2内存限制
- **日志轮转**: 自动清理旧日志

### 应用优化
- **连接池**: 数据库连接复用
- **缓存策略**: Redis多级缓存
- **负载均衡**: Nginx upstream
- **静态资源**: CDN加速

## 🆘 故障处理

### 常见问题
1. **端口冲突** → 检查端口占用，调整配置
2. **内存不足** → 调整PM2内存限制
3. **服务启动失败** → 查看PM2日志排查
4. **数据库连接失败** → 检查网络和认证配置

### 紧急恢复
```bash
# 快速回滚
./deployment/scripts/backup.sh restore latest.tar.gz service server_001 0

# 重启所有服务
pm2 restart all

# 清理并重新部署
pm2 delete all
./deployment/deploy.sh all server_001 0 production
```

---

## 🎉 部署完成检查清单

- [ ] 所有服务正常启动 (`pm2 list`)
- [ ] 健康检查通过 (`./scripts/health-check.sh`)  
- [ ] 端口正常监听 (`netstat -tlnp`)
- [ ] 日志正常输出 (`pm2 logs`)
- [ ] 数据库连接正常
- [ ] Redis缓存正常
- [ ] Nginx代理正常
- [ ] SSL证书有效
- [ ] 监控告警配置
- [ ] 备份策略启用

**🚀 恭喜！Football Game Server 部署完成！**
