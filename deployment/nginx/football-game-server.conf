# ========================================
# Football Game Server Nginx 配置
# ========================================
# 
# 功能：
# - 反向代理到各个微服务
# - 负载均衡配置
# - SSL 终端
# - 静态资源服务
# - WebSocket 支持
# - 健康检查
# ========================================

# 上游服务器配置
upstream gateway_backend {
    least_conn;
    server 127.0.0.1:3000 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3001 max_fails=3 fail_timeout=30s backup;
    keepalive 32;
}

upstream auth_backend {
    least_conn;
    server 127.0.0.1:3100 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3101 max_fails=3 fail_timeout=30s backup;
    keepalive 32;
}

# 区服服务器配置 (server_001)
upstream character_server_001_backend {
    least_conn;
    server 127.0.0.1:3200 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3201 max_fails=3 fail_timeout=30s backup;
    keepalive 16;
}

upstream economy_server_001_backend {
    least_conn;
    server 127.0.0.1:3300 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3301 max_fails=3 fail_timeout=30s backup;
    keepalive 16;
}

upstream social_server_001_backend {
    least_conn;
    server 127.0.0.1:3400 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3401 max_fails=3 fail_timeout=30s backup;
    keepalive 16;
}

upstream activity_server_001_backend {
    least_conn;
    server 127.0.0.1:3500 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3501 max_fails=3 fail_timeout=30s backup;
    keepalive 16;
}

upstream match_server_001_backend {
    least_conn;
    server 127.0.0.1:3600 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3601 max_fails=3 fail_timeout=30s backup;
    keepalive 16;
}

# 限流配置
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=auth_limit:10m rate=5r/s;
limit_conn_zone $binary_remote_addr zone=conn_limit:10m;

# 主服务器配置
server {
    listen 80;
    listen [::]:80;
    server_name game.yourdomain.com;
    
    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name game.yourdomain.com;
    
    # SSL 配置
    ssl_certificate /etc/ssl/certs/game.yourdomain.com.crt;
    ssl_certificate_key /etc/ssl/private/game.yourdomain.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 基本配置
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    keepalive_timeout 65s;
    send_timeout 60s;
    
    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 日志配置
    access_log /var/log/nginx/football-game-server-access.log;
    error_log /var/log/nginx/football-game-server-error.log;
    
    # 静态资源
    location /static/ {
        alias /opt/football-game-server/public/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Served-By "nginx";
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # API 网关代理
    location /api/ {
        limit_req zone=api_limit burst=20 nodelay;
        limit_conn conn_limit 10;
        
        proxy_pass http://gateway_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
    }
    
    # 认证服务代理
    location /auth/ {
        limit_req zone=auth_limit burst=10 nodelay;
        limit_conn conn_limit 5;
        
        proxy_pass http://auth_backend/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # WebSocket 支持
    location /ws/ {
        proxy_pass http://gateway_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket 特殊配置
        proxy_read_timeout 86400s;
        proxy_send_timeout 86400s;
        proxy_connect_timeout 30s;
    }
    
    # 管理面板 (可选)
    location /admin/ {
        auth_basic "Admin Area";
        auth_basic_user_file /etc/nginx/.htpasswd;
        
        proxy_pass http://gateway_backend/admin/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 默认路由到网关
    location / {
        proxy_pass http://gateway_backend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}

# 区服专用配置 (server_001)
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name server001.game.yourdomain.com;
    
    # SSL 配置 (与主服务器相同)
    ssl_certificate /etc/ssl/certs/game.yourdomain.com.crt;
    ssl_certificate_key /etc/ssl/private/game.yourdomain.com.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    
    # 角色服务
    location /api/character/ {
        proxy_pass http://character_server_001_backend/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Server-ID "server_001";
    }
    
    # 经济服务
    location /api/economy/ {
        proxy_pass http://economy_server_001_backend/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Server-ID "server_001";
    }
    
    # 社交服务
    location /api/social/ {
        proxy_pass http://social_server_001_backend/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Server-ID "server_001";
    }
    
    # 活动服务
    location /api/activity/ {
        proxy_pass http://activity_server_001_backend/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Server-ID "server_001";
    }
    
    # 比赛服务
    location /api/match/ {
        proxy_pass http://match_server_001_backend/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Server-ID "server_001";
    }
}
