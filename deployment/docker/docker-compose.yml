# ========================================
# Football Game Server Docker Compose 配置
# ========================================
# 
# 支持：
# - 全局服务和分区分服服务
# - 服务发现和负载均衡
# - 数据库和缓存服务
# - 监控和日志收集
# ========================================

version: '3.8'

# ========== 网络配置 ==========
networks:
  football-game-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ========== 数据卷配置 ==========
volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  logs_data:
    driver: local
  game_configs:
    driver: local

# ========== 服务配置 ==========
services:
  # ========== 基础设施服务 ==========
  
  # MongoDB 数据库
  mongodb:
    image: mongo:6.0
    container_name: football-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: ${MONGODB_ROOT_PASSWORD:-admin123}
      MONGO_INITDB_DATABASE: football_game
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongodb/init:/docker-entrypoint-initdb.d
    networks:
      football-game-network:
        ipv4_address: ***********
    command: mongod --replSet rs0 --bind_ip_all
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis 缓存
  redis:
    image: redis:7.0-alpine
    container_name: football-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      football-game-network:
        ipv4_address: ***********
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ========== 全局服务 ==========
  
  # API 网关
  gateway:
    build:
      context: ../
      dockerfile: deployment/docker/Dockerfile
      target: gateway
      args:
        SERVICE_NAME: gateway
        PORT: 3000
    container_name: football-gateway
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3000
      SERVICE_NAME: gateway
      SERVER_ID: global
      INSTANCE_INDEX: 0
      MONGODB_URI: mongodb://admin:${MONGODB_ROOT_PASSWORD:-admin123}@mongodb:27017/football_game?authSource=admin
      REDIS_URL: redis://redis:6379
    ports:
      - "3000:3000"
    volumes:
      - logs_data:/app/logs
      - game_configs:/app/game-configs
    networks:
      football-game-network:
        ipv4_address: ***********
    depends_on:
      - mongodb
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 认证服务
  auth:
    build:
      context: ../
      dockerfile: deployment/docker/Dockerfile
      target: auth
      args:
        SERVICE_NAME: auth
        PORT: 3100
    container_name: football-auth
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3100
      SERVICE_NAME: auth
      SERVER_ID: global
      INSTANCE_INDEX: 0
      MONGODB_URI: mongodb://admin:${MONGODB_ROOT_PASSWORD:-admin123}@mongodb:27017/football_game?authSource=admin
      REDIS_URL: redis://redis:6379
    ports:
      - "3100:3100"
    volumes:
      - logs_data:/app/logs
    networks:
      football-game-network:
        ipv4_address: ***********
    depends_on:
      - mongodb
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3100/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ========== 区服服务 (server_001) ==========
  
  # 角色服务
  character-server-001:
    build:
      context: ../
      dockerfile: deployment/docker/Dockerfile
      target: character
      args:
        SERVICE_NAME: character
        PORT: 3200
    container_name: football-character-server-001
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3200
      SERVICE_NAME: character
      SERVER_ID: server_001
      INSTANCE_INDEX: 0
      MONGODB_URI: mongodb://admin:${MONGODB_ROOT_PASSWORD:-admin123}@mongodb:27017/football_game_server_001?authSource=admin
      REDIS_URL: redis://redis:6379
    ports:
      - "3200:3200"
    volumes:
      - logs_data:/app/logs
      - game_configs:/app/game-configs
    networks:
      football-game-network:
        ipv4_address: ***********
    depends_on:
      - mongodb
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3200/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 经济服务
  economy-server-001:
    build:
      context: ../
      dockerfile: deployment/docker/Dockerfile
      target: economy
      args:
        SERVICE_NAME: economy
        PORT: 3300
    container_name: football-economy-server-001
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3300
      SERVICE_NAME: economy
      SERVER_ID: server_001
      INSTANCE_INDEX: 0
      MONGODB_URI: mongodb://admin:${MONGODB_ROOT_PASSWORD:-admin123}@mongodb:27017/football_game_server_001?authSource=admin
      REDIS_URL: redis://redis:6379
    ports:
      - "3300:3300"
    volumes:
      - logs_data:/app/logs
    networks:
      football-game-network:
        ipv4_address: ***********
    depends_on:
      - mongodb
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3300/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 社交服务
  social-server-001:
    build:
      context: ../
      dockerfile: deployment/docker/Dockerfile
      target: social
      args:
        SERVICE_NAME: social
        PORT: 3400
    container_name: football-social-server-001
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3400
      SERVICE_NAME: social
      SERVER_ID: server_001
      INSTANCE_INDEX: 0
      MONGODB_URI: mongodb://admin:${MONGODB_ROOT_PASSWORD:-admin123}@mongodb:27017/football_game_server_001?authSource=admin
      REDIS_URL: redis://redis:6379
    ports:
      - "3400:3400"
    volumes:
      - logs_data:/app/logs
    networks:
      football-game-network:
        ipv4_address: ***********
    depends_on:
      - mongodb
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3400/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 活动服务
  activity-server-001:
    build:
      context: ../
      dockerfile: deployment/docker/Dockerfile
      target: activity
      args:
        SERVICE_NAME: activity
        PORT: 3500
    container_name: football-activity-server-001
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3500
      SERVICE_NAME: activity
      SERVER_ID: server_001
      INSTANCE_INDEX: 0
      MONGODB_URI: mongodb://admin:${MONGODB_ROOT_PASSWORD:-admin123}@mongodb:27017/football_game_server_001?authSource=admin
      REDIS_URL: redis://redis:6379
    ports:
      - "3500:3500"
    volumes:
      - logs_data:/app/logs
    networks:
      football-game-network:
        ipv4_address: ***********
    depends_on:
      - mongodb
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3500/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 比赛服务
  match-server-001:
    build:
      context: ../
      dockerfile: deployment/docker/Dockerfile
      target: match
      args:
        SERVICE_NAME: match
        PORT: 3600
    container_name: football-match-server-001
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3600
      SERVICE_NAME: match
      SERVER_ID: server_001
      INSTANCE_INDEX: 0
      MONGODB_URI: mongodb://admin:${MONGODB_ROOT_PASSWORD:-admin123}@mongodb:27017/football_game_server_001?authSource=admin
      REDIS_URL: redis://redis:6379
    ports:
      - "3600:3600"
    volumes:
      - logs_data:/app/logs
    networks:
      football-game-network:
        ipv4_address: ***********
    depends_on:
      - mongodb
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3600/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ========== 监控服务 ==========
  
  # Nginx 反向代理
  nginx:
    image: nginx:1.24-alpine
    container_name: football-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/football-game-server.conf:/etc/nginx/conf.d/default.conf
      - ./nginx/ssl:/etc/ssl/certs
      - logs_data:/var/log/nginx
    networks:
      football-game-network:
        ipv4_address: ************
    depends_on:
      - gateway
      - auth
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
