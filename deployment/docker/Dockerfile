# ========================================
# Football Game Server Docker 镜像
# ========================================
# 
# 多阶段构建，支持：
# - 编译阶段：构建 TypeScript 代码
# - 运行阶段：精简的生产环境镜像
# - 支持多服务构建
# ========================================

# ========== 构建阶段 ==========
FROM node:18.17.0-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装构建依赖
RUN apk add --no-cache python3 make g++ git

# 复制 package 文件
COPY package*.json ./
COPY tsconfig*.json ./
COPY nest-cli.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY libs/ ./libs/
COPY apps/ ./apps/

# 构建应用
ARG SERVICE_NAME=character
RUN npm run build:${SERVICE_NAME}

# ========== 运行阶段 ==========
FROM node:18.17.0-alpine AS runtime

# 安装运行时依赖
RUN apk add --no-cache \
    dumb-init \
    curl \
    tzdata \
    && rm -rf /var/cache/apk/*

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建应用用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S football -u 1001 -G nodejs

# 设置工作目录
WORKDIR /app

# 复制构建产物
ARG SERVICE_NAME=character
COPY --from=builder --chown=football:nodejs /app/dist/apps/${SERVICE_NAME} ./
COPY --from=builder --chown=football:nodejs /app/dist/libs ./libs/
COPY --from=builder --chown=football:nodejs /app/node_modules ./node_modules/

# 复制配置文件
COPY --chown=football:nodejs .env* ./

# 复制游戏静态配置文件（核心配置数据）
COPY --chown=football:nodejs libs/game-config/src/data/ ./game-config-data/

# 复制其他资源文件
COPY --chown=football:nodejs game-configs/ ./game-configs/ 2>/dev/null || true
COPY --chown=football:nodejs public/ ./public/ 2>/dev/null || true

# 创建日志目录
RUN mkdir -p /app/logs && chown football:nodejs /app/logs

# 设置环境变量
ENV NODE_ENV=production
ENV SERVICE_NAME=${SERVICE_NAME}

# 暴露端口
ARG PORT=3000
EXPOSE ${PORT}

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:${PORT}/health || exit 1

# 切换到应用用户
USER football

# 启动应用
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "main.js"]

# ========== 开发阶段 ==========
FROM node:18.17.0-alpine AS development

WORKDIR /app

# 安装开发依赖
RUN apk add --no-cache python3 make g++ git

# 复制 package 文件
COPY package*.json ./
RUN npm ci

# 复制源代码
COPY . .

# 暴露端口
EXPOSE 3000

# 开发模式启动
CMD ["npm", "run", "start:dev"]

# ========== 多服务构建示例 ==========

# Gateway 服务
FROM runtime AS gateway
ARG SERVICE_NAME=gateway
ARG PORT=3000
ENV SERVICE_NAME=${SERVICE_NAME}
EXPOSE ${PORT}

# Auth 服务
FROM runtime AS auth
ARG SERVICE_NAME=auth
ARG PORT=3100
ENV SERVICE_NAME=${SERVICE_NAME}
EXPOSE ${PORT}

# Character 服务
FROM runtime AS character
ARG SERVICE_NAME=character
ARG PORT=3200
ENV SERVICE_NAME=${SERVICE_NAME}
EXPOSE ${PORT}

# Economy 服务
FROM runtime AS economy
ARG SERVICE_NAME=economy
ARG PORT=3300
ENV SERVICE_NAME=${SERVICE_NAME}
EXPOSE ${PORT}

# Social 服务
FROM runtime AS social
ARG SERVICE_NAME=social
ARG PORT=3400
ENV SERVICE_NAME=${SERVICE_NAME}
EXPOSE ${PORT}

# Activity 服务
FROM runtime AS activity
ARG SERVICE_NAME=activity
ARG PORT=3500
ENV SERVICE_NAME=${SERVICE_NAME}
EXPOSE ${PORT}

# Match 服务
FROM runtime AS match
ARG SERVICE_NAME=match
ARG PORT=3600
ENV SERVICE_NAME=${SERVICE_NAME}
EXPOSE ${PORT}
