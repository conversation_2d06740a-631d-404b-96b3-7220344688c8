# ========================================
# 开发环境服务器配置
# ========================================

# 服务器连接信息
server_host="***************"
server_user="root"
server_port="22"
server_key=""  # 使用密码认证或默认密钥

# 服务器环境
server_environment="development"
server_region="local"
server_zone="local"

# 资源配置
server_cpu_cores=4
server_memory_gb=8
server_disk_gb=50

# 网络配置
server_public_ip="***************"
server_private_ip="***************"
server_domain="dev.local"

# 负载均衡配置
load_balancer_enabled=false
load_balancer_type="nginx"
ssl_enabled=false

# 数据库配置
database_host="***************"
database_port=27017
database_replica_set="rs0"
database_ssl_enabled=false

# Redis 配置
redis_host="***************"
redis_port=6379
redis_cluster_enabled=false
redis_ssl_enabled=false

# 监控配置
monitoring_enabled=true
prometheus_enabled=false
grafana_enabled=false
alertmanager_enabled=false

# 日志配置
log_level="debug"
log_rotation_enabled=false
log_retention_days=7
centralized_logging=false

# 安全配置
firewall_enabled=false
fail2ban_enabled=false
security_updates_enabled=false
intrusion_detection_enabled=false

# 备份配置
backup_enabled=false
backup_schedule="0 4 * * *"  # 每天凌晨4点
backup_retention_days=7
backup_encryption_enabled=false

# 性能配置
max_open_files=4096
tcp_keepalive_enabled=false
kernel_optimization_enabled=false

# 容器配置
docker_enabled=false
kubernetes_enabled=false

# CDN 配置
cdn_enabled=false
