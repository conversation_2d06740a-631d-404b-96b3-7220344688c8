# ========================================
# 生产环境服务器配置
# ========================================

# 服务器连接信息
server_host="your-production-server.com"
server_user="deploy"
server_port="22"
server_key="~/.ssh/football-game-server-prod"

# 服务器环境
server_environment="production"
server_region="asia-east1"
server_zone="asia-east1-a"

# 资源配置
server_cpu_cores=8
server_memory_gb=16
server_disk_gb=100

# 网络配置
server_public_ip="*******"
server_private_ip="*********"
server_domain="game.yourdomain.com"

# 负载均衡配置
load_balancer_enabled=true
load_balancer_type="nginx"
ssl_enabled=true
ssl_cert_path="/etc/ssl/certs/game.yourdomain.com.crt"
ssl_key_path="/etc/ssl/private/game.yourdomain.com.key"

# 数据库配置
database_host="prod-db.yourdomain.com"
database_port=27017
database_replica_set="rs0"
database_ssl_enabled=true

# Redis 配置
redis_host="prod-redis.yourdomain.com"
redis_port=6379
redis_cluster_enabled=true
redis_ssl_enabled=true

# 监控配置
monitoring_enabled=true
prometheus_enabled=true
grafana_enabled=true
alertmanager_enabled=true

# 日志配置
log_level="info"
log_rotation_enabled=true
log_retention_days=30
centralized_logging=true

# 安全配置
firewall_enabled=true
fail2ban_enabled=true
security_updates_enabled=true
intrusion_detection_enabled=true

# 备份配置
backup_enabled=true
backup_schedule="0 2 * * *"  # 每天凌晨2点
backup_retention_days=30
backup_encryption_enabled=true

# 性能配置
max_open_files=65536
tcp_keepalive_enabled=true
kernel_optimization_enabled=true

# 容器配置 (如果使用 Docker)
docker_enabled=false
kubernetes_enabled=false
container_registry="your-registry.com"

# CDN 配置
cdn_enabled=true
cdn_provider="cloudflare"
static_assets_cdn="https://cdn.yourdomain.com"
