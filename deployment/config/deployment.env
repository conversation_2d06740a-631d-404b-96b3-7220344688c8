# ========================================
# Football Game Server 部署配置
# ========================================

# 远程服务器配置
REMOTE_SERVER=production

# 部署环境
DEPLOYMENT_ENVIRONMENT=production

# 服务器列表配置
# 可以配置多个服务器环境：development, staging, production
AVAILABLE_SERVERS="development staging production"

# 默认配置
DEFAULT_NODE_VERSION="18.17.0"
DEFAULT_PM2_VERSION="latest"

# 部署路径配置
REMOTE_DEPLOY_PATH="/opt/football-game-server"
REMOTE_LOG_PATH="/var/log/football-game-server"
REMOTE_BACKUP_PATH="/opt/football-game-server/backups"

# 备份配置
BACKUP_RETENTION_DAYS=7
AUTO_BACKUP_BEFORE_DEPLOY=true

# 健康检查配置
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_TIMEOUT=30
HEALTH_CHECK_RETRIES=3

# 部署策略
DEPLOYMENT_STRATEGY="rolling"  # rolling, blue-green, recreate
ROLLING_UPDATE_MAX_UNAVAILABLE=1
ROLLING_UPDATE_MAX_SURGE=1

# 监控配置
MONITORING_ENABLED=true
METRICS_COLLECTION_ENABLED=true
LOG_AGGREGATION_ENABLED=true

# 安全配置
SSH_KEY_PATH="~/.ssh/football-game-server"
STRICT_HOST_KEY_CHECKING=false
CONNECTION_TIMEOUT=30

# 性能配置
PARALLEL_DEPLOYMENTS=3
UPLOAD_TIMEOUT=300
DEPLOYMENT_TIMEOUT=600

# 通知配置
NOTIFICATION_ENABLED=false
NOTIFICATION_WEBHOOK=""
NOTIFICATION_CHANNELS="slack,email"

# 调试配置
DEBUG_MODE=false
VERBOSE_LOGGING=true
DRY_RUN=false
