#!/bin/bash

# ========================================
# Football Game Server 部署脚本
# ========================================
# 
# 功能：
# - 支持全局服务（gateway、auth）和分区分服服务部署
# - 自动打包编译文件和必要资源
# - 远程服务器连接和上传
# - PM2 进程管理
# - 区服参数和实例序号支持
#
# 使用方法：
# ./deploy.sh [service] [server_id] [instance_index] [environment]
#
# 示例：
# ./deploy.sh gateway global 0 production
# ./deploy.sh character server_001 0 production
# ./deploy.sh all server_001 0 production
# ========================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOYMENT_DIR="$SCRIPT_DIR"
BUILD_DIR="$PROJECT_ROOT/dist"
PACKAGE_DIR="$DEPLOYMENT_DIR/packages"

# 默认配置
DEFAULT_ENVIRONMENT="production"
DEFAULT_SERVER_ID="server_001"
DEFAULT_INSTANCE_INDEX="0"

# 服务配置
declare -A SERVICE_TYPES=(
    ["gateway"]="global"
    ["auth"]="global"
    ["character"]="regional"
    ["economy"]="regional"
    ["social"]="regional"
    ["activity"]="regional"
    ["match"]="regional"
)

declare -A BASE_PORTS=(
    ["gateway"]=3000
    ["auth"]=3100
    ["character"]=3200
    ["economy"]=3300
    ["social"]=3400
    ["activity"]=3500
    ["match"]=3600
)

# 解析命令行参数
SERVICE_NAME=${1:-""}
SERVER_ID=${2:-$DEFAULT_SERVER_ID}
INSTANCE_INDEX=${3:-$DEFAULT_INSTANCE_INDEX}
ENVIRONMENT=${4:-$DEFAULT_ENVIRONMENT}

# 验证参数
if [[ -z "$SERVICE_NAME" ]]; then
    log_error "请指定服务名称"
    echo "用法: $0 <service> [server_id] [instance_index] [environment]"
    echo "服务: gateway, auth, character, economy, social, activity, match, all"
    exit 1
fi

# 加载部署配置
load_deployment_config() {
    local config_file="$DEPLOYMENT_DIR/config/deployment.env"
    if [[ -f "$config_file" ]]; then
        source "$config_file"
        log_info "已加载部署配置: $config_file"
    else
        log_warning "部署配置文件不存在: $config_file"
    fi
}

# 计算服务端口
calculate_port() {
    local service=$1
    local server_id=$2
    local instance_index=$3
    
    local base_port=${BASE_PORTS[$service]}
    if [[ -z "$base_port" ]]; then
        log_error "未知服务: $service"
        return 1
    fi
    
    # 全局服务端口计算
    if [[ "${SERVICE_TYPES[$service]}" == "global" ]]; then
        echo $((base_port + instance_index))
        return 0
    fi
    
    # 区服服务端口计算
    # 提取服务器编号 (server_001 -> 1)
    local server_num=$(echo "$server_id" | sed 's/server_0*//')
    if [[ -z "$server_num" ]]; then
        server_num=1
    fi
    
    # 端口 = 基础端口 + (服务器编号-1)*10 + 实例序号
    local port=$((base_port + (server_num - 1) * 10 + instance_index))
    echo $port
}

# 构建服务
build_service() {
    local service=$1
    
    log_info "开始构建服务: $service"
    
    cd "$PROJECT_ROOT"
    
    if [[ "$service" == "all" ]]; then
        npm run build:all
    else
        npm run "build:$service"
    fi
    
    if [[ $? -eq 0 ]]; then
        log_success "服务构建完成: $service"
    else
        log_error "服务构建失败: $service"
        exit 1
    fi
}

# 打包服务文件
package_service() {
    local service=$1
    local server_id=$2
    local instance_index=$3
    local environment=$4
    
    log_info "开始打包服务: $service (服务器: $server_id, 实例: $instance_index)"
    
    # 创建打包目录
    local package_name="${service}-${server_id}-${instance_index}-${environment}"
    local service_package_dir="$PACKAGE_DIR/$package_name"
    
    rm -rf "$service_package_dir"
    mkdir -p "$service_package_dir"
    
    # 复制编译文件
    if [[ -d "$BUILD_DIR/apps/$service" ]]; then
        cp -r "$BUILD_DIR/apps/$service" "$service_package_dir/"
        log_info "已复制编译文件: apps/$service"
    fi
    
    # 复制共享库编译文件
    if [[ -d "$BUILD_DIR/libs" ]]; then
        cp -r "$BUILD_DIR/libs" "$service_package_dir/"
        log_info "已复制共享库文件: libs"
    fi
    
    # 复制必要的配置文件
    copy_config_files "$service" "$service_package_dir" "$server_id" "$instance_index" "$environment"
    
    # 复制资源文件
    copy_resource_files "$service" "$service_package_dir"
    
    # 生成 PM2 配置
    generate_pm2_config "$service" "$service_package_dir" "$server_id" "$instance_index" "$environment"
    
    # 创建压缩包
    cd "$PACKAGE_DIR"
    tar -czf "${package_name}.tar.gz" "$package_name"
    
    log_success "服务打包完成: ${package_name}.tar.gz"
    echo "$PACKAGE_DIR/${package_name}.tar.gz"
}

# 复制配置文件
copy_config_files() {
    local service=$1
    local target_dir=$2
    local server_id=$3
    local instance_index=$4
    local environment=$5
    
    # 复制根目录 .env 文件
    if [[ -f "$PROJECT_ROOT/.env" ]]; then
        cp "$PROJECT_ROOT/.env" "$target_dir/"
        log_info "已复制根目录 .env 文件"
    fi
    
    # 复制服务特定的 .env 文件
    local service_env_file="$PROJECT_ROOT/apps/$service/.env"
    if [[ -f "$service_env_file" ]]; then
        cp "$service_env_file" "$target_dir/.env.service"
        log_info "已复制服务 .env 文件: $service"
    fi
    
    # 复制区服配置文件
    local server_env_file="$PROJECT_ROOT/apps/$service/.env.server"
    if [[ -f "$server_env_file" ]]; then
        cp "$server_env_file" "$target_dir/.env.server"
        # 更新区服配置
        sed -i "s/SERVER_ID=.*/SERVER_ID=$server_id/" "$target_dir/.env.server"
        sed -i "s/INSTANCE_INDEX=.*/INSTANCE_INDEX=$instance_index/" "$target_dir/.env.server"
        log_info "已复制并更新区服配置文件"
    fi
    
    # 复制 package.json (仅生产依赖)
    if [[ -f "$PROJECT_ROOT/package.json" ]]; then
        # 创建简化的 package.json
        cat > "$target_dir/package.json" << EOF
{
  "name": "football-game-server-$service",
  "version": "1.0.0",
  "description": "Football Game Server - $service",
  "main": "$service/main.js",
  "scripts": {
    "start": "node $service/main.js",
    "start:prod": "NODE_ENV=production node $service/main.js"
  },
  "dependencies": {}
}
EOF
        log_info "已生成简化的 package.json"
    fi
}

# 复制资源文件
copy_resource_files() {
    local service=$1
    local target_dir=$2
    
    # 复制游戏配置文件
    if [[ -d "$PROJECT_ROOT/game-configs" ]]; then
        cp -r "$PROJECT_ROOT/game-configs" "$target_dir/"
        log_info "已复制游戏配置文件"
    fi
    
    # 复制静态资源文件
    if [[ -d "$PROJECT_ROOT/public" ]]; then
        cp -r "$PROJECT_ROOT/public" "$target_dir/"
        log_info "已复制静态资源文件"
    fi
    
    # 复制服务特定资源
    local service_assets_dir="$PROJECT_ROOT/apps/$service/assets"
    if [[ -d "$service_assets_dir" ]]; then
        cp -r "$service_assets_dir" "$target_dir/assets"
        log_info "已复制服务资源文件: $service"
    fi
}

# 生成 PM2 配置
generate_pm2_config() {
    local service=$1
    local target_dir=$2
    local server_id=$3
    local instance_index=$4
    local environment=$5

    local port=$(calculate_port "$service" "$server_id" "$instance_index")
    local app_name="${service}-${server_id}-${instance_index}"

    cat > "$target_dir/ecosystem.config.js" << EOF
module.exports = {
  apps: [{
    name: '$app_name',
    script: '$service/main.js',
    cwd: '/opt/football-game-server/$app_name',
    instances: 1,
    exec_mode: 'fork',
    env: {
      NODE_ENV: '$environment',
      PORT: $port,
      SERVER_ID: '$server_id',
      INSTANCE_INDEX: $instance_index,
      SERVICE_NAME: '$service'
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: $port,
      SERVER_ID: '$server_id',
      INSTANCE_INDEX: $instance_index,
      SERVICE_NAME: '$service'
    },
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    error_file: '/var/log/football-game-server/$app_name-error.log',
    out_file: '/var/log/football-game-server/$app_name-out.log',
    log_file: '/var/log/football-game-server/$app_name-combined.log',
    time: true,
    autorestart: true,
    max_restarts: 10,
    min_uptime: '10s',
    max_memory_restart: '1G',
    watch: false,
    ignore_watch: ['node_modules', 'logs'],
    source_map_support: false,
    instance_var: 'INSTANCE_ID'
  }]
};
EOF

    log_info "已生成 PM2 配置文件: $app_name"
}

# 上传到远程服务器
upload_to_server() {
    local package_file=$1
    local server_config=$2

    log_info "开始上传到远程服务器: $server_config"

    # 加载服务器配置
    local server_host server_user server_port server_key
    load_server_config "$server_config"

    if [[ -z "$server_host" ]]; then
        log_error "服务器配置不完整: $server_config"
        return 1
    fi

    # 构建 SSH 连接参数
    local ssh_opts="-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null"
    if [[ -n "$server_port" ]]; then
        ssh_opts="$ssh_opts -p $server_port"
    fi
    if [[ -n "$server_key" ]]; then
        ssh_opts="$ssh_opts -i $server_key"
    fi

    # 上传文件
    log_info "上传文件到服务器..."
    scp $ssh_opts "$package_file" "$server_user@$server_host:/tmp/"

    if [[ $? -eq 0 ]]; then
        log_success "文件上传完成"
        return 0
    else
        log_error "文件上传失败"
        return 1
    fi
}

# 远程部署
remote_deploy() {
    local package_file=$1
    local server_config=$2
    local service=$3
    local server_id=$4
    local instance_index=$5

    log_info "开始远程部署..."

    # 加载服务器配置
    local server_host server_user server_port server_key
    load_server_config "$server_config"

    # 构建 SSH 连接参数
    local ssh_opts="-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null"
    if [[ -n "$server_port" ]]; then
        ssh_opts="$ssh_opts -p $server_port"
    fi
    if [[ -n "$server_key" ]]; then
        ssh_opts="$ssh_opts -i $server_key"
    fi

    local package_name=$(basename "$package_file" .tar.gz)
    local app_name="${service}-${server_id}-${instance_index}"
    local deploy_path="/opt/football-game-server/$app_name"

    # 执行远程部署命令
    ssh $ssh_opts "$server_user@$server_host" << EOF
        set -e

        # 创建部署目录
        sudo mkdir -p $deploy_path
        sudo mkdir -p /var/log/football-game-server

        # 解压文件
        cd /tmp
        tar -xzf $package_name.tar.gz

        # 停止旧服务
        pm2 stop $app_name || true
        pm2 delete $app_name || true

        # 备份旧版本
        if [[ -d $deploy_path ]]; then
            sudo mv $deploy_path ${deploy_path}.backup.\$(date +%Y%m%d_%H%M%S)
        fi

        # 部署新版本
        sudo mv $package_name $deploy_path
        sudo chown -R \$USER:\$USER $deploy_path

        # 安装依赖 (如果需要)
        cd $deploy_path
        if [[ -f package.json ]]; then
            npm install --production --silent
        fi

        # 启动服务
        pm2 start ecosystem.config.js --env production
        pm2 save

        # 清理临时文件
        rm -f /tmp/$package_name.tar.gz

        echo "部署完成: $app_name"
EOF

    if [[ $? -eq 0 ]]; then
        log_success "远程部署完成: $app_name"
    else
        log_error "远程部署失败: $app_name"
        return 1
    fi
}

# 加载服务器配置
load_server_config() {
    local config_name=$1
    local config_file="$DEPLOYMENT_DIR/config/servers/$config_name.env"

    if [[ -f "$config_file" ]]; then
        source "$config_file"
        log_info "已加载服务器配置: $config_name"
    else
        log_error "服务器配置文件不存在: $config_file"
        return 1
    fi
}

# 主函数
main() {
    log_info "开始部署 Football Game Server"
    log_info "服务: $SERVICE_NAME, 服务器: $SERVER_ID, 实例: $INSTANCE_INDEX, 环境: $ENVIRONMENT"

    # 加载部署配置
    load_deployment_config

    # 创建必要目录
    mkdir -p "$PACKAGE_DIR"

    if [[ "$SERVICE_NAME" == "all" ]]; then
        # 部署所有服务
        for service in gateway auth character economy social activity match; do
            log_info "处理服务: $service"

            # 构建服务
            build_service "$service"

            # 打包服务
            local package_file=$(package_service "$service" "$SERVER_ID" "$INSTANCE_INDEX" "$ENVIRONMENT")

            # 上传和部署 (如果配置了远程服务器)
            if [[ -n "$REMOTE_SERVER" ]]; then
                upload_to_server "$package_file" "$REMOTE_SERVER"
                remote_deploy "$package_file" "$REMOTE_SERVER" "$service" "$SERVER_ID" "$INSTANCE_INDEX"
            fi
        done
    else
        # 部署单个服务
        build_service "$SERVICE_NAME"
        local package_file=$(package_service "$SERVICE_NAME" "$SERVER_ID" "$INSTANCE_INDEX" "$ENVIRONMENT")

        # 上传和部署 (如果配置了远程服务器)
        if [[ -n "$REMOTE_SERVER" ]]; then
            upload_to_server "$package_file" "$REMOTE_SERVER"
            remote_deploy "$package_file" "$REMOTE_SERVER" "$SERVICE_NAME" "$SERVER_ID" "$INSTANCE_INDEX"
        fi
    fi

    log_success "部署完成!"
}

# 执行主函数
main "$@"
