#!/bin/bash

# ========================================
# 服务健康检查脚本
# ========================================
# 
# 功能：
# - 检查所有服务的运行状态
# - 验证端口连通性
# - 检查服务响应时间
# - 生成健康检查报告
# ========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
HEALTH_CHECK_TIMEOUT=10
REPORT_FILE="/tmp/health-check-$(date +%Y%m%d_%H%M%S).json"

# 服务配置
declare -A SERVICES=(
    ["gateway-global-0"]="3000"
    ["auth-global-0"]="3100"
    ["character-server_001-0"]="3200"
    ["economy-server_001-0"]="3300"
    ["social-server_001-0"]="3400"
    ["activity-server_001-0"]="3500"
    ["match-server_001-0"]="3600"
)

# 检查 PM2 进程状态
check_pm2_status() {
    local service_name=$1
    
    log_info "检查 PM2 进程状态: $service_name"
    
    local status=$(pm2 jlist | jq -r ".[] | select(.name==\"$service_name\") | .pm2_env.status" 2>/dev/null || echo "not_found")
    
    case $status in
        "online")
            log_success "$service_name: 运行中"
            return 0
            ;;
        "stopped")
            log_error "$service_name: 已停止"
            return 1
            ;;
        "errored")
            log_error "$service_name: 错误状态"
            return 1
            ;;
        "not_found")
            log_error "$service_name: 进程不存在"
            return 1
            ;;
        *)
            log_warning "$service_name: 未知状态 ($status)"
            return 1
            ;;
    esac
}

# 检查端口连通性
check_port_connectivity() {
    local service_name=$1
    local port=$2
    local host=${3:-"localhost"}
    
    log_info "检查端口连通性: $service_name ($host:$port)"
    
    if timeout $HEALTH_CHECK_TIMEOUT bash -c "echo >/dev/tcp/$host/$port" 2>/dev/null; then
        log_success "$service_name: 端口 $port 可访问"
        return 0
    else
        log_error "$service_name: 端口 $port 不可访问"
        return 1
    fi
}

# 检查 HTTP 健康端点
check_http_health() {
    local service_name=$1
    local port=$2
    local host=${3:-"localhost"}
    local endpoint=${4:-"/health"}
    
    log_info "检查 HTTP 健康端点: $service_name"
    
    local url="http://$host:$port$endpoint"
    local response_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time $HEALTH_CHECK_TIMEOUT "$url" 2>/dev/null || echo "000")
    
    if [[ "$response_code" == "200" ]]; then
        log_success "$service_name: HTTP 健康检查通过 ($response_code)"
        return 0
    else
        log_error "$service_name: HTTP 健康检查失败 ($response_code)"
        return 1
    fi
}

# 检查响应时间
check_response_time() {
    local service_name=$1
    local port=$2
    local host=${3:-"localhost"}
    local endpoint=${4:-"/health"}
    
    log_info "检查响应时间: $service_name"
    
    local url="http://$host:$port$endpoint"
    local response_time=$(curl -s -o /dev/null -w "%{time_total}" --max-time $HEALTH_CHECK_TIMEOUT "$url" 2>/dev/null || echo "999")
    
    # 转换为毫秒
    local response_time_ms=$(echo "$response_time * 1000" | bc -l 2>/dev/null || echo "999000")
    response_time_ms=${response_time_ms%.*}  # 去掉小数部分
    
    if [[ $response_time_ms -lt 1000 ]]; then
        log_success "$service_name: 响应时间正常 (${response_time_ms}ms)"
        return 0
    elif [[ $response_time_ms -lt 3000 ]]; then
        log_warning "$service_name: 响应时间较慢 (${response_time_ms}ms)"
        return 0
    else
        log_error "$service_name: 响应时间过慢 (${response_time_ms}ms)"
        return 1
    fi
}

# 检查系统资源
check_system_resources() {
    log_info "检查系统资源使用情况"
    
    # CPU 使用率
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
    log_info "CPU 使用率: ${cpu_usage}%"
    
    # 内存使用率
    local memory_info=$(free | grep Mem)
    local total_memory=$(echo $memory_info | awk '{print $2}')
    local used_memory=$(echo $memory_info | awk '{print $3}')
    local memory_usage=$(echo "scale=1; $used_memory * 100 / $total_memory" | bc)
    log_info "内存使用率: ${memory_usage}%"
    
    # 磁盘使用率
    local disk_usage=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
    log_info "磁盘使用率: ${disk_usage}%"
    
    # 检查资源警告
    if [[ $(echo "$cpu_usage > 80" | bc) -eq 1 ]]; then
        log_warning "CPU 使用率过高: ${cpu_usage}%"
    fi
    
    if [[ $(echo "$memory_usage > 80" | bc) -eq 1 ]]; then
        log_warning "内存使用率过高: ${memory_usage}%"
    fi
    
    if [[ $disk_usage -gt 80 ]]; then
        log_warning "磁盘使用率过高: ${disk_usage}%"
    fi
}

# 检查日志错误
check_log_errors() {
    local service_name=$1
    local log_file="/var/log/football-game-server/${service_name}-error.log"
    
    if [[ -f "$log_file" ]]; then
        local error_count=$(tail -n 100 "$log_file" | grep -i "error\|exception\|fatal" | wc -l)
        if [[ $error_count -gt 0 ]]; then
            log_warning "$service_name: 发现 $error_count 个错误日志"
        else
            log_success "$service_name: 无错误日志"
        fi
    else
        log_warning "$service_name: 日志文件不存在"
    fi
}

# 生成健康检查报告
generate_health_report() {
    log_info "生成健康检查报告: $REPORT_FILE"
    
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    local hostname=$(hostname)
    
    cat > "$REPORT_FILE" << EOF
{
  "timestamp": "$timestamp",
  "hostname": "$hostname",
  "overall_status": "healthy",
  "services": {},
  "system": {
    "cpu_usage": "0%",
    "memory_usage": "0%",
    "disk_usage": "0%"
  },
  "summary": {
    "total_services": 0,
    "healthy_services": 0,
    "unhealthy_services": 0
  }
}
EOF
    
    log_success "健康检查报告已生成"
}

# 执行完整健康检查
run_full_health_check() {
    log_info "开始执行完整健康检查..."
    
    local total_services=0
    local healthy_services=0
    local unhealthy_services=0
    
    # 检查系统资源
    check_system_resources
    
    # 检查每个服务
    for service_name in "${!SERVICES[@]}"; do
        local port=${SERVICES[$service_name]}
        total_services=$((total_services + 1))
        
        log_info "========== 检查服务: $service_name =========="
        
        local service_healthy=true
        
        # PM2 进程状态检查
        if ! check_pm2_status "$service_name"; then
            service_healthy=false
        fi
        
        # 端口连通性检查
        if ! check_port_connectivity "$service_name" "$port"; then
            service_healthy=false
        fi
        
        # HTTP 健康检查
        if ! check_http_health "$service_name" "$port"; then
            service_healthy=false
        fi
        
        # 响应时间检查
        if ! check_response_time "$service_name" "$port"; then
            service_healthy=false
        fi
        
        # 日志错误检查
        check_log_errors "$service_name"
        
        # 统计结果
        if $service_healthy; then
            healthy_services=$((healthy_services + 1))
            log_success "$service_name: 整体状态健康"
        else
            unhealthy_services=$((unhealthy_services + 1))
            log_error "$service_name: 整体状态异常"
        fi
        
        echo ""
    done
    
    # 生成报告
    generate_health_report
    
    # 输出总结
    log_info "========== 健康检查总结 =========="
    log_info "总服务数: $total_services"
    log_success "健康服务: $healthy_services"
    log_error "异常服务: $unhealthy_services"
    
    if [[ $unhealthy_services -eq 0 ]]; then
        log_success "所有服务运行正常!"
        return 0
    else
        log_error "发现 $unhealthy_services 个异常服务"
        return 1
    fi
}

# 主函数
main() {
    local action=${1:-"full"}
    
    case $action in
        "full")
            run_full_health_check
            ;;
        "pm2")
            for service_name in "${!SERVICES[@]}"; do
                check_pm2_status "$service_name"
            done
            ;;
        "ports")
            for service_name in "${!SERVICES[@]}"; do
                local port=${SERVICES[$service_name]}
                check_port_connectivity "$service_name" "$port"
            done
            ;;
        "system")
            check_system_resources
            ;;
        *)
            echo "用法: $0 [full|pm2|ports|system]"
            echo "  full   - 执行完整健康检查 (默认)"
            echo "  pm2    - 仅检查 PM2 进程状态"
            echo "  ports  - 仅检查端口连通性"
            echo "  system - 仅检查系统资源"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
