#!/bin/bash

# ========================================
# 服务器环境初始化脚本
# ========================================
# 
# 功能：
# - 安装 Node.js 和 PM2
# - 配置系统环境
# - 创建必要的目录和用户
# - 配置防火墙和安全设置
# - 安装监控工具
# ========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
NODE_VERSION="18.17.0"
PM2_VERSION="latest"
DEPLOY_USER="deploy"
APP_DIR="/opt/football-game-server"
LOG_DIR="/var/log/football-game-server"
BACKUP_DIR="/opt/football-game-server/backups"

# 检查是否为 root 用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要 root 权限运行"
        exit 1
    fi
}

# 更新系统
update_system() {
    log_info "更新系统包..."
    
    if command -v apt-get &> /dev/null; then
        apt-get update
        apt-get upgrade -y
        apt-get install -y curl wget git build-essential
    elif command -v yum &> /dev/null; then
        yum update -y
        yum groupinstall -y "Development Tools"
        yum install -y curl wget git
    else
        log_error "不支持的操作系统"
        exit 1
    fi
    
    log_success "系统更新完成"
}

# 安装 Node.js
install_nodejs() {
    log_info "安装 Node.js $NODE_VERSION..."
    
    # 使用 NodeSource 仓库安装 Node.js
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    
    if command -v apt-get &> /dev/null; then
        apt-get install -y nodejs
    elif command -v yum &> /dev/null; then
        yum install -y nodejs npm
    fi
    
    # 验证安装
    node_version=$(node --version)
    npm_version=$(npm --version)
    
    log_success "Node.js 安装完成: $node_version"
    log_success "npm 版本: $npm_version"
}

# 安装 PM2
install_pm2() {
    log_info "安装 PM2..."
    
    npm install -g pm2@$PM2_VERSION
    
    # 配置 PM2 开机自启
    pm2 startup
    
    # 创建 PM2 日志轮转配置
    pm2 install pm2-logrotate
    pm2 set pm2-logrotate:max_size 10M
    pm2 set pm2-logrotate:retain 30
    pm2 set pm2-logrotate:compress true
    
    log_success "PM2 安装完成"
}

# 创建部署用户
create_deploy_user() {
    log_info "创建部署用户: $DEPLOY_USER"
    
    # 创建用户
    if ! id "$DEPLOY_USER" &>/dev/null; then
        useradd -m -s /bin/bash "$DEPLOY_USER"
        log_success "用户 $DEPLOY_USER 创建完成"
    else
        log_warning "用户 $DEPLOY_USER 已存在"
    fi
    
    # 添加到 sudo 组
    usermod -aG sudo "$DEPLOY_USER"
    
    # 创建 SSH 目录
    sudo -u "$DEPLOY_USER" mkdir -p "/home/<USER>/.ssh"
    sudo -u "$DEPLOY_USER" chmod 700 "/home/<USER>/.ssh"
    
    log_success "部署用户配置完成"
}

# 创建应用目录
create_app_directories() {
    log_info "创建应用目录..."
    
    # 创建主要目录
    mkdir -p "$APP_DIR"
    mkdir -p "$LOG_DIR"
    mkdir -p "$BACKUP_DIR"
    
    # 设置权限
    chown -R "$DEPLOY_USER:$DEPLOY_USER" "$APP_DIR"
    chown -R "$DEPLOY_USER:$DEPLOY_USER" "$LOG_DIR"
    chown -R "$DEPLOY_USER:$DEPLOY_USER" "$BACKUP_DIR"
    
    # 设置日志目录权限
    chmod 755 "$LOG_DIR"
    
    log_success "应用目录创建完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        # Ubuntu/Debian 使用 ufw
        ufw --force enable
        ufw default deny incoming
        ufw default allow outgoing
        
        # 允许 SSH
        ufw allow ssh
        
        # 允许游戏服务端口范围
        ufw allow 3000:3700/tcp
        
        # 允许 HTTP/HTTPS
        ufw allow 80/tcp
        ufw allow 443/tcp
        
        log_success "UFW 防火墙配置完成"
        
    elif command -v firewall-cmd &> /dev/null; then
        # CentOS/RHEL 使用 firewalld
        systemctl enable firewalld
        systemctl start firewalld
        
        # 允许服务端口
        firewall-cmd --permanent --add-port=3000-3700/tcp
        firewall-cmd --permanent --add-service=ssh
        firewall-cmd --permanent --add-service=http
        firewall-cmd --permanent --add-service=https
        
        firewall-cmd --reload
        
        log_success "Firewalld 防火墙配置完成"
    else
        log_warning "未找到防火墙管理工具，请手动配置"
    fi
}

# 优化系统参数
optimize_system() {
    log_info "优化系统参数..."
    
    # 增加文件描述符限制
    cat >> /etc/security/limits.conf << EOF
$DEPLOY_USER soft nofile 65536
$DEPLOY_USER hard nofile 65536
root soft nofile 65536
root hard nofile 65536
EOF
    
    # 优化网络参数
    cat >> /etc/sysctl.conf << EOF
# 网络优化
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_tw_buckets = 5000
EOF
    
    # 应用系统参数
    sysctl -p
    
    log_success "系统参数优化完成"
}

# 安装监控工具
install_monitoring() {
    log_info "安装监控工具..."
    
    # 安装 htop
    if command -v apt-get &> /dev/null; then
        apt-get install -y htop iotop nethogs
    elif command -v yum &> /dev/null; then
        yum install -y htop iotop nethogs
    fi
    
    # 安装 PM2 监控模块
    pm2 install pm2-server-monit
    
    log_success "监控工具安装完成"
}

# 配置日志轮转
configure_logrotate() {
    log_info "配置日志轮转..."
    
    cat > /etc/logrotate.d/football-game-server << EOF
$LOG_DIR/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $DEPLOY_USER $DEPLOY_USER
    postrotate
        pm2 reloadLogs
    endscript
}
EOF
    
    log_success "日志轮转配置完成"
}

# 创建系统服务
create_systemd_service() {
    log_info "创建系统服务..."
    
    cat > /etc/systemd/system/football-game-server.service << EOF
[Unit]
Description=Football Game Server
After=network.target

[Service]
Type=forking
User=$DEPLOY_USER
WorkingDirectory=$APP_DIR
ExecStart=/usr/bin/pm2 start ecosystem.config.js --env production
ExecReload=/usr/bin/pm2 reload ecosystem.config.js --env production
ExecStop=/usr/bin/pm2 stop ecosystem.config.js
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
    
    systemctl daemon-reload
    systemctl enable football-game-server
    
    log_success "系统服务创建完成"
}

# 安装额外工具
install_additional_tools() {
    log_info "安装额外工具..."
    
    if command -v apt-get &> /dev/null; then
        apt-get install -y nginx certbot python3-certbot-nginx
    elif command -v yum &> /dev/null; then
        yum install -y nginx certbot python3-certbot-nginx
    fi
    
    log_success "额外工具安装完成"
}

# 主函数
main() {
    log_info "开始初始化服务器环境..."
    
    check_root
    update_system
    install_nodejs
    install_pm2
    create_deploy_user
    create_app_directories
    configure_firewall
    optimize_system
    install_monitoring
    configure_logrotate
    create_systemd_service
    install_additional_tools
    
    log_success "服务器环境初始化完成!"
    log_info "请执行以下步骤完成配置："
    log_info "1. 配置 SSH 密钥认证"
    log_info "2. 配置 Nginx 反向代理"
    log_info "3. 配置 SSL 证书"
    log_info "4. 测试部署脚本"
}

# 执行主函数
main "$@"
