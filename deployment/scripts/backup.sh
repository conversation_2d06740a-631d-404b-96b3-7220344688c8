#!/bin/bash

# ========================================
# 服务备份脚本
# ========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
BACKUP_DIR="/opt/football-game-server/backups"
APP_DIR="/opt/football-game-server"
RETENTION_DAYS=7
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 创建备份
create_backup() {
    local service_name=$1
    local server_id=$2
    local instance_index=$3
    
    local app_name="${service_name}-${server_id}-${instance_index}"
    local source_dir="$APP_DIR/$app_name"
    local backup_name="${app_name}-${TIMESTAMP}"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    if [[ ! -d "$source_dir" ]]; then
        log_error "源目录不存在: $source_dir"
        return 1
    fi
    
    log_info "创建备份: $app_name"
    
    # 创建备份目录
    mkdir -p "$BACKUP_DIR"
    
    # 停止服务
    log_info "停止服务: $app_name"
    pm2 stop "$app_name" || true
    
    # 创建备份
    log_info "复制文件到备份目录..."
    cp -r "$source_dir" "$backup_path"
    
    # 压缩备份
    log_info "压缩备份文件..."
    cd "$BACKUP_DIR"
    tar -czf "${backup_name}.tar.gz" "$backup_name"
    rm -rf "$backup_name"
    
    # 重启服务
    log_info "重启服务: $app_name"
    pm2 start "$app_name"
    
    log_success "备份完成: ${backup_name}.tar.gz"
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理 $RETENTION_DAYS 天前的备份..."
    
    find "$BACKUP_DIR" -name "*.tar.gz" -type f -mtime +$RETENTION_DAYS -delete
    
    log_success "旧备份清理完成"
}

# 恢复备份
restore_backup() {
    local backup_file=$1
    local service_name=$2
    local server_id=$3
    local instance_index=$4
    
    local app_name="${service_name}-${server_id}-${instance_index}"
    local target_dir="$APP_DIR/$app_name"
    
    if [[ ! -f "$backup_file" ]]; then
        log_error "备份文件不存在: $backup_file"
        return 1
    fi
    
    log_info "恢复备份: $backup_file"
    
    # 停止服务
    log_info "停止服务: $app_name"
    pm2 stop "$app_name" || true
    pm2 delete "$app_name" || true
    
    # 备份当前版本
    if [[ -d "$target_dir" ]]; then
        log_info "备份当前版本..."
        mv "$target_dir" "${target_dir}.pre-restore.$(date +%Y%m%d_%H%M%S)"
    fi
    
    # 解压备份
    log_info "解压备份文件..."
    cd "$BACKUP_DIR"
    tar -xzf "$(basename "$backup_file")"
    
    # 移动到目标位置
    local backup_name=$(basename "$backup_file" .tar.gz)
    mv "$backup_name" "$target_dir"
    
    # 设置权限
    chown -R deploy:deploy "$target_dir"
    
    # 启动服务
    log_info "启动服务: $app_name"
    cd "$target_dir"
    pm2 start ecosystem.config.js --env production
    
    log_success "备份恢复完成: $app_name"
}

# 列出备份
list_backups() {
    log_info "可用备份列表:"
    
    if [[ ! -d "$BACKUP_DIR" ]]; then
        log_warning "备份目录不存在: $BACKUP_DIR"
        return 1
    fi
    
    ls -la "$BACKUP_DIR"/*.tar.gz 2>/dev/null | while read -r line; do
        echo "  $line"
    done
}

# 主函数
main() {
    local action=${1:-""}
    
    case $action in
        "create")
            local service_name=${2:-""}
            local server_id=${3:-"server_001"}
            local instance_index=${4:-"0"}
            
            if [[ -z "$service_name" ]]; then
                log_error "请指定服务名称"
                echo "用法: $0 create <service> [server_id] [instance_index]"
                exit 1
            fi
            
            create_backup "$service_name" "$server_id" "$instance_index"
            cleanup_old_backups
            ;;
            
        "restore")
            local backup_file=${2:-""}
            local service_name=${3:-""}
            local server_id=${4:-"server_001"}
            local instance_index=${5:-"0"}
            
            if [[ -z "$backup_file" || -z "$service_name" ]]; then
                log_error "请指定备份文件和服务名称"
                echo "用法: $0 restore <backup_file> <service> [server_id] [instance_index]"
                exit 1
            fi
            
            restore_backup "$backup_file" "$service_name" "$server_id" "$instance_index"
            ;;
            
        "list")
            list_backups
            ;;
            
        "cleanup")
            cleanup_old_backups
            ;;
            
        *)
            echo "用法: $0 <action> [options]"
            echo ""
            echo "Actions:"
            echo "  create <service> [server_id] [instance_index]  - 创建备份"
            echo "  restore <backup_file> <service> [server_id] [instance_index]  - 恢复备份"
            echo "  list                                           - 列出备份"
            echo "  cleanup                                        - 清理旧备份"
            echo ""
            echo "Examples:"
            echo "  $0 create character server_001 0"
            echo "  $0 restore /opt/football-game-server/backups/character-server_001-0-20231201_120000.tar.gz character server_001 0"
            echo "  $0 list"
            echo "  $0 cleanup"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
