#!/bin/bash

# ========================================
# Football Game Server 快速部署脚本
# ========================================
# 
# 一键部署脚本，支持：
# - 自动检测环境
# - 智能选择部署方式
# - 交互式配置
# - 部署验证
# ========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

log_question() {
    echo -e "${CYAN}[QUESTION]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${BLUE}"
    echo "=========================================="
    echo "  Football Game Server 快速部署工具"
    echo "=========================================="
    echo -e "${NC}"
    echo "版本: 1.0.0"
    echo "作者: Football Game Team"
    echo "日期: $(date '+%Y-%m-%d %H:%M:%S')"
    echo ""
}

# 检查系统环境
check_environment() {
    log_step "检查系统环境..."
    
    # 检查操作系统
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        log_info "操作系统: Linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        log_info "操作系统: macOS"
    else
        log_error "不支持的操作系统: $OSTYPE"
        exit 1
    fi
    
    # 检查必要工具
    local required_tools=("node" "npm" "git")
    for tool in "${required_tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            local version=$($tool --version 2>/dev/null || echo "unknown")
            log_success "$tool: $version"
        else
            log_error "缺少必要工具: $tool"
            exit 1
        fi
    done
    
    # 检查 Docker (可选)
    if command -v docker &> /dev/null; then
        DOCKER_AVAILABLE=true
        log_success "Docker: $(docker --version)"
    else
        DOCKER_AVAILABLE=false
        log_warning "Docker 未安装，将使用传统部署方式"
    fi
    
    # 检查 PM2 (可选)
    if command -v pm2 &> /dev/null; then
        PM2_AVAILABLE=true
        log_success "PM2: $(pm2 --version)"
    else
        PM2_AVAILABLE=false
        log_warning "PM2 未安装，建议安装以获得更好的进程管理"
    fi
}

# 交互式配置
interactive_config() {
    log_step "交互式配置..."
    
    # 部署方式选择
    echo ""
    log_question "请选择部署方式:"
    echo "1) 传统部署 (推荐用于生产环境)"
    echo "2) Docker 部署 (推荐用于开发环境)"
    echo "3) 混合部署 (数据库用 Docker，服务用传统方式)"
    
    while true; do
        read -p "请输入选择 [1-3]: " deploy_method
        case $deploy_method in
            1)
                DEPLOY_METHOD="traditional"
                log_info "选择: 传统部署"
                break
                ;;
            2)
                if [[ "$DOCKER_AVAILABLE" == "true" ]]; then
                    DEPLOY_METHOD="docker"
                    log_info "选择: Docker 部署"
                    break
                else
                    log_error "Docker 不可用，请选择其他方式"
                fi
                ;;
            3)
                if [[ "$DOCKER_AVAILABLE" == "true" ]]; then
                    DEPLOY_METHOD="hybrid"
                    log_info "选择: 混合部署"
                    break
                else
                    log_error "Docker 不可用，请选择其他方式"
                fi
                ;;
            *)
                log_error "无效选择，请重新输入"
                ;;
        esac
    done
    
    # 环境选择
    echo ""
    log_question "请选择部署环境:"
    echo "1) 开发环境 (development)"
    echo "2) 测试环境 (staging)"
    echo "3) 生产环境 (production)"
    
    while true; do
        read -p "请输入选择 [1-3]: " env_choice
        case $env_choice in
            1)
                ENVIRONMENT="development"
                log_info "选择: 开发环境"
                break
                ;;
            2)
                ENVIRONMENT="staging"
                log_info "选择: 测试环境"
                break
                ;;
            3)
                ENVIRONMENT="production"
                log_info "选择: 生产环境"
                break
                ;;
            *)
                log_error "无效选择，请重新输入"
                ;;
        esac
    done
    
    # 服务选择
    echo ""
    log_question "请选择要部署的服务:"
    echo "1) 全部服务"
    echo "2) 仅全局服务 (gateway + auth)"
    echo "3) 仅区服服务 (character + economy + social + activity + match)"
    echo "4) 自定义选择"
    
    while true; do
        read -p "请输入选择 [1-4]: " service_choice
        case $service_choice in
            1)
                SERVICES="all"
                log_info "选择: 全部服务"
                break
                ;;
            2)
                SERVICES="gateway auth"
                log_info "选择: 仅全局服务"
                break
                ;;
            3)
                SERVICES="character economy social activity match"
                log_info "选择: 仅区服服务"
                break
                ;;
            4)
                echo "可选服务: gateway, auth, character, economy, social, activity, match"
                read -p "请输入服务名称 (空格分隔): " SERVICES
                log_info "选择: $SERVICES"
                break
                ;;
            *)
                log_error "无效选择，请重新输入"
                ;;
        esac
    done
    
    # 区服配置 (仅当部署区服服务时)
    if [[ "$SERVICES" == *"character"* ]] || [[ "$SERVICES" == *"economy"* ]] || [[ "$SERVICES" == "all" ]]; then
        echo ""
        read -p "请输入服务器ID (默认: server_001): " SERVER_ID
        SERVER_ID=${SERVER_ID:-"server_001"}
        
        read -p "请输入实例序号 (默认: 0): " INSTANCE_INDEX
        INSTANCE_INDEX=${INSTANCE_INDEX:-"0"}
        
        log_info "区服配置: $SERVER_ID, 实例: $INSTANCE_INDEX"
    else
        SERVER_ID="global"
        INSTANCE_INDEX="0"
    fi
    
    # 远程部署配置 (可选)
    echo ""
    read -p "是否部署到远程服务器? [y/N]: " remote_deploy
    if [[ "$remote_deploy" =~ ^[Yy]$ ]]; then
        REMOTE_DEPLOY=true
        read -p "请输入远程服务器配置名称 (默认: production): " REMOTE_SERVER
        REMOTE_SERVER=${REMOTE_SERVER:-"production"}
        log_info "将部署到远程服务器: $REMOTE_SERVER"
    else
        REMOTE_DEPLOY=false
        log_info "仅本地部署"
    fi
}

# 传统部署
traditional_deploy() {
    log_step "执行传统部署..."
    
    # 安装 PM2 (如果需要)
    if [[ "$PM2_AVAILABLE" == "false" ]]; then
        log_info "安装 PM2..."
        npm install -g pm2
    fi
    
    # 执行部署
    if [[ "$SERVICES" == "all" ]]; then
        ./deploy.sh all "$SERVER_ID" "$INSTANCE_INDEX" "$ENVIRONMENT"
    else
        for service in $SERVICES; do
            log_info "部署服务: $service"
            ./deploy.sh "$service" "$SERVER_ID" "$INSTANCE_INDEX" "$ENVIRONMENT"
        done
    fi
}

# Docker 部署
docker_deploy() {
    log_step "执行 Docker 部署..."
    
    cd docker
    
    # 设置环境变量
    export MONGODB_ROOT_PASSWORD=${MONGODB_ROOT_PASSWORD:-"admin123"}
    
    # 构建和启动服务
    if [[ "$SERVICES" == "all" ]]; then
        docker-compose up -d
    else
        # 启动基础设施
        docker-compose up -d mongodb redis
        
        # 启动指定服务
        for service in $SERVICES; do
            log_info "启动 Docker 服务: $service"
            if [[ "$service" == "character" || "$service" == "economy" || "$service" == "social" || "$service" == "activity" || "$service" == "match" ]]; then
                docker-compose up -d "${service}-server-001"
            else
                docker-compose up -d "$service"
            fi
        done
    fi
    
    cd ..
}

# 混合部署
hybrid_deploy() {
    log_step "执行混合部署..."
    
    # 启动基础设施 (Docker)
    log_info "启动基础设施服务..."
    cd docker
    docker-compose up -d mongodb redis
    cd ..
    
    # 等待基础设施就绪
    log_info "等待基础设施服务就绪..."
    sleep 10
    
    # 部署应用服务 (传统方式)
    traditional_deploy
}

# 部署验证
verify_deployment() {
    log_step "验证部署结果..."
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 15
    
    # 执行健康检查
    if [[ -f "scripts/health-check.sh" ]]; then
        log_info "执行健康检查..."
        chmod +x scripts/health-check.sh
        ./scripts/health-check.sh
    else
        log_warning "健康检查脚本不存在，跳过验证"
    fi
}

# 显示部署结果
show_deployment_result() {
    log_step "部署完成!"
    
    echo ""
    echo -e "${GREEN}=========================================="
    echo "  部署信息"
    echo -e "==========================================${NC}"
    echo "部署方式: $DEPLOY_METHOD"
    echo "环境: $ENVIRONMENT"
    echo "服务: $SERVICES"
    echo "服务器ID: $SERVER_ID"
    echo "实例序号: $INSTANCE_INDEX"
    
    if [[ "$REMOTE_DEPLOY" == "true" ]]; then
        echo "远程服务器: $REMOTE_SERVER"
    fi
    
    echo ""
    echo -e "${BLUE}=========================================="
    echo "  服务访问地址"
    echo -e "==========================================${NC}"
    
    if [[ "$SERVICES" == *"gateway"* ]] || [[ "$SERVICES" == "all" ]]; then
        echo "API 网关: http://localhost:3000"
    fi
    
    if [[ "$SERVICES" == *"auth"* ]] || [[ "$SERVICES" == "all" ]]; then
        echo "认证服务: http://localhost:3100"
    fi
    
    if [[ "$SERVICES" == *"character"* ]] || [[ "$SERVICES" == "all" ]]; then
        echo "角色服务: http://localhost:3200"
    fi
    
    echo ""
    echo -e "${YELLOW}=========================================="
    echo "  常用命令"
    echo -e "==========================================${NC}"
    
    if [[ "$DEPLOY_METHOD" == "docker" ]]; then
        echo "查看服务状态: docker-compose ps"
        echo "查看日志: docker-compose logs -f [service]"
        echo "停止服务: docker-compose down"
        echo "重启服务: docker-compose restart [service]"
    else
        echo "查看服务状态: pm2 list"
        echo "查看日志: pm2 logs [service]"
        echo "停止服务: pm2 stop [service]"
        echo "重启服务: pm2 restart [service]"
        echo "健康检查: ./scripts/health-check.sh"
    fi
    
    echo ""
    log_success "部署完成! 🎉"
}

# 主函数
main() {
    show_banner
    check_environment
    interactive_config
    
    # 确认部署
    echo ""
    log_question "确认部署配置:"
    echo "部署方式: $DEPLOY_METHOD"
    echo "环境: $ENVIRONMENT"
    echo "服务: $SERVICES"
    echo "服务器ID: $SERVER_ID"
    echo "实例序号: $INSTANCE_INDEX"
    
    read -p "确认部署? [y/N]: " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi
    
    # 执行部署
    case $DEPLOY_METHOD in
        "traditional")
            traditional_deploy
            ;;
        "docker")
            docker_deploy
            ;;
        "hybrid")
            hybrid_deploy
            ;;
    esac
    
    # 验证部署
    verify_deployment
    
    # 显示结果
    show_deployment_result
}

# 执行主函数
main "$@"
