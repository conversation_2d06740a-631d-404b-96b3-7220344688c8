// 自动生成的Training模块类型定义
// 基于服务端Schema生成，请勿手动修改

// 训练类型枚举
export enum TrainingCategory {
  PHYSICAL = 'physical',
  TECHNICAL = 'technical',
  TACTICAL = 'tactical',
  MENTAL = 'mental'
}

// 训练效果接口
export interface TrainingEffect {
  readonly attribute: string;
  readonly value: number;
}

// 训练类型接口
export interface TrainingType {
  readonly id: string;
  readonly name: string;
  readonly description: string;
  readonly category: TrainingCategory;
  readonly level: number;
  readonly cost: number;
  readonly duration: number; // 毫秒
  readonly effects: readonly TrainingEffect[];
  readonly requirements?: {
    readonly minLevel?: number;
    readonly maxLevel?: number;
    readonly positions?: readonly string[];
  };
  readonly icon?: string;
  readonly unlockLevel?: number;
}

// 训练队列接口
export interface TrainingQueue {
  readonly id: string;
  readonly heroId: string;
  readonly heroName: string;
  readonly heroAvatar?: string;
  readonly trainingTypeId: string;
  readonly trainingTypeName: string;
  readonly progress: number; // 0-100
  readonly remainingTime: string;
  readonly startTime: string;
  readonly endTime: string;
  readonly status: 'waiting' | 'training' | 'completed' | 'cancelled';
}

// 训练结果接口
export interface TrainingResult {
  readonly heroId: string;
  readonly heroName?: string;
  readonly trainingTypeId: string;
  readonly success: boolean;
  readonly attributeGains: Record<string, number>;
  readonly expGained: number;
  readonly message: string;
  readonly completedAt?: string;
  readonly bonusApplied?: boolean;
}

// 训练历史接口
export interface TrainingHistory {
  readonly id: string;
  readonly heroId: string;
  readonly heroName: string;
  readonly trainingTypeId: string;
  readonly trainingTypeName: string;
  readonly result: TrainingResult;
  readonly startTime: string;
  readonly endTime: string;
  readonly cost: number;
}

// 训练设施接口
export interface TrainingFacility {
  readonly id: string;
  readonly name: string;
  readonly level: number;
  readonly maxLevel: number;
  readonly description: string;
  readonly effects: readonly TrainingEffect[];
  readonly upgradeCost: number;
  readonly upgradeTime: number;
  readonly requirements?: {
    readonly characterLevel?: number;
    readonly otherFacilities?: Record<string, number>;
  };
}

// 训练计划接口
export interface TrainingPlan {
  readonly id: string;
  readonly name: string;
  readonly description: string;
  readonly trainingTypes: readonly string[];
  readonly duration: number; // 天数
  readonly totalCost: number;
  readonly expectedGains: Record<string, number>;
  readonly isActive: boolean;
  readonly createdAt: string;
}

// 每日任务接口
export interface DailyTask {
  readonly id: string;
  readonly name: string;
  readonly title: string;
  readonly description: string;
  readonly current: number;
  readonly target: number;
  readonly progress: number;
  readonly completed: boolean;
  readonly claimed: boolean;
  readonly reward: string;
  readonly type: string;
  readonly priority: number;
  readonly expiresAt?: string;
}

// 可变版本（用于前端编辑）
export interface MutableTrainingQueue extends Omit<TrainingQueue, never> {
  // 训练队列通常是只读的，不需要可变版本
}

export interface MutableTrainingPlan extends Omit<TrainingPlan, 'trainingTypes'> {
  trainingTypes: string[];
}