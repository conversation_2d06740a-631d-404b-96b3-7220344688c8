import { wsService } from './websocket';
import { useGlobalStore } from '@/stores/global';

/**
 * 推送事件监听器
 * 
 * 统一管理所有业务推送事件的客户端监听
 * 确保与服务端推送事件名称保持一致
 */

// 推送事件常量（与服务端保持一致）
export const PUSH_EVENTS = {
  // 角色相关
  CHARACTER_LEVEL_UP: 'character_level_up',
  CHARACTER_GOLD_CHANGE: 'character_gold_change',
  CHARACTER_ENERGY_FULL: 'character_energy_full',
  
  // 英雄相关
  HERO_LEVEL_UP: 'hero_level_up',
  HERO_STAR_UP: 'hero_star_up',
  HERO_ACQUIRED: 'hero_acquired',
  HERO_TRAINING_COMPLETE: 'hero_training_complete',
  
  // 社交相关
  FRIEND_REQUEST: 'friend_request',
  PRIVATE_MESSAGE: 'private_message',
  CLUB_INVITATION: 'club_invitation',
  
  // 比赛相关
  MATCH_FOUND: 'match_found',
  MATCH_EVENT: 'match_event',
  MATCH_SCORE_UPDATE: 'match_score_update',
  MATCH_FINISHED: 'match_finished',
  
  // 任务相关
  TASK_COMPLETE: 'task_complete',
  TASK_PROGRESS_UPDATE: 'task_progress_update',
  
  // 邮件相关
  NEW_MAIL: 'new_mail',
  
  // 系统相关
  SYSTEM_ANNOUNCEMENT: 'system_announcement',
  MAINTENANCE_NOTICE: 'maintenance_notice',
} as const;

/**
 * 推送事件监听器类
 */
export class PushEventListener {
  private globalStore = useGlobalStore();
  private eventHandlers = new Map<string, (data: any) => void>();

  /**
   * 初始化所有推送事件监听器
   */
  initialize(): void {
    this.setupCharacterEventListeners();
    this.setupHeroEventListeners();
    this.setupSocialEventListeners();
    this.setupMatchEventListeners();
    this.setupTaskEventListeners();
    this.setupMailEventListeners();
    this.setupSystemEventListeners();
  }

  /**
   * 清理所有事件监听器
   */
  cleanup(): void {
    Object.values(PUSH_EVENTS).forEach(eventName => {
      wsService.unsubscribe(eventName);
    });
  }

  // ==================== 角色相关事件监听 ====================

  private setupCharacterEventListeners(): void {
    // 角色升级
    wsService.subscribe(PUSH_EVENTS.CHARACTER_LEVEL_UP, (data) => {
      this.handleCharacterLevelUp(data);
    });

    // 金币变化
    wsService.subscribe(PUSH_EVENTS.CHARACTER_GOLD_CHANGE, (data) => {
      this.handleGoldChange(data);
    });

    // 体力恢复满
    wsService.subscribe(PUSH_EVENTS.CHARACTER_ENERGY_FULL, (data) => {
      this.handleEnergyFull(data);
    });
  }

  private handleCharacterLevelUp(data: any): void {
    console.log('角色升级事件:', data);

    // 显示升级通知
    this.globalStore.addNotification({
      type: 'success',
      title: '角色升级',
      message: `恭喜！您的角色升级到${data.newLevel}级！`
    });

    // 显示奖励（如果有）
    if (data.rewards && data.rewards.length > 0) {
      this.showRewards(data.rewards);
    }
  }

  private handleGoldChange(data: any): void {
    console.log('金币变化事件:', data);

    // 显示金币变化提示
    const changeText = data.change > 0 ? `+${data.change}` : `${data.change}`;
    this.globalStore.addNotification({
      type: data.change > 0 ? 'success' : 'warning',
      title: '金币变化',
      message: `${changeText} 金币 (${data.reason})`
    });
  }

  private handleEnergyFull(data: any): void {
    console.log('体力恢复事件:', data);

    // 显示体力恢复通知
    this.globalStore.addNotification({
      type: 'info',
      title: '体力恢复',
      message: '您的体力已恢复满！'
    });
  }

  // ==================== 英雄相关事件监听 ====================

  private setupHeroEventListeners(): void {
    // 英雄升级
    wsService.subscribe(PUSH_EVENTS.HERO_LEVEL_UP, (data) => {
      this.handleHeroLevelUp(data);
    });

    // 英雄升星
    wsService.subscribe(PUSH_EVENTS.HERO_STAR_UP, (data) => {
      this.handleHeroStarUp(data);
    });

    // 获得新英雄
    wsService.subscribe(PUSH_EVENTS.HERO_ACQUIRED, (data) => {
      this.handleHeroAcquired(data);
    });

    // 英雄训练完成
    wsService.subscribe(PUSH_EVENTS.HERO_TRAINING_COMPLETE, (data) => {
      this.handleHeroTrainingComplete(data);
    });
  }

  private handleHeroLevelUp(data: any): void {
    console.log('英雄升级事件:', data);

    // 显示升级通知
    this.globalStore.addNotification({
      type: 'success',
      title: '英雄升级',
      message: `${data.heroName}升级到${data.newLevel}级！`
    });

    // 播放升级动画
    this.playLevelUpAnimation(data.heroId);
  }

  private handleHeroStarUp(data: any): void {
    console.log('英雄升星事件:', data);

    // 显示升星通知
    this.globalStore.addNotification({
      type: 'success',
      title: '英雄升星',
      message: `${data.heroName}升星到${data.newStar}星！`
    });

    // 播放升星特效
    this.playStarUpEffect(data.heroId);
  }

  private handleHeroAcquired(data: any): void {
    console.log('获得新英雄事件:', data);

    // 显示获得英雄通知
    this.globalStore.addNotification({
      type: 'success',
      title: '获得新英雄',
      message: `恭喜获得${data.rarity}英雄：${data.heroName}！`
    });

    // 显示英雄获得动画
    this.showHeroAcquiredModal(data);
  }

  private handleHeroTrainingComplete(data: any): void {
    console.log('英雄训练完成事件:', data);

    // 显示训练完成通知
    this.globalStore.addNotification({
      type: 'info',
      title: '训练完成',
      message: `${data.heroName}的${data.trainingType}训练已完成！`
    });
  }

  // ==================== 社交相关事件监听 ====================

  private setupSocialEventListeners(): void {
    // 好友申请
    wsService.subscribe(PUSH_EVENTS.FRIEND_REQUEST, (data) => {
      this.handleFriendRequest(data);
    });

    // 私聊消息
    wsService.subscribe(PUSH_EVENTS.PRIVATE_MESSAGE, (data) => {
      this.handlePrivateMessage(data);
    });

    // 俱乐部邀请
    wsService.subscribe(PUSH_EVENTS.CLUB_INVITATION, (data) => {
      this.handleClubInvitation(data);
    });
  }

  private handleFriendRequest(data: any): void {
    console.log('好友申请事件:', data);

    // 显示好友申请通知
    this.globalStore.addNotification({
      type: 'info',
      title: '好友申请',
      message: `${data.fromUserName}向您发送了好友申请`
    });
  }

  private handlePrivateMessage(data: any): void {
    console.log('私聊消息事件:', data);

    // 显示消息通知
    this.globalStore.addNotification({
      type: 'info',
      title: '新消息',
      message: `${data.fromUserName}: ${data.content}`
    });

    // 播放消息提示音
    this.playMessageSound();
  }

  private handleClubInvitation(data: any): void {
    console.log('俱乐部邀请事件:', data);

    // 显示邀请通知
    this.globalStore.addNotification({
      type: 'info',
      title: '俱乐部邀请',
      message: `您收到了来自${data.clubName}的邀请`
    });
  }

  // ==================== 比赛相关事件监听 ====================

  private setupMatchEventListeners(): void {
    // 匹配成功
    wsService.subscribe(PUSH_EVENTS.MATCH_FOUND, (data) => {
      this.handleMatchFound(data);
    });

    // 比赛事件
    wsService.subscribe(PUSH_EVENTS.MATCH_EVENT, (data) => {
      this.handleMatchEvent(data);
    });

    // 比分更新
    wsService.subscribe(PUSH_EVENTS.MATCH_SCORE_UPDATE, (data) => {
      this.handleMatchScoreUpdate(data);
    });

    // 比赛结束
    wsService.subscribe(PUSH_EVENTS.MATCH_FINISHED, (data) => {
      this.handleMatchFinished(data);
    });
  }

  private handleMatchFound(data: any): void {
    console.log('匹配成功事件:', data);

    // 显示匹配成功通知
    this.globalStore.addNotification({
      type: 'success',
      title: '匹配成功',
      message: '找到对手，比赛即将开始！'
    });

    // 自动跳转到比赛界面
    this.navigateToMatch(data.matchId);
  }

  private handleMatchEvent(data: any): void {
    console.log('比赛事件:', data);

    // 如果是重要事件，显示通知
    if (this.isImportantMatchEvent(data.eventType)) {
      this.globalStore.addNotification({
        type: 'info',
        title: '比赛事件',
        message: data.description
      });
    }
  }

  private handleMatchScoreUpdate(data: any): void {
    console.log('比分更新事件:', data);
  }

  private handleMatchFinished(data: any): void {
    console.log('比赛结束事件:', data);

    // 显示比赛结果通知
    const isWin = data.result.winner === 'home'; // 假设用户是主队
    this.globalStore.addNotification({
      type: isWin ? 'success' : 'error',
      title: '比赛结束',
      message: isWin ? '恭喜获胜！' : '很遗憾败北'
    });
  }

  // ==================== 任务相关事件监听 ====================

  private setupTaskEventListeners(): void {
    // 任务完成
    wsService.subscribe(PUSH_EVENTS.TASK_COMPLETE, (data) => {
      this.handleTaskComplete(data);
    });

    // 任务进度更新
    wsService.subscribe(PUSH_EVENTS.TASK_PROGRESS_UPDATE, (data) => {
      this.handleTaskProgressUpdate(data);
    });
  }

  private handleTaskComplete(data: any): void {
    console.log('任务完成事件:', data);

    // 显示任务完成通知
    this.globalStore.addNotification({
      type: 'success',
      title: '任务完成',
      message: `任务"${data.taskName}"已完成！`
    });

    // 显示奖励
    if (data.rewards && data.rewards.length > 0) {
      this.showRewards(data.rewards);
    }
  }

  private handleTaskProgressUpdate(data: any): void {
    console.log('任务进度更新事件:', data);
  }

  // ==================== 邮件相关事件监听 ====================

  private setupMailEventListeners(): void {
    // 新邮件
    wsService.subscribe(PUSH_EVENTS.NEW_MAIL, (data) => {
      this.handleNewMail(data);
    });
  }

  private handleNewMail(data: any): void {
    console.log('新邮件事件:', data);

    // 显示新邮件通知
    this.globalStore.addNotification({
      type: 'info',
      title: '新邮件',
      message: `您收到了来自${data.sender}的邮件：${data.title}`
    });
  }

  // ==================== 系统相关事件监听 ====================

  private setupSystemEventListeners(): void {
    // 系统公告
    wsService.subscribe(PUSH_EVENTS.SYSTEM_ANNOUNCEMENT, (data) => {
      this.handleSystemAnnouncement(data);
    });

    // 维护通知
    wsService.subscribe(PUSH_EVENTS.MAINTENANCE_NOTICE, (data) => {
      this.handleMaintenanceNotice(data);
    });
  }

  private handleSystemAnnouncement(data: any): void {
    console.log('系统公告事件:', data);

    // 显示系统公告
    this.globalStore.addNotification({
      type: 'warning',
      title: '系统公告',
      message: data.title
    });
  }

  private handleMaintenanceNotice(data: any): void {
    console.log('维护通知事件:', data);

    // 显示维护通知
    this.globalStore.addNotification({
      type: 'warning',
      title: '维护通知',
      message: `服务器将于${new Date(data.startTime).toLocaleString()}进行维护`
    });
  }

  // ==================== 辅助方法 ====================

  private showRewards(rewards: any[]): void {
    console.log('显示奖励:', rewards);
    // TODO: 实现奖励弹窗显示逻辑
  }

  private playLevelUpAnimation(heroId: string): void {
    console.log('播放升级动画:', heroId);
    // TODO: 实现升级动画逻辑
  }

  private playStarUpEffect(heroId: string): void {
    console.log('播放升星特效:', heroId);
    // TODO: 实现升星特效逻辑
  }

  private showHeroAcquiredModal(heroData: any): void {
    console.log('显示英雄获得弹窗:', heroData);
    // TODO: 实现英雄获得弹窗逻辑
  }

  private playMessageSound(): void {
    console.log('播放消息提示音');
    // TODO: 实现音效播放逻辑
  }

  private acceptFriendRequest(requestId: string): void {
    console.log('接受好友申请:', requestId);
    // TODO: 实现接受好友申请逻辑
  }

  private rejectFriendRequest(requestId: string): void {
    console.log('拒绝好友申请:', requestId);
    // TODO: 实现拒绝好友申请逻辑
  }

  private navigateToMatch(matchId: string): void {
    console.log('跳转到比赛界面:', matchId);
    // TODO: 实现路由跳转逻辑
  }

  private isImportantMatchEvent(eventType: string): boolean {
    // 判断是否为重要比赛事件
    return ['goal', 'red_card', 'penalty'].includes(eventType);
  }
}

// 导出单例实例
export const pushEventListener = new PushEventListener();
export default pushEventListener;
