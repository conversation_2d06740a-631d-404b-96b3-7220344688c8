import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import type { Hero, TrainHeroDto, TrainResultDto, ServiceResponse } from '@/types'
import { gameService } from '@/services/game.service'
import { wsService } from '@/services/websocket'
import { useGlobalStore } from './global'

/**
 * 球员状态管理
 * 管理球员数据、训练、进化等功能
 */
export const useHeroStore = defineStore('hero', () => {
  const globalStore = useGlobalStore()

  // 状态定义
  const heroes = ref<Hero[]>([])
  const selectedHero = ref<Hero | null>(null)
  const isLoading = ref(false)

  // 计算属性
  const heroCount = computed(() => heroes.value.length)
  const mainTeamHeroes = computed(() => heroes.value.filter(h => h.isInFormation))
  const benchHeroes = computed(() => heroes.value.filter(h => !h.isInFormation && h.status === 'active'))
  const injuredHeroes = computed(() => heroes.value.filter(h => h.status === 'injured'))
  const suspendedHeroes = computed(() => heroes.value.filter(h => h.status === 'suspended'))

  // 按位置分组的球员
  const heroesByPosition = computed(() => {
    const grouped: Record<string, Hero[]> = {
      GK: [],
      DEF: [],
      MID: [],
      ATT: []
    }
    
    heroes.value.forEach(hero => {
      if (grouped[hero.position]) {
        grouped[hero.position].push(hero)
      }
    })
    
    return grouped
  })

  // 按品质分组的球员
  const heroesByQuality = computed(() => {
    const grouped: Record<number, Hero[]> = {}
    
    heroes.value.forEach(hero => {
      if (!grouped[hero.quality]) {
        grouped[hero.quality] = []
      }
      grouped[hero.quality].push(hero)
    })
    
    return grouped
  })

  // 动作方法

  /**
   * 获取球员列表 - 使用真实的WebSocket接口
   */
  const fetchHeroes = async () => {
    try {
      isLoading.value = true

      const response = await gameService.getHeroList()

      if (response.code === 0) {
        heroes.value = response.data || []
      } else {
        throw new Error(response.message || '获取球员列表失败')
      }
    } catch (error: any) {
      await globalStore.addNotification({
        type: 'error',
        title: '获取球员失败',
        message: error.message || '无法获取球员列表'
      })
      throw error
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 获取球员详情 - 使用真实的WebSocket接口
   */
  const fetchHeroDetail = async (heroId: string): Promise<Hero | null> => {
    try {
      const response: ServiceResponse<Hero> = await wsService.sendMessage('hero.getDetail', { heroId })

      if (response.code === 0 && response.data) {
        const hero = response.data

        // 更新本地缓存
        const index = heroes.value.findIndex(h => h.heroId === heroId)
        if (index !== -1) {
          heroes.value[index] = hero
        }

        return hero
      } else {
        throw new Error(response.message || '获取球员详情失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '获取球员详情失败',
        message: error.message
      })
      return null
    }
  }

  /**
   * 创建球员
   */
  const createHero = async (createDto: { name: string; position: string; configId?: number }): Promise<Hero | null> => {
    try {
      isLoading.value = true
      
      const response = await wsService.sendMessage('hero.create', createDto)
      
      if (response.code === 0) {
        const newHero = response.data
        heroes.value.push(newHero)
        
        globalStore.addNotification({
          type: 'success',
          title: '球员创建成功',
          message: `成功创建球员 ${newHero.name}`
        })
        
        return newHero
      } else {
        throw new Error(response.message || '创建球员失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '创建球员失败',
        message: error.message
      })
      return null
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 训练球员 - 使用真实的WebSocket接口
   */
  const trainHero = async (trainDto: TrainHeroDto): Promise<TrainResultDto | null> => {
    try {
      const response: ServiceResponse<TrainResultDto> = await wsService.sendMessage('training.train', trainDto)

      if (response.code === 0 && response.data) {
        const result = response.data

        // 更新球员数据 - 使用Vue3响应式更新
        const heroIndex = heroes.value.findIndex(h => h.heroId === trainDto.heroId)
        if (heroIndex !== -1 && result) {
          // 使用$patch进行批量更新，保持响应式
          const updates: Partial<Hero> = {}

          // 更新属性 - 创建新的attributes对象
          if (result.attributeChanges) {
            updates.attributes = {
              ...heroes.value[heroIndex].attributes,
              ...Object.fromEntries(
                Object.entries(result.attributeChanges).map(([attr, change]) => [
                  attr,
                  change.newValue
                ])
              )
            }
          }

          // 更新训练相关数据 - 创建新的training对象
          if (heroes.value[heroIndex].training) {
            updates.training = {
              ...heroes.value[heroIndex].training,
              trainingCount: (heroes.value[heroIndex].training?.trainingCount || 0) + (result.trainCount || 1),
              lastTrainingTime: result.trainingTime || Date.now()
            }
          }

          // 批量更新整个hero对象
          heroes.value[heroIndex] = {
            ...heroes.value[heroIndex],
            ...updates
          }
        }

        globalStore.addNotification({
          type: 'success',
          title: '训练完成',
          message: `球员 ${heroes.value[heroIndex]?.name} 训练完成，属性得到提升`
        })

        return result
      } else {
        throw new Error(response.message || '训练失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '训练失败',
        message: error.message
      })
      return null
    }
  }

  /**
   * 球员进化
   */
  const evolveHero = async (heroId: string, materialIds: string[]): Promise<boolean> => {
    try {
      const response = await wsService.sendMessage('hero.evolve', {
        heroId,
        materialIds
      })
      
      if (response.code === 0) {
        const evolvedHero = response.data
        
        // 更新球员数据
        const index = heroes.value.findIndex(h => h.heroId === heroId)
        if (index !== -1) {
          heroes.value[index] = evolvedHero
        }
        
        globalStore.addNotification({
          type: 'success',
          title: '进化成功',
          message: `球员 ${evolvedHero.name} 进化成功！`
        })
        
        return true
      } else {
        throw new Error(response.message || '进化失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '进化失败',
        message: error.message
      })
      return false
    }
  }

  /**
   * 球员升级
   */
  const levelUpHero = async (heroId: string, targetLevel: number): Promise<boolean> => {
    try {
      const response = await wsService.sendMessage('hero.levelUp', {
        heroId,
        targetLevel
      })
      
      if (response.code === 0) {
        const updatedHero = response.data
        
        // 更新球员数据
        const index = heroes.value.findIndex(h => h.heroId === heroId)
        if (index !== -1) {
          heroes.value[index] = updatedHero
        }
        
        globalStore.addNotification({
          type: 'success',
          title: '升级成功',
          message: `球员 ${updatedHero.name} 升级到 ${targetLevel} 级`
        })
        
        return true
      } else {
        throw new Error(response.message || '升级失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '升级失败',
        message: error.message
      })
      return false
    }
  }

  /**
   * 出售球员
   */
  const sellHero = async (heroId: string, price: number): Promise<boolean> => {
    try {
      const response = await wsService.sendMessage('hero.sell', {
        heroId,
        price
      })
      
      if (response.code === 0) {
        // 从列表中移除球员
        heroes.value = heroes.value.filter(h => h.heroId !== heroId)
        
        globalStore.addNotification({
          type: 'success',
          title: '出售成功',
          message: `球员已成功出售，获得 ${price} 金币`
        })
        
        return true
      } else {
        throw new Error(response.message || '出售失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '出售失败',
        message: error.message
      })
      return false
    }
  }

  /**
   * 释放球员
   */
  const releaseHero = async (heroId: string): Promise<boolean> => {
    try {
      const response = await wsService.sendMessage('hero.release', { heroId })
      
      if (response.code === 0) {
        // 从列表中移除球员
        heroes.value = heroes.value.filter(h => h.heroId !== heroId)
        
        globalStore.addNotification({
          type: 'success',
          title: '释放成功',
          message: '球员已成功释放'
        })
        
        return true
      } else {
        throw new Error(response.message || '释放失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '释放失败',
        message: error.message
      })
      return false
    }
  }

  /**
   * 锁定/解锁球员
   */
  const toggleHeroLock = async (heroId: string): Promise<boolean> => {
    try {
      const hero = heroes.value.find(h => h.heroId === heroId)
      if (!hero) return false
      
      const response = await wsService.sendMessage('hero.toggleLock', {
        heroId,
        isLocked: !hero.isLocked
      })
      
      if (response.code === 0) {
        hero.isLocked = !hero.isLocked
        
        globalStore.addNotification({
          type: 'success',
          title: hero.isLocked ? '锁定成功' : '解锁成功',
          message: `球员 ${hero.name} 已${hero.isLocked ? '锁定' : '解锁'}`
        })
        
        return true
      } else {
        throw new Error(response.message || '操作失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '操作失败',
        message: error.message
      })
      return false
    }
  }

  /**
   * 批量训练球员
   */
  const batchTrainHeroes = async (heroIds: string[], trainDto: Omit<TrainHeroDto, 'heroId'>): Promise<boolean> => {
    try {
      const results = await Promise.all(
        heroIds.map(heroId => trainHero({ ...trainDto, heroId }))
      )
      
      const successCount = results.filter(r => r !== null).length
      
      globalStore.addNotification({
        type: 'success',
        title: '批量训练完成',
        message: `成功训练 ${successCount}/${heroIds.length} 个球员`
      })
      
      return successCount > 0
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '批量训练失败',
        message: error.message
      })
      return false
    }
  }

  /**
   * 添加球员到本地状态
   */
  const addHero = (hero: Hero) => {
    const existingIndex = heroes.value.findIndex(h => h.heroId === hero.heroId)
    if (existingIndex !== -1) {
      heroes.value[existingIndex] = hero
    } else {
      heroes.value.push(hero)
    }
  }

  /**
   * 更新球员数据
   */
  const updateHero = (updatedHero: Hero) => {
    const index = heroes.value.findIndex(h => h.heroId === updatedHero.heroId)
    if (index !== -1) {
      heroes.value[index] = updatedHero
    }
  }

  /**
   * 移除球员
   */
  const removeHero = (heroId: string) => {
    heroes.value = heroes.value.filter(h => h.heroId !== heroId)
  }

  /**
   * 根据ID获取球员
   */
  const getHeroById = (heroId: string): Hero | null => {
    return heroes.value.find(h => h.heroId === heroId) || null
  }

  /**
   * 根据位置获取球员
   */
  const getHeroesByPosition = (position: string): Hero[] => {
    return heroes.value.filter(h => h.position === position)
  }

  /**
   * 获取可用于阵容的球员
   */
  const getAvailableHeroes = (): Hero[] => {
    return heroes.value.filter(h => h.status === 'active' && !h.isInFormation)
  }

  /**
   * 清空球员数据
   */
  const clearHeroes = () => {
    heroes.value = []
    selectedHero.value = null
  }

  /**
   * 更新球员属性 - Vue3响应式最佳实践
   */
  const updateHeroAttributes = (heroId: string, attributeUpdates: Partial<Hero['attributes']>) => {
    const heroIndex = heroes.value.findIndex(h => h.heroId === heroId)
    if (heroIndex !== -1) {
      // 创建新的attributes对象，保持响应式
      heroes.value[heroIndex] = {
        ...heroes.value[heroIndex],
        attributes: {
          ...heroes.value[heroIndex].attributes,
          ...attributeUpdates
        }
      }
    }
  }

  /**
   * 批量更新球员数据 - 支持深度合并
   */
  const updateHeroData = (heroId: string, updates: Partial<Hero>) => {
    const heroIndex = heroes.value.findIndex(h => h.heroId === heroId)
    if (heroIndex !== -1) {
      // 深度合并更新，保持响应式
      const currentHero = heroes.value[heroIndex]
      heroes.value[heroIndex] = {
        ...currentHero,
        ...updates,
        // 特殊处理嵌套对象
        attributes: updates.attributes ? {
          ...currentHero.attributes,
          ...updates.attributes
        } : currentHero.attributes,
        training: updates.training ? {
          ...currentHero.training,
          ...updates.training
        } : currentHero.training,
        evolution: updates.evolution ? {
          ...currentHero.evolution,
          ...updates.evolution
        } : currentHero.evolution
      }
    }
  }

  return {
    // 状态
    heroes: readonly(heroes),
    selectedHero: readonly(selectedHero),
    isLoading: readonly(isLoading),
    
    // 计算属性
    heroCount,
    mainTeamHeroes,
    benchHeroes,
    injuredHeroes,
    suspendedHeroes,
    heroesByPosition,
    heroesByQuality,
    
    // 方法
    fetchHeroes,
    fetchHeroDetail,
    createHero,
    trainHero,
    evolveHero,
    levelUpHero,
    sellHero,
    releaseHero,
    toggleHeroLock,
    batchTrainHeroes,
    addHero,
    updateHero,
    removeHero,
    getHeroById,
    getHeroesByPosition,
    getAvailableHeroes,
    clearHeroes,
    updateHeroAttributes,
    updateHeroData
  }
})
