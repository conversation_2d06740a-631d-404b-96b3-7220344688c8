# API访问控制配置
# 
# 环境说明：
# - development: 开发环境，默认禁用API访问控制（便于开发调试）
# - test: 测试环境，强制禁用API访问控制（便于自动化测试）
# - production: 生产环境，强制启用API访问控制（保证安全）

# 是否启用API访问控制
# 开发环境：设置为 true 启用，false 禁用（默认false）
# 测试环境：此设置无效，始终禁用
# 生产环境：此设置无效，始终启用
ENABLE_API_ACCESS_CONTROL=false

# 安全日志级别
# debug: 记录所有API调用（包括允许的）
# info: 记录重要事件
# warn: 只记录被拦截的调用
# error: 只记录错误
SECURITY_LOG_LEVEL=warn

# 是否启用安全事件监控
# 生产环境建议启用，开发环境可以禁用
ENABLE_SECURITY_MONITORING=false

# 安全事件上报地址（生产环境使用）
SECURITY_MONITORING_ENDPOINT=

# 内部API调用统计
# 是否启用API调用统计
ENABLE_API_STATS=true

# 统计数据保留时间（小时）
API_STATS_RETENTION_HOURS=24
